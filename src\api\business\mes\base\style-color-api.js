/**
 * 款式颜色表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-11-03 20:49:04
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const styleColorApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/styleColor/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/styleColor/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/styleColor/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/styleColor/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/styleColor/batchDelete', idList);
  },

  /**
   * 查询全部  <AUTHOR>
   */
  queryAll: (param) => {
      return postRequest('/styleColor/queryAll',param);
  },

};

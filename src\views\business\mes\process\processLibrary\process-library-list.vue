<!--
  * 工序库
  *
  * @Author:    xmt
  * @Date:      2024-07-13 15:19:57
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
            </a-form-item>
            <a-form-item label="停用标识" class="smart-query-form-item">
                <a-select v-model:value="queryForm.enableFlag" placeholder="请选择">
                <a-select-option value="false">启用</a-select-option>
                <a-select-option value="true">停用</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
            @change="handleTableChange"

        >
            <template #bodyCell="{text, record, column }">
                <template v-if="column.dataIndex === 'processLibraryNumber'">
                <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
              <template v-if="column.dataIndex === 'enableFlag'">
                <a-tag :color="PROCESS_STATUS_ENUM.getEnum(text).color">{{ PROCESS_STATUS_ENUM.getEnum(text).label }}</a-tag>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
    <ProcessLibraryDetailForm ref="processLibraryDetailForm" @reloading="queryData" />
</template>
<script setup>
import {reactive, ref, onMounted, watch} from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/src/components/framework/smart-loading';
    import { processLibraryApi } from '/@/api/business/mes/process/process-library-api';
    import {useRouter,useRoute} from 'vue-router';
    import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
    import { smartSentry } from '/src/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import {PROCESS_STATUS_ENUM} from "/@/constants/business/mes/process/process-const.js"
    import ProcessLibraryDetailForm from './process-library-detail-form.vue';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        {
            title: '工艺库编号码',
            dataIndex: 'processLibraryNumber',
            ellipsis: true,
          align: 'center'
        },
      {
        title: '名称',
        dataIndex: 'name',
        ellipsis: true,
        align: 'center'
      },
        {
            title: '停用标识',
            dataIndex: 'enableFlag',
            ellipsis: true,
          align: 'center'
        },

      {
        title: '更新时间',
        dataIndex: 'updateTime',
        ellipsis: true,
        align: 'center',
        sorter: true,//启用排序
        sortDirections: ['ascend', 'descend'], // 允许升序和降序
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        ellipsis: true,
        align: 'center',
        sorter: true,//启用排序
        sortDirections: ['ascend', 'descend'], // 允许升序和降序
      },
      {
        title: '备注',
        dataIndex: 'remark',
        ellipsis: true,
        align: 'center'
      },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 90,
          align: 'center'
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        queryKey: undefined, //关键字查询
        enableFlag: undefined, //停用标识;0启用，1停用
        pageNum: 1,
        pageSize: 10,
      sortItemList:[{column:'create_time',isAsc:false}],
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);
    //
    const router = useRouter()

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }
    //处理表格时间顺序变化事件
 async function handleTableChange(pagination,filters,sorter) {
      if(sorter&&sorter.field==='createTime'){
      queryForm.sortItemList = 
      [{
        column:'create_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='updateTime'){
        queryForm.sortItemList = 
      [{
        column:'update_time',
        isAsc:sorter.order==='ascend'
      }];
    }
    if(!sorter.order){
      queryForm.sortItemList=
      [{
        column:'create_time',
        isAsc:false
      }]
    }
    await queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await processLibraryApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }


    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const processLibraryDetailForm = ref();
    function showForm(data) {
        processLibraryDetailForm.value.show(data);
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await processLibraryApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }
    const back = useRoute()
    //监控路由变化,保存跳转后刷新页面
    watch(() => back.query, () => {
    if(back.name==='249'){
        queryData()
    }
    },{
      immediate: true
    })
</script>
<style scoped>
.ant-table-striped :deep(.table-striped) td {
  background: rgb(244, 243, 243);
}
</style>

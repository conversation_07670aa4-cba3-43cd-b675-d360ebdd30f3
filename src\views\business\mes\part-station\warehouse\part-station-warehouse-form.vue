<!--
  * 裁片仓库表
  *
  * @Author:    cjm
  * @Date:      2024-10-06 16:48:11
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-row>
        <a-col span="24">
          <a-form-item label="仓库编码" name="warehouseCode">
            <a-input style="width: 100%" v-model:value="form.warehouseCode" placeholder="仓库编码" />
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="仓库名称" name="name">
            <a-input style="width: 100%" v-model:value="form.name" placeholder="仓库名称" />
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="仓库地址" name="address">
            <a-input style="width: 100%" v-model:value="form.address" placeholder="仓库地址" />
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="任务人员" name="taskerIds">
            <EmployeeMoreSelect v-model:value="form.taskerIds" placeholder="任务人员" />
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="备注" name="remark">
            <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationWarehouseApi } from '/@/api/business/mes/part-station/warehouse/part-station-warehouse-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import EmployeeMoreSelect from '/@/components/system/employee-more-select/index.vue';
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      const processedData = {
      ...rowData,
      taskerIds: rowData.taskerIds ? JSON.parse(rowData.taskerIds) : []
    };
      Object.assign(form, processedData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined, //主键
    warehouseCode: undefined, //仓库编码
    name: undefined, //仓库名称
    address: undefined, //仓库地址
    remark: undefined, //备注
    taskerIds: [], //任务人员
  };

  let form = reactive({ ...formDefault });

  const rules = {
    // id: [{ required: true, message: '主键 必填' }],
    warehouseCode: [{ required: true, message: '仓库编码 必填' }],
    name: [{ required: true, message: '仓库名称 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await partStationWarehouseApi.update(form);
      } else {
        await partStationWarehouseApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

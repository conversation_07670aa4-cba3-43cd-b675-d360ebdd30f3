<!--
  * 款式尺码多选组件
  * 返回所选尺码名称
  * @Author:    linwj
  * @Date:      2024-11-07 20:49:04
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="'尺码总览'"
      width="800px"
      v-model:open="open"
      @ok="handleOk">

    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="模板" class="smart-query-form-item">
          <a-input style="width: 100px" v-model:value="queryForm.templateName" placeholder="模板查询"/>
        </a-form-item>
        <a-form-item label="尺码" class="smart-query-form-item">
          <a-input style="width: 100px" v-model:value="queryForm.sizeMessage" placeholder="尺码查询"/>
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="queryData">
            <template #icon>
              <SearchOutlined/>
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <div style="margin-bottom: 10px">
      <span style="margin-left: 8px">
        <template v-if="true">
          {{ `已选${num}项` }}
        </template>
      </span>

      <a-button type="primary"
                style="background-color: red; float: right"
                @click="()=> {sizeArr.length = 0
                console.log(sizeArr)}">
        <template #icon>
          <WarningFilled />
        </template>
        清空
      </a-button>
    </div>

    <a-table :columns="columns"
             :dataSource="tableData"
             rowKey="id"
             :pagination="{
                defaultPageSize:pageForm.pageSize, //默认条数
                defaultCurrent:pageForm.pageNum, //默认页码
                pageSizeOptions:PAGE_SIZE_OPTIONS, //每页可展示条数
                showSizeChanger:true, //配合上面的属性，页数切换器
                showQuickJumper:true, //页码跳转
                current:pageForm.pageNum,
                showLessItems:true,
                total:total,
                showTotal:()=> `共 ${total} 条`,
                onChange:handlePageChange,
             }"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex==='sizeInfo'">

          <span v-for="item in record.sizeInfo" :key="item.id">
            <a-badge>
              <!--控制选择标签-->
              <template #count v-if="sizeArr[item.id]!== undefined">
                <CheckCircleFilled style="color: #f5222d"/>
              </template>
              <a-button type="primary" @click="selectOne(item.id,item.sizeMessage)">
                {{item.sizeMessage}}
              </a-button>
            </a-badge>
            <a-divider type="vertical"/>
          </span>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import {computed, onMounted, reactive, ref} from 'vue';
import {sizeTemplateApi} from "/@/api/business/mes/base/size-template-api.js";
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
//---------------- 定义事件 --------------
const emits = defineEmits(['reloadList']);

const columns = ref([
  {
    title: '尺码模板',
    dataIndex: 'templateName',
  },
  {
    title: '尺码信息',
    dataIndex: 'sizeInfo',
  },
]);

//分页表单
const pageInfo = {
  pageSize: 5,
  pageNum: 1,
};
const pageForm = reactive({...pageInfo})
// 分页总数
const total = ref(0);
function handlePageChange(pageNum, pageSize){
  pageForm.pageNum = pageNum
  pageForm.pageSize = pageSize
}

// 重置查询条件
function resetQuery() {
  Object.assign(queryForm, queryFormState);
  Object.assign(pageForm, pageInfo);
  queryData();
}

const open = ref(false);
function show(){
  open.value = true;
}
const handleOk = () => {
  emits('reloadList',sizeArr.value)
  sizeArr.value.length = 0
  open.value = false;
};

const queryFormState = {
  templateName: undefined, //模板查询
  sizeMessage: undefined, //尺码查询
};
// 查询表单form
const queryForm = reactive({...queryFormState});

const tableData = ref([])
// 查询数据
async function queryData() {
  let queryResult = await sizeTemplateApi.querySizeAndTemplateInfo(queryForm);
  tableData.value = queryResult.data.filter((item)=>{
    return item.sizeInfo.length > 0;
  })
  total.value = tableData.value.length
}

//-------------- 选择尺寸信息 ------------------
const sizeArr = ref([])

function selectOne(sizeId,sizeMessage){
  console.log(sizeMessage)
  if(sizeArr.value[sizeId]===undefined){
    sizeArr.value[sizeId] = sizeMessage
  }else {
    delete sizeArr.value[sizeId]
  }
}

const num = computed(()=> {
  let num = 0;
  sizeArr.value.map(()=>{
    num++;
  })
  return num;
})

onMounted(queryData);
//--------------------------------


defineExpose({
  show,
})
</script>




<!--
  * app版本管理
  *
  * @Author:    cjm
  * @Date:      2025-02-25 14:44:40
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
      <a-form-item label="appId" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.appId" placeholder="appId"/>
      </a-form-item>
      <a-form-item label="发布状态" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.status" placeholder="请选择状态" allowClear>
          <a-select-option v-for="item in VERSION_STATUS_ENUM.getOptions()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="发布日期" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.releaseDate" :presets="defaultTimeRanges" style="width: 150px"
                        @change="onChangeReleaseDate"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined/>
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="danger" size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined/>
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData"/>
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                状态
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu @click="({ key }) => onUpdateStatus(record, key)">
                  <a-menu-item v-for="item in VERSION_STATUS_ENUM.getOptions()" :key="item.value">
                    {{ item.label }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <AppVersionForm ref="formRef" @reloadList="queryData"/>

  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {appVersionApi} from '/@/api/business/mes/system/app-version/app-version-api';
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';
import {smartSentry} from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import {defaultTimeRanges} from '/@/lib/default-time-ranges';
import AppVersionForm from './app-version-form.vue';
import { VERSION_STATUS_ENUM, PACKAGE_TYPE_ENUM } from "/@/constants/business/mes/system/app-version-const.js";
// ---------------------------- 表格列 ----------------------------

const columns = ref([


  {
    title: 'APP_ID',
    dataIndex: 'appId',
    ellipsis: true,
  },
  {
    title: 'app名称',
    dataIndex: 'appName',
    ellipsis: true,
  },
  {
    title: '版本号排序',
    dataIndex: 'versionSort',
    ellipsis: true,
  },
  {
    title: '版本名称(号)',
    dataIndex: 'versionName',
    ellipsis: true,
  },
  {
    title: '升级包类型',
    dataIndex: 'packageType',
    ellipsis: true,
    customRender: ({ text }) => {
      const packageTypeEnum = PACKAGE_TYPE_ENUM.getEnum(text);
      return packageTypeEnum ? packageTypeEnum.label : text;
    }
  },
  {
    title: '是否强制更新',
    dataIndex: 'forceUpdate',
    ellipsis: true,
    customRender: ({ text }) => {
      return text ? '是' : '否';
    }
  },
  {
    title: '发布状态',
    dataIndex: 'status',
    ellipsis: true,
    customRender: ({ text }) => {
      const statusEnum = VERSION_STATUS_ENUM.getEnum(text);
      return statusEnum ? statusEnum.label : text;
    }
  },
  {
    title: '发布日期',
    dataIndex: 'releaseDate',
    ellipsis: true,
  },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime',
  //   ellipsis: true,
  // },
  //
  // {
  //   title: '更新时间',
  //   dataIndex: 'updateTime',
  //   ellipsis: true,
  // },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 150,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //关键字查询
  appId: undefined, //appId
  status: undefined, //状态;published（已发布）、draft（草稿）
  releaseDate: [], //发布日期
  releaseDateBegin: undefined, //发布日期 开始
  releaseDateEnd: undefined, //发布日期 结束
  pageNum: 1,
  pageSize: 10,
  sortItemList:
      [{column: "version_sort", isAsc: false}]
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await appVersionApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

function onChangeReleaseDate(dates, dateStrings) {
  queryForm.releaseDateBegin = dateStrings[0];
  queryForm.releaseDateEnd = dateStrings[1];
}

// ---------------------------- 修改状态 ----------------------------
async function onUpdateStatus(record, status) {
  try {
    SmartLoading.show();
    await appVersionApi.updateStatus({
      id: record.id,
      status: status
    });
    message.success('状态修改成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求删除
async function requestDelete(data) {
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await appVersionApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await appVersionApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
</script>

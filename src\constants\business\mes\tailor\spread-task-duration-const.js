/**
 * 铺布任务时长枚举
 *
 * @Author:    AI Assistant
 * @Date:      2025-01-19
 * @Copyright  zscbdic
 */

export const SPREAD_TASK_DURATION_ENUM = {
  DURATION_30: {
    value: 30,
    desc: '30分钟',
    label: '30分钟',
  },
  DURATION_60: {
    value: 60,
    desc: '60分钟',
    label: '60分钟',
  },
  DURATION_90: {
    value: 90,
    desc: '90分钟',
    label: '90分钟',
  },
  DURATION_120: {
    value: 120,
    desc: '120分钟',
    label: '120分钟',
  },
  DURATION_150: {
    value: 150,
    desc: '150分钟',
    label: '150分钟',
  },
  DURATION_180: {
    value: 180,
    desc: '180分钟',
    label: '180分钟',
  },
  DURATION_210: {
    value: 210,
    desc: '210分钟',
    label: '210分钟',
  },
  DURATION_240: {
    value: 240,
    desc: '240分钟',
    label: '240分钟',
  },
  DURATION_270: {
    value: 270,
    desc: '270分钟',
    label: '270分钟',
  },
  DURATION_300: {
    value: 300,
    desc: '300分钟',
    label: '300分钟',
  },

  /**
   * 获取所有选项
   */
  getOptions() {
    return [
      this.DURATION_30,
      this.DURATION_60,
      this.DURATION_90,
      this.DURATION_120,
      this.DURATION_150,
      this.DURATION_180,
      this.DURATION_210,
      this.DURATION_240,
      this.DURATION_270,
      this.DURATION_300,
    ];
  },

  /**
   * 根据值获取枚举
   */
  getEnum(value) {
    for (let key in this) {
      if (this[key].value === value) {
        return this[key];
      }
    }
    return null;
  },
};

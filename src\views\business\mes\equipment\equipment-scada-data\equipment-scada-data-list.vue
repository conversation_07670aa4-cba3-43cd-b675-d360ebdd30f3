<!--
  * 设备监控
  *
  * @Author:    linwj
  * @Date:      2025-02-09 20:12:33
  * @Copyright  zscbdic
-->
<template>
  <div>
    <a-row :gutter="5">
      <a-col :span="4">
      <!-- 设备类型-->
        <div style="flex: 1;height: 100%;background-color: #ffffff;border-radius: 5px;padding: 5px 10px">
          <a-typography-title :level="5">设备类型</a-typography-title>
          <a-tree
              :tree-data="typeTreeData"
              default-expand-all
              @select="handleTypeTreeSelect"
          />
        </div>
      </a-col>

      <a-col :span="20">
        <!-- 设备搜索查询-->
        <a-form class="smart-query-form" style="border-radius: 5px">
          <a-row class="smart-query-form-row">
            <a-form-item label="设备查询" class="smart-query-form-item">
              <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="请输入编号或名称" />
            </a-form-item>
            <a-form-item label="物联平台" class="smart-query-form-item">
              <a-select
                  ref="select"
                  v-model:value="queryForm.iotNetworkPlatform"
                  :allowClear="true"
                  :options="iotPlatformOptions"
                  placeholder="物联平台"
                  style="width: 120px"
              />
            </a-form-item>
            <a-form-item label="车间" class="smart-query-form-item">
              <a-select
                  ref="select"
                  v-model:value="queryForm.workshopId"
                  :allowClear="true"
                  :options="workshopOptions"
                  placeholder="车间"
                  style="width: 120px"
              />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="queryData" >
                <template #icon>
                  <SearchOutlined />
                </template>
                查询
              </a-button>
              <a-button @click="resetQuery" class="smart-margin-left10">
                <template #icon>
                  <ReloadOutlined />
                </template>
                重置
              </a-button>
            </a-form-item>
          </a-row>
        </a-form>
        <!--  设备列表卡片-->
        <a-card :body-style='{padding: "5px 15px 15px"}'>
          <div style="display: flex;justify-content: space-between">
            <a-typography-title :level="5">设备列表</a-typography-title>
<!--            实时刷新开关-->
            <div>
              <a-form-item label="实时更新">
                  <a-switch v-model:checked="realTimeUpdate" checked-children="开" un-checked-children="关" @change="realTimeUpdateChange"/>
              </a-form-item>
            </div>
          </div>
          <div style="display: flex;flex-wrap: wrap;">
            <DeviceCard v-for="device in equipmentScadaDataCopy" :key="device.equipmentId"
                :equipment-name="device.equipmentName"
                :equipment-type-name="device.equipmentTypeName"
                :equipment-number="device.equipmentNumber"
                :work-shop-name="device.workShopName"
                :last-scada-data-update-time="device.lastScadaDataUpdateTime"
                :equipment-scada-run-status="device.equipmentScadaRunStatus"
                :equipment-scada-online-status="device.equipmentScadaOnlineStatus"
                :scada-platform="$smartEnumPlugin.getValueDesc('IOT_PLATFORM_ENUM')[device.scadaPlatform]"
                :last-scada-equipment-up-time="device.lastScadaEquipmentUpTime"
                @click="showDeviceInfoModal(device.equipmentId,device.equipmentName)"
              />
          </div>
          <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
  <DeviceDetailModal ref="deviceInfoModal" />
</template>
<script setup>
import DeviceCard from './components/device-card.vue'
import { equipmentTypeApi } from '/src/api/business/mes/equipment/equipment-type-api';
import {onMounted, onUnmounted, reactive, ref} from "vue";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {equipmentScadaDataApi} from "/@/api/business/mes/equipment/equipment-scada-data-api.js";
import {IOT_PLATFORM_ENUM} from "/@/constants/business/mes/equipment/iot-platform-const.js";
import _ from 'lodash'
import {workshopApi} from "/@/api/business/mes/factory/workshop-api.js";
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import DeviceDetailModal from "/@/views/business/mes/equipment/equipment-scada-data/components/device-detail-modal.vue";

// 默认根节点key
const DEFAULT_ROOT_KEY = 0
const typeTreeData = ref([
  {
    title: '全部',
    key: DEFAULT_ROOT_KEY,
    children: [
    ],
  },
])
// 查询设备类型列表
async function queryEquipmentTypeList() {
  await equipmentTypeApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    res.data.list.forEach((item)=>{
      typeTreeData.value[0].children.push({
        title: item.name,
        key: item.id,
      })
    })
  })
}

// ------------------ 查询相关 ------------------
const queryFormState = {
  queryKey: undefined, // 设备名称或编号
  workshopId:undefined, // 车间
  iotNetworkPlatform:undefined, // 物联平台
  pageNum: 1,
  pageSize: 10,
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

const equipmentScadaData = ref()
// 设备scada数据的副本
const equipmentScadaDataCopy = ref()
// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await equipmentScadaDataApi.queryPage(queryForm);
    equipmentScadaData.value = queryResult.data.list.sort((a, b) => a.equipmentTypeId - b.equipmentTypeId) // 根据equipmentTypeId排序
    equipmentScadaDataCopy.value = _.cloneDeep(equipmentScadaData.value)
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

// 处理类型树选中事件
function handleTypeTreeSelect(typeId){
  if(!(_.isEmpty(typeId) || typeId[0] === DEFAULT_ROOT_KEY)){
    equipmentScadaDataCopy.value = equipmentScadaData.value.filter((item)=>{
        return item.equipmentTypeId === typeId[0]
      })
  }else {
    queryData()
  }
}

const workshopOptions = ref([])
async function queryWorkshopList() {
  await workshopApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    // 输出设备类别options
    workshopOptions.value = res.data.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    })
  });
}

//设备物联平台下拉数据
const iotPlatformOptions = ref([])
function outstandingPlatform(){
  const entries = Object.entries(IOT_PLATFORM_ENUM)
  iotPlatformOptions.value = entries.map((item)=>{
    return {
      label:item[1].label,
      value:item[1].value
    }
  })
}

//------------------- 实时更新 ------------------
// 实时更新check
const realTimeUpdate = ref(false)
let timerId = null

function realTimeUpdateChange(checked) {
  if (checked) {
  // 清除已有定时器，防止重复创建
  clearInterval(timerId)
  queryData()
  // 启动新的定时器并保存ID
  timerId = setInterval(() => {
      queryData()
  }, 5000)
  } else {
    // 清除定时器
    if (timerId) {
      clearInterval(timerId)
      timerId = null
    }
  }
}

// --------------------------- 设备信息对话框 ---------------------------
const deviceInfoModal = ref()
function showDeviceInfoModal(equipmentId,equipmentName){
  deviceInfoModal.value.show(equipmentId,equipmentName)
}

onMounted(()=>{
  queryEquipmentTypeList()
  queryData()
  outstandingPlatform()
  queryWorkshopList()
})

// 页面取消挂载时清除定时器
onUnmounted(() => {
  if (timerId) {
    clearInterval(timerId)
    timerId = null
  }
})
</script>

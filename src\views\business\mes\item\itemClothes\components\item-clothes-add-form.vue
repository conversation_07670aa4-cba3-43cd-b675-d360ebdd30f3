<!--
  * 新增成衣
  *
  * @Author:    fkf
  * @Date:      2025-01-08
  * @Copyright  zscbdic
-->
<template>
  <a-modal title="新增成衣" width="1000px" :open="visibleFlag" @cancel="onClose" @ok="onSubmit">
    <a-tabs v-model:activeKey="activeKey" tab-position="left">
      <a-tab-pane key="1" tab="基本信息">
        <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
          <div><strong>物料基本信息</strong></div>
        </a-row>
        <!--基本信息表-->
        <a-form :rules="rules" :label-col="{ span: 6 }" :model="form">
          <a-row>
            <a-col :span="12">
              <a-form-item label="物料编号" name="number">
                <a-input style="width: 90%" placeholder="请输入,忽略将自动生成" v-model:value="form.number" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物料名称" name="name">
                <a-input style="width: 90%" placeholder="物料名称" v-model:value="form.name" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物料分类" name="typeId">
                <a-cascader style="width: 90%" :options="itemTypeTree" placeholder="请选择物料分类" v-model:value="form.typeId" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="单位" name="unitId">
                <a-select style="width: 90%" :options="unitOption" placeholder="请选择" v-model:value="form.unitId" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="规格型号" name="model">
                <a-input style="width: 90%" placeholder="规格型号" v-model:value="form.model" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="供应商" name="supplierId">
                <a-select style="width: 90%" :options="supplierOption" placeholder="请选择" v-model:value="form.supplierId" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="季度" name="seasonId">
                <a-cascader style="width: 90%" :options="seasonTree" placeholder="请选择" v-model:value="form.seasonId" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="款式" name="styleId">
                <a-cascader style="width: 90%" placeholder="请选择" :options="itemStyleTree" v-model:value="form.styleId" />
              </a-form-item>
            </a-col>
            <!-- <a-col :span="12">
              <a-form-item label="类型"  name="category" >
                <a-radio-group  button-style="solid">
                  <a-radio-button :value="0">半成品</a-radio-button>
                  <a-radio-button :value="1">成品</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col> -->
            <a-col :span="12">
              <a-form-item label="部位" name="partList">
                <part-select v-model:value="form.partList" width="90%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否停用" name="enableFlag">
                <a-radio-group button-style="solid" v-model:value="form.enableFlag">
                  <a-radio-button :value="false">启用</a-radio-button>
                  <a-radio-button :value="true">停用</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>
      <a-tab-pane key="2" tab="颜色尺码管理">
        <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
          <div><strong>颜色尺码管理</strong></div>
        </a-row>
        <a-form>
          <a-row>
            <a-col :span="2">
              <a-form-item label="颜色">
                <a-button size="small" @click="showColorModal">+</a-button>
              </a-form-item>
            </a-col>
            <a-col :span="22">
              <a-tag v-for="item in colorList" :key="item" closable @close="handleColorClose(item)" class="tag-item">
                {{ item }}
              </a-tag>
            </a-col>
          </a-row>
          <!--款式颜色组件-->
          <StyleColorSelect ref="colorFormRef" @reloadList="colorListPush" />
          <a-row>
            <a-col :span="2">
              <a-form-item label="尺码">
                <a-button size="small" @click="showSizeModal">+</a-button>
              </a-form-item>
            </a-col>
            <a-col :span="22">
              <a-tag v-for="item in sizeList" :key="item" closable @close="handleSizeClose(item)" class="tag-item">
                {{ item }}
              </a-tag>
            </a-col>
          </a-row>
          <!--款式尺码组件-->
          <StyleSizeModal ref="sizeFormRef" @reloadList="sizeListPush" />
          <a-row>
            <div>
              <strong>属性组合</strong>
              <a-divider type="vertical" />
              <a-button type="primary" @click="generate"> 属性生成 </a-button>
              <a-divider type="vertical" />
              <a-button style="background: #ff4d4f; color: white" @click="deleteItems"> 批量删除 </a-button>
            </div>
          </a-row>
        </a-form>
        <a-table
          :dataSource="tableDataList"
          :columns="columns"
          :row-selection="{
            selectedRowKeys,
            onChange: onSelectChange,
          }"
          :scroll="{ y: 280 }"
          size="small"
          rowKey="key"
          bordered
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'price'">
              <a-input v-model:value="editableData[record.key].price" style="width: 50%" /> 元
            </template>
            <template v-if="column.dataIndex === 'skuNumber'">
              <a-input placeholder="请输入,忽略将自动生成" v-model:value="editableData[record.key].skuNumber" />
            </template>
            <template v-if="column.dataIndex === 'imgUrl'">
              <file-upload :default-file-list="pictureArr" @change="(e) => handleFileChange(e, record, column)" />
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { unitApi } from '/@/api/business/mes/base/unit-api';
  import { itemTypeApi } from '/@/api/business/mes/item/item-type-api';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { seasonApi } from '/@/api/business/mes/base/season-api';
  import { styleApi } from '/@/api/business/mes/base/style-api';
  import StyleSizeModal from '/@/components/business/mes/base/size-modal/index.vue';
  import StyleColorSelect from '/@/components/business/mes/base/style-color-modal/index.vue';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import { itemClothesApi } from '/@/api/business/mes/item/item-clothes.js';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';

  //表单数据
  const formDefault = {
    id: undefined,
    typeId: undefined, //物料分类id;关联t_item_type
    name: undefined, //物料名称
    supplierId: undefined, //供应商id;关联t_item_supplier
    model: undefined, //规格型号
    number: undefined, //spu编号
    unitId: undefined, //单位id;关联t_unit
    seasonId: undefined, //季度id
    styleId: undefined, //款式品类id
    enableFlag: false, //停用标识;0启用，1停用
    partList: [], //部位列表
    // category: '', //类型;0半成品 1成品
    // attribute: '', //成衣
  };
  const form = reactive({ ...formDefault });
  // ------------------------ 必填数据 ------------------------
  const rules = {
    name: [{ required: true, message: '请输入物料名称' }],
    unitId: [{ required: true, message: '请选择单位' }],
    enableFlag: [{ required: true, message: '请选择启用状态' }],
  };
  const activeKey = ref('1');
  const itemTypeTree = ref([]); //物料分类
  const itemStyleTree = ref([]); //款式
  const unitOption = ref([]);
  const supplierOption = ref([]);
  const seasonTree = ref([]);
  // ------------------------ 获取数据 ------------------------
  async function queryItemType() {
    let queryResult = await itemTypeApi.queryCategoryTree({});
    itemTypeTree.value = queryResult.data;
  }

  async function queryUnitList() {
    let queryResult = await unitApi.querySelect({});
    unitOption.value = queryResult.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }

  async function querySupplierList() {
    let queryResult = await supplierApi.queryAll({});
    supplierOption.value = queryResult.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }
  async function querySeasonTree() {
    let queryResult = await seasonApi.queryTree({});
    seasonTree.value = queryResult.data;
  }

  async function queryItemStyle() {
    let queryResult = await styleApi.queryTree({});
    itemStyleTree.value = queryResult.data;
  }
  // ------------------------ 颜色尺码表 ------------------------
  const selectedRowKeys = ref([]);
  const tableDataList = ref([]);
  const editableData = ref({});

  const columns = [
    { title: '图片', dataIndex: 'imgUrl', width: 200 },
    { title: 'sku编号', dataIndex: 'skuNumber', width: 200 },
    { title: '颜色', dataIndex: 'styleColor', width: 100 },
    { title: '尺码', dataIndex: 'size', width: 100 },
    { title: '价格', dataIndex: 'price', width: 100 },
  ].map((col) => ({ ...col, ellipsis: true, align: 'center' }));

  const onSelectChange = (keys) => {
    selectedRowKeys.value = keys;
  };

  const deleteItems = () => {
    if (!tableDataList.value.length) return message.error('列表为空');
    if (!selectedRowKeys.value.length) return message.error('请选择要删除的行');
    tableDataList.value = tableDataList.value.filter((item) => !selectedRowKeys.value.includes(item.key));
    selectedRowKeys.value = []; // 清空选择
    message.success('删除成功');
  };

  //生成表格数据
  const generate = () => {
    if (!colorList.value.length || !sizeList.value.length) {
      return message.error('颜色或尺码为空');
    }

    tableDataList.value = [];
    let key = 0;

    colorList.value.forEach((color) => {
      sizeList.value.forEach((size) => {
        key++;
        tableDataList.value.push({
          key: String(key),
          skuNumber: undefined,
          imgUrl: [],
          styleColor: color,
          size,
          price: 0,
        });
      });
    });

    if (tableDataList.value.length) {
      editableData.value = Object.fromEntries(tableDataList.value.map((record) => [record.key, { ...record }]));
      message.success('生成成功');
    }
  };

  //文件上传
  const pictureArr = ref([]);
  const handleFileChange = (event, record) => {
    const index = parseInt(record.key);
    if (index > 0 && index <= tableDataList.value.length) {
      editableData.value[record.key].imgUrl = event;
    }
  };

  //监听数据变化
  watch(
    editableData,
    (newData) => {
      Object.entries(newData).forEach(([key, newRecord]) => {
        const index = tableDataList.value.findIndex((item) => item.key === key);
        if (index !== -1) {
          Object.assign(tableDataList.value[index], newRecord);
        }
      });
    },
    { deep: true }
  );
  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show() {
    visibleFlag.value = true;
  }

  function onClose() {
    resetForm();
    emit('reload');
    visibleFlag.value = false;
  }

  // ------------------------ 颜色与尺寸对话框 ------------------------

  const colorList = ref([]); //颜色标签数组
  const sizeList = ref([]); //尺寸标签数组

  const sizeFormRef = ref();
  const colorFormRef = ref();

  function showColorModal() {
    colorFormRef.value.show();
  }
  function showSizeModal() {
    sizeFormRef.value.show();
  }

  function colorListPush(data) {
    //过滤掉重复选项
    data.value.map((item) => {
      if (!colorList.value.includes(item)) {
        colorList.value.push(item);
      } else {
        message.warning(`${item} 已经选择过了`);
      }
    });
  }
  function sizeListPush(data) {
    data.value.map((item) => {
      if (!sizeList.value.includes(item)) {
        sizeList.value.push(item);
      } else {
        message.warning(`${item} 已经选择过了`);
      }
    });
  }

  // 添加删除标签的方法
  const handleColorClose = (removedTag) => {
    colorList.value = colorList.value.filter((tag) => tag !== removedTag);
  };
  const handleSizeClose = (removedTag) => {
    sizeList.value = sizeList.value.filter((tag) => tag !== removedTag);
  };
  //------------------------提交 ------------------------
  //清空提交数据
  const resetForm = () => {
    Object.assign(form, formDefault);
    colorList.value = [];
    sizeList.value = [];
    tableDataList.value = [];
    editableData.value = {};
    selectedRowKeys.value = [];
    activeKey.value = '1';
  };

  const onSubmit = async () => {
    // 在提交时处理级联选择器的值
    const submitData = {
      ...form,
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
      seasonId: form.seasonId ? form.seasonId[form.seasonId.length - 1] : undefined,
      styleId: form.styleId ? form.styleId[form.styleId.length - 1] : undefined,
      partList: form.partList || [],
      skuAttributes: tableDataList.value.map((item) => ({
        styleColor: item.styleColor,
        size: item.size,
        skuNumber: item.skuNumber,
        imgUrl: item.imgUrl,
        price: item.price || 0,
      })),
    };
    await itemClothesApi.add(submitData);
    message.success('提交成功');
    resetForm();
    emit('reload');
    visibleFlag.value = false;
  };

  onMounted(() => {
    queryItemType();
    queryUnitList();
    querySupplierList();
    querySeasonTree();
    queryItemStyle();
  });

  defineExpose({
    show,
  });
  const emit = defineEmits(['reload']);
</script>
<style>
  .tag-item {
    margin: 2px;
    padding: 3px 8px;
  }
</style>

<!--
  * 物料分类表
  *
  * @Author:    cjm
  * @Date:      2024-07-02 09:54:23
  * @Copyright  zscbdic
-->
<template>
  <!--    &#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; 查询表单form begin -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;-->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="物料名称查询" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.queryKey" placeholder="名称查询"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <!--       查询功能实现           -->
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <!--       重置功能实现       -->
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->
  <!--树表 新建按钮功能实现-->
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="addCategory()" type="primary" size="small">
          <template #icon>
            <PlusOutlined/>
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">

      </div>
    </a-row>


    <!-- -------------从这里开始树表  ---------------------   -->
    <a-table

        :scroll="{ x: 1000}"
        size="small"
        :dataSource="tableDataList"
        :columns="columns"
        rowKey="id"
        bordered
        :pagination="false"
        @expandedRowsChange="changeExand"
        :expanded-row-keys="expandedRowKeys"
    >
      <!-- 操作按钮 -->
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="addCategory(record.id, record)" type="link" >
              增加子分类
            </a-button>
            <a-button @click="addCategory(undefined, record)" type="link">
              编辑
            </a-button>
            <a-button @click="confirmDeleteCategory(record.id)" danger type="link">
              删除
            </a-button>

          </div>
        </template>
      </template>
    </a-table>
    <ItemTypeForm ref="formModal" @reloadList="reloadList"/>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import ItemTypeForm from './item-type-form.vue'
import {itemTypeApi} from '/@/api/business/mes/item/item-type-api.js';
import {smartSentry} from '/@/lib/smart-sentry';
// ---------------------------- 表格列 ----------------------------
const columns = ref([
  {
    title: '物料分类',
    dataIndex: 'name',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'true',
    width: 200,
  },
]);
// 定义树表各项方法变量
// 定义树表列表数组
const tableLoading = ref(false);
const tableDataList = ref([])
const props = defineProps({
  categoryType: Number,
  privilegePrefix: {
    type: String,
    default: '',
  },
});

// 发送异步获取树表数组
async function queryList() {
  try {
    tableLoading.value = true;
    // 请求树表参数
    let queryForm = {
      categoryType: props.categoryType,
    };
    let responseModel = await itemTypeApi.queryCategoryTree(queryForm);
    tableDataList.value = responseModel.data;
  } catch (e) {
    smartSentry.captureError("this is the:", e);
  } finally {
    tableLoading.value = false;
  }
}

const expandedRowKeys = ref([]);

function reloadList(parentId) {
  queryList();
  if (parentId) {
    expandedRowKeys.value.push(parentId);
  }
}

onMounted(()=>{
  queryList()
  queryData()
});
defineExpose({
  queryList,
});

function changeExand(val) {
  expandedRowKeys.value = val;
}

// ------------------------------ 添加 ------------------------------

const formModal = ref();

// 递归函数：根据 id 查找 fullName
function findFullNameById(tree, parentId) {
  for (const node of tree) {
    if (node.id === parentId) {
      return node.fullName;
    }
    if (node.children && node.children.length > 0) {
      const fullName = findFullNameById(node.children, parentId);
      if (fullName) {
        return fullName;
      }
    }
  }
  return null;
}
function addCategory(parentId,record) {
  let categoryType = props.categoryType;
  if (parentId == undefined && record !== undefined) {
    // 获取父级分类的 name
    const fullName = findFullNameById(tableDataList.value, record.parentId);
    if (fullName) {
      categoryType = fullName;
    } else {
      categoryType = '';
    }
  }
    formModal.value.show(categoryType, parentId, record);
}

// ------------------------------ 删除 ------------------------------

function confirmDeleteCategory(categoryId) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除当前分类吗?',
    okText: '确定',
    okType: 'danger',
    async onOk() {
      deleteCategory(categoryId);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

async function deleteCategory(categoryId) {
  try {
    SmartLoading.show();
    await itemTypeApi.delete(categoryId);
    message.success('删除成功');
    queryList();
    formModal.value.getSelectValues(); // 重新渲染表单上级分类下拉选项
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}


//
// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //名称查询
  pageNum: 1,
  pageSize: 10,
};
// 查询表单form
const queryForm = reactive({...queryFormState});
//
// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}
// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await itemTypeApi.queryCategoryTree(queryForm);
    // 重新渲染查询数据
    tableDataList.value = queryResult.data;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}
</script>

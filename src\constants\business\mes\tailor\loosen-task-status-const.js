/**
 * 松布任务 状态枚举
 */

export const LOOSEN_TASK_STATUS_ENUM = {
  UNSTART: {
    value: 0,
    label: '未开始',
    color: 'blue',
  },
  STAY: {
    value: 1,
    label: '静置中',
    color: 'orange',
  },
  COMPLETE: {
    value: 2,
    label: '静置完成',
    color: 'pink',
  },
  END: {
    value: 3,
    label: '已结束',
    color: 'green',
  },
  getEnum(value) {
    for (const key in this) {
      if (typeof this[key] === 'object' && this[key].value === value) {
        return this[key];
      }
    }
    return { label: value, color: '' };
  },
  getOptions() {
    return Object.values(this).filter((item) => typeof item === 'object' && item.value !== undefined);
  },
};
export default {
  LOOSEN_TASK_STATUS_ENUM,
};

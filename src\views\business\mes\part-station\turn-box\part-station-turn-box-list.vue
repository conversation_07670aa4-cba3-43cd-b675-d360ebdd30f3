<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="form.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm()" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="batchAddForm()" type="primary" size="small" >
          <template #icon>
            <PlusOutlined />
          </template>
          批量添加
        </a-button>
        <a-button @click="batchDelete()" type="primary" size="small" danger>
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
    
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" :showSettingIcon="false" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: handleSelectedChange
      }"

    >
      <template #bodyCell="{ text, record, column }">

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="deleteRecord(record.id)" type="link" danger>删除</a-button>
            <!--货位码冒泡显示-->
            <a-popover  :title="record.number" placement="left" trigger="click">
              <template #content>
                <a-image :width="100" :src="qrCode" />
              </template>
              <a-button @click="showQRById(record.id)" type="link">周转箱码</a-button>
            </a-popover>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'number'">
          <a-button type="link" @click="showInsideDetailForm(record.id)">{{ text }}</a-button>
        </template>
        <template v-else-if="column.dataIndex === 'name'">
         {{ text ? text : '暂无'}}
        </template>
        <!-- <template v-else-if="column.dataIndex === 'insideFlag'">
          <a-tag :color="text ? 'blue' : 'green'">
            {{ text ? '内部' : '外部' }}
          </a-tag>
        </template> -->
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="form.pageSize"
        v-model:current="form.pageNum"
        v-model:pageSize="form.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartStationTurnBoxForm ref="formRef" @reload-list="queryData" />
    <PartStationTurnBoxInsideDetailForm ref="insideDetailFormRef"/>
    <PartStationTurnBoxBatchAddForm ref="batchAddFormRef" @reload-list="queryData"/>
  </a-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { partStationTurnBoxApi } from '/@/api/business/mes/part-station/turn-box/part-station-turn-box-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import PartStationTurnBoxForm from './components/part-station-turn-box-form.vue';
import PartStationTurnBoxInsideDetailForm from './components/part-station-turn-box-inside-detail-form.vue';
import PartStationTurnBoxBatchAddForm from './components/part-station-turn-box-batch-add-form.vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';


// ---------------------------- 表格列 ----------------------------
const columns = ref([
  {
    title: '周转箱编号',
    dataIndex: 'number',
    ellipsis: true,
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
  },
  {
    title: '容量',
    dataIndex: 'capacity',
    ellipsis: true,
  },
//   {
//     title: '内外部标识',
//     dataIndex: 'insideFlag',
//     ellipsis: true,
//   },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------
const formState = {
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{ column: 'create_time', isAsc: false }],
  queryKey: undefined,
};

// 查询表单form
const form = reactive({ ...formState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = form.pageSize;
  Object.assign(form, formState);
  form.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await partStationTurnBoxApi.queryPage(form);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

// ---------------------------- 添加/修改 ----------------------------

const formRef = ref();

function showForm(data) {
  formRef.value.showForm(data);
}

const insideDetailFormRef = ref();

function showInsideDetailForm(id) {
  insideDetailFormRef.value.showForm(id);
}

const qrCode = ref();
async function showQRById(id) {
    try {
      let res = await partStationTurnBoxApi.getQRURL(id);
      qrCode.value = res.data;
    } catch (e) {
      message.error('二维码不存在');
      smartSentry.captureError(e);
    }
  }


  const batchAddFormRef = ref();

  function batchAddForm() {
    batchAddFormRef.value.showForm();
  }

// ---------------------------- 删除 ----------------------------

async function deleteRecord(id) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该选项吗?',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      tableLoading.value = true;
      try {
        await partStationTurnBoxApi.delete(id);
        queryData();
      }catch (error){
        smartSentry.captureError(error)
      }finally {
        message.success('删除成功');
        tableLoading.value = false;
      }
    },
    cancelText: '取消',
    onCancel() {},
  });
}

const selectedRowKeys = ref([]);
// 选中的行数据
const selectedRows = ref([]);

function handleSelectedChange(keys, rows) {
  selectedRowKeys.value = keys;
  selectedRows.value = rows;
}
// 批量删除
 function batchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的选项');
    return;
  }
  
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除选项吗?',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      tableLoading.value = true;
      try {
        await partStationTurnBoxApi.batchDelete(selectedRowKeys.value)
        queryData();
      }catch (error){
        smartSentry.captureError(error)
      }finally {
        tableLoading.value = false;
        message.success('删除成功');
      }

    },
    cancelText: '取消',
    onCancel() {},
  });
}
// 页面加载时查询数据
onMounted(queryData);
</script>
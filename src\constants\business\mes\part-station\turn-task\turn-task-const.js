export const TURN_TASK_STATUS_ENUM = {
    /**
            * 待领取
    */
    WAIT_GET: {
        value: "WAIT_GET",
        desc: "待领取",
        color: "#e6a23c"
    },

    /**
     * 待开始
     */
    WAIT_START: {
        value: "WAIT_START",
        desc: "待开始",
        color: "#e6a23c"
    },

    /**
     * 进行中
     */
    PROCESSING: {
        value: "PROCESSING",
        desc: "进行中",
        color: "#409EFF"
    },

    /**
     * 已完成
     */
    FINISH: {
        value: "FINISH",
        desc: "已完成",
        color: "#67C23A"
    },

    /**
     * 已取消
     */
    CANCEL: {
        value: "CANCEL",
        desc: "已取消",
        color: "#909399"
    },
    getOption(value) {
        if (value === TURN_TASK_STATUS_ENUM.WAIT_GET.value) {
            return TURN_TASK_STATUS_ENUM.WAIT_GET;
        } else if (value === TURN_TASK_STATUS_ENUM.WAIT_START.value) {
            return TURN_TASK_STATUS_ENUM.WAIT_START;
        } else if (value === TURN_TASK_STATUS_ENUM.PROCESSING.value) {
            return TURN_TASK_STATUS_ENUM.PROCESSING;
        } else if (value === TURN_TASK_STATUS_ENUM.FINISH.value) {
            return TURN_TASK_STATUS_ENUM.FINISH;
        } else if (value === TURN_TASK_STATUS_ENUM.CANCEL.value) {
            return TURN_TASK_STATUS_ENUM.CANCEL;
        } else {
            return "类型错误";
        }
    },

    getOptions() {
        return Object.values(this).reduce((options, item) => {
            if (item && typeof item === 'object' && 'value' in item && 'desc' in item) {
                options.push({ value: item.value, label: item.desc });
            }
            return options;
        }, []);
    },
};

export const TURN_TASK_PRIORITY_ENUM = {
    /**
     * 低
     */
    HIGH: {
        value: "1",
        desc: "高",
    },
    MIDDLE: {
        value: "2",
        desc: "中",
    },
    LOW: {
        value: "3",
        desc: "低",
    },

    getOption(value) {
        if (value === TURN_TASK_PRIORITY_ENUM.HIGH.value) {
            return TURN_TASK_PRIORITY_ENUM.HIGH;
        } else if (value === TURN_TASK_PRIORITY_ENUM.MIDDLE.value) {
            return TURN_TASK_PRIORITY_ENUM.MIDDLE;
        } else if (value === TURN_TASK_PRIORITY_ENUM.LOW.value) {
            return TURN_TASK_PRIORITY_ENUM.LOW;
        } else {
            return "类型错误";
        }
    },

    getOptions() {
        return Object.values(this).reduce((options, item) => {
            if (item && typeof item === 'object' && 'value' in item && 'desc' in item) {
                options.push({ value: item.value, label: item.desc });
            }
            return options;
        }, []);
    }
};

export const TURN_TASK_TYPE_ENUM = {
    /**
     * 运送至车间
     */
    TO_WORKSHOP: {
        value: "TO_WORKSHOP",
        desc: "运送至车间",
    },

    /**
     * 运送至驿站
     */
    TO_PART_STATION: {
        value: "TO_PART_STATION",
        desc: "运送至裁片驿站",
    },
    getOption(value) {
        if (value === TURN_TASK_TYPE_ENUM.TO_WORKSHOP.value) {
            return TURN_TASK_TYPE_ENUM.TO_WORKSHOP;
        } else if (value === TURN_TASK_TYPE_ENUM.TO_PART_STATION.value) {
            return TURN_TASK_TYPE_ENUM.TO_PART_STATION;
        } else {
            return "类型错误";
        }
    },

    getOptions() {
        return Object.values(this).reduce((options, item) => {
            if (item && typeof item === 'object' && 'value' in item && 'desc' in item) {
                options.push({ value: item.value, label: item.desc });
            }
            return options;
        }, []);
    },
};

export default {
    TURN_TASK_STATUS_ENUM,
    TURN_TASK_PRIORITY_ENUM,
    TURN_TASK_TYPE_ENUM,
};

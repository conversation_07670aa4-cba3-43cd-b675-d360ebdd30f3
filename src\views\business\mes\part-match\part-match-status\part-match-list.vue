<!--
  * 裁片配包情况
  *
  * @Author:    cjm
  * @Date:      2024-11-06 15:25:13
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="颜色" class="smart-query-form-item">
        <a-select
          v-model:value="initColorVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择颜色"
          :options="colorOptions"
          @change="handleColorChange"
        />
      </a-form-item>
      <a-form-item label="尺码" class="smart-query-form-item">
        <a-select
          v-model:value="initSizeVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择尺码"
          :options="sizeOptions"
          @change="handleSizeChange"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData(true)">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 查询表单form end ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
      <template #headerCell="{ column }">
        <template v-if="column.key === 'name'">
          <span>
            <smile-outlined />
            Name
          </span>
        </template>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <a>
            {{ record.name }}
          </a>
        </template>
        <template v-else-if="column.key === 'parts'">
          <a-tag v-for="tag in record.parts" :key="tag" :color="tag.complete ? '#87d068' : '#f50'" style="font-size: 15px">
            {{ tag.positionName }}
          </a-tag>
        </template>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">
                <a-typography-text>{{ value }}</a-typography-text>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, watch, computed } from 'vue';
  import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import { partMatchStatusApi } from '/@/api/business/mes/part-match/part-match-status-api.js';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';

  //两个编号下拉组件引用
  const insItemSelectRef = ref();
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '裁床单编号',
      dataIndex: 'cutBedSheetNumber',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '款名',
      dataIndex: 'itemName',
      align: 'center',
      ellipsis: true,
      width: 120,
    },
    {
      title: '款式颜色',
      dataIndex: 'styleColor',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      align: 'center',
      ellipsis: true,
      width: 80,
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      align: 'center',
      ellipsis: true,
      width: 50,
    },
    {
      title: '扎号',
      dataIndex: 'tieNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '数量',
      key: 'num',
      align: 'center',
      width: 50,
      dataIndex: 'num',
    },
    {
      title: '部位',
      key: 'parts',
      align: 'left',
      width: 120,
      dataIndex: 'parts',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    instructOrderNumber: undefined, //指令单编号
    itemNumber: undefined, //物料编号
    actionStatus: undefined, //收或发
    dispatchRange: undefined, //收发范围
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  //下拉框最多可显示数量
  const maxTagCount = ref(1);
  //颜色和尺码下拉框初始值
  const initColorVal = ref(undefined);
  const initSizeVal = ref(undefined);
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    insItemSelectRef.value.clearAllNumber();
    initColorVal.value = undefined;
    initSizeVal.value = undefined;
    queryForm.colors = undefined;
    queryForm.sizes = undefined;
    colorOptions.value = [];
    sizeOptions.value = [];
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await partMatchStatusApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(() => {
    queryData();
  });

  //-------------------------------合计----------------------------------------
  const totals = computed(() => {
    const sums = {
      totalNum: 0, //总数量
    };
    tableData.value.forEach((item) => {
      sums.totalNum += item.num;
    });
    return sums;
  });
  //计算剩余的空单元格
  const summaryColSpans = 8; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => columns.value.length - summaryColSpans - Object.keys(totals.value).length);

  watch(
    () => queryForm.instructOrderNumber,
    (newVal) => {
      initColorVal.value = undefined;
      initSizeVal.value = undefined;
      if (newVal !== undefined) {
        queryByInsAndItemNumber();
      }
    }
  );

  //根据指令单id获取颜色和尺码信息
  async function queryByInsAndItemNumber() {
    let colorList = await produceInstructOrderClothesApi.queryClothesColorList(queryForm.instructOrderId);
    let sizeList = await produceInstructOrderClothesApi.queryClothesSizeList(queryForm.instructOrderId);
    outstandingOptions(colorList.data, colorOptions);
    outstandingOptions(sizeList.data, sizeOptions);
  }

  //拼装options
  const colorOptions = ref([]);
  const sizeOptions = ref([]);
  function outstandingOptions(sourceData, target) {
    target.value = [];
    sourceData.map((item, index) =>
      target.value.push({
        label: item,
        value: index,
      })
    );
  }

  // ---------------------------- 处理指令单编号和物料编号 ----------------------------
  //两个编号回传
  function insItemChange(data) {
    queryForm.instructOrderNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
    queryForm.instructOrderId = data.instructOrderId;
  }

  // --------------------- 处理颜色和尺码 ------------------------
  function handleColorChange(item, option) {
    queryForm.colors = [];
    option.map((e) => {
      queryForm.colors.push(e.label);
    });
  }

  function handleSizeChange(item, option) {
    queryForm.sizes = [];
    option.map((e) => {
      queryForm.sizes.push(e.label);
    });
  }

  // 选择表格行
  const selectedRowKeyList = ref([]);
</script>

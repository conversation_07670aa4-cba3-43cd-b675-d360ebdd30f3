<!--
  * 批号主档
  *
  * @Author:    cjm
  * @Date:      2025-02-03 19:28:09
  * @Copyright  zscbdic
-->
<template>
  <a-modal title="批号跟踪信息" width="1800px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :footer="null">
    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false"></a-table>

    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
  import { reactive, ref } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { lotMaterialTraceApi } from '/@/api/business/mes/stock/lot-material-trace-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const.js';
  import { LOT_STOCKDIRECT } from '/@/constants/business/mes/stock/lot-master-const.js';
  //---------------------------表格列-----------------------
  const columns = ref([
    {
      title: '移动方向',
      dataIndex: 'stockDirect',
      ellipsis: true,
      width: 80,
      customRender: ({ text }) => LOT_STOCKDIRECT.getOptions(text),
    },
    {
      title: '单据类型',
      dataIndex: 'billType',
      ellipsis: true,
      width: 100,
      customRender: ({ text }) => BillTypeEnum.getOptions(text),
    },
    {
      title: '单据编号',
      dataIndex: 'billNumber',
      ellipsis: true,
      width: 150,
    },
    {
      title: '单据日期',
      dataIndex: 'billTime',
      ellipsis: true,
    },
    {
      title: '数量',
      dataIndex: 'qty',
      ellipsis: true,
      width: 70,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      ellipsis: true,
      width: 50,
    },
    {
      title: '订单类型',
      dataIndex: 'orderType',
      ellipsis: true,
      customRender: ({ text }) => BillTypeEnum.getOptions(text),
    },
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      ellipsis: true,
    },
    {
      title: '货主类型',
      dataIndex: 'ownerType',
      ellipsis: true,
    },
    {
      title: '货主名称',
      dataIndex: 'ownerName',
      ellipsis: true,
    },
    {
      title: '仓库名称',
      dataIndex: 'warehouseName',
      ellipsis: true,
    },
    {
      title: '仓库编号',
      dataIndex: 'warehouseNumber',
      ellipsis: true,
    },
    {
      title: '库位编号',
      dataIndex: 'locationNumber',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
    },
  ]);

  const tableData = ref([]); // 表格数据
  const tableLoading = ref(false);
  const total = ref(0);

  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    lotMasterId: undefined, //批号ID
  };
  const form = reactive({ ...queryFormState });

  //分页查询
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await lotMaterialTraceApi.queryPage(form);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
      message.error(e.message);
    } finally {
      tableLoading.value = false;
    }
  }

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  Object.assign(form, queryFormState);

  function show(rowData) {
    Object.assign(form, queryFormState);
    if (rowData && !_.isEmpty(rowData)) {
      form.lotMasterId = rowData.lotMasterId; // 设置批号id
      queryData();
    }
    visibleFlag.value = true;
  }

  // 修改onClose方法
  function onClose() {
    Object.assign(form, queryFormState);
    tableData.value = []; // 清空表格数据
    total.value = 0;
    visibleFlag.value = false;
  }

  defineExpose({
    show,
  });
</script>

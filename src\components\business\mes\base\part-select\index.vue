<!--
  * 裁片部位选择器（模态框形式）
  * 返回对象列表
  * @Author:    assistant
  * @Date:      2025-01-17
  * @Copyright  zscbdic
-->
<template>
  <!-- 选择框 -->
  <a-select
    v-model:value="displayValue"
    :style="`width: ${width}`"
    :placeholder="placeholder"
    mode="multiple"
    :max-tag-count="1"
    @click="showModal"
    :open="false"
    allow-clear
    :show-arrow="true"
    @clear="handleClear"
  >
    <template #suffixIcon>
      <CaretDownOutlined />
    </template>
  </a-select>

  <!-- 模态框 -->
  <a-modal
    v-model:open="modalVisible"
    title="选择部位"
    width="970px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-tabs v-model:activeKey="activeTab"  :tab-position="'left'">
      <!-- 全部部位 -->
      <a-tab-pane key="all" tab="全部部位">
        <!---------- 查询表单form begin ----------->
        <a-form class="smart-query-form">
          <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
              <a-input style="width: 200px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
            </a-form-item>
            <a-form-item class="smart-query-form-item">
              <a-button type="primary" @click="onSearch">
                <template #icon>
                  <SearchOutlined/>
                </template>
                查询
              </a-button>
              <a-button @click="resetQuery" class="smart-margin-left10">
                <template #icon>
                  <ReloadOutlined/>
                </template>
                重置
              </a-button>
            </a-form-item>
            <a-space direction="vertical" style="margin-left: 5px;margin-top: 5px;margin-bottom: 0;">
              <a-input-search
                v-model:value="newPart"
                placeholder="新增部位"
                enter-button="添加"
                @search="addPart"
              />
            </a-space>
          </a-row>
        </a-form>
        <!---------- 查询表单form end ----------->

        <!---------- 表格 begin ----------->
        <a-table
            size="small"
            :dataSource="partList"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        >
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
          <a-pagination
              showSizeChanger
              showQuickJumper
              show-less-items
              :pageSizeOptions="PAGE_SIZE_OPTIONS"
              :defaultPageSize="queryForm.pageSize"
              v-model:current="queryForm.pageNum"
              v-model:pageSize="queryForm.pageSize"
              :total="total"
              @change="queryPartList"
              @showSizeChange="queryPartList"
              :show-total="(total) => `共${total}条`"
          />
        </div>
      </a-tab-pane>

      <!-- 历史部位 -->
      <a-tab-pane key="history" tab="历史部位">
        <a-table
            size="small"
            :dataSource="historyPartList"
            :columns="historyColumns"
            rowKey="name"
            bordered
            :loading="historyTableLoading"
            :pagination="false"
            :row-selection="{ selectedRowKeys: selectedHistoryRowKeyList, onChange: onHistorySelectChange }"
        >
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <!-- <addPartForm ref="AddPartFormRef" @reloadList="queryPartList"/> -->
</template>

<script setup>
import { ref, watch, onMounted, reactive } from 'vue';
import { partApi } from '/@/api/business/mes/base/part-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { SearchOutlined, ReloadOutlined, CaretDownOutlined } from '@ant-design/icons-vue';
//import addPartForm from '/@/views/business/mes/base/part/part-form.vue';

const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择部位'
  },
  width: {
    type: String,
    default: '90%'
  },
  produceInstructOrderId: {
    type: [String, Number],
    default: undefined
  }
});

const emit = defineEmits(['update:value', 'change']);

// ---------------------------- 模态框状态 ----------------------------
const modalVisible = ref(false);
const activeTab = ref('all');

// ---------------------------- 选择框显示值 ----------------------------
const displayValue = ref([]);

// ---------------------------- 表格列配置 ----------------------------
const columns = [
  { title: '部位名称', dataIndex: 'name', ellipsis: true },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '创建时间', dataIndex: 'createTime', ellipsis: true },
  { title: '更新时间', dataIndex: 'updateTime', ellipsis: true },
];

const historyColumns = [
  { title: '部位名称', dataIndex: 'name', ellipsis: true },
];

// ---------------------------- 数据状态 ----------------------------
const partList = ref([]);
const historyPartList = ref([]);
const selectedRowKeyList = ref([]);
const selectedHistoryRowKeyList = ref([]);
const tableLoading = ref(false);
const historyTableLoading = ref(false);
const total = ref(0);

// ---------------------------- 查询表单 ----------------------------
const queryForm = reactive({
  queryKey: undefined,
  pageNum: 1,
  pageSize: 10,
});

// 重置查询条件
const resetQuery = () => {
  queryForm.queryKey = undefined;
  queryForm.pageNum = 1;
  queryPartList();
};

// 搜索
const onSearch = () => {
  queryForm.pageNum = 1;
  queryPartList();
};
//添加部位
const newPart=ref('');
async function addPart() {
if (!newPart.value) return;

  try {
    await partApi.add({
      name: newPart.value
    });
    newPart.value = ''; // 清空输入框
    queryPartList(); // 重新查询列表
  } catch (e) {
    smartSentry.captureError(e);
  }
}
// 获取全部部位列表
async function queryPartList() {
  tableLoading.value = true;
  try {
    const res = await partApi.queryPage(queryForm);
    partList.value = res.data?.list || [];
    total.value = res.data?.total || 0;
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    tableLoading.value = false;
  }
}

// 获取历史部位列表
async function queryHistoryPartList() {
  if (!props.produceInstructOrderId) return;

  historyTableLoading.value = true;
  try {
    const res = await feTicketApi.queryPartList(props.produceInstructOrderId);
    if (res.data && Array.isArray(res.data)) {
      historyPartList.value = res.data.map(name => ({ name }));
    }
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    historyTableLoading.value = false;
  }
}

// ---------------------------- 监听器 ----------------------------
watch(() => props.produceInstructOrderId, (newVal) => {
  newVal && queryHistoryPartList();
}, { immediate: true });

// ---------------------------- 选择处理 ----------------------------
const onSelectChange = (selectedRowKeys) => {
  selectedRowKeyList.value = selectedRowKeys;
};

const onHistorySelectChange = (selectedRowKeys) => {
  selectedHistoryRowKeyList.value = selectedRowKeys;

  // 根据历史部位名称找到对应ID并合并选择
  const correspondingIds = partList.value
    .filter(item => selectedRowKeys.includes(item.name))
    .map(item => item.id);

  selectedRowKeyList.value = [...new Set([...selectedRowKeyList.value, ...correspondingIds])];
};

// ---------------------------- 模态框操作 ----------------------------
// 同步选择状态的通用函数
const syncSelectionState = () => {
  if (props.value?.length > 0) {
    // 设置全部部位选中状态
    const selectedIds = partList.value
      .filter(item => props.value.includes(item.name))
      .map(item => item.id);
    selectedRowKeyList.value = selectedIds;

    // 设置历史部位选中状态
    const selectedHistoryNames = props.value.filter(name =>
      historyPartList.value.some(item => item.name === name)
    );
    selectedHistoryRowKeyList.value = selectedHistoryNames;
  } else {
    selectedRowKeyList.value = [];
    selectedHistoryRowKeyList.value = [];
  }
};

const showModal = () => {
  modalVisible.value = true;
  syncSelectionState();
};

const handleOk = () => {
  const selectedParts = partList.value.filter(item =>
    selectedRowKeyList.value.includes(item.id)
  );
  const selectedNames = selectedParts.map(item => item.name);

  displayValue.value = selectedNames;
  emit('update:value', selectedNames);
  emit('change', selectedParts);
  modalVisible.value = false;
};

const handleCancel = () => {
  modalVisible.value = false;
  syncSelectionState();
};

  //点击clear按钮清空事件
  const handleClear = () => {
    displayValue.value = [];
    selectedRowKeyList.value = [];
    selectedHistoryRowKeyList.value = [];
    emit('update:value', []);
    emit('change', []);
  };

  watch(
    () => props.value,
    (newVal) => {
      displayValue.value = newVal || [];
    },
    { immediate: true, deep: true }
  );

onMounted(() => {
  queryPartList();
});
</script>

<template>
  <a-modal
    :open="visible"
    title="周转箱内容"
    width="1200px"
    @cancel="handleCancel"
    :footer="null"
  >
 <a-card size="small" :bordered="false" :hoverable="false"> 
    <a-table 
      :columns="columns" 
      :dataSource="filteredData" 
      :pagination="false"
      rowKey="id"
      bordered
      size="small"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: handleSelectedChange
      }"
    >
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="form.pageSize"
        v-model:current="form.pageNum"
        v-model:pageSize="form.pageSize"
        :total="total"
        @change="handleSearch"
        @showSizeChange="handleSearch"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { turnBoxInsideApi } from '/@/api/business/mes/part-station/turn-box-inside/turn-box-inside-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';

import { smartSentry } from '/@/lib/smart-sentry';

  const columns = ref([
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '裁床单编号',
      dataIndex: 'cutBedSheetNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      ellipsis: true,
      align: 'center',
      width: 60,
    },
    {
      title: '扎号',
      dataIndex: 'tieNum',
      ellipsis: true,
      align: 'center',
      width: 50,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '款式颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      title: '部位',
      dataIndex: 'positions',
      ellipsis: true,
      align: 'center',
      width: 60,
    },

    {
      title: '数量',
      dataIndex: 'num',
      ellipsis: true,
      align: 'center',
      width: 60,
    },
  ]);

const visible = ref(false);
const total = ref(0);
const allData = ref([]); // 存储所有数据
const filteredData = ref([]); // 当前页显示的数据
const selectedRowKeys = ref([]); // 选中的行

function handleSelectedChange(keys, rows) {
  selectedRowKeys.value = keys;
}


// 查询表单
const formDefault = reactive({
  pageNum: 1,
  pageSize: 10
});

const form = reactive({ ...formDefault });

// 处理分页查询
const handleSearch = () => {
  const startIndex = (form.pageNum - 1) * form.pageSize;
  const endIndex = startIndex + form.pageSize;
  filteredData.value = allData.value.slice(startIndex, endIndex);
};

async function showForm(turnBoxId) {
  visible.value = true;
  Object.assign(form, formDefault);
  try {
    if (turnBoxId) {
      const queryResult = await turnBoxInsideApi.queryList({ turnBoxId });
      allData.value = queryResult.data || [];
      total.value = allData.value.length;
      handleSearch(); // 处理第一页数据
    }
  } catch (error) {
    smartSentry.captureError(error);
  }
}

const handleCancel = () => {
  visible.value = false;
  Object.assign(form, formDefault);
}

defineExpose({ showForm });
</script>

<style scoped>
.smart-query-table-page {
  margin-top: 16px;
  text-align: right;
}
</style>
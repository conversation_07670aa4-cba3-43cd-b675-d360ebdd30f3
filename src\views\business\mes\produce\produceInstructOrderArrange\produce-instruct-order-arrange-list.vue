<template>
  <board-title :title="'安排仪表盘'" style="line-height: 60px;height: 60px"/>
  <!---------- 查询表单form begin ----------->
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
      <a-form-item label="交货日期" class="smart-query-form-item">
        <a-range-picker v-model:value="deliverTime" :presets="defaultTimeRanges" style="width: 150px"
                        @change="onChangeDeliverTime"/>
      </a-form-item>
<!--      <a-form-item label="生产类型" class="smart-query-form-item">-->
<!--        <a-select style="width: 150px" v-model:value="queryForm.produceType" placeholder="生产类型">-->
<!--          <a-select-option value="0">自产</a-select-option>-->
<!--          <a-select-option value="1">自裁委外</a-select-option>-->
<!--          <a-select-option value="2">整件委外</a-select-option>-->
<!--        </a-select>-->
<!--      </a-form-item>-->
      <a-form-item label="生产业务状态" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.produceStatus" placeholder="生产业务状态">
          <a-select-option value="0">计划</a-select-option>
          <a-select-option value="1">下达</a-select-option>
          <a-select-option value="2">开工</a-select-option>
          <a-select-option value="3">完工</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="优先级" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.priority" placeholder="优先级">
          <a-select-option value="0">一般</a-select-option>
          <a-select-option value="1">紧急</a-select-option>
          <a-select-option value="2">非常紧急</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>


  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData"/>
      </div>
    </a-row>
    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :scroll="{x:'max-content'}"
        :loading="tableLoading"
        :pagination="false"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'instructOrderInfo'">
          <div style="display: flex">
            <file-preview :fileList="(record.instructOrderInfo.imgUrl && record.instructOrderInfo.imgUrl.length>0)?record.instructOrderInfo.imgUrl:[]" type="picture"
            :height="80" :width="80"/>
            <div style="font-size: 12px;color: #606266;margin-left: 10px;text-align: left">
              <div>指令单号：{{ record.instructOrderInfo.instructNumber }}</div>
              <div>款名：{{ record.instructOrderInfo.itemName }}</div>
              <div>客户名称：{{ record.instructOrderInfo.customerName }}</div>
              <div>下达时间：{{ record.instructOrderInfo.issuedTime }}</div>
              <div>生产优先级：
                <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(record.priority.value).color">
                  {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(record.priority.value).label }}
                </a-tag>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'delivery'">
          <div>
            <div :style="{color: record.delivery.daysLeft > 0 ? 'green' : 'red',fontSize: '22px'}">
              {{ record.delivery.daysLeft }}天
            </div>
            <div :style="{color: record.delivery.daysLeft > 0 ? 'green' : 'red'}">{{ record.delivery.deliveryTime }}
            </div>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'nodeInfo'">
          <a-steps :current="record.currentNodeIndex" size="small"  >
            <a-step v-for="(item, index) in record.nodeInfo" :key="index" :title="item.title"
                    @click="showFinishForm(item)">
              <template #icon>
                <template v-if="item.finishFlag === true">
                  <CheckCircleOutlined style="font-size: 22px;color: green"/>
                </template>
                <template v-else>
                  <CloseCircleOutlined style="font-size: 22px;color: red"/>
                </template>
              </template>
              <template #description>
                <div style="font-size: 12px;color: #606266">
                  <div>负责人：{{ item.headName }}</div>
                  <div>计划结束时间：{{ formatTime(item.planEndTime) }}</div>
                </div>
              </template>
            </a-step>
          </a-steps>
        </template>
      </template>

    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>


//-------------------------------
import {h, onMounted, reactive, ref} from "vue";
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {produceInstructOrderArrangeApi} from "/@/api/business/mes/produce/produce-instruct-order-arrange-api.js"
import {CheckCircleOutlined, CloseCircleOutlined} from '@ant-design/icons-vue';
import TableOperator from "/@/components/support/table-operator/index.vue";
import dayjs from "dayjs";
import FilePreview from '/@/components/support/file-preview/index.vue'
import {defaultTimeRanges} from "/@/lib/default-time-ranges.js";
import {
  PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
  PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM
} from "/@/constants/business/mes/produce/produce-instruct-order-const.js";
import BoardTitle from "/@/components/business/mes/board/board-title.vue";

onMounted(queryData);

const queryFormState = {
  queryKey: undefined, //关键字查询
  // deliverTime: [], //交货日期
  deliverTimeBegin: undefined, //交货日期 开始
  deliverTimeEnd: undefined, //交货日期 结束
  produceType: undefined, //生产类型;0自产,1自裁委外，2整件委外
  produceStatus: undefined, //生产业务状态;0计划，1下达，2开工，3完工
  priority: undefined, //优先级;0一般,1紧急,2非常紧急
  pageNum: 1,
  pageSize: 5,
  sortItemList:[{column:'create_time',isAsc:false}]
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);
// 交货日期
const deliverTime=ref([])
// 列
const columns = ref([
  {
    title: '生产指令信息',
    dataIndex: 'instructOrderInfo',
    ellipsis: true,
    width: 40,
    align: 'center',
  },
  {
    title: '交期',
    dataIndex: 'delivery',
    ellipsis: true,
    width: 20,
    align: 'center',
  },
  {
    title: '生产安排节点动态',
    dataIndex: 'nodeInfo',
    ellipsis: true,
    width: 100,
    align: 'center',
  }
]);
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await produceInstructOrderArrangeApi.queryProduceInstructOrderArrangePage(queryForm);
    total.value = queryResult.data.total;
    tableData.value = parseData(queryResult.data.list)
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}
// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  deliverTime.value=[]
  queryData();
}
// 处理查询时间
function onChangeDeliverTime(dates, dateStrings) {
  queryForm.deliverTimeBegin = dateStrings[0];
  queryForm.deliverTimeEnd = dateStrings[1];
}

// 格式化时间
function formatTime(time) {
  // 使用 dayjs 格式化日期，只保留月和日
  return dayjs(time).format('MM/DD');
}


//处理数据
function parseData(data) {
  return data.map(item => {
    // 处理生产指令信息
    let instructOrderInfo = {
      instructNumber: item.instructNumber,
      customerName: item.customerName,
      itemName: item.itemName,
      itemNumber: item.itemNumber,
      issuedTime: item.issuedTime,
      imgUrl: (item.imgUrl&& item.imgUrl.length>0)? [item.imgUrl[0]]:[],
    }

    //处理交期
    let deliveryTime = item.deliverTime;
    const deliveryDate = dayjs(deliveryTime);
    const daysLeft = deliveryDate.diff(dayjs(), 'day');
    const delivery = {deliveryTime, daysLeft};

    //处理节点信息
    let currentNodeIndex = item.arrangeList.findIndex(e => e.finishFlag === false)
    let nodeInfo = item.arrangeList.map(e => {
      return {
        title: e.nodeName,
        description: e.planBeginTime,
        status: e.nodeStatus,
        icon: e.finishFlag === false ? h(CloseCircleOutlined, {style: {color: 'red'}}) : h(CheckCircleOutlined, {style: {color: 'green'}}),
        headName: e.headName,
        planEndTime: e.planEndTime,
        finishFlag: e.finishFlag,
        id: e.id
      }
    })

    //生产优先级
    let priority = {
      desc: item.priority === '0' ? '一般' : item.priority === '1' ? '紧急' : '非常紧急',
      color: item.priority === '0' ? 'green' : item.priority === '1' ? 'orange' : 'red',
      value: item.priority
    }
    //生产业务状态
    let produceStatus = {
      desc: item.produceStatus === '0' ? '计划' : item.produceStatus === '1' ? '下达' : item.produceStatus === '2' ? '开工' : '完工',
      color: item.produceStatus === '0' ? 'pink' : item.produceStatus === '1' ? 'blue' : item.produceStatus === '2' ? 'orange' : 'green',
      value: item.produceStatus
    }
    return {instructOrderInfo, nodeInfo, currentNodeIndex, delivery, priority, produceStatus}
  })

}
</script>

<style scoped lang="less">

</style>

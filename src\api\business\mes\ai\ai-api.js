/**
 * ai api 封装
 *
 * @Author:    fkf
 * @Date:      2025
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';
import { localRead } from '/@/utils/local-util';
import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';

export const aiApi = {
  /**
   * 聊天流
   */
  chatStream: (param, signal) => {
    const token = localRead(LocalStorageKeyConst.USER_TOKEN);
    return fetch(import.meta.env.VITE_APP_API_URL+'/ai/chatStream',{
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-access-token': token,
      },
      body: JSON.stringify(param),
      signal: signal
    });
  },
  /**
   * 聊天
   */
  chat: (param)=>{
    return postRequest('/ai/chat',param);
  },
  /**
   * 清除当前聊天数据
   */
  chatClear:()=>{
    return getRequest('/ai/chat/clear')
  },
  /**
   * 获取聊天历史记录
   */
  chatHistory:()=>{
    return getRequest('/ai/chat/history')
  },
  /**
   * 获取提示词
   */
  recommendPrompt:()=>{
    return getRequest('/ai/chat/recommendPrompt')
  }
};

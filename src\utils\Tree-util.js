/*
 * 树递归遍历 相关操作
 *
 * @Author:   x<PERSON><PERSON><PERSON><PERSON>
 * @Date:      2022-09-06 20:58:49
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// export function findById(data, targetId) {
//     if (!Array.isArray(data)) {
//         return null;
//     }
//     for (const item of data) {
//
//         if (item.id === targetId) {
//             console.log("kk",item)
//             return item;
//         }
//         if (item.children && item.children.length > 0) {
//             const foundInChildren = findById(item.children, targetId);
//             if (foundInChildren) {
//                 return foundInChildren;
//             }
//         }
//     }
//     return null;
// }
// 封装树形选择，用于树形级联
export function convertData(data, valueKey, labelKey){
    if (!Array.isArray(data) || data.length === 0) {
        return null;
    }
    return data.map(item => {
        const hasIdKey = Object.hasOwn(item, valueKey);
        const hasLabelKey = Object.hasOwn(item, labelKey);
        if (!hasIdKey) {
            throw new Error(`属性 ${valueKey} 在当前对象中不存在。`);
        }
        if (!hasLabelKey) {
            throw new Error(`属性 ${labelKey} 在当前对象中不存在。`);
        }
        return {
            value: item[valueKey],
            label: item[labelKey],
            children: item.children? convertData(item.children, valueKey, labelKey) : null
        };
    });
}
export function findById(data, targetId) {
    if (!Array.isArray(data)) {
        return null;
    }
    for (const item of data) {
        // 将 id 和 targetId 转化为数字类型进行比较
        if (Number(item.id) === Number(targetId)) {
            return item;  // 找到后立即返回
        }
        // 如果有子节点，递归进入
        const childrenKey = item.childrenStyle ? 'childrenStyle' : 'children'; // 动态处理子节点
        if (item[childrenKey] && item[childrenKey].length > 0) {
            const foundInChildren = findById(item[childrenKey], targetId);
            // 如果在子节点中找到，立即返回
            if (foundInChildren) {
                return foundInChildren;
            }
        }
    }

    return null;  // 如果都没找到，返回 null
}

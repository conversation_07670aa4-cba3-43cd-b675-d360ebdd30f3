<!--
  * 物料用量信息
  *
  * @Author:    fkf
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
    <div><strong>物料用量信息</strong></div>
  </a-row>
  <a-row>
    <a-col :span="2">
      <a-button type="primary" @click="showItemModal()">添加</a-button>
    </a-col>
    <a-col :span="7">
      <a-form>
        <a-form-item label="选择BOM信息">
          <a-select v-model:value="selectBOMItem" :options="options" placeholder="请选择BOM信息" style="width: 100%" @select="selectBOM" />
        </a-form-item>
      </a-form>
    </a-col>
  </a-row>
  <item-table-select-modal
    ref="itemModal"
    @select-data="selectItemData"
    :attribute-list="[ITEM_ATTRIBUTE_ENUM.OTHER.value, ITEM_ATTRIBUTE_ENUM.CLOTH.value]"
  />
  <br />
  <a-table size="small" :dataSource="itemList" :columns="columns" :pagination="false" bordered :scroll="{ x: 120 }">
    <template #bodyCell="{ text, record, column }">
      <template v-if="['itemNumber', 'itemName', 'itemModel', 'itemUnitName'].includes(column.dataIndex)">
        <a-input v-model:value="record[column.dataIndex]" disabled />
      </template>
      <template v-else-if="column.dataIndex === 'itemCategory'">
        <a-tag v-if="text" :color="ITEM_CATEGORY_ENUM.getEnum(text).color">
          {{ ITEM_CATEGORY_ENUM.getEnum(text).desc }}
        </a-tag>
      </template>
      <template v-else-if="column.dataIndex === 'itemAttribute'">
        <a-tag v-if="text" :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">
          {{ ITEM_ATTRIBUTE_ENUM.getEnum(text).desc }}
        </a-tag>
      </template>
      <template v-else-if="column.dataIndex === 'loss'">
        <a-input-number
          placeholder="单位损失"
          v-model:value="record.loss"
          :formatter="(value) => (value ? `${(value * 100).toFixed(2)}%` : '')"
          :parser="(value) => (value.replace('%', '') ? parseFloat(value) / 100 : '')"
          :step="0.01"
          @change="() => (record.totalDosage = record.dosage * record.itemNum * (1 + record.loss))"
        />
      </template>
      <template v-else-if="column.dataIndex === 'dosage'">
        <a-input-number
          placeholder="单位用量"
          v-model:value="record.dosage"
          style="width: 100%"
          :precision="4"
          :min="0"
          @change="() => (record.totalDosage = record.dosage * record.itemNum * (1 + record.loss))"
        />
      </template>
      <template v-else-if="column.dataIndex === 'itemNum'">
        <a-input-number
          placeholder="用料数量"
          v-model:value="record.itemNum"
          style="width: 100%"
          :precision="2"
          :min="0"
          @change="() => (record.totalDosage = record.dosage * record.itemNum * (1 + record.loss))"
        />
      </template>
      <template v-else-if="column.dataIndex === 'totalDosage'">
        <a-input-number placeholder="总用量" v-model:value="record.totalDosage" style="width: 100%" :precision="2" :min="0" />
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <div class="smart-table-operate">
          <a-button @click="deleteItem(record.key)" danger type="link">删除</a-button>
        </div>
      </template>
    </template>
  </a-table>
</template>
<script setup>
  import { onMounted, ref, watch } from 'vue';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import ItemTableSelectModal from '/@/components/business/mes/item/item-table-select-modal/index.vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { bomApi } from '/@/api/business/mes/bom/bom-api.js';
  // ------------------------------通信事件------------------------------
  const props = defineProps({
    value: [Array],
  });
  const emit = defineEmits(['change']);

  // ------------------------------表格--------------------------------
  const columns = ref([
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      width: 160,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      width: 140,
    },
    {
      title: '物料规格型号',
      dataIndex: 'itemModel',
      width: 120,
    },
    {
      title: '物料类型',
      dataIndex: 'itemCategory',
      width: 100,
    },
    {
      title: '物料属性',
      dataIndex: 'itemAttribute',
      width: 100,
    },
    {
      title: '单位损失',
      dataIndex: 'loss',
      key: 'loss',
      width: 150,
      align: 'center',
    },
    {
      title: '单位用量',
      dataIndex: 'dosage',
      width: 100,
    },
    {
      title: '用料数量',
      dataIndex: 'itemNum',
      width: 100,
    },
    {
      title: '总用量',
      dataIndex: 'totalDosage',
      width: 100,
    },
    {
      title: '物料单位',
      dataIndex: 'itemUnitName',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 90,
    },
  ]);
  // 重置方法
  function reset() {
    itemList.value = [];
    selectBOMItem.value = null;
    options.value = [];
    queryData();
    // 触发更新
    emit('change', itemList.value);
  }
  // ------------------------------请求----------------------------
  // 安排信息下拉框
  const selectBOMItem = ref(null);
  const options = ref([]);
  const selectBOM = (value, option) => {
    //   选中发送请求
    queryId(option.id);
  };
  async function queryData() {
    try {
      let data = await bomApi.queryList();
      data.data.forEach((item) => {
        options.value.push({
          id: item.id,
          value: item.id,
          label: item.bomName,
        });
      });
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // 查询BOM物料列表
  const itemList = ref([]);
  async function queryId(id) {
    try {
      let res = await bomApi.queryBomItemList(id);
      itemList.value = [];
      // 渲染数据
      if (res.data) {
        itemList.value = res.data.map((item, index) => ({
          key: index,
          ...item,
          loss: item.loss || 0,
          dosage: item.dosage || 0,
          itemNum: item.itemNum || 0,
          totalDosage: item.totalDosage || 0,
        }));
      }
      emit('change', [...itemList.value]);
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // ------------------------------选择物料组建--------------------------------
  const itemModal = ref();
  function showItemModal(record) {
    itemModal.value.showModal();
  }
  function selectItemData(selectedItems) {
    if (!selectedItems.length) return;
    // 如果是点击顶部添加按钮
    selectedItems.forEach((data) => {
      const newData = {
        key: itemList.value.length,
        itemId: data.id,
        itemNumber: data.number,
        itemName: data.name,
        itemModel: data.model,
        itemUnitId: data.unitId,
        itemUnitName: data.unitName,
        itemCategory: data.category,
        itemAttribute: data.attribute,
        dosage: 0.0,
        loss: 0,
        itemNum: 0,
        totalDosage: 0,
      };
      itemList.value.push(newData);
    });
    // 重新排序 key
    itemList.value = itemList.value.map((item, index) => ({
      ...item,
      key: index,
    }));
    // 触发更新
    emit('change', itemList.value);
  }
  // ------------------------------删除--------------------------------
  function deleteItem(key) {
    // 查找真正的索引位置
    const index = itemList.value.findIndex((item) => item.key === key);
    if (index !== -1) {
      itemList.value.splice(index, 1);
      // 重新排序 key
      itemList.value = itemList.value.map((item, idx) => ({
        ...item,
        key: idx,
      }));
      // 触发更新
      emit('change', itemList.value);
    }
  }
  // ------------------------------监听--------------------------------
  watch(
    () => props.value,
    (value) => {
      if (value) {
        itemList.value = value.map((item, index) => {
          if (item.key === undefined) {
            return { ...item, key: index };
          }
          return item;
        });
      } else {
        itemList.value = [];
      }
    },
    { deep: true, immediate: true }
  );
  onMounted(() => {
    queryData();
  });
  defineExpose({
    reset,
  });
</script>
<style scoped></style>

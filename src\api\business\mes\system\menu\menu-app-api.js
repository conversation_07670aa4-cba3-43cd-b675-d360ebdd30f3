/**
 * app菜单 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-11-04 11:19:34
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const menuAppApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/menuApp/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/menuApp/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/menuApp/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/menuApp/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/menuApp/batchDelete', idList);
  },

};

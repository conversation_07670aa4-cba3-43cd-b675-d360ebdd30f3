<!--
  * 货位下拉选择组件
  * 返回所选货位列表
  * @Author:    wxx
  * @Date:      2025-02-11
  * @Copyright  zscbdic
-->

<template>
  <a-select
    v-model:value="selectedValue"
    :style="{ width: width }"
    :placeholder="props.placeholder"
    :showSearch="true"
    :allowClear="true"
    :filterOption="filterOption"
    @change="handleChange"
  >
    <a-select-option v-for="item in locationOptions" :key="item.id" :value="item.id">
      {{ item.number }}
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { stkLocationApi } from '/@/api/business/mes/stock/stk-location-api.js';

  const props = defineProps({
    value: [Number, Array],
    placeholder: {
      type: String,
      default: '请选择货位编码',
    },
    // 仓库id查询货位
    warehouseId: {
      type: Number,
      default: 0,
    },
    width: {
      type: String,
      default: '100%',
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 过滤选项
  function filterOption(inputValue, option) {
    const item = locationOptions.value.find((i) => i.id === option.value);
    return item?.name?.toLowerCase().includes(inputValue.toLowerCase());
  }

  //查询仓库列表
  const locationOptions = ref([]);
  async function queryLocationList() {
    try {
      const queryData = await stkLocationApi.queryList({warehouseId: warehouseId.value});
      locationOptions.value = queryData.data;
    } catch (error) {
      message.error('货位编码请求失败');
    }
  }

  // 处理选择变化
  function handleChange(value) {
    const locationItem = locationOptions.value.find((item) => item.id === value);
    emit('update:value', value);
    emit('change', locationItem);
  }

  // 监听props.value的变化
  const selectedValue = ref(props.value);
  watch(
    () => props.value,
    (newVal) => {
      selectedValue.value = newVal;
    }
  );

  // 监听props.warehouseId的变化
  const warehouseId = ref(props.warehouseId);
  watch(
    () => props.warehouseId,
    (newVal) => {
      selectedValue.value = null;
      warehouseId.value = newVal;
      queryLocationList();
    }
  );

  onMounted(() => {
    queryLocationList();
  });
</script>

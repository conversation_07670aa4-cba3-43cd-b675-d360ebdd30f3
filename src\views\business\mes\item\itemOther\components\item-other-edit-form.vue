<template>
    <a-modal
      title="编辑物料"
      width="1000px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
    >
      <div>
        <a-tabs
          v-model:activeKey="activeKey"
          tab-position="left" 
          @tabScroll="callback"
        >
          <a-tab-pane tab="基本信息">
            <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
              <strong>基本信息</strong>
            </a-row>
            <a-form ref="formRef" :model="form" :rules="rules" v-bind="formItemLayout">
                <a-row>
                  <a-col :span="12">
                    <a-form-item label="物料编号" name="number">
                      <a-input style="width: 100%" v-model:value="form.number" disabled/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="物料名称" name="name">
                      <a-input style="width: 100%" v-model:value="form.name" placeholder="物料名称"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="物料分类" name="typeId">
                      <a-cascader
                          style="width: 100%;"
                          :options="itemTypeTree"
                          v-model:value="form.typeId"
                          placeholder="请选择物料分类"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item label="单位" name="unitId">
                      <a-select
                          v-model:value="form.unitId"
                          style="width: 100%"
                          :options="unitList"
                          placeholder="请选择单位"
                      />
                    </a-form-item>
                  </a-col>
  
                  <a-col :span="12">
                    <a-form-item label="规格型号" name="model">
                      <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号"/>
                    </a-form-item>
                  </a-col>
  
                  <a-col :span="12">
                    <a-form-item label="供应商" name="supplierId">
                      <a-select
                          v-model:value="form.supplierId"
                          style="width: 100%"
                          :options="supplierList"
                          placeholder="请选择供应商"
                      />
                    </a-form-item>
                  </a-col>
  
                  <a-col :span="12">
                    <a-form-item label="价格" name="price">
                      <a-input-number style="width: 100%" v-model:value="form.price" placeholder="价格" :precision="2" :min="0">
                        <template #addonAfter>
                          ¥
                        </template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>
  
                  <a-col :span="12">
                    <a-form-item label="停用标识" name="enableFlag">
                      <a-radio-group v-model:value="form.enableFlag" button-style="solid" style="width: 90%">
                        <a-radio-button :value="false">启用</a-radio-button>
                        <a-radio-button :value="true">停用</a-radio-button>
                      </a-radio-group>
                    </a-form-item>
                  </a-col>
                  <!-- <a-col :span="12">
                    <a-form-item label="类型" name="category">
                      <a-radio-group v-model:value="form.category" button-style="solid" style="width: 90%">
                        <a-radio-button value="0">半成品</a-radio-button>
                        <a-radio-button value="1">成品</a-radio-button>
                      </a-radio-group>
                    </a-form-item>
                  </a-col> -->
                </a-row>
                <a-row>
                <a-col style="width: 150%; margin-left: -14%;">
                    <a-form-item label="图片" name="imgUrl">
                      <div>
                      <file-upload 
                      style="padding:10px 10px 0 10px;"
                      @change="form.imgUrl = $event" 
                      :default-file-list="form.imgUrl"
                      :maxUploadSize=10
                      :accept="'.jpg,.jfif,.bmp,.png,.gif'"
                      :maxSize=5
                    />
                    <span style="margin-left: 10px; color:rgba(0, 0, 0, 0.3);">说明：一次最多可上传多张照片，最多支持上传10张，每张图片大小不超过5M，格式为：jpg、jfif、gif、png、bmp</span>
                  </div>
                    </a-form-item>
                </a-col>
            </a-row>
              </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
      <template #footer>
        <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        </a-space>
      </template>
    </a-modal>
  </template>

<script setup>
import {nextTick, onMounted, reactive, ref} from "vue";
import FileUpload from "/@/components/support/file-upload/index.vue";
import {itemTypeApi} from '/@/api/business/mes/item/item-type-api.js';
import {smartSentry} from "/@/lib/smart-sentry.js";
import {unitApi} from "/@/api/business/mes/base/unit-api.js";
import {supplierApi} from "/@/api/business/mes/base/supplier-api.js";
import {message} from "ant-design-vue";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import {itemApi} from "/@/api/business/mes/item/item-api.js";

const emits = defineEmits(['reloadList']);

// 是否显示
const visibleFlag = ref(false);

async function show(rowData) {
  visibleFlag.value = true;
  SmartLoading.show();
  const res = await itemApi.getById(rowData.id); 
  const originalData = res.data;          
  // 构建路径
  if (originalData.typeId) {
    originalData.typeId =await findCascaderPath(itemTypeTree.value, originalData.typeId);
  } 
  Object.assign(form, originalData);
  SmartLoading.hide()
  
  nextTick(() => {
    formRef.value.clearValidate();
  });
}
// 递归查找级联选择器的完整路径
function findCascaderPath(tree, id, path = []) {
    for(const node of tree) {
        const current = [...path, node.value];
        if(node.value === id) return current;
        if(node.children?.length) {
            const found = findCascaderPath(node.children, id, current);
            if(found) return found;
        }
    }
    return null;
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

const formItemLayout = {
  labelCol: {
    xs: {span: 24},
    sm: {span: 7},
  },
  wrapperCol: {
    xs: {span: 24},
    sm: {span: 17},
  },
};
// ------------------------ 表单 ------------------------
// 组件ref
const formRef = ref();
// 物料分类
const itemType = ref("");
// 初始化表单
const formDefault = {
  id: undefined,
  typeId: undefined,
  name: undefined,
  supplierId: undefined,
  model: undefined,
  skuNumber: undefined,
  number: undefined,
  unitId: undefined,
  enableFlag: false,
  price: undefined,
  category: "0",
  attribute: "1",
  imgUrl: [],
};

let form = reactive({...formDefault});

const rules = {
// id: [{ required: true, message: '主键 必填' }],
  // typeId: [{required: true, message: '物料分类id;关联t_item_type 必填'}],
  name: [{required: true, message: '物料名称 必填'}],
  // number: [{required: true, message: '物料编号 必填'}],
  unitId: [{required: true, message: '单位 必填'}],
  // category: [{required: true, message: '类型;0半成品 1成品 必填'}],
  enableFlag: [{required: true, message: '停用标识 必填'}],
  // attribute: [{required: true, message: '属性;0面料，1辅料 必填'}],
};
/**
 * 处理物料分类选择
 * @param value
 * @param selectedOptions
 */
onMounted(() => {
  queryItemType();
  queryUnitList();
  querySupplierList();
});

const itemTypeTree = ref([]); // 物料分类
const unitList = ref([]); // 单位
const supplierList = ref([]); // 供应商

async function queryItemType() {
  try {
    let queryResult = await itemTypeApi.queryCategoryTree({});
    itemTypeTree.value = queryResult.data;
    
  } catch (e) {
    smartSentry.captureError(e);
  }
}

async function queryUnitList() {
  try {
    let queryResult = await unitApi.querySelect({});
    unitList.value = queryResult.data.map(item => ({
      value: item["id"],
      label: item["name"]
    }));
  } catch (e) {
    smartSentry.captureError(e);
  }
}

async function querySupplierList() {
  try {
    let queryResult = await supplierApi.queryAll({});
    supplierList.value = queryResult.data.map(item => ({
      value: item["id"],
      label: item["name"]
    }));
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 编辑API
async function save() {
  SmartLoading.show();
  try {
    // 只传递接口要求的字段
    const dataToSend = {
      id:form.id,
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
      name: form.name,
      supplierId: form.supplierId,
      model: form.model,
      skuNumber: form.skuNumber,
      number: form.number,
      price: form.price,
      unitId: form.unitId,
      enableFlag: form.enableFlag,
      category: form.category,
      attribute: form.attribute,
      imgUrl: form.imgUrl,
    };
    await itemApi.update(dataToSend);
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>
<style scoped>
</style>
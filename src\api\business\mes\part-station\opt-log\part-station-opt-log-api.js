/**
 * 裁片驿站操作日志 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-07 19:47:06
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partStationOptLogApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/partStationOptLog/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/partStationOptLog/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/partStationOptLog/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/partStationOptLog/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/partStationOptLog/batchDelete', idList);
  },

    /**
     * 入库次数与数量
     */
    queryOutboundCountAndNum: (param) => {
        return getRequest('/partStationOptLog/stats/outNum', param);
    },

    /**
     * 出库次数与数量
     */
    queryInboundCountAndNum: (param) => {
        return getRequest('/partStationOptLog/stats/inNum', param);
    },

};

<template>
  <cut-plan-form ref="formRef" @submit="handleSubmit" />
</template>
<script setup>
  import cutPlanForm from '/@/views/business/mes/tailor/cut-plan/cut-plan-form.vue';
  import { ref, nextTick } from 'vue';
  import { cutPlan<PERSON>pi } from '/@/api/business/mes/tailor/cut-plan.js';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { baseLeftColumns } from '/@/views/business/mes/tailor/cut-plan/components/baseColumns.js';
  // ---------------------------- 获取表单数据 ----------------------------
  const emit = defineEmits(['success']);
  const formRef = ref();

  // 加载详情数据
  async function loadDetail(id) {
    try {
      SmartLoading.show();
      const res = await cutPlanApi.get(id);
      console.log(res.data);

      if (res.data) {
        // 转换数据格式用于回显
        const detail = res.data;
        const formData = {
          id: detail.id,
          remark: detail.remark, //备注
          originType: detail.originType, //来源订单类型
          originId: detail.originId, //来源单据ID
          originNumber: detail.originNumber, //来源单据编号
          produceInstructOrderId: detail.produceInstructOrderId, //生产指令单ID
          produceInstructOrderNumber: detail.produceInstructOrderNumber, //生产指令单编号
          number: detail.number, //裁剪计划单编号
          status: detail.status, //计划单状态
          planStartTime: detail.planStartTime, //计划开始时间
          planFinishTime: detail.planFinishTime, //计划完成时间
          realStartTime: detail.realStartTime, //实际开始时间
          realFinishTime: detail.realFinishTime, //实际完成时间
          paperPatternId: detail.paperPatternId, //纸样ID
          paperPatternNumber: detail.paperPatternNumber, //纸样编号
          planDosage: detail.planDosage, //计划单件用量（米）
          realDosage: detail.realDosage, //实际单件用量（米）
          realOverPercent: detail.realOverPercent, //实际超裁比例（%）
          planOverPercent: detail.planOverPercent, //计划超裁比例（%）
          planClothDosage: detail.planClothDosage, //计划需布量（米）
          realClothDosage: detail.realClothDosage, //实际需布量（米）
        };

        // 转换裁床数据用于回显
        const beds = (detail.beds || []).map((bed) => {
          // 创建基础列配置
          const leftColumns = [...baseLeftColumns];

          bed.sizes?.forEach((size, index) => {
            leftColumns.splice(-1, 0, {
              title: size.size,
              dataIndex: `size${index}`,
              align: 'center',
              ratio: size.ratio,
            });
          });
          return {
            id: bed?.id,
            remark: bed?.remark,
            seq: bed?.seq,
            cutNum: bed?.cutNum,
            parts: bed?.parts,
            partsOptions: (bed?.parts).map((part) => ({ label: part, value: part })),
            clothLen: bed?.clothLen,
            unitDosage: bed?.unitDosage,
            leftColumns, // 使用处理后的列配置
            // 计算尺码数量
            leftTableData: (bed?.colors).map((color) => {
              const baseData = {
                styleColor: color?.color,
                clothPutNum: color?.planLayer,
              };
              //计算所有尺码的数量
              const sizeData = {};
              bed.sizes?.forEach((size, index) => {
                // 铺布张数
                const planLayer = color?.planLayer;
                // 尺码比例
                const ratio = size.ratio;
                // 计算该尺码的数量并保存
                sizeData[`size${index}`] = planLayer * ratio;
              });
              return {
                ...baseData,
                ...sizeData,
              };
            }),
          };
        });
        // 设置表单数据
        await formRef.value.setFormData(formData, beds);
        // 等待数据加载完成后，手动触发一次计算
        nextTick(() => {
          const numData = formRef.value.calculateNumTableData();
          formRef.value.numTableData = numData;
        });
      }
    } catch (e) {
      message.error('加载数据失败');
      console.log('加载数据失败', e);
    } finally {
      SmartLoading.hide();
    }
  }

  async function handleSubmit() {
    try {
      const formData = formRef.value.getFormData();
      const cutBedsData = formRef.value.getCutBedsData();

      // 转换裁床数据
      const beds = cutBedsData.map((bed) => {
        // 获取总裁数
        const qty = bed.leftTableData.reduce((sum, row) => {
          const rowTotal = bed.leftColumns
            .filter((col) => col.dataIndex.startsWith('size'))
            .reduce((sizeSum, col) => sizeSum + (row[col.dataIndex] || 0), 0);
          return sum + rowTotal;
        }, 0);

        // 转换尺码数据
        const sizes = bed.leftColumns
          .filter((col) => col.dataIndex.startsWith('size'))
          .map((col) => ({
            size: col.title,
            ratio: col.ratio || 0,
          }));

        // 转换颜色数据
        const colors = bed.leftTableData.map((row) => ({
          color: row.styleColor,
          planLayer: row.clothPutNum || 0,
          unitDosage: bed.unitDosage || 0,
        }));

        // 计算总裁数
        const layer = colors.reduce((sum, color) => sum + color.planLayer, 0);

        return {
          id: bed.id,
          remark: bed.remark,
          seq: bed.seq,
          cutNum: bed.cutNum,
          parts: bed.parts,
          qty,
          clothLen: bed.clothLen,
          layer,
          sizes,
          colors,
        };
      });

      const submitData = {
        ...formData,
        beds,
      };

      // 验证必填字段
      if (!submitData.produceInstructOrderId) {
        message.error('请选择生产指令单');
        return;
      }
      if (!beds.length) {
        message.error('请至少添加一个裁床');
        return;
      }
      if (beds.some(bed => !bed.cutNum)) {
        message.error('请输入床次');
        return;
      }
      if (beds.some(bed => !bed.parts.length)) {
        message.error('请选择部位');
        return;
      }
      if (beds.some(bed => !bed.clothLen)) {
        message.error('请输入铺布长度');
        return;
      }

      // 提交数据
      await cutPlanApi.update(submitData);
      message.success('更新成功');
      emit('success');
      formRef.value.onClose();
    } catch (e) {
      message.error('提交失败');
      console.error(e);
    }
  }

  // ---------------------------- 暴露方法 ----------------------------
  defineExpose({
    show: (id) => {
      formRef.value.show();
      loadDetail(id);
    },
  });
</script>

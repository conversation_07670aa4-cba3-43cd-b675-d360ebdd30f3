/**
 * 工资字段值 api 封装
 *
 * @Author:    linwj
 * @Date:      2024-12-04 23:08:59
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const wageFieldValueApi = {

  /**
   * 更新员工工资项值  <AUTHOR>
   */
  update : (param) => {
    return getRequest('/wageFieldValue/updateValue',param);
  },

  /**
   * 批量调整员工工资项值  <AUTHOR>
   */
  batchUpdateValue : (param) => {
    return postRequest('/wageFieldValue/batchUpdateValue',param);
  },
};

/**
 * 大模型API 封装
 *
 * @Author:    wxx
 * @Date:      2025
 * @Copyright  zscbdic
 */

import { postRequest, getRequest } from '/@/lib/axios';

export const aiModelApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/lLMModel/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/lLMModel/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/lLMModel/update', param);
  },

  /**
   * 单个删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/lLMModel/delete/${id}`);
  },

  /**
   * 列表
   * @returns 
   */
  list: () => {
    return postRequest('/lLMModel/list');
  },
};

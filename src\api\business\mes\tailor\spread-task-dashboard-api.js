import { postRequest, getRequest } from '/@/lib/axios';

export const spreadTaskDashboardApi = {
  /**
   * 逾期任务数 <AUTHOR>
   */
  overdue: (param) => {
    return getRequest('/spreadTask/stats/overdue', param);
  },

  /**
   * 状态数量统计 <AUTHOR>
   */
  statsCount: (param) => {
    return postRequest('/spreadTask/stats/count', param);
  },

  /**
   * 各状态数量统计 <AUTHOR>
   */
  statsCountList: (param) => {
    return postRequest('/spreadTask/stats/count/list', param);
  },

  /**
   * 各任务对象统计 <AUTHOR>
   */
  taskObject: (param) => {
    return postRequest('/spreadTask/stats/taskObject', param);
  },

  /**
   * 铺布任务数量趋势 <AUTHOR>
   */
  countTrend: (param) => {
    return postRequest('/spreadTask/stats/countTrend', param);
  },

  /**
   * 产量趋势 <AUTHOR>
   */
  outputTrend: (param) => {
    return postRequest('/spreadTask/stats/outputTrend', param);
  },
};

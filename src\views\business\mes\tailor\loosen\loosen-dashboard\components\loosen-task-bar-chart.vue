<template>
  <div style="display: flex; justify-content: space-between">
    <div style="font-size: 16px; font-weight: bold">每日松布任务数量</div>
    <a-radio-group v-model:value="timeOption" @change="handleTimeChange" :options="timeOptions" option-type="button" />
  </div>
  <div class="echarts-box">
    <div class="chart-main" id="loosen-task-bar-chart"></div>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted, ref } from 'vue';
  import * as echarts from 'echarts';

  let myChart = null;

  // 时间选择选项
  const timeOptions = [
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' },
    { label: '本季', value: 'quarter' },
    { label: '本年', value: 'year' },
  ];

  const timeOption = ref('week');

  // 静态数据 - 不同时间范围使用相同数据
  const staticData = {
    week: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      values: [8, 12, 16, 9, 14, 6, 10],
    },
    month: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      values: [8, 12, 16, 9, 14, 6, 10],
    },
    quarter: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      values: [8, 12, 16, 9, 14, 6, 10],
    },
    year: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      values: [8, 12, 16, 9, 14, 6, 10],
    },
  };

  onMounted(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (myChart) {
      myChart.dispose();
      myChart = null;
    }
  });

  // 窗口大小变化处理
  function handleResize() {
    if (myChart) {
      myChart.resize();
    }
  }

  // 处理时间选择变化
  function handleTimeChange() {
    updateChart();
  }

  // 初始化图表
  function initChart() {
    const chartDom = document.getElementById('loosen-task-bar-chart');
    if (chartDom) {
      myChart = echarts.init(chartDom);
      updateChart();
    }
  }

  // 更新图表
  function updateChart() {
    if (!myChart) return;

    // 获取当前选择的时间范围对应的数据
    const currentData = staticData[timeOption.value];

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: currentData.dates,
        axisLabel: {
          interval: 0,
          fontSize: 10,
          rotate: currentData.dates.length > 7 ? 45 : 0,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '任务数量',
        nameTextStyle: {
          fontSize: 10,
          padding: [0, 0, 0, 30],
        },
        axisLabel: {
          fontSize: 10,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      series: [
        {
          name: '任务数量',
          type: 'bar',
          barWidth: '50%',
          data: currentData.values,
          itemStyle: {
            color: '#4687f0',
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 10,
          },
        },
      ],
    };

    myChart.setOption(option);

    // 强制重新计算大小，确保图表充满容器
    setTimeout(() => {
      myChart.resize();
    }, 200);
  }
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: calc(100% - 40px);

    .chart-main {
      width: 100%;
      height: 100%;
      min-height: 230px;
      background: #fff;
    }
  }
</style>

<template>
    <a-modal
      :title="'导出工资条'"
      :open="visibleFlag"
      @cancel="onClose"
      width="950px"
  >
    <div class="print-content">
        <div id="pdf-content" >
            <div v-for="(item, index) in resArray" :key="index" id="salary-item">
                <!-- 表头 -->
                <div class="salary-title">工资条</div>
                <!-- 表格 -->
                <table class="salary-table">
                <tr>
                    <th rowspan="2">员工姓名</th>
                    <th rowspan="2">归属月份</th>
                    <th rowspan="2">总金额</th>
                    <th rowspan="2">计件金额</th>
                    <th rowspan="2">其他项</th>
                    <th rowspan="2">发放时间</th>
                    <template v-if=" getPayableItems(item).length > 0">
                        <th :colspan="getPayableItems(item).length">应发项</th>
                    </template>
                    <template v-if=" getDeductItems(item).length > 0">
                        <th :colspan="getDeductItems(item).length">扣款项</th>
                    </template>
                </tr>
                <tr>
                    <td v-for="(amount, index) in getPayableItems(item)" :key="index">
                        {{amount.fieldName}}
                    </td>
                    <td v-for="(amount, index) in getDeductItems(item)" :key="index">
                        {{amount.fieldName}}
                    </td>
                </tr>
                <tr>
                    <td>{{ item.actualName }}</td>
                    <td>{{ item.belongMonth }}</td>
                    <td>{{ item.totalAmount }}</td>
                    <td>{{ item.pieceAmount }}</td>
                    <td>{{ item.otherAmount }}</td>
                    <td>{{ item.payoffTime}}</td>
                <td v-for="(amount, index) in item.otherAmounts" :key="index">
                    <div>{{amount.fieldValue}}</div>
                </td>
                </tr>
                </table>
            </div>
        </div>
    </div>
    <template #footer>
        <div style="float: left;">
        <span style="white-space: nowrap;padding-top: 5px;">文件名:</span>
            <a-input 
                v-model:value="fileName" 
                style="margin-left: 8px; width: 200px;" 
                placeholder="请输入文件名"
            />
        </div>
      <a-button @click="pdfExport" type="primary">导出</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { getPayoffRecordPdf } from '/@/utils/pdf-util';

const resArray = ref([])//数据
const visibleFlag = ref(false);//是否显示
const fileName = ref('工资条')//文件名

function onClose() {
  visibleFlag.value = false
 }

async function pdfExport(){
    await getPayoffRecordPdf(fileName.value)
}

//计算 发和扣 属性 
const getPayableItems = (item) => item.otherAmounts.filter(amount => amount.type === '0');
const getDeductItems = (item) => item.otherAmounts.filter(amount => amount.type === '1');

function downloadPdf(data){
    visibleFlag.value = true
    resArray.value = data.map(item => ({
        ...item,
        belongMonth: item.belongMonth?.substring(0, 10), // 只保留 YYYY-MM 格式
        payoffTime: item.payoffTime?.substring(0, 10) // 只保留 YYYY-MM-DD 格式
    }))
}

defineExpose({
  downloadPdf,
})
</script>

<style scoped>
    .print-content{
       max-height: 600px;
       overflow-y: auto;
    }
    #pdf-content{
        width: 100%;
        max-width: 842px;
        margin: 0 auto;
    }
    #salary-item{
        width:842px;
    }
    .salary-title{
        font-size: 20px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 5px;
    }
    .salary-table{
        width: 100%;
        border: 1px solid #000;
        border-collapse: collapse; /* 添加这行确保边框正确折叠 */
        table-layout: fixed;  /* 添加这行使列宽平均分布 */
        font-size: 12px;
    }
    th,td{
        border: 1px solid #000;
        border-collapse: collapse;
        text-align: center;
        padding: 0 5px;
        height: 20px;
        font-size: inherit;  
        word-break: break-all;  /* 添加文字换行 */
    }
</style>


<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="仓库" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.warehouseId" placeholder="请选择仓库"/>
      </a-form-item>
      <a-form-item label="时间范围" class="smart-query-form-item">
        <a-range-picker/>
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>


  <a-card size="small" >
    <div style="display: flex; justify-content: space-between;align-items: center">
      <div class="card">
        <div>
          <div class="desc">出库次数</div>
          <div class="value">123</div>
        </div>
        <div>
          <div class="sub-desc">相较昨日</div>
          <div class="sub-value"><span style="color: #fa2e23">10% <CaretUpOutlined /></span></div>
        </div>
      </div>

      <div class="card">
        <div>
          <div class="desc">出库数量</div>
          <div class="value">123</div>
        </div>
        <div>
          <div class="sub-desc">相较昨日</div>
          <div class="sub-value"><span style="color: #fa2e23">10% <CaretUpOutlined /></span></div>
        </div>
      </div>

      <div class="card">
        <div>
          <div class="desc">入库次数</div>
          <div class="value">123</div>
        </div>
        <div>
          <div class="sub-desc">相较昨日</div>
          <div class="sub-value"><span style="color: #67C23A">12% <CaretDownOutlined /></span></div>
        </div>
      </div>

      <div class="card">
        <div>
          <div class="desc">入库数量</div>
          <div class="value">123</div>
        </div>
        <div>
          <div class="sub-desc">相较昨日</div>
          <div class="sub-value"><span style="color: #67C23A">12% <CaretDownOutlined /></span></div>
        </div>
      </div>

      <div class="card">
        <div>
          <div class="desc">库存总量</div>
          <div class="value">123</div>
        </div>
        <div>
        </div>
      </div>

    </div>

  </a-card>
</template>
<script setup>
import {reactive} from "vue";

const props = defineProps({
  warehouseId: {
    type: Number,
    default: null
  },
  dateBegin: {
    type: String,
    default: null
  },
  dateEnd: {
    type: String,
    default: null
  }
})

const queryFormState = {
  warehouseId: null,
  dateBegin: null,
  dateEnd: null,
};
// 查询表单form
const queryForm = reactive({...queryFormState});

//-----------------------------------

async function queryData() {
  console.log('queryData', queryForm);
}

function resetQuery() {
  Object.assign(queryForm, queryFormState);
}

</script>
<style scoped lang="less">
.card{
  display: flex;
  justify-content: space-around;
  background-color: #f0f3f7;
  padding: 10px;
  border-radius: 6px;
  width: 19%;

  .desc{
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 6px;
  }
  .value{
    font-size: 18px;
    font-weight: bold;
  }

  .sub-desc{
    font-size: 18px;
  }
  .sub-value{
    font-size: 18px;
  }
}
</style>

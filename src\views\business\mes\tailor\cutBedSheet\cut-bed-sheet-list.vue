<!--
  * 裁床单
  *
  * @Author:    cjm
  * @Date:      2024-07-13 10:53:07
  * @Copyright  cjm
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="床次" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.cutNum" placeholder="床次" />
      </a-form-item>
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showSecondForm(record)" type="link">查看</a-button>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'attribute'">
          <a-tag :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">{{ ITEM_ATTRIBUTE_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'feTicketGenerateFlag'">
          <a-tag :color="FETICKET_CREATE_ENUM.getEnum(text).color">{{ FETICKET_CREATE_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'cutNumber'">
          <a-button type="link" @click="showSecondForm(record)">{{ text }}</a-button>
        </template>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell>
                <a-typography-text>{{ value }}</a-typography-text>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <CutBedSheetForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { cutBedSheetApi } from '/@/api/business/mes/tailor/cut-bed-sheet-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import CutBedSheetForm from './cut-bed-sheet-form.vue';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { FETICKET_CREATE_ENUM } from '/@/constants/business/mes/tailor/fe-ticket-const.js';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';

  import { useRouter } from 'vue-router';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '单据编号',
      dataIndex: 'cutNumber',
      ellipsis: true,
    },
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      ellipsis: true,
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      ellipsis: true,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: '总裁数',
      dataIndex: 'quantity',
      ellipsis: true,
    },
    {
      title: '菲票生成',
      dataIndex: 'feTicketGenerateFlag',
      ellipsis: true,
    },
    {
      title: '裁剪人',
      dataIndex: 'cutter',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    cutNum: undefined, //床次
    instructNumber: undefined, //指令单编号
    itemNumber: undefined, //物料编号
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    insItemSelectRef.value.clearAllNumber();
    Object.assign(queryForm, queryFormState);

    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await cutBedSheetApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  //-------------------------------合计----------------------------------------
  const totals = computed(() => {
    const sums = {
      totalQuantity: 0, //合计总裁数
    };
    tableData.value.forEach((item) => {
      sums.totalQuantity += item.quantity;
    });
    return sums;
  });
  //计算剩余的空单元格
  const summaryColSpans = 5; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => columns.value.length - summaryColSpans - Object.keys(totals.value).length);

  // ---------------------------- 处理两个编号 ----------------------------
  const insItemSelectRef = ref();
  function insItemChange(data) {
    // 回传数据
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
    queryForm.instructOrderId = data.instructOrderId;
  }

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();
  const toRouter = useRouter();
  function showForm(data) {
    //跳转路由
    toRouter.push({
      path: '/business/mes/tailor/cutBedSheet/cut-bed-sheet-add',
      query: data.id != null && data.id !== '' && data.id !== undefined ? { id: data.id } : {},
    });
  }
  function showSecondForm(data) {
    formRef.value.show(data);
  }

</script>

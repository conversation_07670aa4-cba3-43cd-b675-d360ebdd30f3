
<!--
  * 床次输入框（带详情悬浮显示）不合并
  *
  * @Author:    pxz
  * @Date:      2025-07-22
  * @Copyright  zscbdic
-->
 <template>
  <a-input
    v-model:value="inputValue"
    :placeholder="placeholder"
    :style="`width: ${width}`"
    @change="handleChange"
  >
    <template #addonAfter>
      <a-popover
        title="床次情况"
        trigger="hover"
        placement="topRight"
      >
        <template #content>
          <div v-if="loading" style="text-align: center; padding: 20px;">
            <a-spin />
          </div>
          <div v-else-if="bedBlocks.length === 0" style="padding: 20px; text-align: center; color: #999;">
            暂无数据
          </div>
          <div v-else style="display: flex; flex-wrap: wrap; gap: 8px; max-width: 300px;">
            <div
              v-for="block in bedBlocks"
              :key="block.bedNum"
              :class="['bed-block', getBlockColorClass(block.types)]"
            >
              <div class="bed-types">{{ block.types.join('/') }}</div>
              <div class="bed-number">{{ block.bedNum }}</div>
            </div>
          </div>
        </template>
        <span style="cursor: pointer; color: #1890ff;">详情</span>
      </a-popover>
    </template>
  </a-input>
</template>

<script setup>
import { ref, watch } from 'vue';
import { cutPlanApi } from '/@/api/business/mes/tailor/cut-plan.js';
import { smartSentry } from '/@/lib/smart-sentry';
import { BED_TASK_TYPE_ENUM } from '/@/constants/business/mes/tailor/bed-task-const.js';

const props = defineProps({
  value: {
    type: [String, Number],
    default: ''
  },
  placeholder: {
    type: String,
    default: '床次'
  },
  width: {
    type: String,
    default: '100%'
  },
  produceInstructOrderId: {
    type: [String, Number],
    default: ''
  }
});

const emit = defineEmits(['update:value', 'change']);

const inputValue = ref(props.value);
const loading = ref(false);
const bedBlocks = ref([]);

// 处理输入变化
function handleChange(e) {
  const value = e.target.value;
  inputValue.value = value;
  emit('update:value', value);
  emit('change', value);
}

// 获取方块颜色类名
function getBlockColorClass(types) {
  // 如果只有一种类型，返回对应颜色类名
  // if (types.length === 1) {
  //   const taskEnum = BED_TASK_TYPE_ENUM.getEnumByLabel(types[0]);
  //   return taskEnum ? taskEnum.cssClass : '';
  // }
  if(types.includes(BED_TASK_TYPE_ENUM.CUT.label)){
    return BED_TASK_TYPE_ENUM.CUT.cssClass;
  }
  // 多种类型使用默认颜色
  return '';
}

// 获取床次统计数据
async function getBedNumList() {
  if (!props.produceInstructOrderId) {
    bedBlocks.value = [];
    return;
  }

  loading.value = true;
  try {
    const res = await cutPlanApi.getBedNumList(props.produceInstructOrderId);
    if (res.data) {
      const data = res.data;

      // 获取所有床次号
      const allBedNums = new Set([
        ...(data[BED_TASK_TYPE_ENUM.PLAN.value] || []),
        ...(data[BED_TASK_TYPE_ENUM.SPREAD.value] || []),
        ...(data[BED_TASK_TYPE_ENUM.CUT.value] || [])
      ]);

      // 生成小方块数据
      bedBlocks.value = Array.from(allBedNums)
        .sort((a, b) => a - b)
        .map(bedNum => {
          const types = [];
          if ((data[BED_TASK_TYPE_ENUM.PLAN.value] || []).includes(bedNum)) {
            types.push(BED_TASK_TYPE_ENUM.PLAN.label);
          }
          if ((data[BED_TASK_TYPE_ENUM.SPREAD.value] || []).includes(bedNum)) {
            types.push(BED_TASK_TYPE_ENUM.SPREAD.label);
          }
          if ((data[BED_TASK_TYPE_ENUM.CUT.value] || []).includes(bedNum)) {
            types.push(BED_TASK_TYPE_ENUM.CUT.label);
          }

          return {
            bedNum,
            types
          };
        });
    }
  } catch (err) {
    smartSentry.captureError(err);
    bedBlocks.value = [];
  } finally {
    loading.value = false;
  }
}

// 监听props变化
watch(() => props.value, (newVal) => {
  inputValue.value = newVal;
});

watch(() => props.produceInstructOrderId, (newVal) => {
  if (newVal) {
    getBedNumList();
  } else {
    bedBlocks.value = [];
  }
}, { immediate: true });
</script>

<style scoped>
:deep(.ant-input-group-addon) {
  padding: 0 8px;
}

.bed-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
  font-size: 12px;
  transition: all 0.3s;
}

.bed-block:hover {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

.bed-number {
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.bed-types {
  color: #1890ff;
  font-size: 12px;
  line-height: 1;
}

/* 不同类型的颜色样式 - 使用枚举配置 */
.bed-block-spread {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.bed-block-spread .bed-types {
  color: #1890ff;
}

.bed-block-plan {
  background-color: #fff7e6;
  border-color: #fa8c16;
}

.bed-block-plan .bed-types {
  color: #fa8c16;
}

.bed-block-cut {
  background-color: #f6ffed;
  border-color: #52c41a;
}

.bed-block-cut .bed-types {
  color: #52c41a;
}
</style>


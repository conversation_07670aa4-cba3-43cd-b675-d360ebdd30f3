<template>
  <a-row :gutter="[10, 10]">
    <a-col :span="8">
      <div class="target-card">
        <div class="title">今日新单</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>
        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>

        <div style="display: flex; justify-content: space-between; align-items: baseline">
          <div class="value">
            <span>{{ requestDataNumForm.todayNewOrderNum }}</span
            >单
          </div>
          <div style="color: #9b9b9b">
            相较昨日
            <span v-if="newOrderCompare >= 0" style="color: #00b42a"><CaretUpFilled style="color: #00b42a" />{{ newOrderCompare }}单</span>
            <span v-else style="color: #ff4d4f"> <CaretDownFilled style="color: #ff4d4f" />{{ Math.abs(newOrderCompare) }}单 </span>
          </div>
        </div>
      </div>
    </a-col>

    <a-col :span="8">
      <div class="target-card">
        <div class="title">今日下达</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>
        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: baseline">
          <div class="value">
            <span>{{ requestDataNumForm.todayIssueOrderNum }}</span
            >单
          </div>
          <div style="color: #9b9b9b">
            相较昨日
            <span v-if="issueOrderCompare >= 0" style="color: #00b42a"><CaretUpFilled style="color: #00b42a" />{{ issueOrderCompare }}单</span>
            <span v-else style="color: #ff4d4f"> <CaretDownFilled style="color: #ff4d4f" />{{ Math.abs(issueOrderCompare) }}单 </span>
          </div>
        </div>
      </div>
    </a-col>

    <a-col :span="8">
      <div class="target-card">
        <div class="title">今日完成</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>

        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>

        <div style="display: flex; justify-content: space-between; align-items: baseline">
          <div class="value">
            <span>{{ requestDataNumForm.todayFinishOrderNum }}</span
            >单
          </div>
          <div style="color: #9b9b9b">
            相较昨日
            <span v-if="finishOrderCompare >= 0" style="color: #00b42a"><CaretUpFilled style="color: #00b42a" />{{ finishOrderCompare }}单</span>
            <span v-else style="color: #ff4d4f"> <CaretDownFilled style="color: #ff4d4f" />{{ Math.abs(finishOrderCompare) }}单 </span>
          </div>
        </div>
      </div>
    </a-col>

    <a-col :span="8">
      <div class="target-card">
        <div class="title">现有指令单数</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>

        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>

        <div class="value">
          <span>{{ requestDataNumForm.currentOrderNum }}</span
          >单
        </div>
      </div>
    </a-col>

    <a-col :span="8">
      <div class="target-card">
        <div class="title">进行中指令单数</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>

        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>

        <div class="value">
          <span style="color: #faad14">{{ requestDataNumForm.doingOrderNum }}</span
          >单
        </div>
      </div>
    </a-col>

    <a-col :span="8">
      <div class="target-card">
        <div class="title">逾期指令单数</div>
        <div class="time">{{ dateForm.todayDate }}~{{ dateForm.todayDate }}</div>
        <div class="now">
          {{ dateForm.todayMonthDay }} <span>({{ dateForm.todayWeek }})</span>
        </div>

        <div class="value">
          <span style="color: #ff4d4f">{{ requestDataNumForm.unfinishedOrderNum }}</span
          >单
        </div>
      </div>
    </a-col>

    <a-col :span="16">
      <produce-instruct-order-line
        :data="Array.from(echartDataForm.lineChartData)"
        :time="Object.create({
          startTime: dateForm.thirtyDaysAgo,
          endTime: dateForm.todayDate,
          detailCurrentTime: dateForm.todayMonthDay,
          week: dateForm.todayWeek
        })"
        style="width: 100%"
      />
    </a-col>
    <a-col :span="8">
      <produce-instruct-order-pie
        :data="Array.from(echartDataForm.pieChartData)"
        :time="Object.create({ startTime: dateForm.thirtyDaysAgo, endTime: dateForm.todayDate })"
        style="width: 100%"
      />
    </a-col>
  </a-row>
</template>
<script setup>
  import { onMounted, ref, computed } from 'vue';
  import ProduceInstructOrderLine from '/@/views/system/home/<USER>/echarts/prodruce-instruct-order-line.vue';
  import ProduceInstructOrderPie from '/@/views/system/home/<USER>/echarts/prodruce-instruct-order-pie.vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import dayjs from 'dayjs';
  import { produceInstructOrderStatsApi } from '/@/api/business/mes/produce/produce-instruct-order-stats-api.js';

  //-------------------------------------------------日期数据--------------------------------------------------
  //定义日期数据
  const dateForm = ref({
    todayDate: '', //今日日期   yyyy-mm-dd
    todayMonthDay: '', //今日月日   mm-dd
    yesterdayDate: '', //昨日日期   yyyy-mm-dd
    todayWeek: '', //今日星期   星期几
    thirtyDaysAgo: '', //距今日30天前的日期   yyyy-mm-dd
  });

  //获取日期数据
  function getDateData() {
    dateForm.value.todayDate = dayjs().format('YYYY-MM-DD'); //今日日期 yyyy-mm-dd
    dateForm.value.todayMonthDay = dayjs().format('MM-DD'); //今日月日 mm-dd
    dateForm.value.yesterdayDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD'); //昨日日期 yyyy-mm-dd
    dateForm.value.todayWeek = dayjs().locale('zh-cn').format('dddd'); //今日星期几
    dateForm.value.thirtyDaysAgo = dayjs().subtract(30, 'day').format('YYYY-MM-DD'); //距今日30天前的日期 yyyy-mm-dd
  }

  //---------------------------------------查询数量数据，计算今昨日比较数量------------------------------------------
  //定义数量数据
  const requestDataNumForm = ref({
    todayNewOrderNum: 0, //今日新单数
    yesterdayOrderNum: 0, //昨日工单数
    todayIssueOrderNum: 0, //今日下达数
    yesterdayIssueOrderNum: 0, //昨日下达数
    todayFinishOrderNum: 0, //今日完成数
    yesterdayFinishOrderNum: 0, //昨日完成数
    currentOrderNum: 0, //现有指令单数
    doingOrderNum: 0, //进行中指令单数
    unfinishedOrderNum: 0, //逾期指令单数
  });

  //----计算今昨日比较数量----
  //完成数比较
  const newOrderCompare = computed(() => {
    return requestDataNumForm.value.todayNewOrderNum - requestDataNumForm.value.yesterdayOrderNum;
  });
  //下达数比较
  const issueOrderCompare = computed(() => {
    return requestDataNumForm.value.todayIssueOrderNum - requestDataNumForm.value.yesterdayIssueOrderNum;
  });
  //完成数比较
  const finishOrderCompare = computed(() => {
    return requestDataNumForm.value.todayFinishOrderNum - requestDataNumForm.value.yesterdayFinishOrderNum;
  });

  //----工单数量----
  //今日
  async function queryOrderNum() {
    let param = {
      startTime: dateForm.value.todayDate,
      endTime: dateForm.value.todayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryNewOrder(param);
    requestDataNumForm.value.todayNewOrderNum = queryData.data;
  }
  //昨日
  async function queryYesterdayOrderNum() {
    let param = {
      startTime: dateForm.value.yesterdayDate,
      endTime: dateForm.value.yesterdayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryNewOrder(param);
    requestDataNumForm.value.yesterdayOrderNum = queryData.data;
  }

  //----下达数量----
  //今日
  async function queryTodayIssueOrderNum() {
    let param = {
      startTime: dateForm.value.todayDate,
      endTime: dateForm.value.todayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryIssueOrder(param);
    requestDataNumForm.value.todayIssueOrderNum = queryData.data;
  }
  //昨日
  async function queryYesterdayIssueOrderNum() {
    let param = {
      startTime: dateForm.value.yesterdayDate,
      endTime: dateForm.value.yesterdayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryIssueOrder(param);
    requestDataNumForm.value.yesterdayIssueOrderNum = queryData.data;
  }

  //----完成数量----
  //今日
  async function queryTodayFinishOrderNum() {
    let param = {
      startTime: dateForm.value.todayDate,
      endTime: dateForm.value.todayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryFinishOrder(param);
    requestDataNumForm.value.todayFinishOrderNum = queryData.data;
  }
  //昨日
  async function queryYesterdayFinishOrderNum() {
    let param = {
      startTime: dateForm.value.yesterdayDate,
      endTime: dateForm.value.yesterdayDate,
    };
    let queryData = await produceInstructOrderStatsApi.queryFinishOrder(param);
    requestDataNumForm.value.yesterdayFinishOrderNum = queryData.data;
  }

  //----现有指令单数量----
  async function querycurrentOrderNum() {
    let param = {};
    let queryData = await produceInstructOrderStatsApi.queryPlanOrder(param);
    requestDataNumForm.value.currentOrderNum = queryData.data;
  }

  //----进行中指令单数量----
  async function queryDoingOrderNum() {
    let param = {};
    let queryData = await produceInstructOrderStatsApi.queryDoingOrder(param);
    requestDataNumForm.value.doingOrderNum = queryData.data;
  }

  //----逾期指令单数量----
  async function queryUnfinishedOrderNum() {
    let param = {};
    let queryData = await produceInstructOrderStatsApi.queryOverTimeOrder(param);
    requestDataNumForm.value.unfinishedOrderNum = queryData.data;
  }

  //---------------------------------------查询echart数据-----------------------------------------------------
  //定义echart数据
  const echartDataForm = ref({
    lineChartData: {}, //折线图数据
    pieChartData: {}, //饼图数据
  });

  //查询折线图数据
  async function queryLineEchartData() {
    try {
      const params = {
        startTime: dateForm.value.thirtyDaysAgo,
        endTime: dateForm.value.todayDate,
      };
      const res = await produceInstructOrderStatsApi.queryOrderChange(params);
      echartDataForm.value.lineChartData = res.data;
    } catch (error) {
      smartSentry.captureError(error);
    }
  }

  // 查询饼图数据
  async function queryPieChartData() {
    try {
      const res = await produceInstructOrderStatsApi.queryOrderPieChart({});
      echartDataForm.value.pieChartData = res.data;
    } catch (error) {
      smartSentry.captureError(error);
    }
  }

  onMounted(() => {
    getDateData(); //获取日期数据
    queryOrderNum(); //今日新单数量
    queryYesterdayOrderNum(); //昨日工单数量
    queryTodayIssueOrderNum(); //今日下达数量
    queryYesterdayIssueOrderNum(); //昨日下达数量
    queryTodayFinishOrderNum(); //今日完成数量
    queryYesterdayFinishOrderNum(); //昨日完成数量
    querycurrentOrderNum(); //现有指令单数量
    queryDoingOrderNum(); //进行中指令单数量
    queryUnfinishedOrderNum(); //逾期指令单数量
    queryLineEchartData(); //查询折线图数据
    queryPieChartData(); //查询饼图数据
  });
</script>
<style scoped lang="less">
  .target-card {
    height: 170px;
    background-color: #fff;
    padding: 28px;

    .title {
      font-size: 16px;
      color: #303133;
      margin-bottom: 4px;
    }

    .time {
      font-size: 16px;
      color: #9b9b9b;
      margin-bottom: 4px;
    }

    .now {
      font-size: 16px;
      color: #5b5d60;
    }

    .value {
      span {
        font-size: 50px;
        color: #303133;
        font-weight: 500;
      }
    }
  }
</style>

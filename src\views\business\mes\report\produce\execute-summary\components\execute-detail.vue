<template>
  <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
    <template #bodyCell="{ text, record, column }">
      <template v-if="column.dataIndex === 'unCutNum'">
        <span v-if="record.num - record.cutNum <= 0" style="color: green">
          +{{ -(record.num - record.cutNum) }}
          <span v-if="record.num - record.cutNum < 0" style="color: green">超裁</span>
        </span>
        <span v-else style="color: red"> {{ -(record.num - record.cutNum) }} 欠裁 </span>
      </template>
      <template v-else-if="column.dataIndex === 'unFinishNum'">
        <span v-if="record.num - record.finishNum <= 0" style="color: green"> +{{ -(record.num - record.finishNum) }} </span>
        <span v-else style="color: red">
          {{ -(record.num - record.finishNum) }}
        </span>
      </template>
      <template v-else-if="column.dataIndex === 'percent'">
        <a-progress :percent="((record.finishNum / record.num) * 100).toFixed(0)" :size="6" />
      </template>
    </template>
  </a-table>
</template>
<script setup>
  import { onMounted, ref } from 'vue';
  import { produceReportApi } from '/@/api/business/mes/produce/produce-report-api.js';

  const tableLoading = ref(false);
  const tableData = ref([]);
  const props = defineProps({
    data: Array,
    id: Number,
  });

  onMounted(() => {
    queryData();
  });

  const columns = ref([
    {
      title: '进度',
      dataIndex: 'percent',
      ellipsis: true,
      width: '50px',
      align: 'center',
    },
    {
      title: '颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '尺寸',
      dataIndex: 'size',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '下达数量',
      dataIndex: 'num',
      ellipsis: true,
      width: 50,
      align: 'center',
    },

    {
      title: '已裁剪数量',
      dataIndex: 'cutNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '未裁剪数量',
      dataIndex: 'unCutNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '完成数量',
      dataIndex: 'finishNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '未完成数',
      dataIndex: 'unFinishNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
  ]);

  async function queryData() {
    tableLoading.value = true;
    let executeDetail = await produceReportApi.queryExecuteDetail(props.id);
    tableData.value = executeDetail.data;
    tableLoading.value = false;
  }
</script>

<style scoped lang="less"></style>

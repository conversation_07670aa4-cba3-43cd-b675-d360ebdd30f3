<template>
  <a-modal
    :open="visible"
    width="80%"
    :title="'BOM ' + (form.id ? '编辑' : '新增')"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <a-card size="small" :bordered="false">
      <!-- BOM详情表单 -->
      <a-form ref="formRef" :model="form" :rules="formRules" :loadding="loading">
        <a-row style="display: flex; margin-bottom: 15px">
          <a-button type="primary" @click="showClothesModal()" style="width: 10%" :icon="h(SearchOutlined)">选择物料</a-button>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="版本号" name="versionNumber" style="margin-left: 25px">
              <a-input-number min="1" style="width: 90%" v-model:value="form.versionNumber" placeholder="默认版本号为1.0" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="BOM编号" name="bomNumber">
              <a-input style="width: 90%" v-model:value="form.bomNumber" placeholder="请输入BOM编号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="BOM名称" name="bomName">
              <a-input style="width: 90%" v-model:value="form.bomName" placeholder="请输入BOM名称" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="物料编号" name="itemNumber">
              <a-input style="width: 90%" v-model:value="form.itemNumber" placeholder="请输入物料编号" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="物料名称" name="itemName">
              <a-input style="width: 90%" v-model:value="form.itemName" placeholder="请输入物料名称" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="规格型号" name="itemModel">
              <a-input style="width: 90%" v-model:value="form.itemModel" placeholder="请输入规格型号" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="物料单位" name="itemUnitName">
              <a-input style="width: 90%" v-model:value="form.itemUnitName" placeholder="请输入物料单位" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="物料属性" name="itemAttribute">
              <a-radio-group v-model:value="form.itemAttribute" button-style="solid" disabled style="width: 80%">
                <a-radio :value="'0'">面料</a-radio>
                <a-radio :value="'2'">成衣</a-radio>
                <a-radio :value="'1'">其他</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="物料类型" name="itemCategory" placeholder="">
              <a-radio-group v-model:value="form.itemCategory" button-style="solid" disabled style="width: 80%">
                <a-radio :value="'0'">半成品</a-radio>
                <a-radio :value="'1'">成品</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="8">
            <a-form-item label="停用标识" name="enableFlag">
              <a-radio-group v-model:value="form.enableFlag" button-style="solid" disabled style="width: 80%">
                <a-radio :value="false">启用</a-radio>
                <a-radio :value="true">停用</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- BOM明细表格 -->
      <a-button type="primary" @click="showItemModal()">添加</a-button>
      <a-table :columns="columns" :data-source="dataSource" :pagination="false" :loadding="loading" style="width: 100%" :scroll="{ x: 1500 }">
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'itemName'">
            <a-input placeholder="请输入物料名称" v-model:value="record.itemName" disabled />
          </template>
          <template v-else-if="column.dataIndex === 'itemNumber'">
            <a-input placeholder="请输入物料编号" v-model:value="record.itemNumber" disabled />
          </template>
          <template v-else-if="column.dataIndex === 'itemModel'">
            <a-input placeholder="请输入物料规格型号" v-model:value="record.itemModel" disabled />
          </template>
          <template v-else-if="column.dataIndex === 'itemCategory'">
            <a-tag v-if="text" :color="ITEM_CATEGORY_ENUM.getEnum(text).color">
              {{ ITEM_CATEGORY_ENUM.getEnum(text).desc }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'itemUnitName'">
            <a-input placeholder="请输入物料类型" v-model:value="record.itemUnitName" disabled />
          </template>
          <template v-else-if="column.dataIndex === 'itemAttribute'">
            <a-tag v-if="text" :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">
              {{ ITEM_ATTRIBUTE_ENUM.getEnum(text).desc }}
            </a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'loss'">
            <a-input-number
              placeholder="损失率"
              v-model:value="record.loss"
              precision="4"
              addon-after="%"
              min="0"
              max="100"
            />
          </template>
          <template v-else-if="column.dataIndex === 'dosage'">
                <a-input-number v-model:value="record.dosage" min="0" precision="4" max="100" />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button @click="onDelete(record)" danger type="link">删除</a-button>
            </div>
          </template>
        </template>
      </a-table>

      <item-table-select-modal
        ref="itemModal"
        @select-data="selectItemData"
        :attribute-list="[ITEM_ATTRIBUTE_ENUM.OTHER.value, ITEM_ATTRIBUTE_ENUM.CLOTH.value]"
        selection-type="checkbox"
      />
      <item-table-select-modal
        ref="clothesModal"
        @select-data="selectItemData"
        :attribute-list="[ITEM_ATTRIBUTE_ENUM.CLOTHES.value]"
        selection-type="radio"
      />
    </a-card>
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref, h } from 'vue';
import { bomApi } from '/@/api/business/mes/bom/bom-api';
import { message } from 'ant-design-vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { SearchOutlined } from '@ant-design/icons-vue';
import ItemTableSelectModal from '/@/components/business/mes/item/item-table-select-modal/index.vue';
import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
import { SmartLoading } from '/@/components/framework/smart-loading';

// 定义emit
const emit = defineEmits(['reload-list']);

// 控制模态框显示
const visible = ref(false);
const loading = ref(false);

// 表单数据
const formDefault = {
  id: '', //主键
  versionNumber: 1, //版本号  默认为1
  bomNumber: '', //BOM编号
  bomName: '', //BOM名称
  itemNumber: '', //物料编号
  itemId: '', //物料ID
  itemName: '', //物料名称
  itemCategory: '', //物料类型
  itemUnitId: '', //物料单位
  itemTypeId: '', //物料类型ID
  itemTypeName: '', //物料类型名称
  itemUnitName: '', //物料单位名称
  itemAttribute: '', //物料属性
  itemModel: '', //物料规格型号
  remark: '', //备注
  enableFlag: false, //启用标识
  bomDetailList: [], //BOM明细列表
};

let form = reactive({ ...formDefault });

// 表单规则
const formRules = {
  bomNumber: [{ required: true, message: 'BOM编号 必填' }],
  bomName: [{ required: true, message: 'BOM名称 必填' }],
  itemNumber: [{ required: true, message: '物料编号 必填' }],
  itemId: [{ required: true, message: '物料ID 必填' }],
  itemName: [{ required: true, message: '物料名称 必填' }],
  itemCategory: [{ required: true, message: '物料类型 必填' }],
  itemUnitName: [{ required: true, message: '物料单位 必填' }],
  itemUnitId: [{ required: true, message: '物料单位ID 必填' }],
  itemTypeId: [{ required: true, message: '物料类型ID 必填' }],
  itemAttribute: [{ required: true, message: '物料属性 必填' }],
  itemModel: [{ required: true, message: '物料规格型号 必填' }],
};

// 表格列定义
const columns = [
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    key: 'itemNumber',
    width: 150,
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'itemName',
    key: 'itemName',
    width: 150,
    align: 'center',
  },
  {
    title: '物料规格型号',
    dataIndex: 'itemModel',
    key: 'itemModel',
    width: 150,
    align: 'center',
  },
  {
    title: '用量',
    dataIndex: 'dosage',
    key: 'dosage',
    width: 150,
    align: 'center',
  },
  {
    title: '单位损失',
    dataIndex: 'loss',
    key: 'loss',
    width: 150,
    align: 'center',
  },
  {
    title: '物料单位',
    dataIndex: 'itemUnitName',
    key: 'itemUnitName',
    width: 150,
    align: 'center',
  },
  {
    title: '物料类型',
    dataIndex: 'itemCategory',
    key: 'itemCategory',
    width: 150,
    align: 'center',
  },
  {
    title: '物料属性',
    dataIndex: 'itemAttribute',
    key: 'itemAttribute',
    width: 150,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
  },
];

const dataSource = ref([]);

// 打开模态框
const showForm = async (id) => {
  visible.value = true;
  if (id) {
    await loadData(id);
  } else {
    onReset();
    await getClientNo();
  }
};

// 加载数据
const loadData = async (id) => {
  SmartLoading.show();
  try {
    let queryResult = await bomApi.queryById(id);
    Object.assign(form, queryResult.data);
    console.log(form,form.itemUnitName,queryResult.data);
    form.bomDetailList.forEach((item) => {
      item.loss = (item.loss * 100);
    });
    dataSource.value = form.bomDetailList;
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
};

// 关闭模态框
const handleCancel = () => {
  visible.value = false;
  Object.assign(form, formDefault);
  dataSource.value = [];
};

const formRef = ref(null);
// 提交表单
const onSubmit = async () => {
  SmartLoading.show();
  try {
    await formRef.value.validate();
    form.bomDetailList = dataSource.value;
    form.bomDetailList.forEach((item) => {
      item.loss = (item.loss / 100);
  });
    if (form.id) {
      await bomApi.update(form);
      message.success('编辑成功');
    } else {
      await bomApi.add(form);
      message.success('添加成功');
    }
    handleCancel();
    emit('reload-list');
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
};

// 重置表单
const onReset = () => {
  form = reactive({ ...formDefault });
  dataSource.value = [];
};

// 获取BOM编号
const getClientNo = async () => {
  try {
    const res = await bomApi.getBOMNo();
    form.bomNumber = res.data;
  } catch (err) {
    smartSentry.captureError(err);
  }
};

// 模态框相关
const modalType = {
  Item: 'item',
  Clothes: 'clothes',
};

// 存储当前行record
const currentModalType = ref(null);
const currentRecord = ref(null);
const itemModal = ref({});
const clothesModal = ref({});

function showItemModal(record) {
  currentRecord.value = record || null;
  currentModalType.value = modalType.Item;
  itemModal.value.showModal();
}

function showClothesModal() {
  currentModalType.value = modalType.Clothes;
  clothesModal.value.showModal();
}

function selectItemData(selectedItems) {
  if (!selectedItems.length) return;

  if (currentModalType.value === modalType.Clothes) {
    // 如果是选择成衣物料（单选）
    const data = selectedItems[0];
    Object.assign(form, {
      itemId: data.id,
      itemNumber: data.number,
      itemName: data.name,
      itemModel: data.model,
      itemUnitId: data.unitId,
      itemUnitName: data.unitName,
      itemCategory: data.category,  
      itemAttribute: data.attribute,
      itemTypeId: data.typeId,
    });
  } else {
    // 如果是选择BOM明细物料（多选）
    if (!currentRecord.value) {
      // 点击顶部添加按钮
      selectedItems.forEach((data) => {
        const newData = {
          id: dataSource.value.length + 1,
          itemId: data.id,
          itemNumber: data.number,
          itemName: data.name,
          itemModel: data.model,
          itemUnitId: data.unitId,
          itemUnitName: data.unitName,
          itemCategory: data.category,
          itemAttribute: data.attribute,
          dosage: 0.0,
          loss: 0,
          itemTypeId: 0,
          remark: '',
        };
        dataSource.value.push(newData);
      });
    } else {
      // 点击行内选择按钮
      const currentIndex = dataSource.value.findIndex((item) => item.id === currentRecord.value.id);

      selectedItems.forEach((data, index) => {
        if (index === 0) {
          // 第一条数据更新到当前选中行
          Object.assign(currentRecord.value, {
            itemId: data.id,
            itemNumber: data.number,
            itemName: data.name,
            itemModel: data.model,
            itemUnitId: data.unitId,
            itemUnitName: data.unitName,
            itemCategory: data.category,
            itemAttribute: data.attribute,
            dosage: currentRecord.value.dosage || 0.0,
            loss: currentRecord.value.loss || 0,
            itemTypeId: 0,
            remark: currentRecord.value.remark || '',
          });
        } else {
          // 后续数据插入为新行
          const newData = {
            id: dataSource.value.length + 1,
            itemId: data.id,
            itemNumber: data.number,
            itemName: data.name,
            itemModel: data.model,
            itemUnitId: data.unitId,
            itemUnitName: data.unitName,
            itemCategory: data.category,
            itemAttribute: data.attribute,
            dosage: 0.0,
            loss: 0,
            itemTypeId: 0,
            remark: '',
          };
          dataSource.value.splice(currentIndex + index, 0, newData);
        }
      });
    }

    // 重新排序 id
    dataSource.value.forEach((item, index) => {
      item.id = index + 1;
    });
  }
}

// 删除行
const onDelete = (record) => {
  const index = dataSource.value.indexOf(record);
  if (index !== -1) {
    dataSource.value.splice(index, 1);
  }
};

// 暴露方法给父组件
defineExpose({
  showForm
});
</script>
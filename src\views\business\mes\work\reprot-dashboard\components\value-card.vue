<template>
  <div class="data-card" :style="{backgroundColor: bgColor,borderTopColor: topColor}">
    <div class="title">{{ title}}</div>
    <div>
      <div class="value">{{ value}} <span class="unit">{{ unit}}</span></div>
    </div>
    <div class="desc">{{desc}}</div>
  </div>
</template>
<script setup>
import dayjs from "dayjs";

const props = defineProps({
  value: {
    default: 0,
  },
  unit: {
    default: '',
  },
  title: {
    default: '暂无',
  },
  desc:{
    default: '更新于：'+dayjs().format('YYYY-MM-DD HH:mm:ss'),
  },
  bgColor: {
    default: '#f2f5fa',
  },
  topColor: {
    default: '#317ef2',
  },
});
</script>
<style scoped lang="less">
.data-card{
  padding: 10px;
  border-radius: 3px;
  width: 100%;
  border-top: #00a0e9 3px solid;
  cursor: pointer;

  .title{
    font-weight: bold;
    color: #465769;
    font-size: 16px;
  }

  .value{
    font-size: 34px;

    font-weight: bold;

    color: #303133;
  }

  .unit{
    font-size: 14px;
    color: #606266;
    font-weight: normal;
  }

  .desc{
    color: #909399;
  }
}
</style>

<!--
  * 裁床单比例添加
-->
<template>
  <a-row>
    <span class="partstitle"> 添加部位: </span>
    <part-select v-model:value="selectParts" :produceInstructOrderId="props.produceInstructOrderId" width="300px" />
  </a-row>
  <a-row>
    <a-col :span="8">
      <div class="box">
        <div class="titlebox">设置尺码比例:</div>
        <a-table size="small" :dataSource="sizeRow" :columns="sizeColumns" :pagination="false" style="margin-top: 20px">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'proportion'">
              <a-input-number :min="0" style="width: 100px" v-model:value="record.proportion" />
            </template>
          </template>
        </a-table>
      </div>
    </a-col>
    <a-col :span="16">
      <div class="box">
        <div class="titlebox">
          点击颜色增加一行数据:
          <span class="autoFill">
            <a-switch v-model:checked="autoFill" />
            自动填充
          </span>
        </div>
        <div class="color-buttons">
          <a-tag color="processing" class="color-tag" v-for="item in props.colorList" :key="item.value" @click="addColorRow(item)">
            {{ item.value }}
          </a-tag>
        </div>
        <a-table size="small" :dataSource="colorRow" :columns="colorColumns" :pagination="false">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'layers'">
              <a-input-number :min="0" style="width: 150px" v-model:value="record.layers" @change="(value) => handleLayerChange(index, value)" />
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-button type="link" danger @click="deleteColorRow(record)"> 删除 </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, watch, defineComponent } from 'vue';
  import { message } from 'ant-design-vue';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';
  //----------------------------------父子通信----------------------------------
  const props = defineProps({
    sizeList: {
      type: Array,
      default: () => [],
    },
    colorList: {
      type: Array,
      default: () => [],
    },
    produceInstructOrderId: {
      type: [String, Number],
      default: undefined,
    },
  });
  //----------------------------------添加部位事件---------------------------------
  const selectParts = ref([]);

  //----------------------------------尺寸表格数据----------------------------------
  const sizeRow = ref([]); //尺码比例数据
  const sizeColumns = [
    {
      title: '尺码',
      dataIndex: 'size',
      align: 'center',
    },
    {
      title: '比例',
      dataIndex: 'proportion',
      align: 'center',
    },
  ];
  // 监听输入的尺码列表并转换为表格数据
  watch(
    () => props.sizeList,
    (newVal) => {
      sizeRow.value = newVal.map((size) => ({
        size,
        proportion: 1,
        key: size,
      }));
    },
    { immediate: true, deep: true }
  );
  //-----------------------------------颜色表格数据-----------------------------------
  const colorRow = ref([]); //颜色表格数据
  const colorColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      width: 80,
    },
    {
      title: '颜色',
      dataIndex: 'color',
      align: 'center',
    },
    {
      title: '层数',
      dataIndex: 'layers',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      align: 'center',
      width: 100,
    },
  ];
  //监听床哦来的颜色数据
  watch(
    () => props.colorList,
    (newVal) => {
      colorRow.value = newVal.map((item, index) => ({
        key: `${item.value}-${Date.now()}`,
        index: index + 1,
        color: item.value,
        layers: 0,
      }));
    },
    { immediate: true, deep: true }
  );
  //添加颜色方法
  const addColorRow = (item) => {
    colorRow.value.push({
      key: `${item.value}-${Date.now()}`,
      index: colorRow.value.length + 1,
      color: item.value,
      layers: autoFill.value && colorRow.value.length > 0 ? colorRow.value[0].layers : 0,
    });
  };
  // 删除颜色行方法
  const deleteColorRow = (record) => {
    colorRow.value = colorRow.value.filter((item) => item.key !== record.key);
    // 重新计算序号
    colorRow.value = colorRow.value.map((item, index) => ({
      ...item,
      index: index + 1,
    }));
  };
  //----------------------------------自动填充----------------------------------
  const autoFill = ref(false);
  // 处理任意行数值变化
  const handleLayerChange = (index, value) => {
    if (autoFill.value && colorRow.value.length > index + 1) {
      // 如果开启了自动填充，将当前行的值填充到后面所有行
      for (let i = index + 1; i < colorRow.value.length; i++) {
        colorRow.value[i].layers = value;
      }
    }
  };
  //----------------------------------清除表单数据-------------------------------------
  //清除表单数据
  const resetDate = () => {
    colorRow.value = [];
    sizeRow.value = [];
    selectParts.value = [];
  };
  //初始化
  const initData = () => {
    colorRow.value = [];
    selectParts.value = [];
    colorRow.value = props.colorList.map((item, index) => ({
      key: `${item.value}-${Date.now()}`,
      index: index + 1,
      color: item.value,
      layers: 0,
    }));
    sizeRow.value = [];
    sizeRow.value = props.sizeList.map((size) => ({
      size,
      proportion: 1,
      key: size,
    }));
    autoFill.value = false;
  };
  //暴露方法和数据给父组建
  defineExpose({
    colorRow,
    sizeRow,
    selectParts,
    resetDate,
    initData,
  });
</script>
<style>
  .partstitle {
    font-size: 14px;
    margin: 5px 10px;
  }
  .box {
    margin: 15px;
  }
  .titlebox {
    width: 100%;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
    overflow: hidden;
  }
  .autoFill {
    margin-right: 40px;
    font-size: 14px;
    float: right;
  }
  .color-buttons {
    margin: 8px;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .color-tag {
    height: 24px;
    font-size: 14px;
    line-height: 24px;
    transition: all 0.3s;
    cursor: pointer;
  }
  .color-tag:hover {
    opacity: 0.8;
  }
</style>

<!--
  * 仓库
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:13:18
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
                 <a-button @click="showQuickAddForm" type="primary" size="small"  style="background-color: #4abe4a">
                    <template #icon>
                        <BankOutlined />
                    </template>
                    快速建仓
                </a-button>
                <a-button @click="confirmBatchDelete" type="danger" size="small" :disabled="selectedRowKeyList.length == 0">
                    <template #icon>
                        <DeleteOutlined />
                    </template>
                    批量删除
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
                :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'number'">
                    <a @click="showForm(record)">{{ text }}</a>
                </template>
                <template v-if="column.dataIndex === 'openLocationFlag'">
                    {{ record.openLocationFlag ? '是' : '否' }}
                </template>
                <template v-if="column.dataIndex === 'allowNegativeFlag'">
                    {{ record.allowNegativeFlag ? '是' : '否' }}
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>

        <StkWarehouseForm  ref="formRef" @reloadList="queryData"/>
        <StkWarehouseQuickAddModal ref="quickAddModalRef" @reloadList="queryData"/>
    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { stkWarehouseApi } from '/@/api/business/mes/stock/stk-warehouse-api.js';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import StkWarehouseForm from './stk-warehouse-form.vue';
    import StkWarehouseQuickAddModal from './stk-warehouse-quick-add-modal.vue';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        // {
        //     title: '主键',
        //     dataIndex: 'id',
        //     ellipsis: true,
        // },
        // {
        //     title: '创建时间',
        //     dataIndex: 'createTime',
        //     ellipsis: true,
        // },
        // {
        //     title: '创建人',
        //     dataIndex: 'createBy',
        //     ellipsis: true,
        // },
        // {
        //     title: '更新时间',
        //     dataIndex: 'updateTime',
        //     ellipsis: true,
        // },
        // {
        //     title: '更新人',
        //     dataIndex: 'updateBy',
        //     ellipsis: true,
        // },
        // {
        //     title: '删除标识;0未删除，1删除',
        //     dataIndex: 'deletedFlag',
        //     ellipsis: true,
        // },
      
        {
            title: '仓库编号',
            dataIndex: 'number',
            ellipsis: true,
        },
        {
            title: '仓库名称',
            dataIndex: 'name',
            ellipsis: true,
        },
        {
            title: '仓库地址',
            dataIndex: 'address',
            ellipsis: true,
        },
        {
            title: '是否启用仓位管理',
            dataIndex: 'openLocationFlag',
            ellipsis: true,
        }, 
        {
            title: '是否允许负库存',
            dataIndex: 'allowNegativeFlag',
            ellipsis: true,
        },
         {
            title: '备注',
            dataIndex: 'remark',
            ellipsis: true,
        },

        // {
        //     title: '允许锁库;0否 1是（保留字段）',
        //     dataIndex: 'allowLockFlag',
        //     ellipsis: true,
        // },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 90,
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        queryKey: undefined, //关键字查询
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await stkWarehouseApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }


    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 快速建仓 ----------------------------
    const quickAddModalRef = ref();
        function showQuickAddForm() {
        quickAddModalRef.value.show();
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await stkWarehouseApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await stkWarehouseApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
</script>

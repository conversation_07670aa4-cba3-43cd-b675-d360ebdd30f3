<template>
  <a-modal v-model:open="visible" :width="1400" title="选择生产指令单" @cancel="closeModal" @ok="onSelectItem">
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="params.queryKey" placeholder="关键字" />
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <a-table
      :row-selection="{ selectedRowKeys: [selectedRowKey], onChange: onSelectChange, type: 'radio' }"
      :loading="tableLoading"
      size="small"
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      bordered
      rowKey="id"
      :scroll="{ y: 300 }"
    >
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'imgUrl'">
          <div class="image-container">
            <file-preview :fileList="(record.imgUrl && record.imgUrl.length > 0) > 0 ? [record.imgUrl[0]] : []" type="picture" :width="70" />
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'produceStatus'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'produceNum'">
          <div :style="{ color: text ? 'black' : '#909399', fontStyle: text ? 'normal' : 'italic' }">
            {{ text ? text : '生产未下达' }}
          </div>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="params.pageSize"
        v-model:current="params.pageNum"
        v-model:pageSize="params.pageSize"
        :total="total"
        @change="queryProduceOrderInstructList"
        @showSizeChange="queryProduceOrderInstructList"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
  import { reactive, ref } from 'vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const.js';
  import { itemApi } from '/@/api/business/mes/item/item-api.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api.js';
  import { message } from 'ant-design-vue';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import FilePreview from '/@/components/support/file-preview/index.vue';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';

  const emits = defineEmits(['selectData']);
  defineExpose({
    showModal,
  });
  const props = defineProps({});

  const visible = ref(false);
  const selectedRowKey = ref(null);

  const tableLoading = ref(false);
  const total = ref();

  let defaultParams = {
    pageNum: 1,
    pageSize: PAGE_SIZE,
    queryKey: undefined,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  const params = reactive({ ...defaultParams });

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      width: 90,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      ellipsis: true,
    },
    {
      title: '单据编号',
      dataIndex: 'instructNumber',
      ellipsis: true,
    },

    {
      title: '指令单名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      ellipsis: true,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: '生产数量',
      dataIndex: 'produceNum',
      ellipsis: true,
    },
    {
      title: '生产状态',
      dataIndex: 'produceStatus',
      ellipsis: true,
    },
    {
      title: '交货日期',
      dataIndex: 'deliverTime',
      ellipsis: true,
    },
    {
      title: '下达日期',
      dataIndex: 'issuedTime',
      ellipsis: true,
    },
  ]);

  let tableData = ref([]);

  async function showModal() {
    selectedRowKey.value = null;
    visible.value = true;
    onSearch();
  }

  function closeModal() {
    Object.assign(params, defaultParams);
    selectedRowKey.value = null;
    visible.value = false;
  }

  function onSearch() {
    params.pageNum = 1;
    queryProduceOrderInstructList();
  }

  function reset() {
    Object.assign(params, defaultParams);
    queryProduceOrderInstructList();
  }

  async function queryProduceOrderInstructList() {
    tableLoading.value = true;
    try {
      let res = await produceInstructOrderApi.queryPage(params);
      tableData.value = res.data.list;
      total.value = res.data.total;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      tableLoading.value = false;
    }
  }

  function onSelectChange(selectedRowKeys) {
    if (selectedRowKeys.length > 0) {
      selectedRowKey.value = selectedRowKeys[0];
    } else {
      selectedRowKey.value = null;
    }
  }

  function onSelectItem() {
    if (!selectedRowKey.value) {
      message.warning('请选择指令单');
      return;
    }
    const selectedItem = tableData.value.find((item) => item.id === selectedRowKey.value);
    emits('selectData', selectedItem);
    closeModal();
  }
</script>

<style scoped>
  .image-container {
    width: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-container :deep(img) {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
</style>

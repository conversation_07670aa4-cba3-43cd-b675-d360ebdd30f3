<!--
  * 物料BOM维护
  *
  * @Author:    cyz
  * @Date:      2024-07-09 10:25:22
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="请输入bom名称" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm()" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading"
            :pagination="false" :scroll="{x:'1200px'}">
            <template #bodyCell="{record, column,text }">
              <!--       物料分类       -->
              <template v-if="column.dataIndex==='itemTypeId'">
                {{text}}
              </template>
              <template v-else-if="column.dataIndex === 'bomNumber'">
                <a-button type="link" @click="showForm(record.id)">{{ text }}</a-button>
              </template>
              <!--     物料类型         -->
              <template v-if="column.dataIndex==='itemCategory'">
                <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
              </template>
              <!--      物料属性        -->
              <template v-if="column.dataIndex==='itemAttribute'">
                <a-tag :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">{{ ITEM_ATTRIBUTE_ENUM.getEnum(text).label }}</a-tag>
              </template>
              <!--      操作        -->
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record.id)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination showSizeChanger showQuickJumper show-less-items :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize" v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize" :total="total" @change="queryData" @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`" />
        </div>

        <BomDetailForm ref="formRef" @reloadList="queryData" />

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted ,watch} from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { bomApi } from '/@/api/business/mes/bom/bom-api.js';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import BomDetailForm from './bom-detail-form.vue';
    import { useRouter , useRoute } from 'vue-router';
    import {ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM} from "/@/constants/business/mes/item/item-const.js";
    import {findById} from "/@/utils/Tree-util.js"
    import {itemTypeApi} from "/@/api/business/mes/item/item-type-api.js";
    import {itemApi} from '/@/api/business/mes/item/item-api';

    // ---------------------------- 表格列 ----------------------------



    const columns = ref([
        {
            title: '版本号',
            dataIndex: 'versionNumber',
            ellipsis: true,
            align:"center",
          width: 70
        },
        {
            title: 'BOM编号',
            dataIndex: 'bomNumber',
            ellipsis: true,
          align:"center",
          width: 150
        },
        {
            title: 'BOM名称',
            dataIndex: 'bomName',
            ellipsis: true,
          align:"center",
          width: 200
        },
        {
            title: '物料编号',
            dataIndex: 'itemNumber',
            ellipsis: true,
          align:"center",
          width: 150
        },
        {
            title: '物料名称',
            dataIndex: 'itemName',
            ellipsis: true,
          align:"center",
          width: 150
        },
        {
            title: '物料分类',
            dataIndex: 'itemType',
            ellipsis: true,
          align:"center",
          width: 90
        },
        {
            title: '物料类型',
            dataIndex: 'itemCategory',
            ellipsis: true,
          align:"center",
          width: 90
        },
        {
            title: '物料属性',
            dataIndex: 'itemAttribute',
            ellipsis: true,
          align:"center",
          width: 150
        },
        {
            title: '物料规格型号',
            dataIndex: 'itemModel',
            ellipsis: true,
          align:"center",
          width: 250
        },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 90,
          align:"center"
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        queryKey: undefined, //bom名称
        pageNum: 1,
        pageSize: 10,
      sortItemList: [{column: 'create_time', isAsc: false}]
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);


    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await bomApi.queryPage(queryForm);
            //  添加物料分类属性
            let categoryTree = await itemTypeApi.queryCategoryTree({});
            queryResult.data.list.forEach(item=>{
              let typeId=item.itemTypeId
              // let itemType =findById(categoryTree.data,typeId).fullName
              let itemType =findById(categoryTree.data,typeId)
              if(itemType){
                item.itemType=itemType.name
              }else{
                item.itemType="查询不到物料分类"
              }
            })
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
    onMounted(()=>{
        queryData();
    });


// ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();
    function showForm(id) {
      formRef.value.showForm(id);
    }
    

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
    Modal.confirm({
        title: '提示',
        content: '确定要删除选吗?',
        okText: '删除',
        okType: 'danger',
        onOk() {
            requestDelete(data);
        },
        cancelText: '取消',
        onCancel() { },
    });
}

//请求删除
async function requestDelete(data) {
    SmartLoading.show();
    try {
        await bomApi.delete(data.id);
        message.success('删除成功');
        queryData();
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        SmartLoading.hide();
    }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
// const selectedRowKeyList = ref([]);

// function onSelectChange(selectedRowKeys) {
//     selectedRowKeyList.value = selectedRowKeys;
// }

// 批量删除
// function confirmBatchDelete() {
//     Modal.confirm({
//         title: '提示',
//         content: '确定要批量删除这些数据吗?',
//         okText: '删除',
//         okType: 'danger',
//         onOk() {
//             requestBatchDelete();
//         },
//         cancelText: '取消',
//         onCancel() { },
//     });
// }

//请求批量删除
// async function requestBatchDelete() {
//     try {
//         SmartLoading.show();
//         await bomApi.delete(selectedRowKeyList.value);
//         message.success('删除成功');
//         queryData();
//     } catch (e) {
//         smartSentry.captureError(e);
//     } finally {
//         SmartLoading.hide();
//     }
// }
</script>

/**
 * 其他入库单 api 封装
 *
 * @Author:    pxz
 * @Date:      2025-02-08
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkOtherInStockApi = {
    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/stkOtherInStock/queryPage', param);
    },

    /**
     * 添加  <AUTHOR>
     */
    add: (param) => {
        return postRequest('/stkOtherInStock/add', param);
    },

    /**
     * 修改  <AUTHOR>
     */
    update: (param) => {
        return postRequest('/stkOtherInStock/update', param);
    },
    /**
     * 根据id查询其他入库单  <AUTHOR>
     */
    getById: (id) => {
        return getRequest(`/stkOtherInStock/byId?id=${id}`);
    },

    /**
     * 修改单据状态  <AUTHOR>
     */
    updateStatus: (id) => {
        return getRequest(`/stkOtherInStock/status?id=${id}`);
    },
    /**
     * 根据单据编号查询  <AUTHOR>
     */
    deleteById: (id) => {
        return getRequest(`/stkOtherInStock/delete?id=${id}`);
    }
};
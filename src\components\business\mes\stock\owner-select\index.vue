<!--
  * 货主下拉选择组件
  * 返回所选对应货主列表
  * @Author:    wxx
  * @Date:      2025-02-13
  * @Copyright  zscbdic
-->

<template>
  <a-col :span="6">
    <a-form-item label="货主类型" class="smart-query-form-item">
      <a-select v-model:value="ownerType" placeholder="请先选择货主类型" :style="{ width: width }" :allowClear="true" @change="handleOwnerTypeChange">
        <a-select-option v-for="type in OWNER_TYPE_ENUM.getOptions()" :key="type.value" :value="type.value">
          {{ type.desc }}
        </a-select-option>
      </a-select>
    </a-form-item>
  </a-col>
  <a-col :span="6">
    <a-form-item label="货主名称" class="smart-query-form-item">
      <a-select
        v-model:value="selectedOwnerId"
        placeholder="请选择货主名称"
        :style="{ width: width }"
        :allowClear="true"
        :showSearch="true"
        :filterOption="filterOption"
      >
        <a-select-option v-for="owner in ownerList" :key="owner.id" :value="owner.id">
          {{ owner.name }}
        </a-select-option>
      </a-select>
    </a-form-item>
  </a-col>
</template>

<script setup>
  import { ref, watch, defineEmits } from 'vue';
  import { message } from 'ant-design-vue';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api';
  import { departmentApi } from '/@/api/system/department-api';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { customerApi } from '/@/api/business/mes/base/customer-api';
  import { OWNER_TYPE_ENUM } from '/@/constants/business/mes/stock/owner-type-const';

  const props = defineProps({
    ownerInfo: {
      type: Object,
      default: () => ({
        type: null,
        id: null,
        name: null,
      }),
    },
    width: {
      type: String,
      default: '90%',
    },
  });
  const emit = defineEmits(['update:ownerInfo', 'change']);

  //货主类型
  const ownerType = ref(null);

  //货主名称列表
  const ownerList = ref([]);

  // 选中的货主 ID
  const selectedOwnerId = ref(null);

  //请求对应货主列表
  async function getOwnerList(type) {
    try {
      let data = null;
      switch (type) {
        case OWNER_TYPE_ENUM.WORKSHOP.value:
          data = (await workshopApi.queryList({})).data;
          break;
        case OWNER_TYPE_ENUM.DEPARTMENT.value:
          data = (await departmentApi.queryAllDepartment()).data;
          break;
        case OWNER_TYPE_ENUM.CUSTOMER.value:
          data = (await customerApi.getCustomerName({})).data;
          break;
        case OWNER_TYPE_ENUM.SUPPLIER.value:
          data = (await supplierApi.queryAll({})).data;
          break;
        default:
          ownerList.value = [];
          return;
      }

      ownerList.value = data.map((item) => ({
        id: item.id || item.departmentId,
        name: item.name,
      }));
    } catch (error) {
      message.error('获取货主列表失败');
      ownerList.value = [];
    }
  }

  //监听货主类型的变化
  watch(ownerType, async (newType) => {
    selectedOwnerId.value = null;
    if (newType) {
      await getOwnerList(newType);
    } else {
      ownerList.value = [];
    }
  });

  // 监听选中的货主 ID 的变化，并向父组件发送事件
  watch(selectedOwnerId, (newId) => {
    const ownerInfo = newId ? { type: ownerType.value, ...ownerList.value.find((owner) => owner.id === newId) } : null;
    emit('update:ownerInfo', ownerInfo);
    emit('change', ownerInfo);
  });

  // 监听父组件的 ownerInfo 变化
  watch(
    () => props.ownerInfo,
    async (newOwnerInfo) => {
      if (newOwnerInfo) {
        ownerType.value = newOwnerInfo.type;
        // 如果 ownerList 还没有加载，或者类型改变了，需要重新加载
        if (!ownerList.value.length || ownerType.value !== newOwnerInfo.type) {
          await getOwnerList(newOwnerInfo.type);
        }
        selectedOwnerId.value = newOwnerInfo.id;
      }
    },
    { immediate: true, deep: true }
  );
  // 货主类型变化时，清空 selectedOwnerId
  function handleOwnerTypeChange() {
    selectedOwnerId.value = null;
  }

  // 货主名称搜索过滤函数
  function filterOption(inputValue, option) {
    const item = ownerList.value.find((i) => i.id === option.value);
    return item?.name?.toLowerCase().includes(inputValue.toLowerCase());
  }

  //重置
  function reset() {
    ownerType.value = null;
    selectedOwnerId.value = null; // 重置 selectedOwnerId
    ownerList.value = []; // 清空 ownerList
  }
  defineExpose({
    reset,
  });
</script>

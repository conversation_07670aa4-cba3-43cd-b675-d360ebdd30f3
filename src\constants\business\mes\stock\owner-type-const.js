/**
 *  货主类型 枚举
 */

export const OWNER_TYPE_ENUM = {
  /**
   * 车间
   */
  WORKSHOP: {
    value: 'workshop',
    desc: '车间',
  },

  /**
   * 部门
   */
  DEPARTMENT: {
    value: 'department',
    desc: '部门',
  },

  /**
   * 供应商
   */
  SUPPLIER: {
    value: 'supplier',
    desc: '供应商',
  },

  /**
   * 客户
   */
  CUSTOMER: {
    value: 'customer',
    desc: '客户',
  },

  // 获取所有选项
  getOptions() {
    return Object.values(this).filter((item) => typeof item === 'object');
  },

  // 根据值获取描述
  getDesc(value) {
    const item = this.getOptions().find((item) => item.value === value);
    return item?.desc || '';
  },
};
export default {
  OWNER_TYPE_ENUM,
};

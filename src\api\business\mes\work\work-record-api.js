/**
 * 报工记录 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-22 20:33:41
 * @Copyright  cjm
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const workRecordApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/workRecord/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/workRecord/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/workRecord/updateProcessById', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/workRecord/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/workRecord/batchDelete', idList);
  },


  changeStatus(status){
     return postRequest('/workRecord/status',status)
  },

  changeNum(param){
      return postRequest('/workRecord/num',param)
  },

};

<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="props.placeholder"
    :options="teamList"
    :style="{ width: width }"
    @change="handleChange"
    :disabled="disabled"
    show-search
   :filter-option="filterOption"
      :dropdown-style="{ maxHeight: '200px', overflow: 'auto' }"
  />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import _ from 'lodash';
import { produceTeamApi } from '/@/api/business/mes/factory/produce-team-api';

const props = defineProps({
  value: {
    type: Number,
    default: undefined,
  },
  placeholder: {
    type: String,
    default: '请选择生产小组',
  },
  width: {
    type: String,
    default: '100%',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:value', 'change']);

const selectedValue = ref(props.value);
const teamList = ref([]);

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 监听父组件传入的 value 变化
watch(
  () => props.value,
  (newVal) => {
    selectedValue.value = newVal;
  }
);

// 查询生产班组列表
async function queryTeamList() {
  try {
    const queryForm = {};
    const res = await produceTeamApi.queryList(queryForm);
    teamList.value = [];
    if (!_.isEmpty(res?.data)) {
      teamList.value = res.data.map((item) => ({
        value: item.id,
        label: item.teamName,
      }));
    }
  } catch (error) {
    message.error('生产小组数据请求失败');
  }
}

// 处理选择变化
function handleChange(value) {
  emit('update:value', value);
  const selectedTeam = teamList.value.find((item) => item.value === value);
  emit('change', {
    teamId: value,
    teamName: selectedTeam?.label,
  });
}

onMounted(() => {
  queryTeamList();
});
</script>

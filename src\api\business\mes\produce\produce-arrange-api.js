/**
 * 生产安排信息 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-22 18:44:38
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const produceArrangeApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/produceArrange/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/produceArrange/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/produceArrange/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/produceArrange/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/produceArrange/batchDelete', idList);
  },
  /**
   * 查询详细生成安排  <AUTHOR>
   */
  queryDetailArrangeInfo: (id) => {
    return getRequest(`/produceArrange/${id}`);
  },
  /**
   * 下拉list  <AUTHOR>
   */
  queryList: (param) => {
    return getRequest('/produceArrange/queryList', param);
  },
};

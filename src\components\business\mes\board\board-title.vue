<template>
  <div class="board-title" style="margin-bottom: 8px" >
    {{ props.title }}
  </div>
</template>
<script setup>
import bgImg from '/@/assets/images/layout/background-1.png'
const props = defineProps({
  title: {
    type: String,
    default: '标题'
  }

})
</script>
<style scoped lang="less">
.board-title {
  background-image: url("../../../../assets/images/layout/background-1.png");
  background-size: cover; /* 覆盖整个元素，不重复 */
  background-position: center; /* 背景图片居中显示 */
  background-repeat: no-repeat; /* 不重复背景图片 */
  height: 60px;
  line-height: 60px;
  padding-left: 30px;
  color: #fff;
  font-size: 30px;
  font-weight: bold;
}
</style>

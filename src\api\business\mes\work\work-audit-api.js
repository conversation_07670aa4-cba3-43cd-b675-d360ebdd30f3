/**
 * 报工审核 api 封装
 *
 * @Author:    x<PERSON><PERSON><PERSON><PERSON>
 * @Date:      2024-07-22 20:33:41
 * @Copyright  xiaohuihui
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const workAuditApi = {

    /**
     * 通过  <AUTHOR>
     */
    passRecord: (param) => {
        return postRequest('/workAudit/pass',param);
    },
    /**
     * 驳回  <AUTHOR>
     */
    rebackRecord: (param) => {
        return postRequest('/workAudit/reject',param);
    },

};

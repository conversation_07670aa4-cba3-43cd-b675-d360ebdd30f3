<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!--指令单编号和物料编号联动选择器-->
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="颜色" class="smart-query-form-item">
        <a-select
          v-model:value="initColorVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择颜色"
          :options="colorOptions"
          @change="handleColorChange"
        />
      </a-form-item>
      <a-form-item label="尺码" class="smart-query-form-item">
        <a-select
          v-model:value="initSizeVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择尺码"
          :options="sizeOptions"
          @change="handleSizeChange"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="instructOrderId"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :expand-column-width="70"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'progress'">
          <a-progress :size="8" :percent="((record.receiveNum / (record.sendNum + record.receiveNum)) * 100).toFixed(2)" />
        </template>
      </template>

      <template #expandedRowRender="{ record }">
        <detail :id="record.instructOrderId" />
      </template>
      <template #expandColumnTitle>
        <span>展开</span>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">
                <template v-if="key.includes('TieCount')">
                  {{ value + '扎' }}
                </template>
                <template v-else-if="key.includes('Num') && !key.includes('ObjectNum')">
                  {{ value + '片' }}
                </template>
                <template v-else>
                  {{ value }}
                </template>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { onMounted, reactive, ref, watch, computed } from 'vue';
  import { partDispatchSummaryApi } from '/@/api/business/mes/part-dispatch/part-dispatch-summary-api.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const.js';
  import Detail from '/@/views/business/mes/part-dispatch/summary/components/detail.vue';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';

  const columns = ref([
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      ellipsis: true,
      // width: 135
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      // width: 135
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      // width: 100
    },
    {
      title: '下发对象数',
      dataIndex: 'objectNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
    },
    {
      title: '已下发扎数',
      dataIndex: 'sendTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + record.receiveTieCount + '扎';
      },
    },
    {
      title: '已回收扎数',
      dataIndex: 'receiveTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '扎';
      },
    },
    {
      title: '待回收扎数',
      dataIndex: 'sendTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '扎';
      },
    },
    {
      title: '总扎数',
      dataIndex: 'totalTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return record.sendTieCount + record.receiveTieCount + '扎';
      },
    },
    {
      title: '已下发数量',
      dataIndex: 'sendNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + record.receiveNum + '片';
      },
    },
    {
      title: '已回收数量',
      dataIndex: 'receiveNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '片';
      },
    },
    {
      title: '待回收数',
      dataIndex: 'sendNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '片';
      },
    },

    {
      title: '总数',
      dataIndex: 'totalNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return record.sendNum + record.receiveNum + '片';
      },
    },
    {
      title: '回收进度',
      dataIndex: 'progress',
      ellipsis: true,
      width: 120,
      align: 'center',
    },
  ]);

  const queryFormState = {
    instructNumber: undefined, //生产指令编号
    itemNumber: undefined, // 物料编号
    colors: [],
    sizes: [],
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    //两个编号情况
    insItemSelectRef.value.clearAllNumber();
    //颜色和尺码清空
    initColorVal.value = undefined;
    initSizeVal.value = undefined;
    colorOptions.value = [];
    sizeOptions.value = [];
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await partDispatchSummaryApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;

      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  //-------------------------------合计----------------------------------------
  // 提取数字的工具函数
  const extractNumber = (value) => {
    if (typeof value === 'number') return value;
    if (!value) return 0;
    // 提取字符串中的数字
    const matches = value.match(/\d+/);
    return matches ? Number(matches[0]) : 0;
  };

  const totals = computed(() => {
    const sums = {
      totalObjectNum: 0, // 总生产对象数
      totalHasSendTieCount: 0, // 总已下发扎数 = 总已回收扎数 + 总待回收扎数
      totalReceiveTieCount: 0, // 总已回收扎数
      totalSendTieCount: 0, // 总待回收扎数
      totalTieCount: 0, // 总扎数  = 总已下发扎数
      totalHasSendNum: 0, // 总已下发数量 = 总已回收数量 + 总待回收数量
      totalReceiveNum: 0, // 总已回收数量
      totalSendNum: 0, // 总待回收数量
      totalNum: 0, // 总数量 = 总已下发数量
    };

    tableData.value.forEach((item) => {
      sums.totalObjectNum += item.objectNum;
      sums.totalReceiveTieCount += extractNumber(item.receiveTieCount);
      sums.totalSendTieCount += extractNumber(item.sendTieCount);
      sums.totalReceiveNum += extractNumber(item.receiveNum);
      sums.totalSendNum += extractNumber(item.sendNum);
    });

    sums.totalTieCount = sums.totalHasSendTieCount = sums.totalReceiveTieCount + sums.totalSendTieCount;
    sums.totalNum = sums.totalHasSendNum = sums.totalReceiveNum + sums.totalSendNum;

    return sums;
  });

  //计算剩余的空单元格
  const summaryColSpans = 4; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => 1 + columns.value.length - summaryColSpans - Object.keys(totals.value).length); // 加1是因为多了“展开”列

  // ---------------------------- 处理两个编号 ----------------------------
  const insItemSelectRef = ref();
  function insItemChange(data) {
    // 回传数据
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
    queryForm.instructOrderId = data.instructOrderId;
  }

  // ---------------------------- 获取颜色和尺码 ----------------------------
  //下拉框最多可显示数量
  const maxTagCount = ref(1);
  //颜色和尺码下拉框初始值
  const initColorVal = ref(undefined);
  const initSizeVal = ref(undefined);

  watch(
    () => queryForm.instructNumber,
    (newVal) => {
      initColorVal.value = undefined;
      initSizeVal.value = undefined;
      if (newVal !== undefined) {
        queryByInsAndItemNumber();
      }
    }
  );
  //根据指令单id获取颜色和尺码信息
  async function queryByInsAndItemNumber() {
    let colorList = await produceInstructOrderClothesApi.queryClothesColorList(queryForm.instructOrderId);
    let sizeList = await produceInstructOrderClothesApi.queryClothesSizeList(queryForm.instructOrderId);
    outstandingOptions(colorList.data, colorOptions);
    outstandingOptions(sizeList.data, sizeOptions);
  }

  //拼装options
  const colorOptions = ref([]);
  const sizeOptions = ref([]);
  function outstandingOptions(sourceData, target) {
    target.value = [];
    sourceData.map((item, index) =>
      target.value.push({
        label: item,
        value: index,
      })
    );
  }

  // --------------------- 处理颜色和尺码 ------------------------
  function handleColorChange(item, option) {
    queryForm.colors = [];
    option.map((e) => {
      queryForm.colors.push(e.label);
    });
  }

  function handleSizeChange(item, option) {
    queryForm.sizes = [];
    option.map((e) => {
      queryForm.sizes.push(e.label);
    });
  }
</script>
<style scoped lang="less"></style>

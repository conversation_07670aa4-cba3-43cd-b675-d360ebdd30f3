<template>
  <board-title :title="'库存仪表盘'"/>

  <num-card/>
  <div style="margin-bottom: 10px"></div>

  <a-row :gutter="16">
    <a-col :span="19">
      <in-out-trend-line style="height: 280px"/>
    </a-col>
    <a-col :span="5">
      <inaction-stock-bar style="height: 280px"/>
    </a-col>
  </a-row>
  <div style="margin-bottom: 10px"></div>

  <a-row :gutter="16">
    <a-col :span="19">
      <stock-bar style="height: 280px"/>
    </a-col>
    <a-col :span="5">
      <stock-age-pie style="height: 280px"/>
    </a-col>
  </a-row>

</template>
<script setup>

import BoardTitle from "/@/components/business/mes/board/board-title.vue";
import NumCard from "/@/views/business/mes/stock/dashboard/components/num-card.vue";
import InOutTrendLine from "/@/views/business/mes/stock/dashboard/components/in-out-trend-line.vue";
import StockAgePie from "/@/views/business/mes/stock/dashboard/components/stock-age-pie.vue";
import StockBar from "/@/views/business/mes/stock/dashboard/components/stock-bar.vue";
import InactionStockBar from "/@/views/business/mes/stock/dashboard/components/inaction-stock-bar.vue";
</script>
<style scoped lang="less">

</style>

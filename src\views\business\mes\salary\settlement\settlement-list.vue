<!--
  * 薪酬结算
  *
  * @Author:    linwj
  * @Date:      2024-11-20 20:48:59
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane :key=SETTLEMENT_WAY_ENUM.BY_MONTH.value tab="按月结算">
          <a-form class="smart-query-form">
            <a-row class="smart-query-form-row">
              <a-form-item label="员工" class="smart-query-form-item">
                  <a-select
                      v-model:value="initOptEmp"
                      show-search
                      mode="multiple"
                      :max-tag-count="1"
                      placeholder="员工选择"
                      style="width: 150px"
                      :options="operatorOptions"
                      :filter-option="filterOption"
                      @change="handleEmpChange"
                  />
              </a-form-item>
              <a-form-item label="结算月份" class="smart-query-form-item">
                <a-date-picker
                    v-model:value="settlementMonth"
                    picker="month"
                    format="YYYY-MM"
                    :allowClear="false"
                />
              </a-form-item>
              <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-row>
          </a-form>
        </a-tab-pane>

        <a-tab-pane :key=SETTLEMENT_WAY_ENUM.BY_DAY.value tab="按日结算">
          <a-form class="smart-query-form">
            <a-row class="smart-query-form-row">
              <a-form-item label="员工" class="smart-query-form-item">
                <a-select
                    v-model:value="initOptEmp"
                    show-search
                    mode="multiple"
                    :max-tag-count="1"
                    placeholder="操作人"
                    style="width: 150px"
                    :options="operatorOptions"
                    :filter-option="filterOption"
                    @change="handleEmpChange"
                />
              </a-form-item>
              <a-form-item label="时间范围" class="smart-query-form-item">
                <a-range-picker v-model:value="timeRange" :allowClear="false"/>
              </a-form-item>
              <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-row>
          </a-form>
        </a-tab-pane>
      </a-tabs>

      <a-row class="smart-table-btn-block">
        <a-row span="16">
          <div class="smart-table-operate-block">
            <a-button type="primary" size="small" @click="batchSettlement">
              <MoneyCollectOutlined />
              <span>批量结算</span>
            </a-button>
          </div>
        </a-row>
      </a-row>

      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          bordered
          :loading="tableLoading"
          :pagination="false"
          :row-selection="rowSelection"
          row-key="employeeId"
      >
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'actualName'" >
            <span>{{text}}</span>
          </template>
          <template v-else-if="column.dataIndex==='settledSalary'">
            <span :style="{color: '#16d819'}">{{"￥ "+text}}</span>
          </template>
          <template v-else-if="column.dataIndex==='settledNum'">
            <span :style="{color: '#16d819'}">{{text+" 件"}}</span>
          </template>
          <template v-else-if="column.dataIndex==='unSettledSalary'">
            <span :style="{color: '#e9001a'}">{{"￥ "+text}}</span>
          </template>
          <template v-else-if="column.dataIndex==='unSettledNum'">
            <span :style="{color: '#e9001a'}">{{text+" 件"}}</span>
          </template>

          <template v-else-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button @click="singleSettlement(record)" type="link">结算</a-button>
              <a-button @click="showModal({
                        employeeId:record.employeeId,
                        workerName: record.actualName,
                        activeKey:activeKey,
                        settlementMonth: settlementMonth,
                        timeRange: timeRange,
                        record:record
                  })" type="link">查看</a-button>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'settlementStatus'">
            <span v-if="record.unSettledNum >0 && record.settledNum>0 ">
              部分结算
            </span>
            <span v-else-if="record.unSettledNum >0 && record.settledNum===0 ">
              未结算
            </span>
            <span v-else-if="record.unSettledNum ===0 && record.settledNum>0 ">
              已结算
            </span>
          </template>
        </template>
      </a-table>
      <a-modal v-model:open="settlementOpen"
               :title="'计件结算&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'+queryForm.startDate+' 到 '+queryForm.endDate"
               width="400px"
               @ok="handleSettlementOpenOk">
        <a-divider />
          <a-row>
            <a-col :span="8">结算金额</a-col>
            <a-col :span="8" :offset="8" align="right">{{settlementAmount+" ￥"}}</a-col>
          </a-row>
        <br/>
          <a-row>
            <a-col :span="8">结算人数</a-col>
            <a-col :span="8" :offset="8" align="right">{{numberOfSettlement}}</a-col>
          </a-row>
        <a-divider />
        <a-form-item label="结算月份" class="smart-query-form-item">
          <a-date-picker v-model:value="belongDate"
                         picker="month"
                         format="YYYY-MM"/>
        </a-form-item>
      </a-modal>

      <!---------- 表格 end ----------->

      <div class="smart-query-table-page">
        <a-pagination
            showSizeChanger
            showQuickJumper
            show-less-items
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :defaultPageSize="queryForm.pageSize"
            v-model:current="queryForm.pageNum"
            v-model:pageSize="queryForm.pageSize"
            :total="total"
            @change="queryData"
            @showSizeChange="queryData"
            :show-total="(total) => `共${total}条`"
        />
      </div>

      <SettlementRecordForm  ref="formRef" @reloadList="queryData"/>
  </a-form>
</template>
<script setup>
    import {reactive, ref, onMounted, watch} from 'vue';
    import {settlementApi} from '/@/api/business/mes/salary/settlement-api.js'
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import SettlementRecordForm from './settlement-form.vue';
    import {SETTLEMENT_WAY_ENUM} from "/@/constants/business/mes/salary/settlementWay-const.js";
    import dayjs from "dayjs";
    import _ from 'lodash';
    import {employeeApi} from "/@/api/system/employee-api.js";
    import {
      Work_Record_AuditFlag_ENUM,
      Work_Record_Status_ENUM
    } from "/@/constants/business/mes/work/work-record-const.js";
    import {message} from "ant-design-vue";
    import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
    const activeKey = ref(SETTLEMENT_WAY_ENUM.BY_MONTH.value);
    watch(()=>activeKey.value, (newValue)=>{
      queryForm.settlementWay =  newValue
      // 切换tab栏，清空已选择的员工
      queryForm.queryEmployeeIds.length = 0
      initOptEmp.value = undefined
      // 重新查询
      activeKey.value === '2'?
          settlementMonth.value = dayjs().startOf('month') :
          timeRange.value = [dayjs().startOf('month'), dayjs().endOf('month')]
    })

    // ---------------------------- 处理时间查询----------------------------
    //结算月份
    const settlementMonth = ref(dayjs().startOf('month'));
    //监听结算月份变化
    watch(()=>settlementMonth.value, (newValue) => {
      if(!_.isNull(newValue)){
        queryForm.startDate = newValue.startOf('month').format("YYYY-MM-DD")
        queryForm.endDate = newValue.endOf('month').format("YYYY-MM-DD")
        queryData()
      }
    })

    //时间范围,默认为本月
    const timeRange = ref([dayjs().startOf('month'), dayjs().endOf('month')])
    //监听时间范围变化
    watch(()=>timeRange.value, (newValue)=>{
      if(!_.isNull(newValue)){
        queryForm.startDate = newValue[0].startOf('day').format("YYYY-MM-DD")
        queryForm.endDate = newValue[1].endOf('day').format("YYYY-MM-DD")
        queryData()
      }
    })
    // ---------------------------- 处理查看员工薪资详情 ---------------------------

    const formRef = ref();
    function showModal(data){
      formRef.value.show(data);
    }

    // ---------------------------- 处理操作人下拉 ----------------------------

    //初始化员工下拉
    const initOptEmp = ref()
    const operatorOptions = ref([])

    //输出操作者options
    function outstandingOptOptions(tableData) {
      operatorOptions.value = []
      tableData.map((e) => (
          operatorOptions.value.push({
            value: e.employeeId,
            label: e.actualName
          })
      ))
    }

    function handleEmpChange(empIds) {
      queryForm.queryEmployeeIds.length = 0
      Object.assign(queryForm.queryEmployeeIds,empIds)
    }

    //操作者下拉框搜索
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    //查询所有员工
    async function queryAllEmployee() {
      try {
        let resp = await employeeApi.queryAll();
        outstandingOptOptions(resp.data)
      } catch (e) {
        smartSentry.captureError(e);
      }
    }

    const columns = ref([
        {
            title: '员工姓名',
            dataIndex: 'actualName',
            ellipsis: true,
        },
        {
            title: '电话',
            dataIndex: 'phone',
            ellipsis: true,
        },
        {
            title: '部门名称',
            dataIndex: 'departmentName',
            ellipsis: true,
        },
        {
          title: '未结算件数',
          dataIndex: 'unSettledNum',
          ellipsis: true,
          customRender: (text) => {
            return text.value+" 件"
          }
        },
        {
          title: '未结算',
          dataIndex: 'unSettledSalary',
          ellipsis: true,
          customRender: (text) => {
            return `￥ ${text.value}`
          }
        },
        {
          title: '已结算件数',
          dataIndex: 'settledNum',
          ellipsis: true,
          customRender: (text) => {
            return text.value+" 件"
          }
        },
        {
          title: '已结算',
          dataIndex: 'settledSalary',
          ellipsis: true,
          customRender: (text) => {
            return "￥"+text.value
          }
        },
        {
          title: '结算状态',
          dataIndex: 'settlementStatus',
          ellipsis: true,
        },
        {
          title: '操作',
          dataIndex: 'action',
          ellipsis: true,
          align: 'center',
          width: 130,
        },
    ]);

    const selectedRowKeyList = ref([])
    const selectedRowList = ref(undefined)
    const rowSelection = {
      onChange: (selectedRowKeys,selectedRows) => {
        selectedRowKeyList.value = selectedRowKeys
        selectedRowList.value = selectedRows
      }
    };
    // ---------------------------- 处理结算弹框 ----------------------------
    const settlementOpen = ref(false);
    //结算金额
    const settlementAmount = ref(0);
    //结算人数
    const numberOfSettlement = ref(0);
    const belongDate = ref(undefined)
    //批量结算
    function batchSettlement(){
      if (_.isNil(selectedRowKeyList.value)||_.isEmpty(selectedRowKeyList.value)) {
        return message.warn("请选择数据");
      }
      settlementOpen.value = true;
      initSettlementOpen()
    }
    //单个结算
    function singleSettlement(rowData){
      settlementAmount.value = rowData.unSettledSalary  //结算金额
      numberOfSettlement.value = rowData.actualName  //结算人数
      selectedRowKeyList.value.length = 0  // 重置已选择的员工
      selectedRowKeyList.value.push(rowData.employeeId)

      settlementOpen.value = true;
    }

    //初始化结算金额和人数
    function initSettlementOpen(){
      settlementAmount.value = 0
      numberOfSettlement.value = selectedRowKeyList.value.length
      selectedRowList.value.map((e)=>{
        settlementAmount.value += e.unSettledSalary
      })
    }
    function handleSettlementOpenOk(){
      let param = {
        employeeIds: selectedRowKeyList.value,
        settlementWay: activeKey.value,
        belongDate:belongDate.value,
        startDate: queryForm.startDate,
        endDate: queryForm.endDate,
      }
      try {
          SmartLoading.show();
          settlementApi.settlement(param).then(()=>{
            queryData();
            settlementOpen.value = false
          })
          message.success('结算成功');
      } catch (e) {
          smartSentry.captureError(e);
      } finally {
          SmartLoading.hide();
      }
    }
    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        pageNum: 1,
        pageSize: 10,
        queryEmployeeIds:[], //员工id
        settlementWay: SETTLEMENT_WAY_ENUM.BY_MONTH.value, //结算方式 默认月结
        startDate: dayjs().startOf('month').format("YYYY-MM-DD"), // 开始日期 默认本月
        endDate: dayjs().endOf('month').format("YYYY-MM-DD"), // 结束日期 默认本月
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        //重置所有时间
        settlementMonth.value = dayjs().startOf('month')
        timeRange.value = [dayjs().startOf('month'), dayjs().endOf('month')]
        queryForm.pageSize = pageSize;
        // 切换tab栏，清空已选择的员工
        queryForm.queryEmployeeIds.length = 0
        initOptEmp.value = undefined
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await settlementApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
            //多选按钮清空
          if (!_.isNil(selectedRowList.value)) {
            selectedRowList.value.length = 0
            selectedRowKeyList.value.length = 0
          }
        }
    }

    onMounted(()=>{
      queryData()
      queryAllEmployee()
    });
</script>

<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="placeholder"
    :options="customerNameList"
    :style="{ width: width }"
    @change="handleChange"
    :disabled="disabled"
  />
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import { customerApi } from '/@/api/business/mes/base/customer-api.js';

  const props = defineProps({
    value: {
      type: Number,
      default: undefined,
    },
    placeholder: {
      type: String,
      default: '请选择客户',
    },
    width: {
      type: String,
      default: '100%',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  const selectedValue = ref(props.value);
  const customerNameList = ref([]);

  // 监听父组件传入的 value 变化
  watch(
    () => props.value,
    (newVal) => {
      selectedValue.value = newVal;
    }
  );

  // 查询客户列表
  async function queryCustomerName() {
    try {
      const queryData = await customerApi.getCustomerName({});
      customerNameList.value = [];
      if (!_.isEmpty(queryData.data)) {
        customerNameList.value = queryData.data.map((item) => ({
          value: item.id,
          label: item.name,
        }));
      }
    } catch (error) {
      message.error('客户名称请求失败');
    }
  }

  // 处理选择变化
  function handleChange(value) {
    emit('update:value', value);
    const selectedCustomer = customerNameList.value.find((item) => item.value === value);
    emit('change', {
      customerId: value,
      customerName: selectedCustomer.label ? selectedCustomer.label : undefined,
    });
  }

  onMounted(() => {
    queryCustomerName();
  });
</script>

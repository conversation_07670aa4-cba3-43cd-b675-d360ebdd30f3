import { postRequest, getRequest } from '/@/lib/axios';

export const partStationTurnTaskActionApi = {

    /**
     * 提交任务
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    submitTurnTask: (param) => {
        return postRequest('/partStationTurnTask/submit', param);
    },

    /**
     * 取消任务  <AUTHOR>
     */
    cancelTask: (ids) => {
        return postRequest(`/partStationTurnTask/cancel`, ids);
    }
}

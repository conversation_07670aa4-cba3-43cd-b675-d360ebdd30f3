/**
 * 生产退库单 api 封装
 *
 * @Author:    fkf
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkProdOutApi = {

  /**
   * 分页查询  
   */
  queryPage : (param) => {
    return postRequest('/stkProdOut/queryPage', param);
  },
  /**
   * 增加  
   */
  add: (param) => {
      return postRequest('/stkProdOut/add', param);
  },

  /**
   * 修改  
   */
  update: (param) => {
      return postRequest('/stkProdOut/update', param);
  },

  /**
   * 根据id查询其他出库单  
   */
  byId: (id) => {
      return getRequest(`/stkProdOut/byId?id=${id}`);
  },

  /**
   * 修改单据状态
   */
  status: (id) => {
      return getRequest(`/stkProdOut/status?id=${id}`);
  },

  /**
   * 删除
   */
  delete: (id) => {
      return getRequest(`/stkProdOut/delete?id=${id}`);
  },
};

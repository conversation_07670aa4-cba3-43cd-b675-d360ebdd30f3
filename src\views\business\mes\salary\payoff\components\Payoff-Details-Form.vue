<!--
  * 工资发放查看弹窗
  *
  * @Author:    linwj
  * @Date:      2024-12-10 22:12:45
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="employeeName"
      width="1200px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-card size="small" :bordered="false" :hoverable="true">
      <!---------- 表格 begin ----------->
      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          bordered
          :loading="tableLoading"
          :pagination="false"
      />

      <!---------- 表格 end ----------->

      <div class="smart-query-table-page">
        <a-pagination
            showSizeChanger
            showQuickJumper
            show-less-items
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :defaultPageSize="queryForm.pageSize"
            v-model:current="queryForm.pageNum"
            v-model:pageSize="queryForm.pageSize"
            :total="total"
            @change="queryData"
            @showSizeChange="queryData"
            :show-total="(total) => `共${total}条`"
        />
      </div>
    </a-card>
    <template #footer>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {payoffApi} from "/@/api/business/mes/salary/payoff-api.js";
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';
import {smartSentry} from '/@/lib/smart-sentry';
import _ from "lodash";

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);
const employeeName = ref(null);
function show(employeeInfo,belongMonth) {
  queryForm.belongMonth = belongMonth;
  employeeName.value = employeeInfo.actualName
  queryForm.employeeId = employeeInfo.employeeId
  queryData()
  visibleFlag.value = true;
}

function onClose() {
  visibleFlag.value = false;
}
// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '生成款号',
    dataIndex: 'itemName',
    ellipsis: true,
    width: 150
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    ellipsis: true,
    width: 100
  },
  {
    title: '床次',
    dataIndex: 'cutNum',
    ellipsis: true,
    width: 50
  },
  {
    title: '款式颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    width: 80
  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    width: 50
  },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    width: 50
  },

  {
    title: '工序名称',
    dataIndex: 'processName',
    ellipsis: true,
    width: 80
  },
  {
    title: '报工数量',
    dataIndex: 'workQuantity',
    ellipsis: true,
    width: 80
  },
  {
    title: '单价价格',
    dataIndex: 'price',
    ellipsis: true,
    width: 80

  },
  {
    title: '报工时间',
    dataIndex: 'reportTime',
    ellipsis: true,
    defaultSortOrder: 'descend',
    width: 150

  },
]);
// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  belongMonth: undefined, // 归属月份
  employeeId: undefined,
  pageNum: 1,
  pageSize: 10,
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await payoffApi.queryDetails(queryForm,queryForm.employeeId);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

defineExpose({
  show,
}
)
</script>

<template>
  <a-card>
    <div>工序分析 | 各工序完成数占比数量</div>
    <div style="color: #C0C4CC">{{desc}}</div>
    <div></div>
    <div class="echarts-box">
      <div class="process-pie" id="process-pie"></div>
    </div>
  </a-card>
</template>
<script setup>
import * as echarts from 'echarts';
import {onMounted, reactive} from "vue";
import { workReportRecordApi } from "/src/api/business/mes/work/work-report-record-api.js";
import dayjs from "dayjs";
import { smartSentry } from '/@/lib/smart-sentry';
let option = reactive({});
let myChart = null;

const props = defineProps({
  desc:{
  default: '更新于：'+dayjs().format('YYYY-MM-DD HH:mm:ss'),
  },
})

onMounted(() => {
  init()  
})



async function query(params = {}) {
  try {
    const res = await workReportRecordApi.getProcessOutputRatio(params);
    if (res.data) {
      option.series[0].data = res.data.map(item => ({
        name: item.x,
        value: item.y
      }));
      myChart && myChart.setOption(option);
    }
  } catch (error) {
    smartSentry.captureError(error);
  }
}

function init() {
  option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        color: [ // 自定义颜色集合
          '#33d0bd', '#ffc372', '#fe706e', '#5292ff', '#7585e5',
        ],
        name: '工序产出',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          alignTo: 'edge',
          formatter: '{name|{b}}\n{time|{c} 件}',
          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 10,
              color: '#999'
            }
          }
        },
        emphasis: {
          label: {
            show: false // 隐藏高亮状态下的标签
          }
        },
        labelLine: {
          length: 15,
          length2: 0,
          maxSurfaceAngle: 80
        },
        labelLayout: function (params) {
          const isLeft = params.labelRect.x < myChart.getWidth() / 2;
          const points = params.labelLinePoints;
          points[2][0] = isLeft
              ? params.labelRect.x
              : params.labelRect.x + params.labelRect.width;
          return {
            labelLinePoints: points
          };
        },
        data: [] // 初始化为空数组，等待接口数据
      }
    ]
  };
  
  let chartDom = document.getElementById('process-pie');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    option && myChart.setOption(option);
  }
  
  window.addEventListener("resize", () => {
    myChart && myChart.resize();
  });
}

defineExpose({
  query
})

</script>
<style scoped lang="less">
.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .process-pie {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

<template>
  <a-card>
    <div>产出分析 | 个人工序产出排名</div>
    <div style="color: #C0C4CC">{{ desc }}</div>
    <div class="echarts-box">
      <div class="out-put-rank" id="out-put-rank"></div>
    </div>
  </a-card>
</template>
<script setup>
import dayjs from "dayjs";
import {onMounted, reactive, ref} from "vue";
import { workReportRecordApi } from "/src/api/business/mes/work/work-report-record-api.js";
import * as echarts from "echarts";
import { smartSentry } from '/@/lib/smart-sentry';
let option = reactive({});
let myChart = null;


const props = defineProps({
  desc: {
    default: '更新于：' + dayjs().format('YYYY-MM-DD HH:mm:ss'),
  }
})

onMounted(() => {
  init()
})

async function query(params = {}) {
  try {
    const res = await workReportRecordApi.getOutputRanking(params);
    if (res.data) {
      // 将接口数据转换为图表所需格式
      const names = res.data.map(item => item.x);
      const values = res.data.map(item => item.y);
      
      // 更新图表数据
      option.yAxis.data = names;
      option.series[0].data = values;
      myChart && myChart.setOption(option);
    }
  } catch (error) {
     smartSentry.captureError(error);
  }
}

function init() {
  option = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
      width: "90%",
      height: "90%",
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01],
    },
    yAxis: {
      type: "category",
      data: [], // 初始化为空数组，等待接口数据
    },
    series: [
      {
        name: "个人工序产出",
        type: "bar",
        data: [], // 初始化为空数组，等待接口数据
        barWidth: "80%",
        label: {
          show: true,
          position: "right",
        },
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(180, 180, 180, 0.2)'
        },
        color: '#317ef2'
      },
    ],
  }
  
  let chartDom = document.getElementById('out-put-rank');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    option && myChart.setOption(option);
  }
  
  window.addEventListener("resize", () => {
    myChart && myChart.resize();
  });
}


defineExpose({
  query
})
</script>
<style scoped lang="less">
.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .out-put-rank {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

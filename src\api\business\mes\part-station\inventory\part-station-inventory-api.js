/**
 * 裁片驿站库存表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-07 19:44:34
 * @Copyright  zscbdic
 */
import {postRequest, getRequest} from '/@/lib/axios';

export const partStationInventoryApi = {

    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/partStationInventory/queryPage', param);
    },

    /**
     * 增加  <AUTHOR>
     */
    add: (param) => {
        return postRequest('/partStationInventory/add', param);
    },

    /**
     * 修改  <AUTHOR>
     */
    update: (param) => {
        return postRequest('/partStationInventory/update', param);
    },


    /**
     * 删除  <AUTHOR>
     */
    delete: (id) => {
        return getRequest(`/partStationInventory/delete/${id}`);
    },

    /**
     * 批量删除  <AUTHOR>
     */
    batchDelete: (idList) => {
        return postRequest('/partStationInventory/batchDelete', idList);
    },

    /**
     * 库存查询  <AUTHOR>
     */
    query: (param) => {
        return postRequest(`/partStationInventory/query`, param)
    },

    /**
     * 查询裁片库存情况 <AUTHOR>
     * @param param
     * @returns {*}
     */
    queryInventorySituation: (param) => {
        return postRequest('/partStationInventory/usageSituation', param);
    },

    /**
     * 分页汇总查询  <AUTHOR>
     */
    summaryQuery:(param)=>{
        return postRequest('/partStationInventory/summaryPage',param)
    }
};

/**
 * 松布作业 api 封装
 *
 * @Author:    wcc
 * @Date:      2025-06-22 21:03:26
 * @Copyright  weavewise.zscbdic.cn
 */
import { postRequest, getRequest } from '/src/lib/axios';

export const loosenTaskApi = {
  /**
   * 直接添加松布任务  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/loosenTask/add', param);
  },

  /**
   * 设置松布任务状态为静置中  <AUTHOR>
   */
  setStay: (param) => {
    return postRequest('/loosenTask/status/stay', param);
  },

  /**
   * 设置松布任务状态为已结束  <AUTHOR>
   */
  setEnd: (id) => {
    return getRequest(`/loosenTask/status/end/${id}`);
  },
};

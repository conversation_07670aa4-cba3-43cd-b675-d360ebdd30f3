<!--
  * 生产小组
  *
  * @Author:    Linwj
  * @Date:      2024-07-09 10:32:46
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="小组名称" name="teamName">
        <a-input style="width: 50%" v-model:value="form.teamName" placeholder="生产小组名称" />
      </a-form-item>

      <a-form-item label="负责人" name="leaderId">
        <a-select ref="selectLeaderid" v-model:value="form.leaderId" style="width: 50%" :options="leaderIds"/>
      </a-form-item>

      <a-form-item label="车间" name="workshopId">
        <a-select ref="selectworkShopid" v-model:value="form.workshopId" style="width: 50%" :options="workShopIds"  />
      </a-form-item>

      <a-form-item label="成员信息" name="memberInfo">
        <!-- <a-select
          ref="memberInfos"
          v-model:value="form.memberId"
          mode="multiple"
          style="width: 70%"
          placeholder="请选择成员"
          :options="leaderIds"
          :filter-option="filterOption"
          @change="defaultmemberChange"
        /> -->
        <EmployeeMoreSelect v-model:value="form.memberId" style="width: 70%" placeholder="请选择成员" :options="leaderIds" @change="defaultmemberChange"/>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea style="width: 70%" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { produceTeamApi } from '/@/api/business/mes/factory/produce-team-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { employeeApi } from '/@/api/system/employee-api.js';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api.js';
  import EmployeeMoreSelect from '/@/components/system/employee-more-select/index.vue';

  // ------------------------ 成员信息 --------------------

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);
  //存放负责人信息和车间信息
  const leaderIds = ref([]);
  const workShopIds = ref([]);
  const memberIds = ref([]);
  function defaultmemberChange(option) {
    memberIds.value = option.map(item => ({
      memberId: item.employeeId,
    }));
  }
  //新增，编辑搜索功能
  const filterOption = (input, option) => {
    return option.label.indexOf(input) >= 0;
  };
  // 拼装函数
  function mergeString(data, value, label) {
    return data.map((e) => ({
      value: e[value],
      label: e[label],
    }));
  }

  async function defaultList() {
    try {
      // 请求参数
      let queryForm = {
        queryKey: '',
      };
      let responseModel1 = await employeeApi.queryAll(queryForm);
      leaderIds.value = mergeString(responseModel1.data, 'employeeId', 'actualName');
      let responseModel2 = await workshopApi.queryList(queryForm);
      workShopIds.value = mergeString(responseModel2.data, 'id', 'name');
    } catch (e) {
      message.error('请求失败！');
      smartSentry.captureError('this is the:', e);
    }
  }
  defaultList();
  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
      if (rowData.produceTeamMemberVOList !== undefined) {
        form.memberId = [];
        form.produceTeamMemberVOList.map((e) => {
          form.memberId.push(e.memberId);
        });
      }
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    form.memberId = [];
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined,
    remark: undefined, //备注
    teamName: undefined, //生产小组名称
    leaderId: undefined, //负责人id
    workshopId: undefined, //车间id
    memberId: [], //成员id
    memberIds: undefined,
  };

  let form = reactive({ ...formDefault });

  const rules = {
    teamName: [{ required: true, message: '生产小组名称 必填' }],
    workshopId: [{ required: true, message: '车间 必填' }],
    leaderId: [{ required: true, message: '负责人 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      form.memberIds = memberIds.value;
      await save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await produceTeamApi.update(form);
      } else {
        await produceTeamApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

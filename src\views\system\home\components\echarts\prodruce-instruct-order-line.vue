<template>
  <div class="card">
    <div class="title">指令单30天变化趋势</div>
    <div class="time">{{ props.time.startTime }}~{{ props.time.endTime }}</div>
    <div class="now">
      {{ props.time.detailCurrentTime }}<span>({{ props.time.week }})</span>
    </div>

    <div class="echarts-box">
      <div class="line-main" id="line-main"></div>
    </div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts';
  import { onMounted, ref, defineProps, watch } from 'vue';
  const props = defineProps({
    data: {
      type: Array,
      default: ()=> [],
    },
    time: {
      type: Object,
      default: () => ({
        startTime: '暂无时间',
        endTime: '暂无时间',
        detailCurrentTime: '暂无时间',
        week: '暂无时间',
      }),
    },
  });
  const option = ref({});
  onMounted(() => {
    init();
  });
  watch(
    () => props.data,
    (newValue) => {
      if (newValue) {
        init(newValue);
      }
    }
  );

  function init(data) {
    option.value = {
      grid: {
        // 让图表占满容器
        top: '20',
        left: '20',
        right: '0',
        bottom: '30',
      },

      xAxis: {
        type: 'category',
        data: [
          '1',
          '2',
          '3',
          '4',
          '5',
          '6',
          '7',
          '8',
          '9',
          '10',
          '11',
          '12',
          '13',
          '14',
          '15',
          '16',
          '17',
          '18',
          '19',
          '20',
          '21',
          '22',
          '23',
          '24',
          '25',
          '26',
          '27',
          '28',
          '29',
          '30',
        ],
      },
      yAxis: {
        type: 'value',
        interval: 1, // 设置刻度间隔为1
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#333',
        },
        formatter: function (params) {
          const data = params[0];
          // 从 startTime 中获取年月信息
          const [year, month] = props.time.startTime.split('-');
          return `日期：${year}年${month}月${data.name}日<br/>数值：${data.value}`;
        },
      },
      series: [
        {
          data: [12, 23, 24, 21, 13, 14, 26, 25, 26, 21, 12, 15, 30],
          type: 'line',
          lineStyle: {
            color: '#488bff',
            width: 3,
          },
          itemStyle: {
            color: '#488bff',
          },
          // 鼠标悬停时圆点样式
          emphasis: {
            scale: true,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#488bff',
            },
          },
        },
      ],
    };
    if (data) {
      if (data instanceof Array) {
        let yData = [];
        let xData = [];
        data.forEach((item) => {
          const day = Number(item.x.split('-')[2]).toString();
          xData.push(day);
          yData.push(item.y);
        });
        option.value.xAxis.data = xData;
        option.value.series[0].data = yData;
      }
    } else {
      option.value.series[0].data = [];
    }
    let chartDom = document.getElementById('line-main');
    if (chartDom) {
      let myChart = echarts.init(chartDom);
      document.getElementById('line-main').setAttribute('_echarts_instance_', '');
      option.value && myChart.setOption(option.value, true);
    }
  }
</script>
<style scoped lang="less">
  .card {
    background-color: #fff;
    padding: 20px;
    height: 350px;
    width: 100%;
    .title {
      font-size: 16px;
      //font-weight: bold;
      color: #303133;
      margin-bottom: 4px;
    }

    .time {
      font-size: 16px;
      color: #9b9b9b;
      margin-bottom: 4px;
    }

    .now {
      font-size: 16px;
      color: #5b5d60;
    }

    .echarts-box {
      display: flex;
      align-items: center;
      justify-content: center;
      .line-main {
        width: 100%;
        height: 200px;
        background: #fff;
      }
    }
  }
</style>

<template>
  <div style="display: flex;flex-direction: column;align-items: center;">
    <div style="text-align: center" v-if="qrStatus===waitConfirm">
      <div style="font-size: 22px;color: #1f2329;font-weight: 600;">扫码成功</div>
      <div
          style="font-size: 14px;color: #646a73;margin-top: 8px;line-height: 20px;min-height: 40px;white-space: pre-wrap">
        请在智裁移动端确认登录
      </div>
    </div>
    <div style="text-align: center" v-else>
      <div style="font-size: 22px;color: #1f2329;font-weight: 600;">扫码登录</div>
      <div
          style="font-size: 14px;color: #646a73;margin-top: 8px;line-height: 20px;min-height: 40px;white-space: pre-wrap">
        请使用智裁移动端扫描二维码
      </div>
    </div>

    <div style="position: relative;">
      <img :src="qrCodeBase64Image" style="width: 200px; position: relative; z-index: 1;">
      <div v-if="qrStatus === invalid" class="qr-overlay" style="background-color: rgba(0, 0, 0, 0.68);"
           @click="refreshQrCode">
        <RedoOutlined/>
        刷新二维码
      </div>
      <div v-else-if="qrStatus === waitConfirm" class="qr-overlay">
        <div class="name-circle">{{ name }}</div>
      </div>
      <div v-else-if="qrStatus===loginSuccess" class="qr-overlay" style="background-color: rgba(0,0,0,0.38);">
        <div class="name-circle">{{ name }}</div>
        <div style="color: white;font-size: 16px;font-weight: bold;margin-top: 10px">登录成功</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {onMounted, onUnmounted, ref} from "vue";
import {loginApi} from "/@/api/system/login-api.js";
import {qrLoginApi} from "/@/api/system/qr-login-api.js";
import {localSave} from "/@/utils/local-util.js";
import LocalStorageKeyConst from "/@/constants/local-storage-key-const.js";
import {message} from "ant-design-vue";
import {useUserStore} from "/@/store/modules/system/user.js";
import {buildRoutes} from "/@/router/index.js";
import {useRouter} from "vue-router";
import smartLoading from "/@/lib/smart-watermark.js";

const router = useRouter();

const confirmSuccess = "CONFIRM_SUCCESS"
const invalid = "INVALID"
const waitScan = "WAIT_SCAN"
const waitConfirm = "WAIT_CONFIRM"
const loginSuccess = "LOGIN_SUCCESS"

const qrCodeBase64Image = ref('');
const qrToken = ref('');

const qrStatus = ref(waitScan);

const name = ref('');

let refreshCaptchaInterval = null;

onMounted(async () => {
  await getQrCode()
});

onUnmounted(() => {
  stopRefreshInterval();
});

async function refreshQrCode() {
  await getQrCode();
  qrStatus.value = waitScan;
}

async function getQrCode() {
  try {
    // 获取二维码
    let res = await qrLoginApi.getQrCode();
    qrToken.value = res.data.qrToken;
    qrCodeBase64Image.value = res.data.qrBase64;
    // 获取二维码有效期
    beginRefreshInterval();
  } catch (e) {
    console.log(e);
  }
}

async function getQrStatus() {
  try {
    let res = await qrLoginApi.getQrCodeStatus({qrToken: qrToken.value});
    qrStatus.value = res.data.status;
    if (qrStatus.value === invalid) {
      name.value = ''
      stopRefreshInterval();
    } else if (qrStatus.value === waitConfirm) {
      name.value = res.data.name
    } else if (qrStatus.value === confirmSuccess) {
      // 登录
      qrStatus.value = loginSuccess;

      let res = await qrLoginApi.login({qrToken: qrToken.value})
      stopRefreshInterval()

      localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
      message.success('登录成功');
      //更新用户信息到pinia
      useUserStore().setUserLoginInfo(res.data);
      //构建系统的路由
      buildRoutes();
      await router.push('/home');
    }
  } catch (e) {
    console.log(e);
  }
}

function beginRefreshInterval() {
  if (refreshCaptchaInterval === null) {

    refreshCaptchaInterval = setInterval(getQrStatus, 2000);
  }
}

function stopRefreshInterval() {
  if (refreshCaptchaInterval != null) {
    clearInterval(refreshCaptchaInterval);
    refreshCaptchaInterval = null;
  }
}


</script>
<style scoped lang="less">
.qr-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: white;
  font-size: 16px;
  border-radius: 10px;
}

.name-circle {
  background-color: #00a0e9;
  color: white;
  font-size: 18px;
  font-weight: bold;
  width: 66px;
  height: 66px;
  border-radius: 50%;
  line-height: 66px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

}
</style>

/**
 * 尺码表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-05 22:37:21
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const sizeApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/size/queryPage', param);
  },

 /**
   * 下拉全部  <AUTHOR>
   */
  queryAll: (param) => {
    return postRequest('/size/queryAll', param);
  },
  
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/size/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/size/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/size/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/size/batchDelete', idList);
  },
 
};

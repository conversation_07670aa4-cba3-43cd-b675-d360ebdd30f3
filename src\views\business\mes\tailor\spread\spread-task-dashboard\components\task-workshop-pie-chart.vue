<template>
  <a-card>
    <div style="display: flex; justify-content: space-between">
      <div style="font-size: 16px">车间任务分布</div>
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="pie-main-3"></div>
    </div>
  </a-card>
</template>
<script setup>
  import { onMounted, reactive, ref, watch } from 'vue';
  import * as echarts from 'echarts';
  import { spreadTaskDashboardApi } from '/@/api/business/mes/tailor/spread-task-dashboard-api';

  const props = defineProps({
    dateRange: {
      type: Object,
      default: () => ({
        startDate: null,
        endDate: null,
      }),
    },
  });

  let myChart = null;
  const loading = ref(false);
  const chartData = ref([]);

  onMounted(() => {
    initChart();
    queryWorkshopTaskData();
  });

  // 初始化图表
  function initChart() {
    const chartDom = document.getElementById('pie-main-3');
    if (chartDom) {
      myChart = echarts.init(chartDom);

      window.addEventListener('resize', () => {
        myChart && myChart.resize();
      });
    }
  }

  // 获取车间任务分布数据
  async function queryWorkshopTaskData() {
    loading.value = true;
    try {
      const params = {
        planBeginTimeBegin: props.dateRange.startDate,
        planBeginTimeEnd: props.dateRange.endDate,
        taskObject: 'workshop',
      };

      const res = await spreadTaskDashboardApi.taskObject(params);
      if (res && res.data) {
        chartData.value = res.data.map((item) => ({
          name: item.x,
          value: item.y,
        }));
        updateChart();
      }
    } catch (e) {
      console.error('获取车间任务分布数据失败:', e);
      chartData.value = [];
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    if (!myChart) return;

    const option = {
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ccc',
        textStyle: {
          color: '#333',
        },
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        show: true,
        left: '2%',
        top: 'center',
        orient: 'vertical',
        textStyle: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['0%', '80%'],
          avoidLabelOverlap: true,
          label: {
            show: false,
            position: 'outside',
            formatter: '{b}: {c} ({d}%)',
            color: '#333',
            fontSize: 14,
          },
          labelLine: {
            show: false,
            lineStyle: {
              color: '#999',
            },
          },
          itemStyle: {
            borderRadius: 1,
            borderColor: '#fff',
            borderWidth: 2,
          },
          data: chartData.value.length > 0 ? chartData.value : [{ value: 0, name: '暂无数据' }],
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          shadowBlur: 10,
          shadowColor: 'rgba(0, 150, 255, 0.3)',
        },
      ],
      color: ['#409EFF', '#1890FF', '#40a9ff', '#73c0de', '#fac858', '#91cc75', '#ee6666', '#73c0de', '#3ba272', '#fc8452'],
    };

    myChart.setOption(option);
  }

  watch(
    () => props.dateRange,
    () => {
      queryWorkshopTaskData();
    },
    { deep: true }
  );
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .chart-main {
      width: 100%;
      height: 200px;
    }
  }
</style>

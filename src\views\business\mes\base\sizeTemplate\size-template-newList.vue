<template>
    <!-----------------------尺码模板-------------------------->
  <a-row :gutter="16" >
    <!-----------------------左侧模板-------------------------->
    <a-col :span="8">
      <a-card title="尺码模板列表" :bordered="false" hoverable body-style="height:45rem;">
        <!--   右上角添加按钮     -->
        <template #extra>
          <a-button type="primary" @click="handleAddTemplate">+添加模板</a-button>
        </template>
        <a-list
            class="demo-loadmore-list"

            item-layout="horizontal"
            :data-source="list"
        >
          <template #renderItem="{ item ,index}">
            <a-list-item @click="handleClick(item,index)">
              <template #actions>
                <a-tag v-if="chooseIndex===index" :bordered="false" color="geekblue">选中</a-tag>
                <a key="list-loadmore-edit" @click="handleEdit(item,index)">编辑</a>
                <a style="color: red" @click="showDeleteConfirm('left')">删除</a>
              </template>

              <a-list-item-meta
              >
                <template #title>
                  <div>{{item.templateName}}</div>
                </template>

              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
        <template #actions>
            <!--    分页        -->
          <a-pagination
              v-model:current="currentPageNum"
              :total="totalNum"
              show-less-items
              hideOnSinglePage="true"
              @change="handlePagination"
            />
        </template>
      </a-card>
    </a-col>
    <!-----------------------右侧尺码大小列表-------------------------->
    <a-col :span="16">
      <!--      <a-card title="尺码大小列表" :bordered="false" body-style="height:70vh;">-->
      <a-card title="尺码大小列表" :bordered="false" body-style="height:45rem;">
        <!--     头部   -->
        <template #extra>
          <a-button type="primary" @click="handleSize">+添加码数</a-button>
        </template>
        <!--  内容区      -->
        <a-list
            class="demo-loadmore-list"
            item-layout="horizontal"
            :data-source="sizeList"
        >
          <template #renderItem="{ item ,index}">
            <a-list-item @click="handleSizeClick(item,index)">
              <template #actions>
                <a-tag v-if="chooseSizeIndex===index" :bordered="false" color="purple">选中</a-tag>
                <a key="list-loadmore-edit" @click="handleSizeEdit(item,index)">编辑</a>
                <a style="color: red" @click="showDeleteConfirm('right')">删除</a>
              </template>

              <a-list-item-meta
              >
                <template #title>
                  <div>{{item.sizeMessage}}</div>
                </template>

              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>

        <!-- 尾部分页       -->
        <template #actions>
          <!--    分页        -->
          <a-pagination
              v-model:current="currentPageSize"
              :total="totalSize"
              show-less-items
              hideOnSinglePage="true"
              @change="handleSizePagination"
          />
        </template>
      </a-card>
    </a-col>

  </a-row>
  <!-- 添加尺码模板对话框  -->
  <a-modal bodyStyle="justify-content: center;align-items:center;"  style="top: 200px;" v-model:open="templateAddOpen" title="添加模板" @ok="handleAddOk" >
    <div style="display:flex;justify-content: center;align-items:center;">
      <div style="width: 100%">
        <a-form :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item label="尺码模板名称" v-bind="validateInfos.templateName">
            <a-input v-model:value="templateModel.templateName" />
          </a-form-item>
          <a-form-item label="尺码尺寸编码" v-bind="validateInfos.sizeCode">
            <a-input v-model:value="templateModel.sizeCode" />
          </a-form-item>
        </a-form>
      </div>
    </div>

  </a-modal>
    <!-- 尺码模板编辑对话框 -->
  <a-modal  style="top: 200px" v-model:open="templateEditOpen" title="编辑当前数据" @ok="handleEditOk">
    <a-input type="text" v-model:value="newTemplate" placeholder="输入新的尺码模板"/>
  </a-modal>
  <!-- 尺码模板删除对话框 -->


  <!-- 添加码数对话框 -->
  <a-modal  style="top: 200px" v-model:open="sizeOpen" title="添加码数" @ok="handleSizeAdd">
    <a-form :label-col="labelCol" :wrapper-col="wrapperCol" :model="sizeModel">
      <a-form-item label="添加码数名称" :rules="[{ required: true, message: '输入新的尺码' }]">
        <a-input v-model:value="sizeModel.sizeMessage"  placeholder="输入新的尺码"/>
      </a-form-item>
    </a-form>
  </a-modal>
  <!-- 尺码大小编辑对话框 -->
  <a-modal  style="top: 200px" v-model:open="sizeEditOpen" title="编辑当前数据" @ok="handleSizeEditOk">
    <a-input type="text" v-model:value="newSize" placeholder="输入新的尺码"/>
  </a-modal>
</template>
<script setup>
//----------------------左边尺码模板 ----------------------
import {onMounted, ref, createVNode, reactive, toRaw, watch} from 'vue';
import {message, Modal, Form} from "ant-design-vue";
import {sizeTemplateApi} from '/@/api/business/mes/base/size-template-api.js';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';

const list = ref([]);
// 数据总数
const totalNum = ref(0)
// 当前选中数据下标
const chooseIndex = ref(0)
// 当前选中的数据
const currentItem = ref(null)
// 当前页数
const currentPageNum = ref(1);
// 尺码模板编辑控制
const templateEditOpen = ref(false)
// 尺码模板添加控制
const templateAddOpen = ref(false)
// 尺码模板公用
const newTemplate = ref("")
// 校验表单
const useForm = Form.useForm;
const labelCol = {
  span: 8,
};
const wrapperCol = {
  span: 14,
};
const templateModel = reactive({
  sizeCode: '',
  templateName: '',
  standard: ''
});
const {resetFields, validate, validateInfos} = useForm(
    templateModel,
    reactive({
      templateName: [
        {
          required: true,
          message: '请输入尺码模板名称',
        },
      ],
      sizeCode: [
        {
          required: true,
          message: '请输入尺码尺码编码',
        },
      ],
    }),
);

// 删除对话框
const showDeleteConfirm = (target) => {
  Modal.confirm({
    title: '删除当前数据',
    icon: createVNode(ExclamationCircleOutlined),
    content: '你确定是否删除当前数据?',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      if(target=='left'){
        deleteItem(currentItem.value.id)
      }else{
        deleteSizeItem(currentSizeItem.value.id)
      }

    },
    onCancel() {
      console.log('Cancel');
    },
  });
};
// 异步请求尺码模板数据，并设置为默认第一个
const queryDefaultTmeplateData = async () => {
  let param = {
    pageNum: currentPageNum.value,
    pageSize: 10,
    sortItemList: [{column: 'create_time', isAsc: false}],
  }
  console.log("moren",param)
  try {
    const queyData = await sizeTemplateApi.queryPage(param)
    // 渲染数据
    list.value = queyData.data.list
    // 数据总数
    totalNum.value = queyData.data.total
    console.log("toalNum", totalNum.value, list.value)
  //   设置默认数据
    currentItem.value=list.value[0]
    console.log("1231",currentItem.value)
    chooseIndex.value=0
  } catch (error) {
    message.error("尺码模板获取失败！")
  }
}
// 异步请求尺码模板数据
const queryTmeplateData = async () => {
  // 判断是否为空
  // 如果currentItem为空，渲染第一个默认数据
  // 如果选择了其他分页，默认渲染第一个数据，
  // 如果选择了其他分页，且currentItem不是第一个数据，则不变，刷新数据即可
  let param = {
    pageNum: currentPageNum.value,
    pageSize: 10,
  }
  try {
    const queyData = await sizeTemplateApi.queryPage(param)
    // 渲染数据
    list.value = queyData.data.list
    // 数据总数
    totalNum.value = queyData.data.total
    console.log("toalNum", totalNum.value, list.value)

  } catch (error) {
    message.error("尺码模板获取失败！")
  }
}
// 分页
const handlePagination = (page, pageSize) => {
//   更新分页
  currentPageNum.value = page
  queryDefaultTmeplateData()
}
// 选择列表数据
const handleClick = (item, index) => {
  chooseIndex.value = index
  currentItem.value = item
  querySizeData()
  // message.success(`${item.templateName},${index}`)
}
// 单条数据编辑
const handleEdit = (item, index) => {
  templateEditOpen.value = true
  newTemplate.value=item.templateName
}
// 单条数据删除
const deleteItem = async (id) => {
  try {
    await sizeTemplateApi.delete(id)
  } catch (error) {
    message.error("删除尺码模板失败！")
  } finally {
    await queryTmeplateData()
  }

}

// 添加尺码模板对话框
const handleAddTemplate = () => {
  templateAddOpen.value = true

}
// 异步请求添加尺码模板
const handleAddOk = async () => {
  await validate()
  try {
    await sizeTemplateApi.add(templateModel)
  } catch (error) {
    message.error("添加尺码模板失败！")
  } finally {
    // 清空表单
    templateModel.templateName = ''
    templateModel.sizeCode = ''
    templateModel.standard = ''
    templateAddOpen.value = false
    await queryTmeplateData()
  }
}
// 异步更新尺码模板
const handleEditOk = async () => {
  try {
    let params = {
      id: currentItem.value.id,
      sizeCode: currentItem.value.sizeCode,
      templateName: newTemplate.value,
      standard: currentItem.value.standard
    }
    console.log("this", params)
    await sizeTemplateApi.update(params)
  } catch (error) {
    message.error("更新尺码模板失败！")
  } finally {
    newTemplate.value = ""
    templateEditOpen.value = false
    await queryTmeplateData()
  }
}

onMounted(() => {
  // 请求数据
  queryDefaultTmeplateData()
})

//----------------------右边尺码大小 ----------------------
import {sizeApi} from "/@/api/business/mes/base/size-api.js";
// 尺码尺寸列表
const sizeList = ref([])
// 选中当前尺寸数据下标
const chooseSizeIndex = ref(0)
// 当前尺码大小列表元素
const currentSizeItem=ref(null)
// 当前分页
const currentPageSize=ref(1)
//
const totalSize=ref(0)
// 添加码数对话框控制量
const sizeOpen=ref(false)
const sizeEditOpen=ref(false)
const newSize=ref("")
// 校验
const sizeModel = reactive({
  sizeMessage:'',
});
// 选中当前点击函数
const handleSizeClick=(item,index)=>{
  currentSizeItem.value=item
  chooseSizeIndex.value=index
}
//
// 编辑当前元素
const handleSizeEdit=(item,index)=>{
  sizeEditOpen.value=true
  newSize.value=item.sizeMessage
}
const handleSizeEditOk=async ()=>{
  let param={
    id:currentSizeItem.value.id,
    templateId:currentItem.value.id,
    sizeMessage:newSize.value,
    remark: ''
  }
  try{
    await sizeApi.update(param)
  }catch (error){
    message.error("更新尺码大小失败！")
  }finally {
    newSize.value=""
    sizeEditOpen.value=false
    await querySizeData()
  }

}
// 删除当前元素
// 单条数据删除
const deleteSizeItem = async (id) => {
  try {
    await sizeApi.delete(id)
  } catch (error) {
    message.error("删除尺码模板失败！")
  } finally {
    await querySizeData()
  }

}


// 尺寸分页
// 异步请求尺码大小列表
const querySizeData = async () => {
  console.log("jinlaile1233",currentItem.value)
  let param = {
    pageNum: currentPageSize.value,
    pageSize: 10,
    templateId: currentItem.value.id
  }
  console.log("jinlaile",param)
  try {
    console.log("params123123:",currentItem.value)

    console.log("params123123:",param)
    let queryData = await sizeApi.queryPage(param)
    sizeList.value = queryData.data.list
    // 更新总数据
    totalSize.value=queryData.data.total
    console.log("kk,", sizeList.value)
  } catch (error) {
    message.error("获取尺寸数据失败！")
  }
}
const handleSizePagination = (page, pageSize) => {
//   更新分页
  currentPageSize.value = page
  querySizeData()
}
// 添加码数按钮
const handleSize=()=>{
  sizeOpen.value=true
}
// 确定添加码数
const handleSizeAdd=async ()=>{
  console.log("1313",validateInfos)
  let param={
    templateId:currentItem.value.id,
    sizeMessage:sizeModel.sizeMessage,
    remark:''
  }
  try{
    await sizeApi.add(param)
  }catch (error){
    message.error("添加尺码大小失败！")
  }finally {
    // 清空newSize
    newSize.value=""
    sizeModel.sizeMessage=""
    sizeOpen.value=false
    await querySizeData()
  }

}
// 监听数据，发送尺码大小列表请求
watch(()=>currentItem.value,(newValue)=>{
  querySizeData()
},
    {
      immediate:true
    }
)


</script>
<style scoped lang="less">

</style>

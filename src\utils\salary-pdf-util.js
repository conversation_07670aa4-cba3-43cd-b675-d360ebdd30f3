/*
 * pdf导出工具类
 *
 * @Author:   linwj
 * @Date:      2024-12-14 20:58:49
 * @Copyright  zscbdic
 */
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import {WAGE_TYPE_ENUM} from "/@/constants/business/mes/salary/custom-pay-const.js";
import _ from "lodash";
import dayjs from "dayjs";

pdfMake.vfs = import("/@/assets/fonts/myfonts.js")
//字体设置(全局)
pdfMake.fonts = {
    simhei:{
        normal: 'simhei.ttf', // 黑体
    },
}
/**
 * 导出工资条
 * @param columns 表格标题
 * @param selectedRowList 数据源，即每人的数据
 */
export function exportSalary(columns,selectedRowList){
    //标题
    const titles = [[],[]]
    //数据部分
    const tableBody = []
    //配置项
    const content = []
    // 将操作列和其他项移除
    columns.value.filter(item=> item.dataIndex!=='action' && _.isNil(item.type)).forEach(item => {
        titles[0].push({
            text: item.title,
            index: item.dataIndex,
            rowSpan:2,
            alignment: 'center',
            margin: [0,9,0,0],
        })
        titles[1].push({})
    })
    // 构造其他项
    let payCount = 0;
    let deductCount = 0;
    columns.value.forEach(item => {
        if (!_.isNil(item.type)) {
            if(item.type === WAGE_TYPE_ENUM.ADD.value){
                payCount++;
                titles[1].push({
                    text:item.title,
                    index:item.dataIndex,
                    alignment: 'center',
                })
            }else if(item.type === WAGE_TYPE_ENUM.SUB.value){
                deductCount++;
                titles[1].push({
                    text:item.title,
                    index:item.dataIndex,
                    alignment: 'center',
                })
            }
        }
    })
    for (let i = 0; i < payCount; i++) {
        const title = '应发项';
        if (i === 0) {
            titles[0].push({
                text: title,
                colSpan: payCount,
                alignment: 'center'
            })
        } else {
            titles[0].push({})
        }

    }
    for (let i = 0; i < deductCount; i++) {
        const title = '应扣项';
        if (i === 0) {
            titles[0].push({
                text: title,
                colSpan: deductCount,
                alignment: 'center'
            })
        } else {
            titles[0].push({})
        }
    }


    selectedRowList.forEach(rowData=>{
        tableBody.length = 0  // 清空数组,防止叠加
        titles[0].forEach((titleIndex) => {
            const index = `${titleIndex.index}`;
            if (!_.isNil(rowData[index])) {
                // 基础项
                tableBody.push({text:rowData[index],alignment: 'center', margin: [0,9,0,0]});
            }
        })
        titles[1].forEach((titleIndex) => {
            // 其他项
            const item = rowData.otherAmounts.find(item => item.fieldName === titleIndex.index);
            if (item) {
                console.log(item)
                tableBody.push({text:item.fieldValue,alignment: 'center', margin: [0,9,0,0]});
            }
        })
        content.push(outstandingTitle("工资条",'center',14),)
        content.push({
            style: 'tableExample',  //控制多个表格之间的间距
            table: {
                widths: Array(titles[0].length).fill('*'), // 根据内容自适应宽度
                body: [
                    _.cloneDeep(titles[0]), // 必须深度克隆
                    _.cloneDeep(titles[1]), // 必须深度克隆
                    _.cloneDeep(tableBody)
                ],
            },
        })
    })

    const docDefinition = {
        pageOrientation: 'landscape', //横屏
        content: content,

        styles:{
            tableExample: {
                margin: [0, 5, 0, 15]
            },
        },
        defaultStyle: { //更改默认字体为中文
            font: 'simhei',
            fontSize: 10
        }
    };
    pdfMake.createPdf(docDefinition).download('工资条'); //下载
}

/**
 * 导出结算记录
 * @param headerAndFooter 标题和页脚信息
 * @param columns 标题
 * @param records 报工记录详细
 */
export function exportSettlementRecord(headerAndFooter,columns,records){
    const body = []
    outstandingHeader(body,headerAndFooter,columns.length)
    body.push(_.cloneDeep(columns))
    records.data.forEach(item=>{
        let temp = []
        columns.forEach(e =>{
            if(e.dataIndex==='amount'){
                temp.push((item.price*item.workQuantity).toFixed(2)) // 保留小数点后两位
            }else {
                temp.push(item[`${e.dataIndex}`])
            }
        })
        body.push(_.cloneDeep(temp))
    })
    outstandingFooter(body,headerAndFooter,columns.length)
    pdfMake.fonts = {
        simhei:{
            normal: 'simhei.ttf',
            bold: 'simhei.ttf',
        },
    }
    var docDefinition = {
        content: [
            outstandingTitle('计件工资明细','center',14),
            {
                table: {
                    widths: Array(columns.length-1).fill('auto').concat('*'),
                    body: body,
                },
            },
        ],
        defaultStyle:{
            font: 'simhei',
            fontSize: 10
        }
    };
    pdfMake.createPdf(docDefinition).download("计件工资明细");
}

// 构造表头
function outstandingHeader(body,headerAndFooter,length){
    // 构造length长度的空对象数组
    const tmp = Array(length).fill({})
    const month = headerAndFooter.belongMonth.split("-")
    tmp[0] = { text: '员工姓名：'+headerAndFooter.actualName, alignment: 'left',colSpan:length-1,border:[true,true,false,true]}
    tmp[length-1] = { text: '结算月份：'+month[0]+"-"+month[1], alignment: 'right',colSpan:1,border:[false,true,true,true]}
    body.push(tmp)
}
// 构造页脚
function outstandingFooter(body,headerAndFooter,length){
    const now = dayjs().format("YYYY-MM-DD")
    // 构造length长度的空对象数组
    const tmp = Array(length).fill({})
    tmp[0] = { text: '合计金额：'+headerAndFooter.totalAmount +'\t' +
            '合计数量：'+headerAndFooter.totalNum+'\t'+
            '审核：'+'\t\t'+
            '员工签字：'+'\t\t', alignment: 'left' ,colSpan:length-3,border:[true,true,false,true]}
    tmp[length-3] = { text: '打印时间：'+now, alignment: 'right',colSpan:3,border:[false,true,true,true]}
    body.push(tmp)
}
// 构造标题
function outstandingTitle(text, alignment, fontSize){
    return {
        text: text,
        alignment: alignment,
        fontSize: fontSize,
    }
}
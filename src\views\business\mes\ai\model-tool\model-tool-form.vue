<!--
  * 大模型工具新建编辑模态框
  *
  * @Author:    lyq
  * @Date:      2024-04-27
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="Bean名称" name="beanName">
        <a-input style="width: 100%" v-model:value="form.beanName" placeholder="Bean名称" />
      </a-form-item>
      <a-form-item label="描述" name="description">
        <a-input style="width: 100%" v-model:value="form.description" placeholder="描述" />
      </a-form-item>
      <a-form-item label="分类" name="type">
        <dict-select v-model:value="form.type" keyCode="AI_LLM_TOOL_TYPE" width="100%" />
      </a-form-item>
      <a-form-item label="停用标识" name="enableFlag">
        <a-switch v-model:checked="form.enableFlag" :unCheckedValue="false" :checkedValue="true" />
        <!-- 0启动，1停用" class="custom-switch"-->
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { modelToolApi } from '/@/api/business/mes/ai/model-tool/model-tool-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { dictApi } from '/@/api/support/dict-api.js';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }
  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined,
    beanName: undefined, //Bean名称
    description: undefined, //描述
    enableFlag: true, //停用标识;true启用，false停用
    type: undefined, //大模型工具类型
  };

  let form = reactive({ ...formDefault });

  const rules = {
    beanName: [{ required: true, message: 'Bean名称 必填' }],
    description: [{ required: true, message: '描述 必填' }],
    enableFlag: [{ required: true, message: '停用标识 必填' }],
    type: [{ required: true, message: '分类 必填' }],
  };
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      console.log('提交表单数据:', JSON.stringify(form)); // 添加日志检查提交的数据
      if (form.id) {
        await modelToolApi.update(form);
      } else {
        await modelToolApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  defineExpose({
    show,
  });
</script>

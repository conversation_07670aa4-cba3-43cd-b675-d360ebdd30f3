/**
 * 供应商表 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-03 09:43:14
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const supplierApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/supplier/queryPage', param);
  },
  // 供应商查询全部
  queryAll:(param) => {
    return postRequest('/supplier/all', param);
  },


  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/supplier/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/supplier/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/supplier/delete/${id}`);
  },

};

<!--
  * 裁床单表格
-->
<template>
  <div class="cutBedTable">
    <a-row class="cutBedTableHeader">
    <a-col :span="3" class="cutBedtext">裁床单</a-col>
    <a-col :span="21">
        <a-row>
        <a-col :span="4">
            <span style="margin-right: 10px">行复制:</span>
            <a-switch @change="onCopyRow" v-model:checked="isCopyRow" />
        </a-col>
        <a-col :span="4">
            <span style="margin-right: 10px">列复制:</span>
            <a-switch @change="onCopyCol" v-model:checked="isCopyCol" />
        </a-col>
        </a-row>
    </a-col>
    </a-row>
    <a-row style="height: 550px">
    <a-col :span="3" style="height: 100%">
        <a-list size="large" :data-source="colorData" style="height: 100%; overflow-x: hidden; overflow-y: auto">
        <template #header>
            <div class="list-title">
            <strong>颜色</strong>
            </div>
        </template>
        <template #renderItem="{ item, index }">
            <a-list-item style="display: flex; justify-content: center; align-content: center">
            <a-badge :count="item.clicknum" :offset="[-10, 0]" showZero>
                <a-tag @click="chooseColorTag(item, index)" class="list-item-tag">
                {{ item.value }}
                </a-tag>
            </a-badge>
            </a-list-item>
        </template>
        </a-list>
    </a-col>
    <a-col :span="18">
        <a-table
        size="small"
        :dataSource="colorSizeForm"
        :columns="columns"
        rowKey="id"
        :scroll="{ y: 500 }"
        bordered
        :pagination="false"
        style="height: 90%"
        >
        <template #headerCell="{ column }">
            <template v-if="dataIndexList.some((item) => item.includes(column.dataIndex))">
            <CloseCircleTwoTone two-tone-color="#eb2f96" @click="deleteColumn(column)" />
            {{ column.title }}
            </template>
        </template>
        <template #bodyCell="{ record, column, index }">
            <template v-if="dataIndexList.some((item) => item.includes(column.dataIndex))">
            <a-input placeholder="数量" v-model:value="colorSizeForm[index][column.dataIndex]" style="width: 70px" />
            <span style="margin-left: 5px">件</span>
            <CopyOutlined v-if="isCopyRow" style="margin-left: 5px" @click="copyRow(colorSizeForm, column, index)" />
            <CopyOutlined v-if="isCopyCol" style="margin-left: 5px" @click="copyCol(colorSizeForm, column, index)" />
            </template>
            <template v-if="column.dataIndex === 'option'">
            <div class="smart-table-operate">
                <a-button @click="onDelete(record)" danger type="link">删除</a-button>
            </div>
            </template>
        </template>
        </a-table>
    </a-col>
    <a-col :span="3" style="height: 100%">
        <a-list size="large" :data-source="sizeData" style="height: 100%; overflow-x: hidden; overflow-y: auto">
        <template #header>
            <div class="list-title">
            <strong>尺寸</strong>
            </div>
        </template>
        <template #renderItem="{ item, index }">
            <a-list-item style="display: flex; justify-content: center; align-content: center">
            <a-tag @click="chooseSizeTag(item, index)" class="list-item-tag">
                {{ item }}
            </a-tag>
            </a-list-item>
        </template>
        </a-list>
    </a-col>
    </a-row>
  </div>
</template>
  
<script setup>
  import { ref } from 'vue';
  import {message, Modal} from 'ant-design-vue';
  import { CopyOutlined, CloseCircleTwoTone } from '@ant-design/icons-vue';

  //-----------------------props通信--------------------------------
  const props = defineProps({
    colorData: {
      type: Array,
      default: () => []
    },
    sizeData: {
      type: Array,
      default: () => []
    },
    colorSizeForm: {
      type: Array,
      default: () => []
    }
  })
  const emit = defineEmits(['update:colorSizeForm'])
  //-----------------------表格--------------------------------
  const columnsDefault = [
    {
      title: '颜色',
      dataIndex: 'styleColor',
      fixed: 'left',
      width: 120,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      fixed: 'right',
      align: 'center',
      width: 100,
    },
  ]
  const columns = ref(columnsDefault);
  const indexKey = ref(1); // 列索引
  const dataIndexList = ref([]); //动态列数组
  //-------------------------行复制--------------------------------
  const isCopyRow = ref(false);
  const isCopyCol = ref(false);
  //控制行列开关
  const onCopyRow = (checked) => {
    isCopyRow.value = checked;
    isCopyCol.value = false;
  };
  const onCopyCol = (checked) => {
    isCopyCol.value = checked;
    isCopyRow.value = false;
  };

  const copyRow = (form, column, index) => {
    //获取当前单元格的值
    const currentValue = form[index][column.dataIndex];
    //复制到当前行的所有行
    dataIndexList.value.forEach((dataIndex) => {
      form[index][dataIndex] = currentValue;
    });
    emit('update:colorSizeForm', form);
  };

  const copyCol = (form, column, index) => {
    //获取当前单元格的值
    const currentValue = form[index][column.dataIndex];
    //复制到当前列的所有列
    form.forEach((row) => {
      row[column.dataIndex] = currentValue;
    });
    emit('update:colorSizeForm', form);
  };
  // 根据颜色添加新行到表格中
  const chooseColorTag = (item, index) => {
    item.clicknum += 1;
    const newRow = {
      styleColor: item.value,
      rowNumber: props.colorData.reduce((acc, curritem) => acc + curritem.clicknum, 0), // 计算行号（累加所有颜色的使用次数）
    };
    props.colorSizeForm.push(newRow);
    emit('update:colorSizeForm', props.colorSizeForm);
  };
  // 根据尺寸添加新列到表格中
  const chooseSizeTag = (item, index) => {
    // 在倒数第二列插入新的尺码列
    columns.value.splice(-1, 0, {
      key: indexKey.value,
      title: item, // 尺码标题
      dataIndex: item +'-'+ indexKey.value, // 尺码列唯一dataIndex
      align: 'center',
      width: 150,
    });
    indexKey.value += 1;
    dataIndexList.value = columns.value
      .filter((item) => props.sizeData.some((str) => str.includes(item.title))) // 过滤出包含尺码的列
      .map((item) => item.dataIndex); // 遍历获取尺码列的dataIndex
  };
  // 删除列
  const deleteColumn = (column) => {
    // 从可输入列列表中移除
    columns.value = columns.value.filter((item) => item.dataIndex !== column.dataIndex);
    // 从可输入列列表中移除
    dataIndexList.value = dataIndexList.value.filter((item) => item != column.dataIndex);
    // 删除每一行中该列的数据
    props.colorSizeForm.forEach((item) => {
      delete item[column.dataIndex];
      return item;
    });
    emit('update:colorSizeForm', props.colorSizeForm);
  };
  function onDelete(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        message.success('删除成功');
        const updatedForm = props.colorSizeForm.filter((item) => item !== record);
        emit('update:colorSizeForm', updatedForm);
        props.colorData.forEach((item) => {
          if (item.value === record.styleColor) item.clicknum -= 1;
        });
      },
      cancelText: '取消',
    });
  }
  // 重置数据
  const resetTable = ()=>{
    // 重置颜色数据
    if (props.colorData) {
      props.colorData.forEach(item => {
        if (item.clicknum) {
          item.clicknum = 0;
        }
      });
    }
    // 重置表格数据 并且通知父组建
    emit('update:colorSizeForm', [])
    dataIndexList.value = [];
    indexKey.value = 1;
    columns.value=[...columnsDefault]
  }
  // 暴露方法给父组件
  defineExpose({
    dataIndexList,
    resetTable
  });
</script>

<style>
  .scrollable-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ccc;
  }
  .list-title {
    text-align: center;
    font-size: 16px;
  }
  .list-item-tag {
    font-size: 14px;
    width: 80px;
    line-height: 20px;
    text-align: center;
    white-space: normal; 
    display: -webkit-box; 
    text-overflow: ellipsis; 
  }
  .cutBedTable {
    border: 1px solid #dddfe5;
    border-radius: 10px;
  }
  .cutBedTableHeader {
    height: 50px;
    line-height: 50px;
    text-align: center;
    border-bottom: 1px solid #dddfe5;
  }
  .cutBedtext {
    font-size: 16px;
    font-weight: bold;
    border-right: 1px solid #dddfe5;
  }
</style>
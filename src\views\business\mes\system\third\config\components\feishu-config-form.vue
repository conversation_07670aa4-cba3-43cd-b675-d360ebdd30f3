<template>
  <a-modal
      :title="'编辑'"
      :width="480"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-form-item label="应用ID"  name="appId">
          <a-input style="width: 85%" v-model:value="form.appId" placeholder="应用ID" />
        </a-form-item>
        <a-form-item label="应用密钥"  name="appSecret">
          <a-input-password style="width: 85%" v-model:value="form.appSecret" placeholder="应用密钥" />
        </a-form-item>
        <a-form-item label="重定向地址"  name="redirectUrl">
          <a-input style="width: 85%" v-model:value="form.redirectUrl" placeholder="重定向地址" />
        </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref } from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/src/components/framework/smart-loading';
import { smartSentry } from '/src/lib/smart-sentry';
import { feishuApi } from '/src/api/system/feishu-api';


// 是否显示
const visibleFlag = ref(false);

// 组件ref
const formRef = ref();

// 使用 reactive 创建响应式表单数据
const form = reactive({
    appId: undefined,
    appSecret: undefined,
    redirectUrl: undefined,
});

const rules = {
  appId: [{ required: true, message: '请输入应用ID' }],
  appSecret: [{ required: true, message: '请输入应用密钥' }],
  redirectUrl: [{ required: true, message: '请输入重定向地址' }],
};


// 关闭弹窗
const onClose = () => {
Object.assign(form, {
  appId: undefined,
  appSecret: undefined,
  redirectUrl: undefined,
 });
  visibleFlag.value = false;
};

// 提交表单
const onSubmit = async () => {
    SmartLoading.show();
  try {
    // 表单验证
    await formRef.value.validate();
    await feishuApi.updateFeishuConfig({...form});
    // 关闭弹窗
    message.success('保存成功');
    onClose();
  } catch (error) {
    smartSentry.captureError(error);
    message.error(error.message);
  } finally {
    SmartLoading.hide();
  }
};

// 打开弹窗方法
const open = async () => {
    const res = await feishuApi.getFeishuConfig();
    Object.assign(form,
    {appId: res.data.appId,
    appSecret: res.data.appSecret,
    redirectUrl: res.data.redirectUrl});
    visibleFlag.value = true;
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

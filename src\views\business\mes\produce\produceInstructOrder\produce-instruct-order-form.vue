<!--
  * 生产指令单 模态框
  *
  * @Author:    fkf
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="1500px" :open="visibleFlag" @cancel="onClose" @ok="onSubmit">
    <a-tabs v-model:activeKey="activeKey" tab-position="left">
      <!-- 基本信息 -->
      <a-tab-pane key="basic" tab="基本信息">
        <!-- $event 子组建更新值 -->
        <base-info-form
          ref="refBaseInfoForm"
          :rules="rules"
          v-model:form="form"
          v-model:color-rows="colorRows"
          v-model:dynamic-columns="dynamicColumns"
          @select-item-data="selectItemData"
          @update-total-num="totalNum = $event"
        />
      </a-tab-pane>
      <!-- 其他信息 -->
      <a-tab-pane key="material" tab="物料用量信息">
        <material-form
          ref="refItemForm"
          :value="form.itemList"
          :key="keyData"
          @change="
            (arr) => {
              form.itemList = arr;
            }
          "
        />
      </a-tab-pane>
      <a-tab-pane key="process" tab="工序计划信息" force-render>
        <process-form
          ref="refProcessForm"
          :value="form.processList"
          :total-num="totalNum"
          :key="keyData"
          @change="
            (arr) => {
              form.processList = arr;
            }
          "
        />
      </a-tab-pane>
      <a-tab-pane key="arrange" tab="安排信息">
        <arrange-form
          ref="refArrangeForm"
          :value="form.arrangeList"
          :key="keyData"
          @change="
            (arr) => {
              form.arrangeList = arr;
            }
          "
        />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import BaseInfoForm from './components/base-info-form.vue';
  import MaterialForm from '/src/views/business/mes/produce/produceInstructOrder/components/material-form.vue';
  import ArrangeForm from '/src/views/business/mes/produce/produceInstructOrder/components/arrange-form.vue';
  import ProcessForm from '/src/views/business/mes/produce/produceInstructOrder/components/process-form.vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { itemClothesApi } from '/@/api/business/mes/item/item-clothes.js';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api.js';
  import { SmartLoading } from '/@/components/framework/smart-loading/index.js';
  import { PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
  // ------------------------ 显示与隐藏 ------------------------
  const emit = defineEmits(['reloadList']);
  // 是否显示
  const visibleFlag = ref(false);

  //基本信息表单引用
  const refBaseInfoForm = ref();
  function show(rowData) {
    Object.assign(form, formDefault);
    activeKey.value = 'basic';
    // 编辑
    if (rowData.id) {
      // 单据来源直接赋值0
      form['orderOrigin'] = 0;
      queryDetails(rowData.id);
    }
    nextTick(() => {
      visibleFlag.value = true;
    });
  }
  //-----------------------请求数据 ------------------------
  // 发送异步请求，获取详细数据
  async function queryDetails(id) {
    try {
      let detailsData = await produceInstructOrderApi.getDetails(id);
      Object.assign(form, detailsData.data);
      // 映射物料数据
      form.itemList = reactive(
        detailsData.data.itemList.map((item) => {
          return {
            key: item.orderId,
            ...item,
          };
        })
      );
      // 映射工序计划数据
      form.processList = reactive(
        detailsData.data.processList.map((item) => {
          return {
            key: item.id,
            ...item,
          };
        })
      );
      // 映射安排数据
      form.arrangeList = reactive(
        detailsData.data.arrangeList.map((item) => {
          return {
            key: item.id,
            ...item,
          };
        })
      );
      // 确保clothesList数据正确加载后，手动触发子组建transformDataToColorRows
      nextTick(() => {
        refBaseInfoForm.value.transformDataToColorRows();
        refBaseInfoForm.value.updateTotalNum();
      });
      // 判断生产状态,转化
      changeProduce();
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // 判断生产状态,转化
  function changeProduce() {
    const statusEnum = PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(form.produceStatus);
    form.tempProduceStatus = statusEnum ? statusEnum.desc : '计划';
  }
  // ------------------------ 表单 ------------------------
  //表单数据
  const formDefault = {
    id: undefined, //主键
    instructNumber: undefined, // 单据编号，可为字符串或 undefined，可选
    produceType: '0', // 生产类型，可为字符串或 undefined，可选，0 自产,1 自裁委外，2 整件委外
    customerId: undefined, // 客户 ID，可为整数或 undefined，可选
    customerName: undefined, // 客户名称，可为字符串或 undefined，可选
    salesOrderNumber: undefined, // 销售单号，可为字符串或 undefined，可选
    planStartTime: undefined, // 计划开工日期，可为字符串或 undefined，可选
    planFinishTime: undefined, // 计划完工日期，可为字符串或 undefined，可选
    deliverTime: undefined, // 交货日期，可为字符串或 undefined，可选
    produceStatus: '0', // 生产业务状态，可为字符串或 undefined，可选，0 计划，1 下达，2 开工，3 完工
    priority: '0', // 优先级，可为字符串或 undefined，可选，0 一般,1 紧急,2 非常紧急
    orderOrigin: '0', // 单据来源，可为字符串或 undefined，可选，0 直接下达
    itemId: undefined, // 物料 ID，可为整数或 undefined，可选
    itemNumber: undefined, // 物料编号，可为字符串或 undefined，可选
    model: undefined, // 规格型号，可为字符串或 undefined，可选
    itemName: undefined, // 物料名称，可为字符串或 undefined，可选
    category: 0, // 类型，可为字符串或 undefined，可选，0 半成品 1 成品
    attribute: 0, // 属性，可为字符串或 undefined，可选，0 面料，1 辅料
    unitId: undefined, // 单位 ID，可为整数或 undefined，可选
    unitName: undefined, // 单位名称，可为字符串或 undefined，可选
    name: undefined, // 指令单名称，可为字符串或 undefined，可选
    remark: undefined, // 备注，可为字符串或 undefined，可选
    imgUrl: [], // 图片，可选
    itemList: [], // 物料列表
    selectItem: undefined, // 物料选择
    processList: [], // 工序计划
    clothesList: [], // 生产列表
    arrangeList: [], // 安排列表
    tempProduceStatus: '计划', //  临时生产状态
  };
  let form = reactive({ ...formDefault });
  form.itemList = reactive([]);
  form.processList = reactive([]);
  form.arrangeList = reactive([]);
  // 定义表单规则
  const rules = {
    produceType: [{ required: true, message: '生产类型 必填' }],
    priority: [{ required: true, message: '优先级 必填' }],
    orderOrigin: [{ required: true, message: '订单来源 必填' }],
    itemNumber: [{ required: true, message: '物料编号 必填' }],
    planStartTime: [{ required: true, message: '计划工期 必填' }],
    deliverTime: [{ required: true, message: '交货日期 必填' }],
    produceStatus: [{ required: true, message: '生产业务状态 必填' }],
  };
  // ------------------------ 提交和关闭 ------------------------
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      //    校验基本信息表单
      await refBaseInfoForm.value.validate();
      //   校验工序计划
      if (form.processList.length != 0) {
        nextTick(() => {
          // 如果被调用组件才执行
          if (refProcessForm.value) {
            refProcessForm.value.handleValidate();
          }
        });
      }
      //   校验安排信息
      if (form.arrangeList.length != 0) {
        nextTick(() => {
          // 如果被调用组件才执行
          if (refArrangeForm.value) {
            refArrangeForm.value.handleValidate();
          }
        });
      }
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }
  // 新建、编辑API
  async function save() {
    //过滤无效组合
    form.clothesList = form.clothesList.filter((item) => item.itemId && item.num > 0);
    SmartLoading.show();
    try {
      if (form.id) {
        await produceInstructOrderApi.update(form);
        message.success('更新成功');
      } else {
        await produceInstructOrderApi.add(form);
        message.success('添加成功');
      }
      emit('reloadList');

      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  function onClose() {
    // 重置所有表单数据到初始状态
    form = reactive({
      ..._.cloneDeep(formDefault),
      itemList: reactive([]),
      processList: reactive([]),
      arrangeList: reactive([]),
      clothesList: reactive([]),
    });
    // 重置所有子组件
    refProcessForm.value?.reset();
    refArrangeForm.value?.reset();
    refItemForm.value?.reset();
    // 重置表格数据
    refBaseInfoForm.value?.resetTableData();

    // 重置总数量
    totalNum.value = 0;
    emit('reloadList');
    visibleFlag.value = false;
  }
  // ------------------------ 成衣信息 ------------------------
  // 成衣选中项赋值
  function selectItemData(data) {
    form.itemNumber = data.number; //物料编号
    form.itemId = data.id; // 物料ID
    form.itemName = data.name; // 物料名称
    form.unitName = data.unitName; // 单位名称
    form.imgUrl = data.imgUrl; //   图片
    form.category = data.category; //   类型
    form.attribute = data.attribute; // 属性
    form.model = data.model; // 规格型号
    form.unitId = data.unitId; //  单位ID
    form.remark = data.remark; //   备注
    form.itemList = [];
    form.processList = [];
    form.arrangeList = [];
    // 修改key触发子组件更新
    keyData.value = keyData.value + 1;
    queryData();
  }
  // 查询列表信息
  async function queryData() {
    try {
      let queryResult = await itemClothesApi.queryskuList(form.itemNumber);
      form.clothesList = queryResult.data;
      //   补充添加num和itemId
      form.clothesList.forEach((item) => {
        item.num = 0;
        item.itemId = item.id;
      });
      // 更新动态列
      refBaseInfoForm.value.transformDataToColorRows();
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  // ------------------------ 物料总量 ------------------------
  const totalNum = ref(0);
  // -------------------------- 商品列表信息 -----------------------
  const dynamicColumns = ref([]);
  // 颜色行数据
  const colorRows = ref([]);
  // -------------------------- 成衣，布料，其他物料,其他信息 -----------------------
  // 定义key强制子组件视图刷新
  const keyData = ref(0);
  // 基本信息标识
  // const formRef = ref();
  // 安排信息标识
  const refArrangeForm = ref(null);
  // 工序计划标识
  const refProcessForm = ref(null);
  // 物料信息标识
  const refItemForm = ref(null);
  // 分页标识
  const activeKey = ref('basic');
  defineExpose({
    show,
  });
</script>

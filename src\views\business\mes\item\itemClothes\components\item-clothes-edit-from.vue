<!--
  * 编辑成衣
  *
  * @Author:    fkf
  * @Date:      2025-01-08
  * @Copyright  zscbdic
-->
<template>
  <a-modal title="编辑成衣" width="1000px" :open="visibleFlag" @cancel="onClose" @ok="onSubmit">
    <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
      <div><strong class="title">物料基本信息</strong></div>
    </a-row>
    <!--基本信息表-->
    <a-form :rules="rules" :label-col="{ span: 6 }" :model="form">
      <a-row>
        <a-col :span="12">
          <a-form-item label="物料编号" name="number">
            <a-input style="width: 90%" placeholder="物料编号|可不填写" v-model:value="form.number" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="物料名称" name="name">
            <a-input style="width: 90%" placeholder="物料名称" v-model:value="form.name" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="物料分类" name="typeId">
            <a-cascader style="width: 90%" :options="itemTypeTree" placeholder="请选择物料分类" v-model:value="form.typeId" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="单位" name="unitId">
            <a-select style="width: 90%" :options="unitOption" placeholder="请选择" v-model:value="form.unitId" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="规格型号" name="model">
            <a-input style="width: 90%" placeholder="规格型号" v-model:value="form.model" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="供应商" name="supplierId">
            <a-select style="width: 90%" :options="supplierOption" placeholder="请选择" v-model:value="form.supplierId" allowClear />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="季度" name="seasonId">
            <a-cascader style="width: 90%" :options="seasonTree" placeholder="请选择" v-model:value="form.seasonId" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="款式" name="styleId">
            <a-cascader style="width: 90%" placeholder="请选择" :options="itemStyleTree" v-model:value="form.styleId" />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12">
              <a-form-item label="类型"  name="category" >
                <a-radio-group  button-style="solid">
                  <a-radio-button :value="0">半成品</a-radio-button>
                  <a-radio-button :value="1">成品</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col> -->
        <a-col :span="12">
          <a-form-item label="部位" name="partList">
            <part-select v-model:value="form.partList" width="90%" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否停用" name="enableFlag">
            <a-radio-group button-style="solid" v-model:value="form.enableFlag">
              <a-radio-button :value="false">启用</a-radio-button>
              <a-radio-button :value="true">停用</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
        <div><strong class="title">颜色尺码信息</strong></div>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item label="sku编号" name="skuNumber">
            <a-input style="width: 90%" placeholder="请输入sku编码" v-model:value="form.skuNumber" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="颜色" name="styleColor">
            <a-select style="width: 90%" :options="styleColorOption" placeholder="请选择" v-model:value="form.styleColor" allowClear />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="尺码" name="size">
            <sizeSelect v-model:value="form.size" width="90%" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="价格" name="price">
            <a-input style="width: 90%" placeholder="请输入价格" v-model:value="form.price" />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="图片" name="imgUrl">
            <file-upload :default-file-list="form.imgUrl || []" @change="handleFileChange" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { message } from 'ant-design-vue';
  import { unitApi } from '/@/api/business/mes/base/unit-api';
  import { itemTypeApi } from '/@/api/business/mes/item/item-type-api';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { seasonApi } from '/@/api/business/mes/base/season-api';
  import { styleApi } from '/@/api/business/mes/base/style-api';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import { itemClothesApi } from '/@/api/business/mes/item/item-clothes.js';
  import { styleColorApi } from '/@/api/business/mes/base/style-color-api.js';
  import sizeSelect from '/@/components/business/mes/base/size-select/index.vue';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';
  //表单数据
  const formDefault = {
    id: undefined,
    typeId: undefined, //物料分类id;
    name: undefined, //物料名称
    supplierId: undefined, //供应商id;
    model: undefined, //规格型号
    number: null, //spu编号
    unitId: undefined, //单位id;关联t_unit
    seasonId: undefined, //季度id
    styleId: undefined, //款式品类id
    enableFlag: false, //停用标识;0启用，1停用
    size: undefined,
    styleColor: undefined,
    price: undefined,
    skuNumber: undefined,
    imgUrl: undefined,
    partList: [],
    // attribute: '', //成衣
  };
  const form = reactive({ ...formDefault });
  // ------------------------ 必填数据 ------------------------
  const rules = {
    name: [{ required: true, message: '请输入物料名称' }],
    unitId: [{ required: true, message: '请选择单位' }],
    enableFlag: [{ required: true, message: '请选择启用状态' }],
    skuNumber: [{ required: true, message: '请输入sku编号' }],
  };
  const itemTypeTree = ref([]); //物料分类
  const itemStyleTree = ref([]); //款式
  const unitOption = ref([]);
  const supplierOption = ref([]);
  const seasonTree = ref([]);
  const styleColorOption = ref([]);
  // ------------------------ 获取数据 ------------------------
  async function queryItemType() {
    let queryResult = await itemTypeApi.queryCategoryTree({});
    itemTypeTree.value = queryResult.data;
  }

  async function queryUnitList() {
    let queryResult = await unitApi.querySelect({});
    unitOption.value = queryResult.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }

  async function querySupplierList() {
    let queryResult = await supplierApi.queryAll({});
    supplierOption.value = queryResult.data.map((item) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
  }
  async function querySeasonTree() {
    let queryResult = await seasonApi.queryTree({});
    seasonTree.value = queryResult.data;
  }

  async function queryItemStyle() {
    let queryResult = await styleApi.queryTree({});
    itemStyleTree.value = queryResult.data;
  }
  async function queryColorStyle() {
    let queryResult = await styleColorApi.queryAll({});
    styleColorOption.value = queryResult.data.map((item) => {
      return {
        value: item.styleColor,
        label: item.styleColor,
      };
    });
  }

  //图片数据渲染和图片上传
  const handleFileChange = (event) => {
    form.imgUrl = Array.isArray(event) ? event : [event];
  };

  // 是否显示
  const visibleFlag = ref(false);
  // ------------------------ 绑定数据 ------------------------
  async function show(id) {
    visibleFlag.value = true;
    SmartLoading.show();
    const res = await itemClothesApi.getById({
      id: id,
    });
    // 先获取原始数据并且处理级联选择器的路径
    const originalData = res.data;
    if (originalData.styleId) {
      originalData.styleId = await findCascaderPath(itemStyleTree.value, originalData.styleId);
    }
    if (originalData.typeId) {
      originalData.typeId = await findCascaderPath(itemTypeTree.value, originalData.typeId);
    }
    if (originalData.seasonId) {
      originalData.seasonId = await findCascaderPath(seasonTree.value, originalData.seasonId);
    }
    originalData.partList = originalData.partList || [];
    Object.assign(form, originalData);
    SmartLoading.hide();
  }
  // 递归查找级联选择器的完整路径
  function findCascaderPath(tree, id, path = []) {
    for (const node of tree) {
      const current = [...path, node.value];
      if (node.value === id) return current;
      if (node.children?.length) {
        const found = findCascaderPath(node.children, id, current);
        if (found) return found;
      }
    }
    return null;
  }

  function onClose() {
    resetForm();
    visibleFlag.value = false;
    emit('reload');
  }
  //------------------------提交 ------------------------
  //清空提交数据
  const resetForm = () => {
    Object.assign(form, formDefault);
  };

  const onSubmit = async () => {
    // 处理级联选择器的值
    const submitData = {
      ...form,
      // 取最后一个值作为ID
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
      seasonId: form.seasonId ? form.seasonId[form.seasonId.length - 1] : undefined,
      styleId: form.styleId ? form.styleId[form.styleId.length - 1] : undefined,
      partList: Array.isArray(form.partList) ? form.partList : [], // 确保是数组，即使为空
    };

    console.log('提交的数据：', submitData); // 添加日志，检查提交的数据

    await itemClothesApi.update(submitData);
    message.success('提交成功');
    resetForm();
    emit('reload');
    visibleFlag.value = false;
  };
  onMounted(() => {
    queryItemType();
    queryUnitList();
    querySupplierList();
    querySeasonTree();
    queryItemStyle();
    queryColorStyle();
  });

  defineExpose({
    show,
  });
  const emit = defineEmits(['reload']);
</script>
<style>
  .title {
    margin-left: 40px;
  }
</style>

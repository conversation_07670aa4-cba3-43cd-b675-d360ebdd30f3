<!--
  * 铺布任务
  *
  * @Author:    cjm
  * @Date:      2025-06-21 15:24:27
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      :width="1500"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 }">
      <a-row>
        <a-col :span="6">
          <a-form-item label="生产指令单" name="produceInstructOrderId">
            <produce-instruct-order-select v-model:value="form.produceInstructOrderId" :placeholder="请选择生产指令单"
                                           :width="'90%'"
                                           @change="onProduceInstructOrderChange"
                                           :disabled="!!form.id"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="物料" name="materialId" disabled>
            <MaterialSelect v-model:value="form.materialId" placeholder="请选择物料" :width="'90%'" :disabled="true"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="床次" name="cutNum">
            <bed-num-input v-model:value="form.cutNum" placeholder="床次" :produceInstructOrderId="form.produceInstructOrderId" :width="'90%'"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="车间" name="workshopId">
            <workshop-select v-model:value="form.workshopId" placeholder="请选择车间" style="width: 90%"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="部位" name="partList">
            <part-list-select v-model:value="form.partList" placeholder="请选择部位" style="width: 90%" :produce-instruct-order-id="form.produceInstructOrderId"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="生产小组" name="teamId">
            <produce-team-select v-model:value="form.teamId" placeholder="生产小组" style="width: 90%"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="设备" name="equipmentId">
            <equipment-select v-model:value="form.equipmentId" placeholder="设备" style="width: 90%"
                              :workshopId="form.workshopId"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="计划开始时间" name="planBeginTime">
            <a-date-picker style="width: 90%;" :show-time="{ format: 'HH:mm' }" @change="onChangePlanBeginTime"
                           v-model:value="form.planBeginTime" value-format="YYYY-MM-DD HH:mm:ss"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="计划时长">
            <a-select v-model:value="selectedDuration" placeholder="选择时长" style="width: 90%;"
                      @change="onDurationChange" allowClear>
              <a-select-option v-for="item in SPREAD_TASK_DURATION_ENUM.getOptions()" :key="item.value"
                               :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="计划结束时间" name="planEndTime">
            <a-date-picker style="width: 90%;" :show-time="{ format: 'HH:mm' }" @change="onChangePlanEndTime"
                           v-model:value="form.planEndTime" value-format="YYYY-MM-DD HH:mm:ss"/>
          </a-form-item>
        </a-col>
        <!-- 生产要求 -->
        <a-col :span="6">
          <a-form-item label="生产要求" name="produceRequire">
            <a-textarea v-model:value="form.produceRequire" placeholder="生产要求" style="width: 90%"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="面料检查校验" name="materialCheckFlag">
            <a-switch v-model:checked="form.materialCheckFlag" checked-children="开" un-checked-children="关"/>
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>

    <!-- 任务详情标签页 -->
    <a-tabs v-model:activeKey="activeKey" style="margin-top: 16px;">
      <!-- 颜色尺码信息 -->
      <a-tab-pane key="colorSize" tab="颜色尺码信息" v-if="form.produceInstructOrderId">
        <a-card title="计划颜色尺码配置" size="small">
          <a-table
              :columns="dynamicColumns"
              :data-source="dynamicTableData"
              :pagination="false"
              bordered
              size="small"
              :scroll="{ x: 480 + sizeList.length * 100 }"
              row-key="color"
          >
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex.startsWith('size_') && form.taskSizes?.[column.sizeIndex]">
                <div style="text-align: center;">
                  <div style="margin-bottom: 4px;">
                    <!-- <a-input-number
                        v-model:value="form.taskSizes[column.sizeIndex].planRatio"
                        addon-before="比例:"
                        :controls="false"
                        :min="0"
                        style="width: 100%"
                    /> -->
                    <input-number-keyboard v-model:value="form.taskSizes[column.sizeIndex].planRatio" :unit="'比例'" :input-style="{ width: '100%' }" />
                  </div>
                  <div>{{ column.title }}</div>
                </div>
              </template>
            </template>
            <template #bodyCell="{ column, index }">
              <template
                  v-if="['planLayer', 'planLength'].includes(column.dataIndex) && form.taskColors?.[index]">
                <!-- <a-input-number
                    v-model:value="form.taskColors[index][column.dataIndex]"
                    :min="0"
                    style="width: 100%"
                /> -->
                <input-number-keyboard v-model:value="form.taskColors[index][column.dataIndex]" :unit="column.dataIndex === 'planLayer' ? '层' : '厘米'" :input-style="{ width: '100%' }" />
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 实际数据表格 - 仅编辑时显示 -->
        <a-card title="实际颜色尺码配置" size="small" v-if="form.id" style="margin-top: 16px;">
          <a-table
              :columns="realDataColumns"
              :data-source="realTableData"
              :pagination="false"
              bordered
              size="small"
              :scroll="{ x: 480 + sizeList.length * 100 }"
              row-key="color"
          >
            <template #headerCell="{ column }">
              <template v-if="column.dataIndex.startsWith('size_') && form.taskSizes?.[column.sizeIndex]">
                <div style="text-align: center;">
                  <div style="margin-bottom: 4px;">
                    <a-input-number
                        v-model:value="form.taskSizes[column.sizeIndex].realRatio"
                        addon-before="比例:"
                        :controls="false"
                        :min="0"
                        style="width: 100%"
                        :disabled="true"
                    />
                  </div>
                  <div>{{ column.title }}</div>
                </div>
              </template>
            </template>
            <template #bodyCell="{ column, index }">
              <template
                  v-if="['realLayer', 'realLength'].includes(column.dataIndex) && realTableData?.[index]">
                <a-input-number
                    v-model:value="realTableData[index][column.dataIndex]"
                    :min="0"
                    style="width: 100%"
                    :disabled="true"
                />
              </template>
              <template
                  v-else-if="column.dataIndex.startsWith('size_') && realTableData?.[index]">
                <a-input-number
                    v-model:value="realTableData[index][column.dataIndex]"
                    :min="0"
                    style="width: 100%"
                    :disabled="true"
                />
              </template>
            </template>
          </a-table>
        </a-card>
      </a-tab-pane>
        <!-- 面料信息 -->
      <a-tab-pane key="materials" tab="面料信息" v-if="form.materialCheckFlag">
        <a-table
            :columns="materialColumns"
            :data-source="itemList"
            :pagination="false"
            bordered
            size="small"
            row-key="materialId"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :scroll="{ x: 800 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'action'">
              <div class="smart-table-operate">
                <a-button type="link" danger @click="handleDeleteMaterial(index)">删除</a-button>
              </div>
            </template>
            <template v-if="column.dataIndex === 'materialId'">
              <ClothSelect v-model:value="record.materialId" placeholder="请选择布料" :disabled="true"/>
            </template>
            <template v-if="column.dataIndex === 'lotCheckFlag'">
              <a-switch v-model:checked="record.lotCheckFlag" checked-children="是" un-checked-children="否"/>
            </template>
            <template v-if="column.dataIndex=== 'lotId'">
              <lotSelect v-model:value="record.lotId" placeholder="请选择批次号" :materielId="record.materialId"/>
            </template>
            <template v-if="column.dataIndex === 'index'">
              <span>{{index+1}}</span>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit(SPREAD_TASK_STATUS_ENUM.PLAN.value)">保存</a-button>
        <a-button type="primary" @click="onSubmit(SPREAD_TASK_STATUS_ENUM.ISSUE.value)">保存并下发</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick, watch, computed} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {spreadTaskApi} from '/@/api/business/mes/tailor/spread-task-api.js';
import {smartSentry} from '/@/lib/smart-sentry';
import produceInstructOrderSelect from '/@/components/business/mes/produce/produce-instruct-order-select/index.vue'
import MaterialSelect from '/@/components/business/mes/item/material-all-select/index.vue';
import ClothSelect from '/@/components/business/mes/item/cloth-select/index.vue';
import lotSelect from '/@/components/business/mes/stock/lot/index.vue';
import {SPREAD_TASK_STATUS_ENUM} from '/@/constants/business/mes/tailor/spread-task-const.js';
import {SPREAD_TASK_DURATION_ENUM} from '/@/constants/business/mes/tailor/spread-task-duration-const.js';
import workshopSelect from '/@/components/business/mes/factory/workshop-select/index.vue';
import produceTeamSelect from '/@/components/business/mes/factory/produce-team-select/index.vue';
import equipmentSelect from '/@/components/business/mes/equipment/equipment-select/index.vue';
import {produceInstructOrderApi} from '/@/api/business/mes/produce/produce-instruct-order-api.js';
import partListSelect from '/@/components/business/mes/base/part-select/index.vue';
import {produceInstructOrderClothesApi} from "/@/api/business/mes/produce/produce-instruct-order-clothes-api.js";
import {ITEM_ATTRIBUTE_ENUM} from "/@/constants/business/mes/item/item-const.js";
import bedNumInput from '/@/components/business/mes/tailor/cut-bed-detail/index.vue';
import inputNumberKeyboard from '/@/components/business/mes/common/number-input-keyboard/index.vue';

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

// 标签页激活状态
const activeKey = ref('colorSize');

// 面料信息表格列配置
const materialColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
  },
  {
    title: '物料编号',
    dataIndex: 'materialSpuNumber',
    key: 'materialSpuNumber',
    width: 120,
  },
  {
    title: '布料',
    dataIndex: 'materialId',
    key: 'materialId',
    width: 120,
  },
  {
    title: '批次校验',
    dataIndex: 'lotCheckFlag',
    key: 'lotCheckFlag',
    width: 100,
  },
  {
    title: '批次号',
    dataIndex: 'lotId',
    key: 'lotId',
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    align: 'center',
    width: 50,
    fixed: 'right',
  },
];

async function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    const res = await spreadTaskApi.queryById(rowData.id);
    Object.assign(form, res.data);

    form.taskColors = form.taskColors || [];
    form.taskSizes = form.taskSizes || [];
    form.taskMaterials = form.taskMaterials || [];
    form.taskPlanColors = form.taskPlanColors || [];
    form.taskRealColors = form.taskRealColors || [];

    // 使用taskPlanColors来获取颜色列表，如果没有则使用taskColors
    const planColors = form.taskPlanColors.length > 0 ? form.taskPlanColors : form.taskColors.filter(item => item.originType === 'PLAN' || !item.originType);
    colorList.value = planColors.map(item => item.color);
    sizeList.value = form.taskSizes.map(item => item.size);

    // 编辑时更新选中状态
    updateSelectedRowKeys();
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value?.clearValidate();
  });
}


function onClose() {
  Object.assign(form, formDefault);
  selectedDuration.value = undefined;
  visibleFlag.value = false;
  activeKey.value= 'colorSize';
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  deletedFlag: undefined, //删除标识;0未删除，1删除
  number: undefined, //编号
  materialId: undefined, //物料ID
  produceInstructOrderId: undefined, //生产指令单ID
  partList: [],//部位列表
  cutNum: undefined, //床次
  workshopId: undefined, //车间ID
  teamId: undefined, //生产小组ID
  taskStatus: undefined, //任务状态
  produceRequire: undefined, //生产要求
  equipmentId: undefined, //设备ID
  materialCheckFlag: false, //面料检查校验
  planBeginTime: undefined, //计划开始时间
  planEndTime: undefined, //计划结束时间
  taskMaterials: [],//面料信息
  taskColors: [],  //颜色信息
  taskSizes: [],  //尺码信息
  taskPlanColors: [], //计划颜色信息
  taskRealColors: []  //实际颜色信息
};

let form = reactive({...formDefault});

const rules = {
  id: [{required: true, message: '主键 必填'}],
  deletedFlag: [{required: true, message: '删除标识;0未删除，1删除 必填'}],
  // number: [{ required: true, message: '编号 必填' }],
  produceInstructOrderId: [{required: true, message: '生产指令单ID 必填'}],
  cutNum: [{required: true, message: '床次 必填'}],
  workshopId: [{required: true, message: '车间ID 必填'}],
  taskStatus: [{required: true, message: '任务状态 必填'}],
  // materialCheckFlag: [{ required: true, message: '面料检查校验 必填' }],
};

// 点击确定，验证表单
async function onSubmit(taskStatus) {
  try {
    await formRef.value.validateFields();
    form.taskStatus = taskStatus;
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await spreadTaskApi.update(form);
    } else {
      await spreadTaskApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

function onChangePlanBeginTime(dateString) {
  form.planBeginTime = dateString;
  // 如果已选择时长，自动计算结束时间
  if (selectedDuration.value && dateString) {
    calculateEndTime();
  }
}

function onChangePlanEndTime(dateString) {
  form.planEndTime = dateString;
}

// 时长选择变化处理
function onDurationChange(duration) {
  selectedDuration.value = duration;
  // 如果已有开始时间，自动计算结束时间
  if (form.planBeginTime && duration) {
    calculateEndTime();
  }
}

// 根据开始时间和时长计算结束时间
function calculateEndTime() {
  if (!form.planBeginTime || !selectedDuration.value) {
    return;
  }

  try {
    // 解析开始时间
    const startTime = new Date(form.planBeginTime);
    if (isNaN(startTime.getTime())) {
      return;
    }

    // 添加分钟数
    const endTime = new Date(startTime.getTime() + selectedDuration.value * 60 * 1000);

    // 格式化为 YYYY-MM-DD HH:mm:ss 格式
    const year = endTime.getFullYear();
    const month = String(endTime.getMonth() + 1).padStart(2, '0');
    const day = String(endTime.getDate()).padStart(2, '0');
    const hours = String(endTime.getHours()).padStart(2, '0');
    const minutes = String(endTime.getMinutes()).padStart(2, '0');
    const seconds = String(endTime.getSeconds()).padStart(2, '0');

    form.planEndTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('计算结束时间失败:', error);
  }
}

// 删除面料
function handleDeleteMaterial(index) {
  form.taskMaterials.splice(index, 1);
}

// 时长选择
const selectedDuration = ref(undefined);

// 面料表格选择
const selectedRowKeys = ref([]);

// 处理面料选择变化
const onSelectChange = (selectedKeys) => {
  selectedRowKeys.value = selectedKeys;
  // 根据选中的keys更新form.taskMaterials
  form.taskMaterials = itemList.value.filter(item => selectedKeys.includes(item.materialId));

};

// 更新选中状态（用于编辑时回显）
const updateSelectedRowKeys = () => {
  const selectedIds = form.taskMaterials
    .filter(material => itemList.value.some(item => item.materialId === material.materialId))
    .map(material => material.materialId);

  selectedRowKeys.value = selectedIds;
};

// 动态列配置
const dynamicColumns = computed(() => {
  const columns = [{title: '颜色', dataIndex: 'color', width: 80, fixed: 'left'}];

  // 添加尺码列
  sizeList.value.forEach((size, index) => {
    columns.push({
      title: size,
      dataIndex: `size_${size}`,
      width: 140,
      sizeIndex: index
    });
  });

  // 添加计划数据列
  columns.push(
      {title: '计划层数', dataIndex: 'planLayer', width: 140},
      {title: '计划长度', dataIndex: 'planLength', width: 140}
  );

  return columns;
});

// 实际数据表格列配置
const realDataColumns = computed(() => {
  const columns = [{title: '颜色', dataIndex: 'color', width: 80, fixed: 'left'}];

  // 添加尺码列
  sizeList.value.forEach((size, index) => {
    columns.push({
      title: size,
      dataIndex: `size_${size}`,
      width: 140,
      sizeIndex: index
    });
  });

  // 添加实际数据列
  columns.push(
      {title: '实际层数', dataIndex: 'realLayer', width: 140},
      {title: '实际长度', dataIndex: 'realLength', width: 140}
  );

  return columns;
});

// 计划数据表格
const dynamicTableData = computed(() => {
  if (!colorList.value.length) return [];

  return colorList.value.map((color, index) => {
    const row = {color};

    // 计算尺码数量
    sizeList.value.forEach((size, sizeIndex) => {
      const planLayer = form.taskPlanColors?.[index]?.planLayer || form.taskColors?.[index]?.planLayer || 0;
      const planRatio = form.taskSizes?.[sizeIndex]?.planRatio || 0;
      row[`size_${size}`] = Math.round(planLayer * planRatio);
    });

    return row;
  });
});

// 实际数据表格 - 只显示taskRealColors中存在的颜色
const realTableData = computed(() => {
  if (!form.id || !form.taskRealColors?.length) return [];

  return form.taskRealColors.map((realColorData) => {
    const row = {color: realColorData.color};

    // 计算尺码数量 - 使用实际层数 * 实际比例
    sizeList.value.forEach((size, sizeIndex) => {
      const realLayer = realColorData.realLayer || 0;
      const realRatio = form.taskSizes?.[sizeIndex]?.realRatio || 0;
      row[`size_${size}`] = realLayer > 0 ? Math.round(realLayer * realRatio) : 0;
    });

    // 添加实际层数和长度
    row.realLayer = realColorData.realLayer;
    row.realLength = realColorData.realLength;

    return row;
  });
});


const sizeList = ref([]);
const colorList = ref([]);
const itemList = ref([])

function initColorSizeData() {
  //1
  form.taskColors = form.taskColors || [];
  form.taskSizes = form.taskSizes || [];
  form.taskMaterials = form.taskMaterials || [];

  // 重建数组确保顺序一致
  form.taskColors = colorList.value.map(color => {
    const existing = form.taskColors.find(item => item.color === color);
    return existing || {color, planLayer: 0, planLength: 0, realLayer: null, realLength: null};
  });

  form.taskSizes = sizeList.value.map(size => {
    const existing = form.taskSizes.find(item => item.size === size);
    return existing || {size, planRatio: 0};
  });

  // 更新itemList数据
  itemList.value = itemList.value.map(item => {
    return {materialId: item.itemId, materialSpuNumber: item.itemNumber, lotCheckFlag: false, lotId: undefined};
  });
  // 更新选中状态（编辑时回显）
  updateSelectedRowKeys();
}

async function onProduceInstructOrderChange(value) {
  try {
      const [resSize, resColor, resBaseInfo,resItemList] = await Promise.all([
      produceInstructOrderClothesApi.queryClothesSizeList(value.id),
      produceInstructOrderClothesApi.queryClothesColorList(value.id),
      produceInstructOrderApi.queryBaseInfo(value.id),
      produceInstructOrderApi.getDetails(value.id)
    ]);

    sizeList.value = resSize.data || [];
    colorList.value = resColor.data || [];
    itemList.value = resItemList.data.itemList?.filter(item => item.itemAttribute === ITEM_ATTRIBUTE_ENUM.CLOTH.value) || [];
    form.materialId = resBaseInfo.data?.itemId;
    initColorSizeData();
  } catch (err) {
    smartSentry.captureError(err);
  }
}

defineExpose({
  show,
});
</script>

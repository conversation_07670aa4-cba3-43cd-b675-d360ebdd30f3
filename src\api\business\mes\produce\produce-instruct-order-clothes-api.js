/**
 * 指令单成衣信息 api 封装
 *
 * @Author:    <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */

import { getRequest, postRequest } from '/@/lib/axios';

export const produceInstructOrderClothesApi = {
  /**
   *分页查询指令单成衣信息
   */
  queryPage: (params) => {
    return postRequest(`/produceInstructOrderClothes/queryPage`, params);
  },
  /**
   * 获取指令单下所有成衣尺码  <AUTHOR>
   */
  queryClothesSizeList: (id) => {
    return getRequest(`/produceInstructOrderClothes/sizeList/${id}`);
  },
  /**
   * 获取指令单下所有成衣颜色  <AUTHOR>
   */
  queryClothesColorList: (id) => {
    return getRequest(`/produceInstructOrderClothes/styleColorList/${id}`);
  },
  /**
   * 获取指令单下所有成衣信息  <AUTHOR>
   */
  queryClotheList: (id) => {
    return getRequest(`/produceInstructOrderClothes/list/${id}`);
  },

};

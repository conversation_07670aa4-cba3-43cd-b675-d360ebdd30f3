<!--
  * 基本信息
  *
  * @Author:    wxx
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <div>
    <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
      <div><strong>物料基本信息</strong></div>
    </a-row>

    <a-form ref="formRef" :model="localForm" :rules="rules">
      <!-- 物料信息展示区 -->
      <a-row>
        <a-col :span="16" :offset="1">
          <a-row :gutter="[, 32]">
            <a-col :span="12">
              <span style="color: #909399; margin-right: 10px; font-size: 16px">物料编号</span>
              <span :value="localForm.itemNumber" style="color: #303133; font-size: 16px" v-if="localForm.itemNumber">{{
                localForm.itemNumber
              }}</span>
              <span style="color: #f56c6c" v-else>您还未选择物料</span>
            </a-col>
            <a-col :span="12">
              <span style="color: #909399; margin-right: 10px; font-size: 16px">物料名称</span>
              <span :value="localForm.itemName" style="color: #303133; font-size: 16px" v-if="localForm.itemNumber">{{ localForm.itemName }}</span>
              <span style="color: #f56c6c" v-else>您还未选择物料</span>
            </a-col>
            <a-col :span="12">
              <span style="color: #909399; margin-right: 10px; font-size: 16px">单位名称</span>
              <span :value="localForm.unitName" style="color: #303133; font-size: 16px" v-if="localForm.itemNumber">{{ localForm.unitName }}</span>
              <span style="color: #f56c6c" v-else>您还未选择物料</span>
            </a-col>
            <a-col :span="12">
              <span style="color: #909399; margin-right: 10px; font-size: 16px">规格型号</span>
              <span :value="localForm.model" style="color: #303133; font-size: 16px" v-if="localForm.itemNumber">{{ localForm.model }}</span>
              <span style="color: #f56c6c" v-else>您还未选择物料</span>
            </a-col>
          </a-row>
        </a-col>
        <a-col :span="6">
          <a-form-item>
            <file-upload :default-file-list="localForm.imgUrl" @change="handleFileChange" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="8">
          <a-form-item label="单据编号" name="instructNumber">
            <a-input
              v-model:value="localForm.instructNumber"
              style="width: 80%"
              placeholder="单据编号，可不填写"
              :disabled="!!localForm.id"
              @change="emitFormUpdate"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="客户名称">
            <customer-select v-model:value="localForm.customerId" style="width: 80%" @change="handleCustomerSelect" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="生产状态" name="enableFlag">
            <a-select
              ref="select"
              v-model:value="localForm.produceStatus"
              placeholder="请选择"
              :options="Object.values(PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM).slice(0, -1)"
              style="width: 80%"
              disabled
              @change="emitFormUpdate"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="计划工期" name="planStartTime">
            <a-range-picker
              :value="[
                localForm.planStartTime ? dayjs(localForm.planStartTime) : null,
                localForm.planFinishTime ? dayjs(localForm.planFinishTime) : null,
              ]"
              @change="handleDateRangeChange"
              valueFormat="YYYY-MM-DD"
              placement="bottomLeft"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="交货日期 " name="deliverTime">
            <a-date-picker
              v-model:value="localForm.deliverTime"
              style="width: 80%"
              placement="bottomLeft"
              valueFormat="YYYY-MM-DD"
              @change="emitFormUpdate"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row>
        <a-col :span="8">
          <a-form-item label="选择物料">
            <a-space>
              <a-button type="primary" @click="showItemModal" :icon="h(SearchOutlined)">选择物料</a-button>
              <item-table-select-modal ref="itemModal" @select-data="handleSelectItemData" :attribute="ITEM_ATTRIBUTE_ENUM.CLOTHES.value" />
              <a-button type="primary" @click="showClothesForm">
                <template #icon>
                  <PlusOutlined />
                </template>
                快速建款
              </a-button>
            </a-space>
            <item-clothes-add-form ref="clothesFormRef" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="优先级" name="priority">
            <a-radio-group v-model:value="localForm.priority" button-style="solid" style="width: 100%" @change="emitFormUpdate">
              <a-radio-button value="0">一般</a-radio-button>
              <a-radio-button value="1">紧急</a-radio-button>
              <a-radio-button value="2">非常紧急</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="备注">
            <a-textarea v-model:value="localForm.remark" placeholder="请输入备注" :rows="4" style="width: 80%" @change="emitFormUpdate" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <a-table size="small" :dataSource="colorRows" :columns="dynamicColumns" :pagination="false" bordered :scroll="{ x: 500, y: 500 }">
      <template #bodyCell="{ record, column, text }">
        <template v-if="hasValidItemId(record.styleColor, column.dataIndex.replace('size_', ''))">
          <a-input-number
            placeholder="请输入数量"
            v-model:value="record.sizes[column.dataIndex]"
            style="width: 100%"
            @change="updateTotalNum"
            :controls="false"
            addon-after="件"
          />
        </template>
        <template v-if="column.dataIndex === 'option'">
          <div class="smart-table-operate">
            <a-button @click="handleDeleteRow(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { ref, h, watch, onMounted, reactive, toRaw, nextTick } from 'vue';
  import { Modal, message } from 'ant-design-vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import CustomerSelect from '/@/components/business/mes/base/customer-select/index.vue';
  import ItemTableSelectModal from '/@/components/business/mes/item/item-clothes-table-select-modal/index.vue';
  import itemClothesAddForm from '/@/views/business/mes/item/itemClothes/components/item-clothes-add-form.vue';
  import dayjs from 'dayjs';
  import { ITEM_ATTRIBUTE_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const';
  import _ from 'lodash';

  // 定义props
  const props = defineProps({
    form: {
      type: Object,
      required: true,
    },
    rules: {
      type: Object,
      required: true,
    },
    colorRows: {
      type: Array,
      required: true,
    },
    dynamicColumns: {
      type: Array,
      required: true,
    },
  });

  // 定义emit
  const emit = defineEmits(['select-item-data', 'update-total-num', 'update:form', 'update:colorRows', 'update:dynamicColumns']);

  // 创建本地状态，深拷贝props.form
  const localForm = reactive(_.cloneDeep(props.form));

  // 监听props.form的变化，更新本地状态
  watch(
    () => props.form,
    (newVal) => {
      // 使用深拷贝避免引用问题
      Object.assign(localForm, _.cloneDeep(newVal));
    },
    { deep: true }
  );

  // 通用方法：向父组件发送表单更新
  function emitFormUpdate() {
    emit('update:form', toRaw(localForm));
  }

  // 表单引用
  const formRef = ref();
  const clothesFormRef = ref();

  // 选择物料模态框引用
  const itemModal = ref();

  // 打开选择物料模态框
  function showItemModal() {
    itemModal.value.showModal();
  }

  // 打开新建成衣模态框
  function showClothesForm() {
    clothesFormRef.value.show();
  }

  // 文件上传变化事件
  function handleFileChange(files) {
    localForm.imgUrl = files;
    emitFormUpdate();
  }

  // 客户选择事件
  function handleCustomerSelect(customer) {
    localForm.customerId = customer.customerId;
    localForm.customerName = customer.customerName;
    emitFormUpdate();
  }

  // 物料选择事件
  function handleSelectItemData(data) {
    emit('select-item-data', data);
  }

  // 检查颜色尺寸组合是否有效
  function hasValidItemId(color, size) {
    const item = localForm.clothesList?.find((item) => item.styleColor === color && item.size === size);
    return item && item.itemId;
  }

  // 删除行
  function handleDeleteRow(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: '危险',
      onOk() {
        // 从colorRows中删除
        const updatedRows = props.colorRows.filter((row) => row.styleColor !== record.styleColor);
        // 从clothesList中删除对应颜色的所有项
        const updatedList = localForm.clothesList.filter((item) => item.styleColor !== record.styleColor);

        // 更新本地状态
        localForm.clothesList = updatedList;

        // 通过事件更新父组件数据
        emit('update:colorRows', updatedRows);
        emitFormUpdate();

        // 更新总数
        updateTotalNum();
        message.success('删除成功');
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  // 重制数据
  function resetTableData() {
    emit('update:dynamicColumns', []);
    emit('update:colorRows', []);
  }

  //------------------------------时间选择器------------------------------
  function handleDateRangeChange(dates, dateStrings) {
    localForm.planStartTime = dateStrings[0];
    localForm.planFinishTime = dateStrings[1];
    emitFormUpdate();
  }

  //------------------------------表格数据处理方法------------------------------
  const BASE_COLUMNS = [
    {
      title: '颜色',
      dataIndex: 'styleColor',
      width: 100,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '操作',
      dataIndex: 'option',
      width: 100,
      align: 'center',
      fixed: 'right',
    },
  ];

  // 创建尺寸列配置
  const createSizeColumn = (size) => ({
    title: size,
    dataIndex: `size_${size}`,
    width: 120,
    align: 'center',
  });

  // 获取唯一的颜色和尺寸
  const getUniqueAttributes = (list, attribute) => [...new Set(list.map((item) => item[attribute]))];

  //处理动态表格
  function transformDataToColorRows() {
    if (!localForm.clothesList?.length) return;

    // 获取唯一的颜色和尺寸
    const colors = getUniqueAttributes(localForm.clothesList, 'styleColor');
    const sizes = getUniqueAttributes(localForm.clothesList, 'size');

    // 构建动态列
    const newColumns = [...BASE_COLUMNS];
    // 插入尺寸列
    sizes.forEach((size) => {
      newColumns.splice(newColumns.length - 1, 0, createSizeColumn(size));
    });

    // 构建行数据
    const newRows = colors.map((color) => {
      // 为每个颜色创建一行
      const row = {
        styleColor: color,
        key: color,
        sizes: {},
      };
      // 初始化所有尺寸字段
      sizes.forEach((size) => {
        row.sizes[`size_${size}`] = 0;
      });
      // 填充已有数据
      localForm.clothesList.forEach((item) => {
        if (item.styleColor === color) {
          row.sizes[`size_${item.size}`] = item.num || 0;
        }
      });
      return row;
    });

    // 更新状态并通知父组件
    emit('update:dynamicColumns', newColumns);
    emit('update:colorRows', newRows);
  }

  // 更新总数量
  function updateTotalNum() {
    if (!localForm.clothesList?.length || !props.colorRows?.length) return;

    const updatedList = [];
    // 遍历颜色行和尺寸列
    props.colorRows.forEach((row) => {
      // 将对象转化为键值对数组遍历
      Object.entries(row.sizes).forEach(([sizeKey, num]) => {
        const size = sizeKey.replace('size_', '');
        // 查找原始列表中是否存在该颜色和尺寸的组合
        const existingItem = localForm.clothesList.find((item) => item.styleColor === row.styleColor && item.size === size);

        if (existingItem) {
          // 更新现有项
          updatedList.push({ ...existingItem, num });
        } else {
          // 创建新项并尝试查找匹配的itemId
          const similarItem = localForm.clothesList.find((item) => item.styleColor === row.styleColor && item.size === size);
          updatedList.push({
            id: similarItem?.id,
            styleColor: row.styleColor,
            size,
            num,
            itemId: similarItem?.itemId,
          });
        }
      });
    });

    // 更新本地状态
    localForm.clothesList = updatedList;

    // 计算总数
    const total = updatedList.reduce((sum, item) => sum + (Number(item.num) || 0), 0);

    // 更新表单并通知父组件
    emitFormUpdate();
    emit('update-total-num', total);
  }
  // 表单验证方法
  async function validate() {
    try {
      await formRef.value.validateFields();
      return true;
    } catch (error) {
      return Promise.reject(error);
    }
  }
  //--------------------------挂载----------------------------
  // 监听本地form.clothesList变化自动更新表格
  watch(
    () => localForm.clothesList,
    () => {
      transformDataToColorRows();
    },
    { deep: true }
  );

  onMounted(() => {
    // 初始化表格
    nextTick(() => {
      transformDataToColorRows();
    });
  });

  // 暴露方法给父组件
  defineExpose({
    validate,
    formRef,
    transformDataToColorRows,
    updateTotalNum,
    resetTableData,
  });
</script>

<style scoped>
  .smart-table-btn-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .smart-table-operate {
    display: flex;
    gap: 8px;
  }
</style>

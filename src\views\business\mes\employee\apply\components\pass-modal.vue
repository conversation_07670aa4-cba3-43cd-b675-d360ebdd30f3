<!--
  * 员工申请表
  *
  * @Author:    cjm
  * @Date:      2024-09-19 22:50:32
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      title="请选择角色和部门"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"


  >
    <a-form ref="formRef" :model="form" :rules="rule" :label-col="{ span:6 }">

      <a-row>
        <a-col :span="24" >
          <a-form-item label="申请部门" name="departmentId" >
            <a-cascader
                style="width: 90%"
                :options="departmentTreeOptions"
                expand-trigger="hover"
                placeholder="请选择部门"
                change-on-select
                @change="departmentChange"
            />
<!--            <department-tree-select  />-->

          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24" >
          <a-form-item label="申请角色" name="roleIdList" >
            <a-select
                ref="select"
                label-in-value
                placeholder="请选择角色"
                style="width: 90%"
                :options="roleOptions"
                @change="handleChange"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">通过申请</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue';
import {departmentApi} from "/@/api/system/department-api.js";
import {convertData} from "/src/utils/Tree-util.js";
import {roleApi} from "/@/api/system/role-api.js";
import {message} from "ant-design-vue";
import {employeeApplyApi} from '/@/api/business/mes/employee/employee-apply-api.js';
import {smartSentry} from "/@/lib/smart-sentry.js";

defineExpose({
  show,
});

const emits = defineEmits(['reloadList']);

onMounted(()=>{
  queryDepartmentTree()
  queryRoleList()
})

const departmentTreeOptions=  ref([])
const roleOptions= ref([])


// 是否显示
const visibleFlag = ref(false);
const formRef = ref();

const formDefault={
  id: undefined,
  roleIdList: undefined,
  disabledFlag: false,
  departmentId: undefined
}
const form=reactive({...formDefault})

const rule = {
  roleIdList: [
    { required: true, message: '请选择角色', trigger: 'blur' },
  ],
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'blur' },
  ],
}

function show(rowData) {
  Object.assign(form, formDefault);
  form.id = rowData.id
  visibleFlag.value = true;
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    await pass()
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }


}

async function pass() {
  try {
    await employeeApplyApi.pass(form)
    // console.log(form)
    message.success("申请成功");
    emits('reloadList');
    visibleFlag.value=false
  }catch (err){
    smartSentry.captureError(err)
  }
}

// 选择部门回调
function departmentChange(value,selectOptions){
  form.departmentId = value[value.length-1]
}
// 选择角色回调
function handleChange(value) {
  form.roleIdList = [value.value]
}
async  function queryDepartmentTree(){
  let res=  await departmentApi.queryDepartmentTree()
  departmentTreeOptions.value=convertData(res.data,"departmentId","name")
  // console.log(departmentTreeOptions)
}

async function queryRoleList(){
  let res= await roleApi.queryAll()
  roleOptions.value=convertData(res.data,"roleId","roleName")
}



</script>

<template>
  <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
    <template #bodyCell="{ text, record, column }">
      <template v-if="column.dataIndex === 'progress'">
        <a-progress :size="8" :percent="((record.receiveNum / (record.sendNum + record.receiveNum)) * 100).toFixed(2)" />
      </template>
    </template>
  </a-table>
</template>
<script setup>
  import { onMounted, ref } from 'vue';
  import { produceReportApi } from '/@/api/business/mes/produce/produce-report-api.js';
  import { partDispatchSummaryApi } from '/@/api/business/mes/part-dispatch/part-dispatch-summary-api.js';

  const tableLoading = ref(false);
  const tableData = ref([]);
  const props = defineProps({
    data: Array,
    id: Number,
  });

  onMounted(() => {
    queryData();
  });

  const columns = ref([
    {
      title: '下发对象',
      dataIndex: 'objectName',
      ellipsis: true,
      // width: 50,
      align: 'center',
    },
    {
      title: '颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      // width: 50,
      align: 'center',
    },
    {
      title: '尺寸',
      dataIndex: 'size',
      ellipsis: true,
      // width: 50,
      align: 'center',
    },
    {
      title: '部位',
      dataIndex: 'positions',
      ellipsis: true,
      // width: 50,
      align: 'center',
    },
    {
      title: '已下发扎数',
      dataIndex: 'sendTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + record.receiveTieCount + '扎';
      },
    },
    {
      title: '已回收扎数',
      dataIndex: 'receiveTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '扎';
      },
    },
    {
      title: '待回收扎数',
      dataIndex: 'sendTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '扎';
      },
    },
    {
      title: '总扎数',
      dataIndex: 'totalTieCount',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return record.sendTieCount + record.receiveTieCount + '扎';
      },
    },
    {
      title: '已下发数量',
      dataIndex: 'sendNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + record.receiveNum + '片';
      },
    },
    {
      title: '已回收数量',
      dataIndex: 'receiveNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '片';
      },
    },
    {
      title: '待回收数',
      dataIndex: 'sendNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return text + '片';
      },
    },

    {
      title: '总数',
      dataIndex: 'totalNum',
      ellipsis: true,
      // width: 50,
      align: 'center',
      customRender: ({ text, record, index, column }) => {
        return record.sendNum + record.receiveNum + '片';
      },
    },
    {
      title: '回收进度',
      dataIndex: 'progress',
      ellipsis: true,
      width: 120,
      align: 'center',
      // customRender: ({text, record, index, column}) => {
      //   let num = record.sendNum + record.receiveNum
      //   let progress = record.receiveNum / num * 100
      //   return progress.toFixed(2) + "%"
      // }
    },
  ]);

  async function queryData() {
    tableLoading.value = true;
    let executeDetail = await partDispatchSummaryApi.queryDetail(props.id);
    tableData.value = executeDetail.data;
    tableLoading.value = false;
  }
</script>

<style scoped lang="less"></style>

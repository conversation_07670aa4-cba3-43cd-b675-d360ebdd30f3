/**
 * 生产指令单 api 封装
 *
 * @Author:    cyz
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const produceInstructOrderProcessApi = {

  getProcessInfoById: (id) => {
    return getRequest(`/produceInstructOrderProcess/process/${id}`);
  },
//   工序统计
  processCountStats:(beginTime,endTime)=>{
    return getRequest(`/produceInstructOrderProcess/processCountStats?beginTime=${beginTime}&endTime=${endTime}`);
  },
// 指令单工序分页查询
 queryPage:(param)=>{
    return postRequest(`/produceInstructOrderProcess/produceInstructOrder/queryPage`,param);
  },
  //工序列表
  getProcessList:(orderId)=>{
    return getRequest(`/produceInstructOrderProcess/list/${orderId}`);
  }
};

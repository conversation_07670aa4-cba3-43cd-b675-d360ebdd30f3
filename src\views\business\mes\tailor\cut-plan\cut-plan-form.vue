<!--
  * 新增裁床计划
  *
  * @Author:    fkf
  * @Date:      2025-01-18
  * @Copyright  zscbdic
-->
<template>
  <a-modal :open="visibleFlag" @cancel="onClose" width="86%" @ok="onOk">
    <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
      <div><strong>生产指令单信息</strong></div>
    </a-row>
    <a-row>
      <a-col :span="16">
        <a-form :label-col="{ span: 12 }">
          <a-row>
            <a-button @click="showForm" type="primary" style="margin-bottom: 10px" :disabled="form.id">
              <template #icon>
                <SearchOutlined />
              </template>
              选择指令单
            </a-button>
          </a-row>
          <a-row>
            <a-col :span="8">
              <a-form-item label="生产指令单编号">
                <a-input v-model:value="form.produceInstructOrderNumber" style="width: 90%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="裁剪计划单编号">
                <a-input v-model:value="form.number" style="width: 90%" placeholder="请输入裁剪计划单编号" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="计划单状态">
                <a-select
                  v-model:value="form.status"
                  style="width: 90%"
                  :options="Object.values(CUT_PLAN_STATUS_ENUM)"
                  placeholder="请选择状态"
                  disabled
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="计划开始时间">
                <a-date-picker v-model:value="form.planStartTime" style="width: 90%" placement="bottomLeft" valueFormat="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="计划完成时间">
                <a-date-picker v-model:value="form.planFinishTime" style="width: 90%" placement="bottomLeft" valueFormat="YYYY-MM-DD" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="实际开始时间">
                <a-input v-model:value="form.realStartTime" style="width: 90%" disabled />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="实际完成时间">
                <a-input v-model:value="form.realFinishTime" style="width: 90%" disabled />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <a-row>
          <a-button type="primary" @click="addCutBed" style="margin-bottom: 16px">
            <plus-outlined />
            添加裁床
          </a-button>
        </a-row>
        <a-row class="cut-plan-container">
          <!-- 裁床计划单 
         bed用于表达是哪一个表单的对象 -->
          <a-card v-for="(bed, index) in cutBeds" :key="index" :title="`裁床计划单${index + 1}`" style="width: 100%">
            <div style="margin-bottom: 10px">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-form-item label="序号">
                    <a-input v-model:value="bed.seq" :min="0" style="width: 100%" disabled placeholder="请输入序号" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="床次">
                    <cut-bed-detail v-model:value="bed.cutNum" :produceInstructOrderId="form.produceInstructOrderId" />
                  </a-form-item>
                </a-col>

                <a-col :span="6">
                  <a-form-item label="铺布长度">
                    <a-input v-model:value="bed.clothLen" style="width: 100%" placeholder="请输入铺布长度" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="单件用量">
                    <a-input v-model:value="bed.unitDosage" style="width: 100%" disabled />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="部位">
                    <part-select v-model:value="bed.parts" :produceInstructOrderId="form.produceInstructOrderId" />
                  </a-form-item>
                </a-col>
                <a-col :span="6">
                  <a-form-item label="备注">
                    <a-textarea v-model:value="bed.remark" :auto-size="{ minRows: 1, maxRows: 2 }" placeholder="请输入备注 (可换行)" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            <!-- 删除按钮 -->
            <template #extra>
              <a-button type="link" danger @click="deleteCutBed(index)">
                <delete-outlined />
                删除
              </a-button>
            </template>

            <!-- 表格 -->
            <a-table
              :columns="bed.leftColumns"
              :data-source="bed.leftTableData"
              :pagination="false"
              size="small"
              :scroll="{ x: 1000 }"
              bordered
              @change="calculateSizeNum(bed)"
            >
              <template #headerCell="{ column }">
                <template v-if="column.dataIndex.startsWith('size')">
                  <a-input-number
                    v-model:value="column.ratio"
                    addon-before="比例:"
                    :controls="false"
                    :min="0"
                    style="width: 100%"
                    @change="calculateSizeNum(bed)"
                  />
                  <span>{{ column.title }}</span>
                </template>
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex.startsWith('size')">
                  {{ record[column.dataIndex] }}
                </template>
                <template v-if="column.dataIndex === 'clothPutNum'">
                  <a-input-number v-model:value="record.clothPutNum" @change="calculateSizeNum(bed)" :min="0" />
                </template>
              </template>
            </a-table>
          </a-card>
        </a-row>
      </a-col>
      <a-col :span="8">
        <a-card title="生产数量" style="width: 100%">
          <a-table :columns="rightColumns" :data-source="rightTableData" :pagination="false" size="small" :scroll="{ x: 450 }" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex.startsWith('size_')">
                {{ record[column.dataIndex] }}
              </template>
            </template>
          </a-table>
        </a-card>
        <a-card title="计划总数" style="width: 100%; margin: 16px 0">
          <a-table :columns="rightColumns" :data-source="totalTableData" :pagination="false" size="small" :scroll="{ x: 450 }" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex.startsWith('size_')">
                {{ record[column.dataIndex] }}
              </template>
            </template>
          </a-table>
        </a-card>
        <a-card title="计划单余数" style="width: 100%">
          <a-table :columns="rightColumns" :data-source="numTableData" :pagination="false" size="small" :scroll="{ x: 450 }" bordered>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex !== 'styleColor'">
                {{ record[column.dataIndex] }}
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
    </a-row>
  </a-modal>
  <produce-instruct-order-table-select-modal ref="formRef" @select-data="selectData" />
</template>

<script setup>
  import { reactive, ref, watch, nextTick } from 'vue';
  import { CUT_PLAN_STATUS_ENUM } from '/@/constants/business/mes/tailor/cut-plan-const.js';
  import ProduceInstructOrderTableSelectModal from '/@/components/business/mes/produce/produce-instruct-order-table-select-modal/index.vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { baseLeftColumns, baseRightColumns } from '/@/views/business/mes/tailor/cut-plan/components/baseColumns.js';
  import cutBedDetail from '/@/components/business/mes/tailor/cut-bed-detail/index.vue';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';

  const formDefault = {
    id: undefined,
    remark: undefined, //备注
    originType: undefined, //来源订单类型
    originId: undefined, // 来源单据ID
    originNumber: undefined, // 来源单据编号
    produceInstructOrderId: undefined, //生产指令单ID
    produceInstructOrderNumber: undefined, //生产指令单编号
    number: undefined, //裁剪计划单编号
    status: 'DRAFT', //计划单状态
    planStartTime: undefined, //计划开始时间
    planFinishtime: undefined, //计划完成时间
    realStartTime: undefined, //实际开始时间
    realFinishTime: undefined, //实际完成时间
    paperPatternId: undefined, //纸样ID
    paperPatternNumber: undefined, //纸样编号
    planDosage: undefined, //计划单件用量（米）
    realDosage: undefined, //实际单件用量（米）
    realOverPercent: undefined, //实际超裁比例（%）
    planOverPercent: undefined, //计划超裁比例（%）
    planClothDosage: undefined, //计划需布量（米）
    realClothDosage: undefined, //实际需布量（米）
  };
  const emit = defineEmits(['submit', 'update']);
  const form = reactive({ ...formDefault });
  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show() {
    visibleFlag.value = true;
  }
  // 提交
  async function onOk() {
    emit('submit'); // 触发提交事件
  }
  // 重制数据
  function resetForm() {
    Object.assign(form, formDefault);
    cutBeds.value = [];
    rightTableData.value = [];
    leftColumns.value = [];
    numTableData.value = [];
    totalTableData.value = [];
    colorCache.value = [];
    rightColumns.value = baseRightColumns;
    leftColumns.value = baseLeftColumns;
  }
  // 关闭模态框
  function onClose() {
    visibleFlag.value = false;
    resetForm();
  }
  // ---------------------------- 表格列 ----------------------
  const leftColumns = ref(baseLeftColumns);
  const rightColumns = ref(baseRightColumns);
  const colorCache = ref([]); // 颜色
  // ------------------------ 生产指令单触发 ------------------------
  const formRef = ref();
  // 触发模态框
  function showForm(data) {
    // 清楚列表数据选择
    resetForm();
    formRef.value.showModal(data);
  }

  const selectData = async (data) => {
    // 特定赋值
    form.produceInstructOrderId = data.id;
    form.produceInstructOrderNumber = data.instructNumber;
    form.realStartTime = data.realStartTime;
    form.realFinishTime = data.realFinishTime;

    // 请求数据
    leftColumns.value = await queryTableSizeList();
    colorCache.value = await queryTableColorList();
    queryClothesProduceInfo();
  };
  // ------------------------ 裁床表单 ------------------------
  // 裁床计划单数据
  const cutBeds = ref([]);
  const rightTableData = ref([]); //右侧表格数据
  const totalTableData = ref([]); //总数量数据
  const numTableData = ref([]); //计算余量数据
  // 添加裁床计划单并且渲染尺寸颜色列表
  const addCutBed = async () => {
    if (!form.produceInstructOrderId) {
      message.warning('请先选择生产指令单');
      return;
    }

    //深拷贝数据
    const colorList = JSON.parse(JSON.stringify(colorCache.value));
    const bedLeftColumns = JSON.parse(JSON.stringify(leftColumns.value));

    cutBeds.value.push({
      id: undefined, //裁床ID
      leftTableData: colorList,
      leftColumns: bedLeftColumns, // 赋值裁床计划单的列定义
      seq: cutBeds.value.length + 1, //裁床序号
      cutNum: undefined, //裁床床次
      parts: [], //裁床部位
      remark: undefined, //裁床备注
      clothLen: undefined, //铺布长度
      unitDosage: undefined, //单件用量
    });
  };
  // 删除裁床
  const deleteCutBed = (index) => {
    cutBeds.value.splice(index, 1);
    //重新计算数值
    cutBeds.value.forEach((bed, item) => {
      bed.seq = item + 1;
    });
  };
  // 计算每行尺码数量
  const calculateSizeNum = (bed) => {
    bed.leftTableData.forEach((row) => {
      // 遍历所有尺码列
      bed.leftColumns.forEach((col) => {
        if (col.dataIndex.startsWith('size')) {
          // 计算公式：铺布张数 * 比例
          row[col.dataIndex] = (row.clothPutNum || 0) * (col.ratio || 0);
        }
      });
    });
  };

  // 计算单件用量
  const calculateUnitDosage = (bed) => {
    if (!bed.clothLen) return;
    // 获取所有比例的总和
    const totalRatio = bed.leftColumns.filter((col) => col.dataIndex.startsWith('size')).reduce((sum, col) => sum + (col.ratio || 0), 0);
    // 如果总比例为0，不计算
    if (totalRatio === 0) return;
    // 计算单件用量 = 铺布长度 / 总比例
    bed.unitDosage = Number((bed.clothLen / totalRatio).toFixed(4)); // 保留四位小数
  };
  // ------------------------ 请求裁床数据 ------------------------
  //请求左表格尺码返回列表
  async function queryTableSizeList() {
    let res = await produceInstructOrderClothesApi.queryClothesSizeList(form.produceInstructOrderId);
    let newColumns = [...baseLeftColumns];
    res.data.forEach((item, index) => {
      newColumns.splice(-1, 0, {
        title: item,
        dataIndex: `size${index}`,
        align: 'center',
        ratio: undefined,
      });
    });
    return newColumns;
  }

  //请求颜色返回数据
  async function queryTableColorList() {
    let res = await produceInstructOrderClothesApi.queryClothesColorList(form.produceInstructOrderId);
    return res.data.map((color) => ({ styleColor: color }));
  }

  //请求指令单所有成衣生产信息
  async function queryClothesProduceInfo() {
    const res = await produceInstructOrderClothesApi.queryClotheList(form.produceInstructOrderId);

    //获取所有的尺码和颜色 并且去重
    const sizes = [...new Set(res.data.map((item) => item.size))];
    const colors = [...new Set(res.data.map((item) => item.styleColor))];

    //修复右侧列数据配置
    rightColumns.value = [
      ...baseRightColumns,
      ...sizes.map((size) => ({
        title: size,
        dataIndex: size,
        align: 'center',
      })),
    ];

    //处理表格数据
    rightTableData.value = colors.map((color) => {
      const row = { styleColor: color };
      sizes.forEach((size) => {
        // 在原始数据中查找当前颜色和尺码的组合
        row[size] = res.data.find((d) => d.styleColor === color && d.size === size)?.num || 0; //没找到就赋值0
      });
      return row;
    });
  }
  // 计算总数量表
  const calculateTotalTableData = () => {
    if (!cutBeds.value.length) {
      return [];
    }

    // 复制右侧表格数据结构，初始值都设为0
    const totalData = rightTableData.value.map((row) => {
      const newRow = { styleColor: row.styleColor };
      // 将所有尺码列初始化为0
      leftColumns.value.forEach((col) => {
        if (col.dataIndex.startsWith('size')) {
          newRow[col.title] = 0;
        }
      });
      return newRow;
    });
    // 遍历所有裁床数据，累加每个尺码的数量
    cutBeds.value.forEach((bed) => {
      bed.leftTableData.forEach((leftRow) => {
        const totalRow = totalData.find((row) => row.styleColor === leftRow.styleColor);
        if (totalRow) {
          bed.leftColumns.forEach((col) => {
            if (col.dataIndex.startsWith('size')) {
              // 计算公式：铺布张数 * 比例
              const calculatedNum = leftRow.clothPutNum * (col.ratio || 0);
              totalRow[col.title] = (totalRow[col.title] || 0) + (calculatedNum || 0);
            }
          });
        }
      });
    });
    return totalData;
  };
  // 计算余量表
  const calculateNumTableData = () => {
    if (!cutBeds.value.length) {
      console.log('没有裁床数据');
      return [];
    }
    const numData = rightTableData.value.map((row) => ({
      ...row,
    }));
    // 遍历所有裁床数据
    cutBeds.value.forEach((bed) => {
      bed.leftTableData.forEach((leftRow) => {
        // 找到对应颜色的行
        const numRow = numData.find((row) => row.styleColor === leftRow.styleColor);
        if (numRow) {
          // 遍历所有尺码列
          bed.leftColumns.forEach((col) => {
            if (col.dataIndex.startsWith('size')) {
              // 计算公式：铺布张数 * 比例
              const calculatedNum = leftRow.clothPutNum * (col.ratio || 0);
              // 在对应的格子中减去计算出的数量
              numRow[col.title] -= calculatedNum || 0;
            }
          });
        }
      });
    });

    return numData;
  };
  // 监听左右表数据变化，更新余量表
  watch(
    () => cutBeds.value,
    () => {
      numTableData.value = calculateNumTableData();
      totalTableData.value = calculateTotalTableData();
      cutBeds.value.forEach((bed) => {
        calculateUnitDosage(bed);
      });
    },
    { deep: true }
  );
  // ------------------------ 暴露方法 ------------------------
  defineExpose({
    show,
    getFormData: () => form,
    getCutBedsData: () => cutBeds.value,
    setFormData: async (formData, beds) => {
      // 设置表单数据
      Object.assign(form, formData);
      // 初始化必要的数据
      leftColumns.value = await queryTableSizeList();
      colorCache.value = await queryTableColorList();
      // 更新右侧表格数据
      await queryClothesProduceInfo();
      // 设置裁床数据
      cutBeds.value = beds;
      // 等待数据加载完成后，手动触发一次计算
      nextTick(() => {
        const numData = calculateNumTableData();
        const totalData = calculateTotalTableData();
        numTableData.value = numData;
        totalTableData.value = totalData;
      });
    },
    // 计算余量表
    calculateNumTableData,
    // // 总数量表
    // totalTableData,
    // 余量表
    numTableData,
    // 关闭模态框
    onClose,
  });
</script>

<style>
  .cut-plan-container {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 8px;
  }
</style>

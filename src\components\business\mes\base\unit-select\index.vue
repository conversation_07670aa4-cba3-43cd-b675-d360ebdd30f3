<!--
  * 单位下拉选择组件
  * 返回所选单位列表
  * @Author:    wxx
  * @Date:      2025-02-11
  * @Copyright  zscbdic
-->

<template>
  <a-select
    v-model:value="selectedValue"
    :style="{ width: width }"
    :placeholder="props.placeholder"
    :showSearch="true"
    :allowClear="true"
    :filterOption="filterOption"
    @change="handleChange"
  >
    <a-select-option v-for="item in unitOptions" :key="item.id" :value="item.id">
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { unitApi } from '/@/api/business/mes/base/unit-api.js';

  const props = defineProps({
    value: [Number, Array],
    placeholder: {
      type: String,
      default: '请选择单位',
    },
    width: {
      type: String,
      default: '100%',
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 过滤选项
  function filterOption(inputValue, option) {
    const item = unitOptions.value.find((i) => i.id === option.value);
    return item?.name?.toLowerCase().includes(inputValue.toLowerCase());
  }

  //查询仓库列表
  const unitOptions = ref([]);
  async function queryUnitList() {
    try {
      const queryData = await unitApi.querySelect({});
      unitOptions.value = queryData.data;
    } catch (error) {
      message.error('单位名称请求失败');
    }
  }

  // 处理选择变化
  function handleChange(value) {
    const unitItem = unitOptions.value.find((item) => item.id === value);
    emit('update:value', value);
    emit('change', unitItem);
  }

  // 监听props.value的变化
  const selectedValue = ref(props.value);
  watch(
    () => props.value,
    (newVal) => {
      selectedValue.value = newVal;
    }
  );

  onMounted(() => {
    queryUnitList();
  });
</script>

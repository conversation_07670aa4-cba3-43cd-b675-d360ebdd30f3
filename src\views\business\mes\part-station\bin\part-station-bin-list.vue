<!--
  * 裁片货位
  *
  * @Author:    cjm
  * @Date:      2024-10-06 20:17:14
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="所属货架" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.rackId"
          style="width: 100px"
          placeholder="选择货架"
          show-search
          :filter-option="filterOption"
          :options="binOptions"
          @focus="binFocus"
        />
      </a-form-item>
      <a-form-item label="编号查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="编号查询" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="printLocationCode" class="smart-margin-left10">
          <template #icon> </template>
          打印货位码
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :rowSelection="rowSelection"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="confirmDelete(record)" type="link" danger>删除</a-button>
            <!--货位码冒泡显示-->
            <a-popover v-model:open="record.QRvisiable" :title="record.binCode" placement="left" trigger="click">
              <template #content>
                <a-image :width="100" :src="qrCode" />
              </template>
              <a-button @click="showQRById(record.id)" type="link">货位码</a-button>
            </a-popover>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'binCode'">
          <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartStationBinForm ref="formRef" @reloadList="queryData" />
    <PrintLocationCode ref="printRef" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationBinApi } from '/@/api/business/mes/part-station/bin/part-station-bin-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import PartStationBinForm from './part-station-bin-form.vue';
  import { Template } from '/@/constants/business/mes/tailor/cut-bed-sheet-const.js';
  import { partStationRackApi } from '/@/api/business/mes/part-station/rack/part-station-rack-api.js';
  import { useRouter } from 'vue-router';
  import PrintLocationCode from '/src/views/business/mes/part-station/bin/printLocationCode.vue';
  import _ from 'lodash';
  // 二维码内容
  const qrList = ref();
  // 货位码列表
  const binCodeList = ref();
  const printRef = ref();
  async function printLocationCode() {
    if (_.isEmpty(selectedRowList.value)) {
      message.warn('请选择数据');
      return;
    }
    try {
      const res = await partStationBinApi.getQrCodeContent(selectedRowKeyList.value);
      qrList.value = res.data;
    } catch (error) {
      console.error('获取二维码内容失败', error);
    }
    printRef.value.show(_.zipObject(binCodeList.value, qrList.value));
  }
  // ---------------------------- 表格列 ----------------------------
  const columns = ref([
    {
      title: '库位编码',
      dataIndex: 'binCode',
      ellipsis: true,
    },
    {
      title: '容量',
      dataIndex: 'capacity',
      ellipsis: true,
    },
    {
      title:'货位描述',
      dataIndex:'binDesc',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 150,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    rackId: undefined, //所属货架id
    queryKey: undefined, //编号查询
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await partStationBinApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // ---------------------------- 查看货位码 ----------------------------
  const qrCode = ref();

  async function showQRById(id) {
    try {
      let res = await partStationBinApi.getQRURL(id);
      qrCode.value = res.data;
    } catch (e) {
      message.error('二维码不存在');
      smartSentry.captureError(e);
    }
  }

  // ---------------------------- 处理货架下拉 ----------------------------
  const binOptions = ref([]);
  async function binFocus() {
    tableLoading.value = true;
    try {
      let res = await partStationRackApi.getAll({});
      //构造options
      binOptions.value = [];
      res.data.map((e) =>
        binOptions.value.push({
          value: e.id,
          label: e.rackCode,
        })
      );
    } catch (e) {
      message.error('获取货架信息失败');
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }
  //下拉框搜索
  const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }

  // ----------------- 处理多选 ------------------------
  const selectedRowKeyList = ref([]);
  const selectedRowList = ref(undefined);
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      selectedRowKeyList.value = selectedRowKeys;
      selectedRowList.value = selectedRows;
      binCodeList.value = selectedRowList.value.map((e) => {
        return e.binCode;
      });
    },
  };

  // ---------------------------- 批量删除 ----------------------------

  // 批量删除
  function confirmDelete(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        Delete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function Delete(id) {
    try {
      SmartLoading.show();
      await partStationBinApi.delete(id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input v-model:value="queryForm.queryKey" placeholder="请输入"/>
      </a-form-item>
      <a-form-item label="生产状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.produceStatus" style="width: 150px" placeholder="请选择"
                  :options="Object.values(PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM).slice(0,-1)"/>
      </a-form-item>
      <a-form-item label="指令单编号" class="smart-query-form-item">
        <a-input v-model:value="queryForm.instructNumber" placeholder="请输入"/>
      </a-form-item>
      <a-form-item label="物料编号" class="smart-query-form-item">
        <a-input v-model:value="queryForm.itemNumber" placeholder="请输入"/>
      </a-form-item>
      <a-form-item label="生产交付时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.deliverTime" :presets="defaultTimeRanges" style="width: 220px"
                        @change="onChangeDeliverTime"/>
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryProcessProgressData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block"></div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="resetQuery"/>
      </div>
    </a-row>
    <a-table
        :columns="processProgressColumns"
        :data-source="processProgressList"
        :loading="tableLoading"
        bordered
        :scroll="{ x: 1700 }"
        :pagination="false">
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'produceStatus'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'processProgress'">
          <a-steps class="step" label-placement="vertical">
            <a-step v-for="(item,index) in record.processList" :key="index" status="process"
                    @mouseenter="showProcessDetail($event, item)" @mouseleave="hideProcessDetail()">
              <template #icon>
                <a-progress
                    style="margin-top: 0px; margin-left: -5px"
                    type="circle"
                    :percent="precentCount(item.finishNum, item.shouldNum)"
                    :size="40"
                />
              </template>
              <template #description>
                <div style="margin-top: -10px">
                  {{ item.name }}
                </div>
              </template>
            </a-step>
          </a-steps>
        </template>
        <template v-if="column.dataIndex === 'realStartTime'">
          {{ text ? dayjs(text).format('YYYY-MM-DD') : '未开工' }}
        </template>
        <template v-if="column.dataIndex === 'realFinishTime'">
          {{ text ? dayjs(text).format('YYYY-MM-DD') : '未完成' }}
        </template>
        <template v-if="column.dataIndex === 'issuedTime'">
          {{ text ? dayjs(text).format('YYYY-MM-DD') : '未下达' }}
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="totals"
          @change="queryProcessProgressData"
          @showSizeChange="queryProcessProgressData"
          :show-total="(totals) => `共${totals}条`"
      />
    </div>
  </a-card>
  <div class="process-detail-popup" v-show="showPopup" :style="popupStyle">
    <div class="popup-title">工序进度</div>
    <div class="popup-content">
      <div class="popup-item">名称: <span class="popup-item-info">{{ currentProcess.name }}</span></div>
      <div class="popup-item">应做数量: <span class="popup-item-info" style="color:#22ad83">{{
          currentProcess.shouldNum
        }}</span></div>
      <div class="popup-item">完成数: <span class="popup-item-info" style="color:#d95751">{{
          currentProcess.finishNum
        }}</span></div>
      <div class="popup-item">订单ID: <span class="popup-item-info">{{ currentProcess.orderId }}</span></div>
      <div class="popup-item">车间名称: <span class="popup-item-info">{{ currentProcess.workshopName }}</span></div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted, reactive} from 'vue';
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const.js';
import {smartSentry} from '/@/lib/smart-sentry.js';
import {produceInstructOrderProcessApi} from '/@/api/business/mes/produce/produce-instruct-order-process-api.js';
import {
  PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM
} from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
import TableOperator from '/@/components/support/table-operator/index.vue';
import dayjs from 'dayjs';


const queryFormState = {
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{column: 'create_time', isAsc: false}],
  deliverTime: [],
  queryKey: undefined,
  searchCount: undefined,
  deliverTimeBegin: undefined,
  deliverTimeEnd: undefined,
  produceType: undefined,
  produceStatus: undefined,
  priority: undefined,
  itemNumber: undefined,
  instructNumber: undefined,
  produceStatusList: [],
}

const queryForm = reactive({...queryFormState});

// 工单进度列表
const processProgressList = ref([]);

const totals = ref(0);


// 计算工序进度条百分比
const precentCount = (finishNum, shouldNum) => {
  return Math.floor((finishNum / shouldNum) * 100);
};

// 悬浮框相关状态
const showPopup = ref(false);
const popupStyle = ref({
  top: '0px',
  left: '0px',
});
const currentProcess = ref({});

// 显示工序详情悬浮框
function showProcessDetail(event, process) {
  currentProcess.value = process;
  // 计算悬浮框位置
  const rect = event.target.getBoundingClientRect();
  popupStyle.value = {
    top: `${rect.top + 60}px`,
    left: `${rect.left - 100}px`,
  };
  showPopup.value = true;
}

// 隐藏工序详情悬浮框
function hideProcessDetail() {
  showPopup.value = false;
}

const processProgressColumns = ref([
  // {
  //   title: '序号',
  //   dataIndex: 'id',
  //   key: 'id',
  //   height: 10,
  //   width: 50,
  //   align: 'center',
  // },
  {
    title: '指令单编号',
    dataIndex: 'instructNumber',
    key: 'instructNumber',
    width: 100,
    align: 'center',
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    key: 'itemNumber',
    width: 50,
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'itemName',
    width: 120,
    key: 'itemName',
    align: 'center',
  },
  {
    title: '应生产数',
    dataIndex: 'produceNum',
    key: 'produceNum',
    width: 80,
    align: 'center',
  },
  {
    title: '已完成数',
    dataIndex: 'finishNum',
    key: 'finishNum',
    width: 80,
    align: 'center',
  },
  {
    title: '生产状态',
    dataIndex: 'produceStatus',
    key: 'produceStatus',
    width: 50,
    align: 'center',
  },
  {
    title: '工序进度',
    dataIndex: 'processProgress',
    key: 'processProgress',
    width: 350,
    align: 'center',
    customCell: () => ({
      style: {
        overflow: 'auto',
        maxWidth: '200px'
      }
    })
  },
  {
    title: '发布时间',
    dataIndex: 'issuedTime',
    key: 'issuedTime',
    width: 90,
    align: 'center',
  },
  {
    title: '实际开始时间',
    dataIndex: 'realStartTime',
    key: 'realStartTime',
    width: 90,
    align: 'center',
  },
  {
    title: '实际完成时间',
    dataIndex: 'realFinishTime',
    key: 'realFinishTime',
    width: 90,
    align: 'center',
  },
  {
    title: '生产交付时间',
    dataIndex: 'deliverTime',
    key: 'deliverTime',
    width: 90,
    align: 'center',
  },
]);


const tableLoading = ref(false);

// 重置查询条件
function resetQuery() {
  Object.assign(queryForm, queryFormState);
  queryProcessProgressData();
}

// 生产交付时间改变
function onChangeDeliverTime(date, dateString) {
  queryForm.deliverTimeBegin = dateString[0];
  queryForm.deliverTimeEnd = dateString[1];
}

// 工序进度报表
async function queryProcessProgressData() {
  tableLoading.value = true;
  try {
    let queryData = await produceInstructOrderProcessApi.queryPage(queryForm);
    processProgressList.value = queryData.data.list;
    totals.value = queryData.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

onMounted(() => {
  queryProcessProgressData();
  // 删掉最后一个元素
  const a = Object.values(PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM)
  console.log(a.slice(0, -1));


});

</script>

<style lang="less" scoped>

.process-detail-popup {
  position: fixed;
  z-index: 1000;
  width: 300px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #f0f0f0;

  .popup-title {
    font-size: 18px;
    font-weight: bold;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fafafa;
  }

  .popup-content {
    padding: 12px 16px;

    .popup-item {
      margin-bottom: 8px;
    }

    .popup-item-info {
      font-weight: bold;
    }
  }

}

</style>
<!--
  * 批号主档
  *
  * @Author:    cjm
  * @Date:      2025-02-03 19:28:09
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="批号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.lotNumber" placeholder="批号" />
      </a-form-item>
      <a-form-item label="物料名称" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielName" placeholder="物料名称" />
      </a-form-item>
      <a-form-item label="物料spu编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielSpuNumber" placeholder="物料spu编号" />
      </a-form-item>
      <a-form-item label="物料sku编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielSkuNumber" placeholder="物料sku编号" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->
  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">详情</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <lotMasterForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { stkLotMasterApi } from '/@/api/business/mes/stock/lot-master-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import lotMasterForm from './lot-master-form.vue';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '批号',
      dataIndex: 'number',
      ellipsis: true,
    },
    {
      title: '物料名称',
      dataIndex: 'materielName',
      ellipsis: true,
    },
    {
      title: '物料型号',
      dataIndex: 'materielModel',
      ellipsis: true,
    },
    {
      title: '物料SPU编号',
      dataIndex: 'materielSpuNumber',
      ellipsis: true,
    },
    {
      title: '物料SKU编号',
      dataIndex: 'materielSkuNumber',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    pageNum: 1,
    pageSize: 10,
    lotNumber:undefined,//批号
    materielName:undefined,//物料名称
    materielSpuNumber:undefined,//物料spu编号
    materielSkuNumber:undefined,//物料sku编号
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await stkLotMasterApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 详情 ----------------------------
  const formRef = ref();

  function showForm(record) {
    formRef.value.show({
      lotMasterId: record.id, // 传递批号id
    });
  }
</script>

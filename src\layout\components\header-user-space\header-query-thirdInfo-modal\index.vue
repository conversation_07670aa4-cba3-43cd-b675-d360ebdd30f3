<template>
  <a-modal 
    :open="visible" 
    title="三方信息" 
    @cancel="handleCancel"
    :footer="null"
  >
 <a-empty v-if="bindInfo.length === 0" description="暂无绑定信息" />
    <a-card v-else v-for="item in bindInfo" :key="item.id" style="margin-bottom: 10px">
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div>
          <img :src="getThirdIcon(item.type)" style="width: 50px; height: 50px;" />
          <span style="margin-left: 10px;">{{ getThirdName(item.type) }}</span>
        </div>
        <a-popconfirm
          title="确定要解除绑定吗？"
          @confirm="handleUnbind(item.id)"
          ok-text="确定"
          cancel-text="取消"
        >
          <a-button type="link" danger>解除绑定</a-button>
        </a-popconfirm>
      </div>
    </a-card>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { thirdAuthApi } from '/src/api/system/third-auth-api';
import { message } from 'ant-design-vue';
import feiShuIcon from '/src/assets/images/login/feishu-icon.png';
import workWechatIcon from '/src/assets/images/login/work-wechat-icon.png';
import yuanyiIcon from '/src/assets/images/yuanyi/yuanyi-logo-2.png';

const visible = ref(false);
const bindInfo = ref([]);

const thirdTypeMap = {//三方类型对象
  wechat_enterprise: {
    name: '企业微信',
    icon: workWechatIcon
  },
  feishu: {
    name: '飞书',
    icon: feiShuIcon
  },
  yuanyi: {
    name: '元一',
    icon: yuanyiIcon
  }
};

function getThirdName(type) {
  return thirdTypeMap[type].name;//获取对应的名称
}

function getThirdIcon(type) {
  return thirdTypeMap[type].icon;//获取对应的图标
}

function handleCancel() {
  visible.value = false;
}

async function handleUnbind(id) {
  try {
    console.log(id)
    await thirdAuthApi.cancelBind(id);
    message.success('解绑成功');
    await queryBindInfo();
  } catch (error) {
    message.error('解绑失败');
  }
}

async function queryBindInfo() {
  try {
    const token = localStorage.getItem('smart_admin_user_token');
    const res = await thirdAuthApi.queryBindInfo(token);
    bindInfo.value = res.data;
  } catch (error) {
    message.error('查询失败');
  }
}

async function showModal() {
  visible.value = true;
  await queryBindInfo();
}

defineExpose({ showModal });
</script>
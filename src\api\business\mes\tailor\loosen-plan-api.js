/**
 * 松布作业 api 封装
 *
 * @Author:    linwj
 * @Date:      2025-06-22 21:03:26
 * @Copyright  weavewise.zscbdic.cn
 */
import { postRequest, getRequest } from '/src/lib/axios';

export const loosenPlanApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/loosenPlan/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/loosenPlan/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/loosenPlan/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/loosenPlan/delete/${id}`);
  },

  /**
   * 详情  <AUTHOR>
   */
  getDetails: (param) => {
    return getRequest('/loosenPlan/queryDetails', param);
  },
};

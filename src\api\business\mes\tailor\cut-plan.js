/**
 * 裁床计划单 api 封装
 *
 * @Author:    fkf
 * @Date:      2025-01-18
 * @Copyright
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const cutPlanApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/cutPlan/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/cutPlan/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/cutPlan/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/cutPlan/delete/${id}`);
  },
  /**
   *  查看详情   <AUTHOR>
   */
  get: (id) => {
    return getRequest(`/cutPlan/get/${id}`);
  },
  /**
   * 审核  <AUTHOR>
   */
  status: (param) => {
    return postRequest('/cutPlan/status', param);
  },
  /**
   * 获取生产指令单下最新计划床次  <AUTHOR>
   */
  getNewCutPlanBed: (id) => {
    return getRequest(`/cutPlanBed/getLatestByProduceInstructOrderId/${id}`);
  },
  /**
   * 获取床次情况
   */
  getBedNumList: (produceInstructOrderId) => {
    return getRequest(`/cutPlan/getBedNumList/${produceInstructOrderId}`);
  },

};

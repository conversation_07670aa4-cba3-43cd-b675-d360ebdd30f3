<template>
  <board-title :title="'松布仪表盘'" />
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="时间范围" class="smart-query-form-item">
        <a-range-picker v-model:value="pickerValue" @change="onDateChange"></a-range-picker>
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card>
    <a-row :gutter="16">
      <a-col :span="6">
        <a-card class="stat-card" style="border-top: 5px solid #4a8cd0" size="small">
          <div class="title">未开始</div>
          <div class="value">
            <span class="number">148</span>
            <span class="unit">个</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card" style="border-top: 5px solid #fedb93" size="small">
          <div class="title">静置中</div>
          <div class="value">
            <span class="number">69</span>
            <span class="unit">个</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card" style="border-top: 5px solid #d87d90" size="small">
          <div class="title">静置完成</div>
          <div class="value">
            <span class="number">54</span>
            <span class="unit">个</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card" style="border-top: 5px solid #6bb276" size="small">
          <div class="title">已结束任务</div>
          <div class="value">
            <span class="number">48</span>
            <span class="unit">个</span>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </a-card>

  <a-row :gutter="16" style="margin-top: 10px">
    <a-col :span="19">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="task-chart-card">
            <loosen-task-pie-chart />
          </a-card>
        </a-col>
        <a-col :span="18">
          <a-card class="task-chart-card">
            <loosen-task-bar-chart />
          </a-card>
        </a-col>
      </a-row>
      <a-row :gutter="16" style="margin-top: 10px">
        <a-col :span="12">
          <a-card class="task-chart-card">
            <loosen-task-time-bar-chart />
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card class="task-chart-card">
            <loosen-task-line-chart />
          </a-card>
        </a-col>
      </a-row>
    </a-col>
    <a-col :span="5">
      <a-card class="plan-broadcast-card" title="松布计划播报" size="small" :extra="`${total}个计划`">
        <div class="carousel-container">
          <a-carousel vertical autoplay :dots="false" :autoplay-speed="5000" v-if="groupedData.length > 0">
            <div v-for="(group, index) in groupedData" :key="index" class="carousel-page">
              <div v-for="item in group" :key="item.id" class="carousel-item">
                <a-card size="small" style="background-color: #f5f5f5; margin-bottom: 8px">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <div>{{ item.fabricLoosenNumber }}</div>
                  </div>
                  <div style="display: flex; justify-content: space-between">
                    <div>
                      <div style="color: #909399; font-size: 12px">{{ item.itemName }}</div>
                      <div style="color: #909399; font-size: 12px">卷数: {{ item.rolls }}</div>
                    </div>
                    <div style="text-align: right">
                      <div style="color: #909399; font-size: 12px">{{ item.workUnit }}</div>
                      <div style="color: #909399; font-size: 12px">
                        {{ item.planedStTime ? dayjs(item.planedStTime).format('YYYY-MM-DD HH:mm') : '' }}
                      </div>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </a-carousel>
          <div v-if="tableData.length === 0" class="empty-data">暂无松布计划数据</div>
        </div>
      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, reactive, onMounted, computed } from 'vue';
  import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import BoardTitle from '/@/components/business/mes/board/board-title.vue';
  import LoosenTaskTimeBarChart from './components/loosen-task-time-bar-chart.vue';
  import LoosenTaskLineChart from './components/loosen-task-line-chart.vue';
  import LoosenTaskBarChart from './components/loosen-task-bar-chart.vue';
  import LoosenTaskPieChart from './components/loosen-task-pie-chart.vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { loosenPlanApi } from '/@/api/business/mes/tailor/loosen-plan-api';
  import dayjs from 'dayjs';

  // 日期选择器值
  const pickerValue = ref([]);
  const dateRange = ref({
    startDate: '',
    endDate: '',
  });

  // 日期变化处理
  const onDateChange = (dates) => {
    if (dates && dates.length === 2) {
      dateRange.value.startDate = dates[0] ? dayjs(dates[0]).format('YYYY-MM-DD') : '';
      dateRange.value.endDate = dates[1] ? dayjs(dates[1]).format('YYYY-MM-DD') : '';
    } else {
      dateRange.value.startDate = '';
      dateRange.value.endDate = '';
    }
  };

  // 查询操作
  const onSearch = () => {
    queryPlans();
  };

  // 重置查询条件
  const resetQuery = () => {
    pickerValue.value = [];
    dateRange.value.startDate = '';
    dateRange.value.endDate = '';
    queryPlans();
  };

  // 表格数据相关
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 每页显示的数据条数
  const itemsPerPage = 5;

  // 将数据分组，每组显示固定数量的记录
  const groupedData = computed(() => {
    const result = [];
    const data = [...tableData.value];

    // 计算可以容纳多少条数据（基于容器高度和每条数据的高度）
    const containerHeight = 300 * 2 + 10 - 58; // 容器高度
    const itemHeight = 80; // 每条数据的估计高度
    const itemsPerPage = Math.floor(containerHeight / itemHeight);

    // 分组数据
    for (let i = 0; i < data.length; i += itemsPerPage) {
      result.push(data.slice(i, i + itemsPerPage));
    }

    return result;
  });

  // 查询松布计划
  async function queryPlans() {
    tableLoading.value = true;
    try {
      const queryParams = {
        pageNum: 1,
        pageSize: 100,
      };

      // 如果有选择日期范围，则添加到查询条件中
      if (dateRange.value.startDate) {
        queryParams.planStartDate = dateRange.value.startDate;
      }

      if (dateRange.value.endDate) {
        queryParams.planEndDate = dateRange.value.endDate;
      }

      const result = await loosenPlanApi.queryPage(queryParams);
      tableData.value = result.data.list;
      total.value = result.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(() => {
    queryPlans();
  });
</script>

<style scoped lang="less">
  .stat-card {
    background-color: #f8f9fc;
    border-radius: 4px;
    text-align: center;

    .title {
      font-size: 20px;
      color: #333;
      margin-bottom: 3px;
    }

    .value {
      .number {
        font-size: 32px;
        font-weight: bold;
        color: #4687f0;
      }

      .unit {
        font-size: 18px;
        margin-left: 5px;
      }
    }
  }

  .task-chart-card {
    height: 300px;
  }

  .plan-broadcast-card {
    height: calc(300px * 2 + 10px); /* 两个图表的高度加上间距 */
  }

  .carousel-container {
    height: calc(300px * 2 + 10px - 58px); /* 总高度减去卡片标题的高度 */
    overflow: hidden;
  }

  .carousel-page {
    padding: 0 5px;
    height: 100%;
    overflow-y: hidden;
  }

  .carousel-item {
    margin-bottom: 8px;
  }

  .empty-data {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #909399;
  }

  /* 自定义走马灯样式 */
  :deep(.ant-carousel .slick-slide) {
    text-align: left;
    background: transparent;
    overflow: hidden;
    height: calc(300px * 2 + 10px - 58px) !important;
  }

  :deep(.ant-carousel .slick-slide h3) {
    color: #333;
  }

  :deep(.ant-carousel) {
    height: 100%;
  }

  :deep(.ant-carousel .slick-slider) {
    height: 100%;
  }

  :deep(.ant-carousel .slick-list) {
    height: 100%;
  }

  :deep(.ant-carousel .slick-track) {
    height: 100%;
  }
</style>

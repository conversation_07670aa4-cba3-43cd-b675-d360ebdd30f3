<!--
  * 裁片仓库表
  *
  * @Author:    cjm
  * @Date:      2024-10-06 16:48:11
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="名称查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="名称查询" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="showQuickAddFormRef" type="primary" size="small" style="background-color: #4abe4a">
          <template #icon>
            <BankOutlined />
          </template>
          快速建仓
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false" @change="handleTableChange">
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
            <a-button @click="toSetMap(record)" type="link">设置地图</a-button>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'warehouseCode'">
          <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartStationWarehouseForm ref="formRef" @reloadList="queryData" />
    <PartStationQuickAddForm ref="quickAddFormRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationWarehouseApi } from '/@/api/business/mes/part-station/warehouse/part-station-warehouse-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import PartStationWarehouseForm from './part-station-warehouse-form.vue';
  import PartStationQuickAddForm from '/@/views/business/mes/part-station/warehouse/part-station-quickAdd-form.vue';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api.js';
  import { useRoute, useRouter } from 'vue-router';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '仓库编码',
      dataIndex: 'warehouseCode',
      ellipsis: true,
    },
    {
      title: '仓库名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '仓库地址',
      dataIndex: 'address',
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
      ellipsis: true,
    },
    // {
    //   title: '更新时间',
    //   dataIndex: 'updateTime',
    //   ellipsis: true,
    // },
    // {
    //   title: '更新人',
    //   dataIndex: 'updateBy',
    //   ellipsis: true,
    // },

    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 160,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //名称查询
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  const router = useRouter();

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }
  //处理表格时间顺序变化事件
  async function handleTableChange(pagination,filters,sorter){
      if(sorter&&sorter.field==='createTime'){
        queryForm.sortItemList = 
        [{
          column:'create_time',
          isAsc:sorter.order==='ascend'
        }];
      }
      if(!sorter.order){
        queryForm.sortItemList=
        [{
          column:'create_time',
          isAsc:false
        }]
      }
      await queryData();
    }
  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await partStationWarehouseApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();
  const quickAddFormRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }
  function showQuickAddFormRef(data) {
    quickAddFormRef.value.show(data);
  }

  function toSetMap(data) {
    router.push({ path: '/part-station/map/detail', query: { mapPartStationWarehouseId: data.id } });
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  function onDelete(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  async function requestDelete(id) {
    try {
      SmartLoading.show();
      await partStationWarehouseApi.delete(id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await partStationWarehouseApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

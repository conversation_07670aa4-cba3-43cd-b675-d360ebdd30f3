/**
 * 主物料表 枚举
 *
 * @Author:    cjm
 * @Date:      2024-07-02 08:33:07
 * @Copyright  zscbdic
 */

export const ITEM_CATEGORY_ENUM = {
    HALF_PRODUCT: {
        value: '0',
        desc: '半成品',
        label: '半成品',
        color: '#108ee9',
    },
    END_PRODUCT: {
        value: '1',
        desc: '成品',
        label: '成品',
        color: '#87d068',
    },
    getEnum(value) {
        if (value === '0') {
            return ITEM_CATEGORY_ENUM.HALF_PRODUCT;
        } else if (value === '1') {
            return ITEM_CATEGORY_ENUM.END_PRODUCT;
        }

    }
};

export const ITEM_ATTRIBUTE_ENUM = {
    CLOTH: {
        value: '0',
        desc: '面料',
        label: '面料',
        color: 'cyan',
    },
    OTHER: {
        value: '1',
        desc: '辅料',
        label: '辅料',
        color: 'purple',
    },
    CLOTHES: {
        value: '2',
        desc: '成衣',
        label: '成衣',
        color: 'orange',
    },
    getEnum(value) {
        if (value === '0') {
            return ITEM_ATTRIBUTE_ENUM.CLOTH;
        } else if (value === '1') {
            return ITEM_ATTRIBUTE_ENUM.OTHER;
        } else if (value === '2') {
            return ITEM_ATTRIBUTE_ENUM.CLOTHES;
        }

    }
};


export default {
    ITEM_CATEGORY_ENUM,
    ITEM_ATTRIBUTE_ENUM
};

import { postRequest, getRequest } from '/@/lib/axios';

export const produceReportApi = {

    /**
     * 查询执行汇总
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    queryExecuteSummary: (param) => {
        return postRequest(`/produceInstructOrderReport/executeSummary`, param);
    },

    /**
     * 查询执行明细
     * @param orderId
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    queryExecuteDetail: (orderId) => {
        return getRequest(`/produceInstructOrderReport/executeDetail/${orderId}`);
    },

    /**
     * 查询入库出库
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    queryProduceInOutStock: (param) => {
        return postRequest(`/proInAndOutStockReport/summaryPage`, param);
    }
};

<!--
  * 物料分类表
  *
  * @Author:    cjm
  * @Date:      2024-07-02 09:54:23
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
<!--                    <a-form-item label="主键"  name="id">-->
<!--                      <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />-->
<!--                    </a-form-item>-->
                    <a-form-item label="上级分类" name="parentId">
                      <a-cascader style="width: 100%" changeOnSelect :options="selectData" @change="handleCascaderChang"
                        :placeholder="placeholder" :disabled="disabledFlag" />
                    </a-form-item>
                    <a-form-item label="分类名称"  name="name">
                      <a-input style="width: 100%" v-model:value="form.name" placeholder="分类名称" />
                    </a-form-item>
                    <a-form-item label="备注"  name="remark">
                      <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
                    </a-form-item>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick,onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { itemTypeApi } from '/@/api/business/mes/item/item-type-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);
  // 是否禁用
const disabledFlag = ref(false);
// 是否为添加状态
const addFlag=ref(false)
// 下拉默认值
const placeholder = ref('请选择上级分类') ;
function show(categoryType, parentId, rowData) {
  // 新建
  if(parentId===undefined&&rowData===undefined){
    Object.assign(form, formDefault);
    disabledFlag.value = false;
    placeholder.value = '请选择上级分类';
    addFlag.value = true;
  }
  // 编辑
  if(parentId===undefined&&rowData!==undefined){
    Object.assign(form, rowData);
    disabledFlag.value = true;
    placeholder.value = categoryType;
    addFlag.value = false;
  }
  // 添加子分类
  if(parentId!==undefined&&rowData!==undefined){
    Object.assign(form, formDefault);
    form.parentId=rowData.id
    form.name=undefined // 清空
    placeholder.value = rowData.fullName;
    disabledFlag.value = true;
    addFlag.value = true;
  }

  visibleFlag.value = true;

  nextTick(() => {
    try {
      formRef.value.clearValidate();
    } catch (error) {
      console.error('清除表单验证失败:', error);
    }
  });
}

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();
  
  const formDefault = {
              id: undefined, //主键
              remark: undefined, //备注
              parentId: undefined, //父级id
              name: undefined, //分类名称
              sort: undefined, //排序
  };
  onMounted(() => getSelectValues())

  let form = reactive({ ...formDefault });

  const rules = {
                  // id: [{ required: true, message: '主键 必填' }],
                  name: [{ required: true, message: '分类名称 必填' }],
  };

  const handleCascaderChang=(value,selectedOptions)=>{
  form.parentId = selectedOptions[selectedOptions.length-1].id

}
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

 // 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (addFlag.value) {
      await itemTypeApi.add(form);

    } else {
      await itemTypeApi.update(form);
    }
    message.success('操作成功');
    getSelectValues();
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}
const selectData = ref([]);
// 获取下拉树数据
async function getSelectValues() {
  try {
    let queryResult = await itemTypeApi.queryCategoryTree({});
    selectData.value = queryResult.data;
  } catch (e) {
    smartSentry.captureError(e);
  } 
}

  defineExpose({
    show,
    getSelectValues,
  });
</script>

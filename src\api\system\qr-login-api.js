/**\
 * 扫描登录api
 */
import {getRequest, postRequest} from '/src/lib/axios';

export const qrLoginApi = {
    /**
     * 获取二维码 <AUTHOR>
     */
    getQrCode: () => {
        return getRequest('/qrLogin/init');
    },

    /**
     * 获取二维码状态 <AUTHOR>
     * @param param 二维码token
     */
    getQrCodeStatus: (param) => {
        return getRequest('/qrLogin/qrStatus', param);
    },

    /**
     * 登录 <AUTHOR>
     * @param param 二维码token
     */
    login: (param) => {
        return getRequest('/qrLogin/login', param);
    }
}

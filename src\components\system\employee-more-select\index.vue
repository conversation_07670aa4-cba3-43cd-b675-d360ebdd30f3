<!--
  * 员工下拉多选选择框
  * 返回对象列表
  * @Author:    fkf
  * @Date:      2025-03-11
  * @Copyright  zscbdic
-->
<template>
    <a-select
      v-model:value="selectValue"
      :style="`width: ${width}`"
      :placeholder="placeholder"
      :showSearch="true"
      :allowClear="true"
      mode="multiple"
      :max-tag-count="1"
      :filterOption="filterOption"
      @change="handleChange"
    >
      <a-select-option
        v-for="item in employeeList"
        :key="item.employeeId"
        :value="item.employeeId"
      >
        {{ item.actualName }}
        <template v-if="item.departmentName">（{{ item.departmentName }}）</template>
      </a-select-option>
    </a-select>
  </template>
  
  <script setup>
  import { ref, watch, onMounted } from 'vue';
  import { employeeApi } from '/src/api/system/employee-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  
  const props = defineProps({
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择负责人'
    },
    width: {
      type: String,
      default: '100%'
    }
  });
  
  const emit = defineEmits(['update:value', 'change']); 
  
  const employeeList = ref([]);
  const selectValue = ref(props.value || []); 
  
  // 监听props变化
  watch(() => props.value, (newVal) => { 
    selectValue.value = newVal || [];
  });
  
  // 获取员工列表
  async function queryEmployeeList() {
    try {
      const res = await employeeApi.queryAll({});
      employeeList.value = res.data;
    } catch (err) {
      smartSentry.captureError(err);
    }
  }
  
  function filterOption(inputValue, option) {
    const item = employeeList.value.find((i) => i.employeeId === option.value);
    return item?.actualName?.toLowerCase().includes(inputValue.toLowerCase());
  }
  
  // 处理选择变化
  function handleChange(values) {
    // 获取所有选中的员工对象列表
    const selectedEmployees = values.map(value => 
      employeeList.value.find(e => e.employeeId == value)
    ).filter(Boolean);
    
    emit('update:value', values); 
    emit('change', selectedEmployees);
  }
  
  onMounted(() => {
    queryEmployeeList();
  });
  </script>
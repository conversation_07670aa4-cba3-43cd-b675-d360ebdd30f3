<!--
  * 自定义工资项值
  * 编辑工资项值
  * @Author:    linwj
  * @Date:      2024-12-05 22:48:59
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      title="批量调整"
      width="450px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-table :columns="columns"
             :data-source="tableData"
             size="small"
             bordered
             :pagination="false"
             row-key="id"
             :row-selection=rowSelection
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'fieldValue'">
          <a-input-number
              default-value="0.00"
              addon-after="元"
              :min=0
              :controls=false
              :precision=2
              v-model:value="record.fieldValue"
          />
        </template>
      </template>
    </a-table>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button @click="onSave" type="primary">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {onMounted, ref} from 'vue';
import {smartSentry} from "/@/lib/smart-sentry.js";
import {wageFieldApi} from "/@/api/business/mes/salary/wage-field-api.js";
import {message} from "ant-design-vue";
import _ from "lodash";
import {wageFieldValueApi} from "/@/api/business/mes/salary/wage-field-value-api.js";
import {itemApi} from "/@/api/business/mes/item/item-api.js";
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(ids) {
  visibleFlag.value = true;
  empIds.value = ids
}

function onClose() {
  selectedRowKeyList.value.length = 0
  selectedRowList.value.length = 0
  tableData.value.map((item)=>{
    item.fieldValue = 0
  })
  visibleFlag.value = false;
}

const columns = [
  {
    title: '工资项',
    dataIndex: 'fieldName',
    align: 'center',
    width: '40px'
  },
  {
    title: '默认值',
    dataIndex: 'fieldValue',
    align: 'center',
    width: '30px'
  },
];

const tableData = ref([]);
// 查询数据
async function queryData() {
  try {
    let queryResult = await wageFieldApi.queryList();
    tableData.value = queryResult.data;
  } catch (e) {
    smartSentry.captureError(e);
  }
}

//------------------ 更新工资项值 --------------------
const selectedRowKeyList = ref([])
const selectedRowList = ref([])
const rowSelection = {
  onChange: (selectedRowKeys,selectedRows) => {
    selectedRowKeyList.value = selectedRowKeys
    selectedRowList.value = selectedRows
  }
};
// 员工id
const empIds = ref([])

//保存工资项
function onSave() {
  if (_.isEmpty(selectedRowKeyList.value)) {
    message.warning("请先勾选要调整的工资项");
    return;
  }
  let params = {
    employeeIds: empIds.value,
    items: selectedRowList.value.map(item=>{
      if(!_.isNil(item.fieldValue)){
        return {
          fieldId: item.id,
          value: item.fieldValue
        }
      }
    })
  }
  saveWageItem(params)
}

async function saveWageItem(params) {
  try {
    await wageFieldValueApi.batchUpdateValue(params)
    message.success("保存成功");
    onClose()
    emits('reloadList')
  } catch (e) {
    smartSentry.captureError(e);
  }
}

onMounted(()=>{
  queryData()
})

defineExpose({
  show,
});
</script>
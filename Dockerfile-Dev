## 设置基础镜像
#FROM nginx
## 定义作者
#MAINTAINER cjm
#
#COPY /nginx/nginx.conf /etc/nginx/nginx.conf
#
## 将dist文件中的内容复制到 /usr/share/nginx/html/ 这个目录下面
#COPY dist/  /usr/share/nginx/html/
#----------------------------------------------

#声明变量
ARG NODE_ENV=docker

#
FROM node:alpine as builder

WORKDIR /app

# 确保路径正确
COPY package-lock.json .
COPY package.json .

RUN npm install --registry=https://mirrors.cloud.tencent.com/npm --legacy-peer-deps

# 复制项目文件
COPY . .

#选择构建环境
RUN npm run build:dev
#打印
RUN echo "ARG ENV = ${NODE_ENV}"


# 打包项目
FROM nginx:1.15.2-alpine

# Nginx config
#COPY nginx/nginx.conf /etc/nginx/nginx.conf

COPY --from=builder /app/dist /usr/share/nginx/html/

# 复制企业微信认证文件
COPY nginx/WW_verify_z2Zffeoe0j3bVAY6.txt /usr/share/nginx/html/

EXPOSE 80


#CMD ["nginx", "-g", "daemon off;"]

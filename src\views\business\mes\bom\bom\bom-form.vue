<!--
  * 物料BOM表
  *
  * @Author:    cyz
  * @Date:      2024-07-09 10:25:22
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-col :span="25">
              <a-form-item label="主键"  name="id">
                <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="BOM编号"  name="bomNumber">
                <a-input style="width: 100%" v-model:value="form.bomNumber" placeholder="bom编号" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="BOM名称"  name="bomName">
                <a-input style="width: 100%" v-model:value="form.bomName" placeholder="bom名称" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="版本号"  name="versionNumber">
                <a-input-number style="width: 100%" v-model:value="form.versionNumber" placeholder="版本号" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料ID"  name="itemId">
                <a-input-number style="width: 100%" v-model:value="form.itemId" placeholder="物料id" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料名称"  name="itemName">
                <a-input style="width: 100%" v-model:value="form.itemName" placeholder="物料名称" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料编号"  name="itemNumber">
                <a-input style="width: 100%" v-model:value="form.itemNumber" placeholder="物料编号" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料规格型号"  name="itemModel">
                <a-input style="width: 100%" v-model:value="form.itemModel" placeholder="物料规格型号" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料分类ID"  name="itemTypeId">
                <a-input-number style="width: 100%" v-model:value="form.itemTypeId" placeholder="物料分类id" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料类型"  name="itemCategory">
                <a-input style="width: 100%" v-model:value="form.itemCategory" placeholder="物料类型" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料单位ID"  name="itemUnitId">
                <a-input-number style="width: 100%" v-model:value="form.itemUnitId" placeholder="物料单位id" />
              </a-form-item>
        </a-col>
        <a-col :span="25">
              <a-form-item label="物料属性"  name="itemAttribute">
                <a-input style="width: 100%" v-model:value="form.itemAttribute" placeholder="物料属性" />
              </a-form-item>
        </a-col>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { bomApi } from '/@/api/business/mes/bom/bom-api.js'
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      id: undefined,
      bomNumber: undefined, //bom编号
      bomName: undefined, //bom名称
      versionNumber: undefined, //版本号
      itemId: undefined, //物料id
      itemName: undefined, //物料名称
      itemNumber: undefined, //物料编号
      itemModel: undefined, //物料规格型号
      itemTypeId: undefined, //物料分类id
      itemCategory: undefined, //物料类型
      itemUnitId: undefined, //物料单位id
      itemAttribute: undefined, //物料属性
  };

  let form = reactive({ ...formDefault });

  const rules = {
      id: [{ required: true, message: '主键 必填' }],
      bomNumber: [{ required: true, message: 'bom编号 必填' }],
      bomName: [{ required: true, message: 'bom名称 必填' }],
      versionNumber: [{ required: true, message: '版本号 必填' }],
      itemId: [{ required: true, message: '物料 必填' }],
      itemName: [{ required: true, message: '物料名称 必填' }],
      itemNumber: [{ required: true, message: '物料编号 必填' }],
      itemCategory: [{ required: true, message: '物料类型 必填' }],
      itemUnitId: [{ required: true, message: '物料单位 必填' }],
      itemAttribute: [{ required: true, message: '物料属性 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await bomApi.update(form);
      } else {
        await bomApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

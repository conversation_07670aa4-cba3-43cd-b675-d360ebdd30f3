<!--
  * 数字输入框组件（带数字键盘）
-->
<template>
  <div class="number-input-keyboard">
    <!-- 输入框 -->
    <a-input-number
        v-model:value="inputValue"
        :placeholder="placeholder"
        :min="min"
        :max="max"
        :precision="precision"
        :disabled="disabled"
        :size="size"
        :style="inputStyle"
        @change="handleChange"
        @blur="handleBlur"
        @focus="handleFocus"
    >
      <!-- 左侧插槽 - 数字键盘触发按钮 -->
      <template #addonBefore>
        <slot name="addonBefore">
          <a-button
              type="text"
              size="small"
              @click="showKeyboard"
              :disabled="disabled"
          >
            <template #icon>
              <calculator-outlined />
            </template>
          </a-button>
        </slot>
      </template>

      <!-- 右侧插槽 - 显示单位 -->
      <template #addonAfter v-if="unit">
        <span>{{ unit }}</span>
      </template>
    </a-input-number>

    <!-- 数字键盘弹窗 -->
    <a-modal
        v-model:open="keyboardVisible"
        title="数字键盘"
        :width="320"
        :footer="null"
        :mask-closable="true"
        @cancel="hideKeyboard"
    >
      <div class="keyboard-container">
        <!-- 显示区域 -->
        <div class="keyboard-display">
          <a-input
              v-model:value="keyboardValue"
              :placeholder="placeholder"
              readonly
              size="large"
              style="text-align: right; font-size: 18px;"
          />
        </div>

        <!-- 键盘区域 -->
        <div class="keyboard-grid">
          <a-button @click="inputDigit('7')" size="large">7</a-button>
          <a-button @click="inputDigit('8')" size="large">8</a-button>
          <a-button @click="inputDigit('9')" size="large">9</a-button>
          <a-button @click="clearAll" size="large" danger>C</a-button>

          <a-button @click="inputDigit('4')" size="large">4</a-button>
          <a-button @click="inputDigit('5')" size="large">5</a-button>
          <a-button @click="inputDigit('6')" size="large">6</a-button>
          <a-button @click="backspace" size="large">⌫</a-button>

          <a-button @click="inputDigit('1')" size="large">1</a-button>
          <a-button @click="inputDigit('2')" size="large">2</a-button>
          <a-button @click="inputDigit('3')" size="large">3</a-button>
          <a-button @click="confirm" size="large" type="primary" class="confirm-btn">确定</a-button>

          <a-button @click="inputDigit('0')" size="large" class="zero-btn">0</a-button>
          <a-button @click="inputDecimal" size="large" v-if="precision > 0">.</a-button>
          <div v-else></div> <!-- 占位符 -->
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { CalculatorOutlined } from '@ant-design/icons-vue';

// Props
const props = defineProps({
  value: {
    type: [Number, String],
    default: null
  },
  placeholder: {
    type: String,
    default: '请输入数字'
  },
  min: {
    type: Number,
    default: undefined
  },
  max: {
    type: Number,
    default: undefined
  },
  precision: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'middle'
  },
  inputStyle: {
    type: Object,
    default: () => ({})
  },
  unit: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:value', 'change', 'blur', 'focus']);

// 响应式数据
const inputValue = ref(props.value);
const keyboardVisible = ref(false);
const keyboardValue = ref('');

// 监听外部值变化
watch(() => props.value, (newVal) => {
  inputValue.value = newVal;
});

// 监听内部值变化
watch(inputValue, (newVal) => {
  emit('update:value', newVal);
});

// 方法
const handleChange = (value) => {
  emit('change', value);
};

const handleBlur = (e) => {
  emit('blur', e);
};

const handleFocus = (e) => {
  emit('focus', e);
};

// 显示键盘
const showKeyboard = () => {
  if (props.disabled) return;
  keyboardValue.value = inputValue.value?.toString() || '';
  keyboardVisible.value = true;
};

// 隐藏键盘
const hideKeyboard = () => {
  keyboardVisible.value = false;
};

// 输入数字
const inputDigit = (digit) => {
  keyboardValue.value += digit;
};

// 输入小数点
const inputDecimal = () => {
  if (props.precision > 0 && !keyboardValue.value.includes('.')) {
    keyboardValue.value += '.';
  }
};

// 退格
const backspace = () => {
  keyboardValue.value = keyboardValue.value.slice(0, -1);
};

// 清空
const clearAll = () => {
  keyboardValue.value = '';
};

// 确定
const confirm = () => {
  let value = parseFloat(keyboardValue.value) || 0;
  
  // 检查范围
  if (props.min !== undefined && value < props.min) {
    value = props.min;
  }
  if (props.max !== undefined && value > props.max) {
    value = props.max;
  }
  
  // 处理精度
  if (props.precision > 0) {
    value = parseFloat(value.toFixed(props.precision));
  } else {
    value = parseInt(value);
  }
  
  inputValue.value = value;
  hideKeyboard();
};
</script>

<style scoped>
.number-input-keyboard {
  width: 100%;
}

.keyboard-container {
  padding: 16px 0;
}

.keyboard-display {
  margin-bottom: 16px;
}

.keyboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  grid-template-areas:
    "seven eight nine clear"
    "four five six back"
    "one two three confirm"
    "zero zero decimal confirm";
}

.keyboard-grid .ant-btn:nth-child(1) { grid-area: seven; }
.keyboard-grid .ant-btn:nth-child(2) { grid-area: eight; }
.keyboard-grid .ant-btn:nth-child(3) { grid-area: nine; }
.keyboard-grid .ant-btn:nth-child(4) { grid-area: clear; }
.keyboard-grid .ant-btn:nth-child(5) { grid-area: four; }
.keyboard-grid .ant-btn:nth-child(6) { grid-area: five; }
.keyboard-grid .ant-btn:nth-child(7) { grid-area: six; }
.keyboard-grid .ant-btn:nth-child(8) { grid-area: back; }
.keyboard-grid .ant-btn:nth-child(9) { grid-area: one; }
.keyboard-grid .ant-btn:nth-child(10) { grid-area: two; }
.keyboard-grid .ant-btn:nth-child(11) { grid-area: three; }
.keyboard-grid .ant-btn:nth-child(12) { grid-area: confirm; }
.keyboard-grid .ant-btn:nth-child(13) { grid-area: zero; }
.keyboard-grid .ant-btn:nth-child(14) { grid-area: decimal; }

.zero-btn {
  grid-column: span 2;
}

.confirm-btn {
  grid-row: span 2;
  height: auto;
}
</style>

<!--
  * 松布作业
  *
  * @Author:    wcc
  * @Date:      2025-06-22 21:03:26
  * @Copyright  weavewise.zscbdic.cn
-->
<template>
  <a-modal
    :title="form.id ? '编辑松布计划' : '添加松布计划'"
    :width="1400"
    :open="visibleFlag"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" style="margin-top: 24px">
      <a-row :gutter="[24, 24]">
        <a-col :span="24">
          <!-- 表单字段区域 - 2x4布局 -->
          <a-row :gutter="[24, 24]">
            <a-col :span="6">
              <div class="info-item">
                <span class="label">生产指令单编号</span>
                <span class="value" v-if="form.instructNumber">{{ form.instructNumber }}</span>
                <span class="empty-value" v-else>未选择</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="info-item">
                <span class="label">物料编号</span>
                <span class="value" v-if="form.itemNumber">{{ form.itemNumber }}</span>
                <span class="empty-value" v-else>未选择</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="info-item">
                <span class="label">物料名称</span>
                <span class="value" v-if="form.itemName">{{ form.itemName }}</span>
                <span class="empty-value" v-else>未选择</span>
              </div>
            </a-col>
            <a-col :span="6">
              <div class="info-item">
                <span class="label">客户名称</span>
                <span class="value" v-if="form.customerName">{{ form.customerName }}</span>
                <span class="empty-value" v-else>-</span>
              </div>
            </a-col>
            <a-col :span="6">
              <a-form-item label="松布计划单编号" name="fabricLoosenNumber">
                <a-input v-model:value="form.fabricLoosenNumber" placeholder="计划单编号，可不填写" style="width: 80%" />
              </a-form-item>
            </a-col>
            <!-- <a-col :span="6">
              <a-form-item label="小组" name="workUnit" style="width: 80%">
                <a-select v-model:value="form.workUnit" placeholder="请选择小组" />
              </a-form-item>
            </a-col> -->
            <a-col :span="6">
              <a-form-item label="计划开始时间" name="planedStTime">
                <a-date-picker
                  v-model:value="form.planedStTime"
                  style="width: 80%"
                  :show-time="{ format: 'HH:mm' }"
                  value-format="YYYY-MM-DD HH:mm"
                  format="YYYY-MM-DD HH:mm"
                />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="总卷数" name="rolls">
                <a-input v-model:value="form.rolls" disabled :addonAfter="'卷'" style="width: 80%" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-col>
      </a-row>

      <!-- 表格区域 -->
      <a-row class="smart-table-btn-block" style="margin: 24px 0">
        <div><strong>松布计划明细</strong></div>
        <a-button @click="showForm" type="primary" style="margin-left: 24px">
          <template #icon><search-outlined /></template>
          选择指令单
        </a-button>
        <produce-instruct-order-table-select-modal ref="produceInstructOrderModalRef" @select-data="selectData" />
      </a-row>
      <a-table size="small" :dataSource="tableData" :columns="columns" :pagination="false" bordered :scroll="{ x: 1000, y: 300 }">
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'needLoosen'">
            <a-checkbox v-model:checked="record.needLoosen" @change="(e) => handleLoosenChange(e, record)" />
          </template>
          <template v-else-if="column.dataIndex === 'rolls'">
            <a-input-number v-model:value="record.rolls" :min="1" style="width: 100%" @change="calculateTotalRolls" :disabled="!record.needLoosen" />
          </template>
          <template v-else-if="column.dataIndex === 'lotNo'">
            <a-input v-model:value="record.lotNo" disabled />
          </template>
          <template v-else-if="column.dataIndex === 'loosenTime'">
            <a-select
              v-model:value="record.loosenTime"
              style="width: 100%"
              placeholder="请选择时长"
              :disabled="!record.needLoosen"
              :options="loosenTimeOptions"
              :fieldNames="{ label: 'label', value: 'value' }"
              @change="(value) => handleLoosenTimeChange(value, record)"
              :defaultActiveFirstOption="true"
            />
          </template>
          <template v-if="column.dataIndex === 'planedStTime'">
            <a-date-picker
              v-model:value="record.planedStTime"
              :show-time="{ format: 'HH:mm' }"
              style="width: 100%"
              @change="() => updateTimes(record, 'planedStTime')"
              format="MM-DD HH:mm"
              value-format="MM-DD HH:mm"
              :disabled="!record.needLoosen"
            />
          </template>
        </template>
      </a-table>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import ProduceInstructOrderTableSelectModal from '/@/components/business/mes/produce/produce-instruct-order-table-select-modal/index.vue';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api';
  import dayjs from 'dayjs';
  import { loosenPlanApi } from '/@/api/business/mes/tailor/loosen-plan-api';

  const emits = defineEmits(['reloadList']);

  //--------------------------------------------表格----------------------------------------
  const tableData = ref([]);
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      align: 'center',
      customRender: ({ index }) => index + 1,
    },
    {
      title: '需要松布',
      dataIndex: 'needLoosen',
      width: 80,
      align: 'center',
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      width: 150,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      width: 150,
    },
    {
      title: '颜色',
      dataIndex: 'itemColor',
      width: 150,
    },
    {
      title: '松布时长',
      dataIndex: 'loosenTime',
      width: 100,
    },
    {
      title: '缸号',
      dataIndex: 'lotNo',
      width: 80,
    },
    {
      title: '卷数',
      dataIndex: 'rolls',
      width: 80,
    },
    {
      title: '单位',
      dataIndex: 'itemUnit',
      width: 80,
    },
    {
      title: '计划开始时间',
      dataIndex: 'planedStTime',
      width: 160,
    },
    {
      title: '计划结束时间',
      dataIndex: 'planedEndTime',
      width: 160,
      customRender: ({ text }) => (text ? dayjs(text).format('MM-DD HH:mm') : ''),
    },
  ];
  //预设松布时长选项
  const loosenTimeOptions = [
    { label: '12小时', value: 12 },
    { label: '24小时', value: 24 },
    { label: '48小时', value: 48 },
  ];
  // 计算卷数总和
  const calculateTotalRolls = () => {
    const total = tableData.value.reduce((sum, row) => {
      if (row.needLoosen && row.rolls) {
        return sum + Number(row.rolls);
      }
      return sum;
    }, 0);
    form.rolls = total;
  };
  //松布时长变化
  const handleLoosenTimeChange = (value, record) => {
    record.loosenTime = value;
    // 更新结束时间
    if (record.planedStTime) {
      record.planedEndTime = dayjs(record.planedStTime).add(value, 'hour');
    }
  };
  //是否需要松布
  const handleLoosenChange = (e, record) => {
    const checked = e.target.checked;
    if (checked) {
      // 勾选时，重置
      record.rolls = 0;
      record.lotNo = '';
      record.loosenTime = 24;
      record.planedStTime = form.planedStTime ? dayjs(form.planedStTime) : dayjs();
      record.planedEndTime = record.planedStTime.add(record.loosenTime, 'hour');
    } else {
      // 取消勾选，清空
      record.rolls = undefined;
      record.lotNo = '';
      record.loosenTime = 0;
      record.planedStTime = form.planedStTime ? dayjs(form.planedStTime) : null;
      record.planedEndTime = null;
    }
    calculateTotalRolls();
  };
  // 计算结束时间
  const updateTimes = (record, changedField) => {
    const { planedStTime, loosenTime } = record;

    if (changedField === 'planedStTime' && planedStTime && loosenTime) {
      record.planedEndTime = dayjs(planedStTime).add(loosenTime, 'hour');
    }
  };

  //--------------------------------------------表单----------------------------------------
  const formDefault = {
    id: undefined,
    fabricLoosenNumber: '', // 松布计划单编号
    planedStTime: '', //计划开始时间
    rolls: 0, //卷数
    // workUnit: '', //小组
    instructId: undefined, //指令单id
    instructNumber: '', // 生产指令单编号
    itemId: undefined, //物料id
    itemNumber: undefined, // 物料编号
    itemName: undefined, // 物料名称
    customerId: undefined, //客户id
    customerName: '', //客户名称
  };
  const form = reactive({ ...formDefault });
  const formRef = ref();
  // 表单校验规则
  const rules = {
    planedStTime: [{ required: true, message: '请选择计划开始时间' }],
    instructId: [{ required: true, message: '请选择生产指令单' }],
  };
  //重置
  function resetForm() {
    Object.assign(form, formDefault);
    tableData.value = [];
  }
  //数据校验
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      if (!form.instructId) {
        message.warning('请选择生产指令单');
        return;
      }
      if (form.rolls === '' || form.rolls === undefined || form.rolls === null) {
        form.rolls = 0;
      }
      await save();
    } catch (error) {
      message.error('表单验证失败，请检查必填项');
    }
  }
  //数据保存
  async function save() {
    const submitData = buildSubmitData();
    try {
      await (form.id ? loosenPlanApi.update(submitData) : loosenPlanApi.add(submitData));
      message.success('保存成功');
      emits('reloadList');
      onClose();
    } catch (error) {
      message.error('保存失败');
    }
  }
  //数据组装
  function buildSubmitData() {
    const loosenPlanDetails = tableData.value
      .filter((item) => item.needLoosen)
      .map((item) => ({
        itemId: item.itemId,
        itemNumber: item.itemNumber || '',
        itemName: item.itemName || '',
        itemColor: item.itemColor || '',
        lotNo: item.lotNo || '',
        rolls: Number(item.rolls) || 0,
        itemUnit: item.itemUnit || '',
        planedStTime: item.planedStTime ? dayjs(item.planedStTime).format('YYYY-MM-DD HH:mm:ss') : '',
        planedEndTime: item.planedEndTime ? dayjs(item.planedEndTime).format('YYYY-MM-DD HH:mm:ss') : '',
        loosenTime: Number(item.loosenTime) || 0,
        loosenStatus: item.loosenStatus || 0,
        fabricLoosenNumber: item.fabricLoosenNumber,
      }));
    const { loosenPlanDetails: _, ...restForm } = form;
    return {
      ...restForm,
      planedStTime: form.planedStTime ? dayjs(form.planedStTime).format('YYYY-MM-DD HH:mm:ss') : '',
      loosenPlanDetails,
    };
  }

  //监听表单的计划开始时间，控制表格计划开始时间
  watch(
    () => form.planedStTime,
    (newTime) => {
      if (newTime && tableData.value.length > 0) {
        tableData.value.forEach((item) => {
          item.planedStTime = dayjs(newTime);
          if (item.needLoosen && item.loosenTime) {
            item.planedEndTime = dayjs(newTime).add(item.loosenTime, 'hour');
          }
        });
      }
    }
  );

  //--------------------------------------------弹窗----------------------------------------
  // 是否显示
  const visibleFlag = ref(false);
  // 显示弹窗
  function show(rowData) {
    if (rowData) {
      Object.assign(form, {
        ...rowData,
        planedStTime: dayjs(rowData.planedStTime),
      });

      tableData.value = (rowData.fabricDetails || []).map((item) => ({
        ...item,
        needLoosen: true,
        planedStTime: dayjs(item.planedStTime),
        planedEndTime: dayjs(item.planedStTime).add(item.loosenTime, 'hour'),
      }));
    } else {
      //重置
      resetForm();
      tableData.value = [];
    }
    visibleFlag.value = true;
  }
  // 关闭弹窗
  function onClose() {
    resetForm();
    visibleFlag.value = false;
  }

  //----------------------------------选择生产指令单数据处理-----------------------------------
  const produceInstructOrderModalRef = ref();
  //生产指令单模态框
  function showForm() {
    produceInstructOrderModalRef.value.showModal();
  }
  //选中指令单处理数据
  const selectData = async (selectedData) => {
    resetForm();
    const filteredData = {
      instructId: selectedData.id,
      instructNumber: selectedData.instructNumber,
      itemId: selectedData.itemId,
      itemNumber: selectedData.itemNumber,
      itemName: selectedData.itemName,
      customerId: selectedData.customerId || undefined,
      customerName: selectedData.customerName || '',
      planedStTime: '',
      rolls: 0,
    };
    Object.assign(form, filteredData);
    const res = await produceInstructOrderApi.getDetails(selectedData.id);
    const fabricItems = res.data.itemList
      // itemAttribute = 0 为布料
      .filter((item) => item.itemAttribute === '0')
      .map((item) => ({
        ...item,
        itemId: item.itemId,
        itemColor: item.itemModel,
        itemUnit: item.itemUnitName,
        rolls: 0,
        needLoosen: false,
        planedStTime: dayjs(),
        loosenTime: 24,
        planedEndTime: null,
      }));
    tableData.value = fabricItems;
  };

  defineExpose({
    show,
  });
</script>

<style scoped>
  .smart-table-btn-block {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 8px;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .label {
    color: #909399;
    margin-right: 10px;
    font-size: 14px;
    min-width: 100px;
  }

  .value {
    color: #303133;
    font-size: 14px;
  }

  .empty-value {
    color: #f56c6c;
    font-size: 14px;
  }
</style>

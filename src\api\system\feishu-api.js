/*
*飞书api
*/

import { getRequest, postRequest } from '/src/lib/axios';

export const feishuApi = {
    /**
     * 获取飞书登录二维码配置
     */
    getFeishuQrCodeConfig: () => {
        return getRequest('/feishu/qrCodeConfig');
    },
    /**
     * 获取飞书配置
     */
    getFeishuConfig: () => {
        return getRequest('/feishu/getConfig');
    },
    /**
     * 更新飞书配置
     */
    updateFeishuConfig: (params) => {
        return postRequest('/feishu/updateConfig', params);
    },
};

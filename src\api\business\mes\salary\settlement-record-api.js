/**
 * 薪酬结算记录 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-11-20 20:48:59
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const settlementRecordApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/settlementRecord/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/settlementRecord/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/settlementRecord/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/settlementRecord/delete/${id}`);
  },

  /**
   * 查询记录详情  <AUTHOR>
   */
  queryDetailsByRecordId: (id) => {
      return getRequest(`/settlementRecord/queryDetails/${id}`);
  },

    /**
     * 取消结算
     */
    settlementCancel: (id) => {
        return getRequest(`/settlementRecord/cancelSettlement?ids=${id}`);
    },
};

<!--
  * 下载菲票
  * @param {Array} ids 菲票id
  * @param {Array} parts 部位信息
  * @param {Array} processes 工序信息
  * @param {String} template 模板
-->

<template>
    <a-drawer
    title="下载菲票内容"
    :width="500"
    :open="visible"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="tickForm" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="模板">
            <a-select
              ref="select"
              placeholder="请输入模板"
              v-model:value="tickForm.template"
              :options="$smartEnumPlugin.getValueDescList('Template')"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="handleDownload">下载菲票</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
  import { ref, getCurrentInstance } from 'vue';
  import { message } from 'ant-design-vue';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  
const { proxy } = getCurrentInstance();
  const visible = ref(false);
  const tickForm = ref({
    ids:[],
    parts:[],
    processes: [],
    template: undefined,
  });

  const rules = {
    template: [{ required: true, message: '请选择模板' }],
  };
  // 关闭抽屉
  const onClose = () => {
    visible.value = false;
    // 重置表单
    tickForm.value = {
      ids: [],
      parts: [],
      processes: [],
      template: undefined,
    };
  };
  
    // 下载菲票
  const handleDownload = async () => {
    try {
      await feTicketApi.downLoad(tickForm.value);
      message.success('下载成功');
      onClose();
    } catch (error) {
      smartSentry.captureError(error);
      message.error('下载菲票失败');
    }
  };

  // 显示抽屉的方法
  const show = (data) => {
    tickForm.value = data;
    visible.value = true;
  };

const getData = (data) => {
  tickForm.value = data;
  const templateOptions = proxy.$smartEnumPlugin.getValueDescList('Template');
  if (templateOptions && templateOptions.length > 0) {
    tickForm.value.template = templateOptions[0].value;
  }
  handleDownload();
};

defineExpose({
  show,
  getData,
});
</script>

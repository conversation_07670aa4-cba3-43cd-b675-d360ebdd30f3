<!--
  * 客户表
  *
  * @Author:    xmt
  * @Date:      2024-07-03 09:43:14
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false"
    :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="客户编号" name="number">
        <a-input style="width: 150px" v-model:value="form.number" placeholder="客户编号" />
      </a-form-item>
      <a-form-item label="客户名称" name="name">
        <a-input style="width: 150px" v-model:value="form.name" placeholder="客户名称" />
      </a-form-item>
      <a-form-item label="公司名称" name="company">
        <a-input style="width: 150px" v-model:value="form.company" placeholder="公司名称" />
      </a-form-item>
      <a-form-item label="联系人" name="contact">
        <a-input style="width: 150px" v-model:value="form.contact" placeholder="联系人" />
      </a-form-item>
      <a-form-item label="电话&nbsp&nbsp" name="telephone">
        <a-input style="width: 150px" v-model:value="form.telephone" placeholder="电话" />
      </a-form-item>
      <a-form-item label="地址&nbsp&nbsp" name="address">
        <a-input style="width: 100%" v-model:value="form.address" placeholder="地址" />
      </a-form-item>
      <a-form-item label="停用标识" name="enableFlag">
        <a-switch v-model:checked="form.enableFlag" :unCheckedValue="0" :checkedValue="1" />
        <!-- 0启动，1停用" class="custom-switch"-->
      </a-form-item>

      <a-form-item label="等级" name="level">
        <a-rate :value="Number(form.level)" @update:value="handleLevelChange"/>
        <!-- 5星最高，无半星" -->
      </a-form-item>
      <a-form-item label="备注&nbsp&nbsp" name="remark">
        <a-textarea style="width: 350px;height: 100px" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { customerApi } from '/@/api/business/mes/base/customer-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    if(!rowData.id){
      getClientNo()
    }
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }
//自动生产客户编号
  async function getClientNo(){
    try{
      const res = await customerApi.getClientNo();
      form.number = res.data
    } catch(err) {
      smartSentry.captureError(err);
    }
  }
  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
          id: undefined,
          number: undefined, //客户编号
          name: undefined, //客户名称
          company: undefined, //公司名称
          contact: undefined, //联系人
          telephone: undefined, //电话
          address: undefined, //地址
          enableFlag: undefined, //停用标识;0启用，1停用
          level: undefined, //等级;5星最高，无半星
          remark: undefined, //备注
  };

  let form = reactive({ ...formDefault });

  const rules = {
      name: [{ required: true, message: '客户名称 必填' }],
      company: [{ required: true, message: '公司名称 必填' }],
  };
  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await customerApi.update(form);
      } else {
        await customerApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  // 处理等级变化
function handleLevelChange(value) {
  form.level = value;
}

  defineExpose({
    show,
  });
</script>
/**
 * 工厂表 api 封装
 *
 * @Author:    cyz
 * @Date:      2024-07-12 11:11:50
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const factoryApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/factory/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/factory/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/factory/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/factory/delete/${id}`);
  },

};

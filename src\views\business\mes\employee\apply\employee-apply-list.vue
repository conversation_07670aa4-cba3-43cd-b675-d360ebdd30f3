<!--
  * 员工申请表
  *
  * @Author:    cjm
  * @Date:      2024-09-19 22:50:32
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
      <a-form-item label="电话号查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.phone" placeholder="电话号查询"/>
      </a-form-item>
      <a-form-item label="性别" class="smart-query-form-item">
        <a-select v-model:value="queryForm.gender" style="width: 100px;" placeholder="请选择性别">
          <a-select-option v-for="item in GENDER_ENUM" :value="item.value" :key="item.value">
            {{item.desc}}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="申请状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.applyStatus" style="width: 100px;" placeholder="请选择申请状态">
          <a-select-option v-for="item in EMPLOYEE_APPLY_STATUS_ENUM" :value="item.value" :key="item.value">{{item.label}}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="创建时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.createTime" :presets="defaultTimeRanges" style="width: 150px"
                        @change="onChangeCreateTime"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <!--        <a-row class="smart-table-btn-block">-->
    <!--            <div class="smart-table-operate-block">-->
    <!--                <a-button @click="showForm" type="primary" size="small">-->
    <!--                    <template #icon>-->
    <!--                        <PlusOutlined />-->
    <!--                    </template>-->
    <!--                    新建-->
    <!--                </a-button>-->
    <!--                <a-button @click="confirmBatchDelete" type="danger" size="small" :disabled="selectedRowKeyList.length == 0">-->
    <!--                    <template #icon>-->
    <!--                        <DeleteOutlined />-->
    <!--                    </template>-->
    <!--                    批量删除-->
    <!--                </a-button>-->
    <!--            </div>-->
    <!--            <div class="smart-table-setting-block">-->
    <!--                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />-->
    <!--            </div>-->
    <!--        </a-row>-->
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false">
      <!--                :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }">-->
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'gender'">
          <div class="smart-table-operate">
            <div v-if="text===1">男</div>
            <div v-else>女</div>

          </div>
        </template>
        <template v-if="column.dataIndex === 'applyStatus'">
          <div class="smart-table-operate">
              <a-tag :color="EMPLOYEE_APPLY_STATUS_ENUM.getEnum(text).color ">
                {{ EMPLOYEE_APPLY_STATUS_ENUM.getEnum(text).label }}
              </a-tag>
            <!--            <span :style="{color:Role_DEPARTMENT_ENUM.getEnum(text).color}"-->
            <!--                  style="font-weight: bold"> {{ Role_DEPARTMENT_ENUM.getEnum(text).label }}</span>-->

          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">

            <a-button  @click="acceptApply(record)" type="link">通过申请</a-button>
<!--            <a-button v-else-if="acceptFlag===true" type="link" style="color: green" disabled>已通过</a-button>-->
<!--            <a-button v-else type="link" style="color: red" disabled>已拒绝</a-button>-->
            <a-button @click="rejectApply(record)" danger type="link">拒绝申请</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <EmployeeApplyForm ref="formRef" @reloadList="queryData"/>
    <!--      部门选择-->
    <pass-modal ref="passModalRef"  @reloadList="queryData"/>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {employeeApplyApi} from '/@/api/business/mes/employee/employee-apply-api.js';
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';    
import {smartSentry} from '/@/lib/smart-sentry';
import EmployeeApplyForm from './employee-apply-form.vue';
import {defaultTimeRanges} from '/@/lib/default-time-ranges';
import {EMPLOYEE_APPLY_STATUS_ENUM} from "/@/constants/business/mes/employee/employee-const.js";
import {GENDER_ENUM} from "/@/constants/system/employee-const.js";
import PassModal from "/@/views/business/mes/employee/apply/components/pass-modal.vue";
// ---------------------------- 表格列 ----------------------------

const columns = ref([
  // {
  //     title: '主键',
  //     dataIndex: 'id',
  //     ellipsis: true,
  // },

  {
    title: '员工名称',
    dataIndex: 'actualName',
    ellipsis: true,
    align: "center"
  },
  {
    title: '性别',
    dataIndex: 'gender',
    ellipsis: true,
    align: "center"
  },
  {
    title: '登录账号',
    dataIndex: 'loginName',
    ellipsis: true,
    align: "center"
  },
  {
    title: '登录密码',
    dataIndex: 'loginPwd',
    ellipsis: true,
    align: "center"
  },
  {
    title: '手机号码',
    dataIndex: 'phone',
    ellipsis: true,
    align: "center"
  },
  {
    title: '申请状态', // 0待审核 1已同意 2已拒绝
    dataIndex: 'applyStatus',
    ellipsis: true,
    align: "center"
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 180,
    align: "center"
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //关键字查询
  phone: undefined, //电话号查询
  gender: undefined, //性别
  applyStatus: undefined, //申请状态;0待审核 1已同意 2已拒绝
  createTime: [], //创建时间
  createTimeBegin: undefined, //创建时间 开始
  createTimeEnd: undefined, //创建时间 结束
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{column: 'create_time', isAsc: false}],
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await employeeApplyApi.queryPage(queryForm);
    // console.log(queryResult)
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

function onChangeCreateTime(dates, dateStrings) {
  queryForm.createTimeBegin = dateStrings[0];
  queryForm.createTimeEnd = dateStrings[1];
}


onMounted(() => {
  queryData()
});

// ---------------------------- 通过申请/拒绝申请 ----------------------------
const formRef = ref(null);
const passModalRef = ref(null)


//  新建
function showForm() {

}



// 通过申请
async function acceptApply(record) {
  // formRef.value.show(data);
  passModalRef.value.show(record)
  // console.log(record)

}

//确认拒绝
function rejectApply(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要拒绝申请吗?',
    okText: '确定',
    okType: 'danger',
    onOk() {
      requestReject(data);
      queryData();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

// -----------------------------请求----------------------------------


//请求拒绝
async function requestReject(data) {
  SmartLoading.show();
  try {
    await employeeApplyApi.refuse(data.id);
    message.success('已拒绝');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await employeeApplyApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
</script>

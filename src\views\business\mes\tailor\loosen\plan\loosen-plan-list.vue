<!--
  * 松布作业-松布计划
  *
  * @Author:    wcc
  * @Date:      2025-06-22 21:03:26
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="松布计划单" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.fabricLoosenNumber" placeholder="松布计划单" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <!--            <a-button @click="onDelete(record)" danger type="link">删除</a-button>-->
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <LoosenPlanForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/src/components/framework/smart-loading';
  import { loosenPlanApi } from '/src/api/business/mes/tailor/loosen-plan-api';
  import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import TableOperator from '/src/components/support/table-operator/index.vue';
  import LoosenPlanForm from './loosen-plan-form.vue';
  import dayjs from 'dayjs';

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '松布计划单',
      dataIndex: 'fabricLoosenNumber',
      ellipsis: true,
    },
    {
      title: '计划开始时间',
      dataIndex: 'planedStTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '卷数',
      dataIndex: 'rolls',
      ellipsis: true,
      width: 100,
    },
    // {
    //   title: '小组',
    //   dataIndex: 'workUnit',
    //   ellipsis: true,
    // },
    {
      title: '指令单编号',
      dataIndex: 'instructNumber',
      ellipsis: true,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: '客户',
      dataIndex: 'customerName',
      ellipsis: true,
    },
    {
      title: '制单时间',
      dataIndex: 'createTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '制单人',
      dataIndex: 'createBy',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    fabricLoosenNumber: undefined, //单据编号
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ isAsc: false, column: 'create_time'}], // 指单时间降序排序
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await loosenPlanApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  async function showForm(record) {
    if (record?.id) {
      const req = {
        planId: record.id,
      };
      const res = await loosenPlanApi.getDetails(req);
      formRef.value.show(res.data);
    } else {
      formRef.value.show();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await loosenPlanApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

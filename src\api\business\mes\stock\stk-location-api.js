/**
 * 货位 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-05 15:14:20
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkLocationApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/stkLocation/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/stkLocation/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/stkLocation/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/stkLocation/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/stkLocation/batchDelete', idList);
  },

  /**
   * 查询列表  <AUTHOR>
   */
  queryList: (param) => {
    return postRequest('/stkLocation/queryList', param);
  },
};

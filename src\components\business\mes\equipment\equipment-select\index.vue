<template>
  <a-select
    v-model:value="selectedValue"
    :placeholder="props.placeholder"
    :options="equipmentList"
    :style="{ width: width }"
    @change="handleChange"
    :disabled="disabled"
    show-search
    :allow-clear="true"
    :filter-option="filterOption"
    :dropdown-style="{ maxHeight: '200px', overflow: 'auto' }"
  />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import _ from 'lodash';
import { equipmentApi } from '/@/api/business/mes/equipment/equipment-api';

const props = defineProps({
  value: {
    type: Number,
    default: undefined,
  },
  placeholder: {
    type: String,
    default: '请选择设备',
  },
  width: {
    type: String,
    default: '100%',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  workshopId: {
    type: Number,
    default: undefined,
  },
});

const emit = defineEmits(['update:value', 'change']);

const selectedValue = ref(props.value);
const equipmentList = ref([]);

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// 监听父组件传入的 value 变化
watch(
  () => props.value,
  (newVal) => {
    selectedValue.value = newVal;
  }
);

// 监听车间ID变化，重新查询设备列表
watch(
  () => props.workshopId,
  () => {
    queryEquipmentList();
  }
);

// 查询设备列表
async function queryEquipmentList() {
  try {
    const queryForm = {};

    // 如果传入了车间ID，添加到查询条件中
    // if (props.workshopId) {
    //   queryForm.workshopId = props.workshopId;
    // }

    const res = await equipmentApi.queryList(queryForm);
    equipmentList.value = [];
    if (!_.isEmpty(res?.data)) {
      equipmentList.value = res.data.map((item) => ({
        value: item.id,
        label: `${item.number} - ${item.name}`, // 显示设备编号和名称

      }));
    }
  } catch (error) {
    message.error('设备数据请求失败');
  }
}

// 处理选择变化
function handleChange(value) {
  emit('update:value', value);
  const selectedEquipment = equipmentList.value.find((item) => item.value === value);
  emit('change', selectedEquipment);
}

onMounted(() => {
  queryEquipmentList();
});
</script>
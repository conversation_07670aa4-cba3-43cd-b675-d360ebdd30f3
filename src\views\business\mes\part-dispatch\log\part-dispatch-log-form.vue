<!--
  * 裁片收发日志
  *
  * @Author:    cjm
  * @Date:      2024-11-06 15:25:13
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="主键" name="id">
        <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键"/>
      </a-form-item>
      <a-form-item label="菲票id" name="ticketId">
        <a-input-number style="width: 100%" v-model:value="form.ticketId" placeholder="菲票id"/>
      </a-form-item>
      <a-form-item label="收发范围" name="dispatchRange">
        <a-input style="width: 100%" v-model:value="form.dispatchRange" placeholder="收发范围"/>
      </a-form-item>
      <a-form-item label="收或发" name="action">
        <a-input style="width: 100%" v-model:value="form.action" placeholder="收或发"/>
      </a-form-item>
      <a-form-item label="供应商id" name="supplierId">
        <a-input style="width: 100%" v-model:value="form.supplierId" placeholder="供应商id"/>
      </a-form-item>
      <a-form-item label="工厂id" name="factoryId">
        <a-input style="width: 100%" v-model:value="form.factoryId" placeholder="工厂id"/>
      </a-form-item>
      <a-form-item label="车间id" name="workshopId">
        <a-input style="width: 100%" v-model:value="form.workshopId" placeholder="车间id"/>
      </a-form-item>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/src/components/framework/smart-loading';
import {partDispatchLogApi} from '/@/api/business/mes/part-dispatch/part-dispatch-log-api.js';
import {smartSentry} from '/src/lib/smart-sentry';

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {

  id: undefined, //主键
  ticketId: undefined, //菲票id
  dispatchRange: undefined, //收发范围
  action: undefined, //收或发
  supplierId: undefined, //供应商id
  factoryId: undefined, //工厂id
  workshopId: undefined, //车间id
};

let form = reactive({...formDefault});

const rules = {
  // id: [{required: true, message: '主键 必填'}],
  ticketId: [{required: true, message: '菲票id 必填'}],
  dispatchRange: [{required: true, message: '收发范围 必填'}],
  action: [{required: true, message: '收或发 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await partDispatchLogApi.update(form);
    } else {
      await partDispatchLogApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

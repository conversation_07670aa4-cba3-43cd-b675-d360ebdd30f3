/**
 * 布料信息表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-02 12:03:01
 * @Copyright  zscbdic
 */
import { postRequest, getRequest,getDownload } from '/@/lib/axios';

export const itemClothApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/item/cloth/queryPage', param);
  },

  /**
   * 根据ID查询详情 <AUTHOR>
   */
  getById: (param) => {
    return getRequest('/item/cloth/byId', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/item/cloth/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/item/cloth/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/item/cloth/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/item/cloth/batchDelete', idList);
  },
   // 下载基础物料导入模板
    downloadTemplate(){
      return getDownload(`/item/cloth/downloadTemplate`);
    },
    // 导入文件
  importfile:(file)=>{
    return postRequest('/item/cloth/import',file,);
  },

  /**
   * 删除  <AUTHOR>
   */
  getQrCode: (id) => {
    return getRequest(`/item/cloth/getQrCodeContent?clothId=${id}`);
  },
};
/**
 * 车间信息 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-09 10:31:51
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const workshopApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/workshop/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/workshop/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/workshop/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/workshop/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/workshop/batchDelete', idList);
  },

  /**
   * 车间下拉  <AUTHOR>
   */
  queryList: (param) => {
    return postRequest('/workshop/queryList', param);
  },
};

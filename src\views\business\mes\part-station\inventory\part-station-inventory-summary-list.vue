<!--
  * 裁片驿站库存表
  *
  * @Author:    cjm
  * @Date:      2024-10-07 19:44:34
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="指令单编号" class="smart-query-form-item">
        <a-select
            v-model:value="initInsOrItem"
            show-search
            placeholder="指令单编号"
            style="width: 200px"
            :options="insNoOptions"
            :filter-option="filterOption"
            @change="handleInsChange"
        />
      </a-form-item>
      <a-form-item label="物料编号" class="smart-query-form-item">
        <a-select
            v-model:value="initInsOrItem"
            show-search
            placeholder="物料编号"
            style="width: 200px"
            :options="itemNoOptions"
            :filter-option="filterOption"
            @change="handleItemChange"
        />
      </a-form-item>
      <a-form-item label="颜色" class="smart-query-form-item">
        <a-select
            v-model:value="initColorVal"
            :max-tag-count="maxTagCount"
            mode="multiple"
            style="width: 145px"
            placeholder="请选择颜色"
            :options="colorOptions"
            @change="handleColorChange"
        />
      </a-form-item>
      <a-form-item label="尺码" class="smart-query-form-item">
        <a-select
            v-model:value="initSizeVal"
            :max-tag-count="maxTagCount"
            mode="multiple"
            style="width: 145px"
            placeholder="请选择尺码"
            :options="sizeOptions"
            @change="handleSizeChange"
        />
      </a-form-item>
      <a-form-item label="部位" class="smart-query-form-item">
        <a-select
            v-model:value="initPositionVal"
            :max-tag-count="maxTagCount"
            mode="multiple"
            style="width: 145px"
            placeholder="请选择部位"
            :options="positionOptions"
            @change="handlePositionsChange"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData(true)">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        :scroll="{x:1000}"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    />
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartStationInventoryForm  ref="formRef" @reloadList="queryData"/>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted, watch} from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { partStationInventoryApi } from '/@/api/business/mes/part-station/inventory/part-station-inventory-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import PartStationInventoryForm from './part-station-inventory-form.vue';
    import {produceInstructOrderApi} from "/@/api/business/mes/produce/produce-instruct-order-api.js";
    import {produceInstructOrderClothesApi} from "/@/api/business/mes/produce/produce-instruct-order-clothes-api.js"
    // ---------------------------- 表格列 ----------------------------
    const columns = ref([
        {
          title: '指令单编号',
          dataIndex: 'instructOrderNumber',
          ellipsis: true,
          width: 135
        },
        {
          title: '物料编号',
          dataIndex: 'itemNumber',
          ellipsis: true,
          width: 135
        },
        {
          title: '物料名称',
          dataIndex: 'itemName',
          ellipsis: true,
          width: 135
        },
        {
          title: '款式颜色',
          dataIndex: 'styleColor',
          ellipsis: true,
          width: 80
        },
        {
          title: '部位',
          dataIndex: 'positions',
          ellipsis: true,
          width: 80

        },
        {
          title: '尺码',
          dataIndex: 'size',
          ellipsis: true,
          width: 120
        },
        {
          title: '汇总',
          dataIndex: 'num',
          ellipsis: true,
          width: 50,
          align:"center"
        },
    ]);
    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        itemNumber: undefined, //物料编号
        instructOrderNumber: undefined, //指令单编号
        colors:[],
        sizes:[],
        positions:[],
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        initInsOrItem.value = undefined
        initColorVal.value = undefined
        initSizeVal.value = undefined
        initPositionVal.value = undefined
        colorOptions.value = []
        sizeOptions.value = []
        positionOptions.value=[]
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
          queryForm.produceInstructOrderNumber = queryForm.instructOrderNumber
          let queryResult = await partStationInventoryApi.summaryQuery(queryForm);
          tableData.value = queryResult.data.list;
          // total.value = 20;
          total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
          tableLoading.value = false;
        }
    }
    onMounted(()=>{
      queryData()
      queryInsOrItem()
    });
    // ---------------------------- 获取指令单编号和物料编号 ----------------------------
    //指令单编号和物料编号共同的value
    const initInsOrItem = ref(undefined)
    //构造两个编号的options
    const insNoOptions = ref([])
    const itemNoOptions = ref([])
    async function queryInsOrItem(){
      tableLoading.value = true;
      try {
        let res = await produceInstructOrderApi.queryInsOrItem();
        outstandingInsAndItemOptions(res.data,insNoOptions,"instructNumber")
        outstandingInsAndItemOptions(res.data,itemNoOptions,"itemNumber")
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        tableLoading.value = false
      }
    }
    //根据type输出options
    function outstandingInsAndItemOptions(source,target,type){
      target.value = []
      source.map((e)=>(
          target.value.push({
            label:e[type],
            value:e.id
          })
      ))
    }

    //两个编号动态筛选
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

// ---------------------------- 处理指令单编号和物料编号 ----------------------------
    function handleInsChange(val,ops){
      queryForm.instructOrderNumber = ops.label
      setQueryFormByType(itemNoOptions,val,"itemNumber")
      //构造颜色和尺码options
    }
    function handleItemChange(val,ops){
      queryForm.itemNumber = ops.label
      setQueryFormByType(insNoOptions,val,"instructOrderNumber")
      console.log(queryForm.instructOrderNumber,queryForm.itemNumber)

    }

    function setQueryFormByType(data,val,type){
      for(let i in data.value){
        if(data.value[i].value===val){
          queryForm[type] = data.value[i].label
          break
        }
      }
    }
    // ---------------------------- 获取颜色和尺码 ----------------------------
    //下拉框最多可显示数量
    const maxTagCount = ref(1)
    //颜色和尺码下拉框初始值
    const initColorVal = ref(undefined)
    const initSizeVal = ref(undefined)
    const initPositionVal = ref(undefined)

    watch(()=>queryForm.instructOrderNumber,(newVal)=>{
      initColorVal.value = undefined
      initSizeVal.value = undefined
      if (newVal !== undefined) {
        queryByInsAndItemNumber()
      }
    })
    //根据指令单id获取颜色、尺码和部位信息
    async function queryByInsAndItemNumber(){
      let colorList = await produceInstructOrderClothesApi.queryClothesColorList(initInsOrItem.value);
      let sizeList = await produceInstructOrderClothesApi.queryClothesSizeList(initInsOrItem.value);
      const positionsList=await partStationInventoryApi.queryPage(queryForm)
      positionsList.data.list.map(e=>{ 
        positionOptions.value.push({
          label:e.positions,
          value:e.positions
        })
      })
      outstandingOptions(colorList.data,colorOptions)
      outstandingOptions(sizeList.data,sizeOptions)
    }

    //拼装options
    const colorOptions = ref([])
    const sizeOptions = ref([])
    const positionOptions = ref([])
    function outstandingOptions(sourceData,target){
      target.value = []
      sourceData.map((item,index)=>(
          target.value.push({
            label:item,
            value:index
          })
      ))
    }
    // --------------------- 处理颜色、尺码和部位 ------------------------
    function handleColorChange(item,option){
      queryForm.colors = []
      option.map((e)=>{
        queryForm.colors.push(e.label)
      })
    }

    function handleSizeChange(item,option){
      queryForm.sizes = []
      option.map((e)=>{
        queryForm.sizes.push(e.label)
      })
    }
    function handlePositionsChange(item,option){
      queryForm.positions = []
      option.map((e)=>{
        queryForm.positions.push(e.label)
      })
    }
    // --------------------- 处理部位搜索 ------------------------
    //TODO 补充部位搜索数据

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await partStationInventoryApi.delete(data.id);
            message.success('删除成功');
            await queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await partStationInventoryApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            await queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
</script>

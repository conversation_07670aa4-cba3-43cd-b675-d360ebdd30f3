<template>
  <view>
    <board-title :title="'使用情况'" style="line-height: 60px;height: 60px"/>
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-col span="8" style="display: flex;align-items: center">
          <a-form-item label="仓库" class="smart-query-form-item" style="display: flex">
            <a-select
                v-model:value="queryForm.warehouseId"
                show-search
                style="width: 180px"
                placeholder="选择仓库"
                :options="warehouseOptions"
                :filter-option="filterOption"
                @change="queryData"
            />
          </a-form-item>
          <a-button @click="queryData" type="primary" style="margin-left: 10px">刷新</a-button>

        </a-col>

        <a-col span="8">
          <div style="width: 80%">
            <div style="display: flex;justify-content: space-between" class="smart-query-form-item">
              <div>总货架数:{{ rackCount }}</div>
              <div>总货位数:{{ binCount }}</div>
            </div>
            <div style="display: flex;justify-content: space-between" class="smart-query-form-item">
              <div style="color: green">空仓货位数: {{ emptyCount }}</div>
              <div style="color: orange">半仓货位数: {{ halfCount }}</div>
              <div style="color: red">满仓货位数: {{ fullCount }}</div>
            </div>
          </div>


        </a-col>
        <a-col span="8">
          <div style="display: flex;" class="smart-query-form-item">
            <div class="status-tag " style="margin-right: 120px">
              <div class="status-block" style="background-color: #0fd560;"></div>
              空仓货位
            </div>
            <div class="status-tag">
              <div class="status-block" style="background-color: #f0a800"></div>
              半仓货位
            </div>
          </div>
          <div style="display: flex;" class="smart-query-form-item">
            <div class="status-tag">
              <div class="status-block" style="background-color: #ff0000"></div>
              满仓货位
            </div>
          </div>
        </a-col>
      </a-row>
    </a-form>

    <a-card>
      <a-spin style="width: 100%;" :spinning="tableLoading"/>
      <div class="inventory-card-container">
        <div class="inventory-card" v-for="(item,index) in inventoryList" :key="index"
             :style="{backgroundColor:item.color}"
             @click="clickCard(item.binId)"
        >
          <div>货架:{{ item.rackCode }}</div>
          <div>货位:{{ item.binCode }}</div>
          <div>已用:{{ item.percent }}%</div>
          <div>容量:{{ item.totalCapacity }}</div>
        </div>
      </div>
    </a-card>
  </view>

  <stock-bin-modal ref="stockBinModal"/>
</template>
<script setup>

import {ref, reactive, toRefs, watch, onMounted} from "vue";
import {partStationWarehouseApi} from "/@/api/business/mes/part-station/warehouse/part-station-warehouse-api.js";
import {message} from "ant-design-vue";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {partStationInventoryApi} from "/@/api/business/mes/part-station/inventory/part-station-inventory-api.js";
import StockBinModal from "/@/views/business/mes/part-station/stock-situation/components/stock-bin-modal.vue";
import BoardTitle from "/@/components/business/mes/board/board-title.vue";

onMounted(async () => {
  await warehouseFocus()
})


// 表格加载loading
const tableLoading = ref(false);

const queryFormState = {
  warehouseId: undefined, //所属仓库id
};
// 查询表单form
const queryForm = reactive({...queryFormState});
const inventoryList = ref([])// 货位库存列表
const warehouseOptions = ref([])// 仓库下拉框数据
const emptyCount = ref(0)// 空货位数
const halfCount = ref(0)// 半仓货位
const fullCount = ref(0)// 满仓货位
const rackCount = ref(0)// 货架数量
const binCount = ref(0)// 货位数量

const stockBinModal = ref(null)

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

async function warehouseFocus() {
  tableLoading.value = true
  try {
    let res = await partStationWarehouseApi.getAll();
    //构造options
    warehouseOptions.value = res.data.map((e) => {
          return {
            value: e.id,
            label: e.name + "-" + e.warehouseCode,
            name: e.name,
            warehouseCode: e.warehouseCode
          }
        }
    )
    if (warehouseOptions.value.length > 0) {
      queryForm.warehouseId = warehouseOptions.value[0].value
      await queryData()
    }
  } catch (e) {
    message.error("获取仓库信息失败")
    smartSentry.captureError(e)
  } finally {
    tableLoading.value = false
  }
}


// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    await queryUsageSituation()//查询库存
    await queryRackCount()
    await queryBinCount()
    computeBinCount()//计算货位数量
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

async function queryRackCount() {
  // 获取所属仓库下货架数量
  const getRackNum = await partStationWarehouseApi.getRackNum({warehouseId: queryForm.warehouseId});
  // console.log('binCountData', getRackNum.data);
  rackCount.value = getRackNum.data;
}

async function queryBinCount() {
  // 获取所属仓库下货位数量
  const getBinNum = await partStationWarehouseApi.getBinNum({warehouseId: queryForm.warehouseId});
  binCount.value = getBinNum.data;
}


async function queryUsageSituation() {
  let res = await partStationInventoryApi.queryInventorySituation(queryForm)
  // console.log(res.data)
  inventoryList.value = res.data.map((curr) => {
    //不保留小数
    curr.percent = curr.usedCapacity === 0 ? 0 : Math.round(curr.usedCapacity / curr.totalCapacity * 100);
    // curr.percent = 120;
    if (curr.percent >= 66) {
      curr.color = "#fe0000";
    } else if (curr.percent >= 33) {
      curr.color = "#f0a800";
    } else {
      curr.color = "#01cc00"
    }

    return curr;
  }, []);

  // console.log(inventoryList.value.length);
}

function computeBinCount() {
  //统计可用货位数
  let tempEmptyCount = 0;
  let tempHalfCount = 0;
  let tempFullCount = 0;
  inventoryList.value.forEach((curr) => {
    if (curr.percent >= 66) {
      tempFullCount++
    } else if (curr.percent >= 33) {
      tempHalfCount++
    } else {
      tempEmptyCount++
    }
  })
  emptyCount.value = tempEmptyCount
  halfCount.value = tempHalfCount
  fullCount.value = tempFullCount
}

function clickCard(binId) {
  stockBinModal.value.showModal(binId)

}
</script>
<style scoped lang="less">
.status-tag {
  display: flex;
  align-items: center;

}

.status-block {
  width: 40px;
  height: 16px;
  background: #1890ff;
  margin-right: 6px;
  display: inline-block;
  vertical-align: middle;
}

.inventory-card-container {
  display: flex;
  flex-wrap: wrap;
  flex-flow: row wrap;
  align-content: flex-start;

  .inventory-card {
    color: white;
    flex: 0 0 10%;
    max-width: 130px;
    border-radius: 3px;
    background: #00a0e9;
    margin-right: 6px;
    margin-bottom: 6px;
    padding: 2px;
    /*文字*/
    white-space: nowrap; /* 禁止文本换行 */
    overflow: hidden; /* 隐藏超出范围的内容 */
    text-overflow: ellipsis; /* 使用省略号 */
    text-align: center;
    font-size: 12px;
    cursor: pointer;
  }
}

</style>

/**
 * 即时库存 枚举
 *
 * @Author:    cjm
 * @Date:      2025-01-14 15:20:23
 * @Copyright  zscbdic
 */
    // 操作类型枚举
   export const OPERATION_TYPE= {
      IN: {
        value: "IN",
        desc: "入库"
      },
      OUT: {
        value: "OUT",
        desc: "出库"
      },
      TAKE: {
        value: "TAKE",
        desc: "盘库"
      },
      MOVE: {
        value: "MOVE",
        desc: "移库"
      },
      
      getOptions(value) {
        return this[value]?.desc || '未知';
      }
    }
<!--
  * 款式品类表
  *
  * @Author:    hzx
  * @Date:      2024-07-05 15:45:34
  * @Copyright  zscbdic
-->
<template>
    <a-card size="small" :bordered="false" :hoverable="true">
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button @click="addCategory()" type="primary" size="small" >
            <template #icon>
              <PlusOutlined/>
            </template>
            新建
          </a-button>
        </div>
        <div class="smart-table-setting-block">

        </div>
      </a-row>

      <!-- -------------从这里开始树表  ---------------------   -->
      <a-table
          :scroll="{ x: 1000}"
          size="small"
          :dataSource="tableDataList"
          :columns="columns"
          rowKey="id"
          bordered
          :pagination="false"
          @expandedRowsChange="changeExand"
          :expanded-row-keys="expandedRowKeys"
          :expandRowByClick="true"
      >
        <!-- 操作按钮 -->
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button @click="addCategory(undefined,record,0)" type="link">
                增加子分类
              </a-button>
              <a-button @click="addCategory(undefined, record,1)" type="link">
                编辑
              </a-button>
              <a-button @click="confirmDeleteCategory(record.id)" danger type="link">
                删除
              </a-button>

            </div>
          </template>
        </template>
      </a-table>
      <!-- ----------------------- 树表结束 -------------------------    -->
        <StyleForm  ref="formModal" @reloadList="reloadList"/>

    </a-card>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {styleApi} from '/@/api/business/mes/base/style-api';
import {smartSentry} from '/@/lib/smart-sentry';
import StyleForm from './style-form.vue';
// ---------------------------- 表格列 ----------------------------

    const columns = ref([
        {
          title: '品类名称',
          dataIndex: 'styleName',
          ellipsis: true,
          width: 150,
          align: 'center',
        },
        {
          title: '品类代码',
          dataIndex: 'styleCode',
          ellipsis: true,
          width: 150,
          align: 'center',
        },
        {
          title: '备注',
          dataIndex: 'remark',
          ellipsis: true,
          align: 'center',
        },

        {
          title: '操作',
          dataIndex: 'action',
          fixed: 'right',
          width: 200,
          align: 'center',
        },
    ]);
    // 定义树表各项方法变量
    // 定义树表列表数组
    const tableLoading = ref(false);
    const tableDataList = ref([])
    const props = defineProps({
      // 分组类型
      categoryType: {
        type: String,
        default: ""
      },

      privilegePrefix: {
        type: String,
        default: '',
      },
    });
    // 定义遍历转换数据对象chrildrenStyle为chrilden属性函数
    function transformChildren(data) {
      return data.map((item) => {
        const { children, ...rest } = item;
        if (children.length > 0) {
          rest.children = transformChildren(children);
        }
        return rest;
      });
    }


    // 发送异步获取树表数组
    async function queryList() {
      try {
        tableLoading.value = true;
        // 请求树表参数
        let queryForm = {
          categoryType: props.categoryType,
        };
        let responseModel = await styleApi.queryTree(queryForm);
        // 将获取到的数据对象里面的childstyle进行转换成children属性
        // 遍历 data 数组中的每个对象
        tableDataList.value = transformChildren(responseModel.data);
      } catch (e) {
        smartSentry.captureError("this is the:", e);
      } finally {
        tableLoading.value = false;
      }
    }

    const expandedRowKeys = ref([]);

    function reloadList(parentId) {
      queryList();
      if (parentId) {
        expandedRowKeys.value.push(parentId);
      }
    }

    onMounted(queryList);
    defineExpose({
      queryList,
    });

    function changeExand(val) {
      expandedRowKeys.value = val;
    }
    // ------------------------------ 添加 ------------------------------

    const formModal = ref();


    // 递归函数：根据 id 查找 fullName
  function findFullNameById(tree, parentId) {
    for (const node of tree) {
      if (node.id === parentId) {
        return node.fullName;
      }
      if (node.children && node.children.length > 0) {
        const fullName = findFullNameById(node.children, parentId);
        if (fullName) {
          return fullName;
        }
      }
    }
    return null;
  }
    function addCategory(parentId, rowData,sign) {
      let categoryType = props.categoryType;
      if (parentId == undefined && rowData !== undefined&& sign===1) {
        // 获取父级分类的 fullName
        const fullName = findFullNameById(tableDataList.value, rowData.parentId);
        if (fullName) {
          categoryType = fullName;
        } else {
          categoryType = '';
        }
      }
        formModal.value.showModal(categoryType, parentId, rowData,sign);
    }

    // ------------------------------ 删除 ------------------------------

    function confirmDeleteCategory(categoryId) {
      Modal.confirm({
        title: '提示',
        content: '确定要删除当前分类吗?',
        okText: '确定',
        okType: 'danger',
        async onOk() {
          deleteCategory(categoryId);
        },
        cancelText: '取消',
        onCancel() {
        },
      });
    }
    async function deleteCategory(categoryId) {
      try {
        SmartLoading.show();
        await styleApi.delete(categoryId);
        message.success('删除成功');
        queryList();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }



    // // ---------------------------- 查询数据表单和方法 ----------------------------
    //
    const queryFormState = {
        queryKey: undefined, //关键字查询
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });

    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);
    // // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await styleApi.queryTree(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
    onMounted(queryData);
</script>

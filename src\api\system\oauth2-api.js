/**\
 * 获取三方登陆配置api
 */
import {getRequest, postRequest} from '/src/lib/axios';

export const oauth2Api = {

    /**
     * 第三方登录
     * @param type
     * @param code
     * @param state
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    thirdLogin: (code, state) => {
        return getRequest(`/oauth/callback?code=${code}&state=${state}`);
    },

    /**
     * 获取三方登录url
     * @param type
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    getThirdAuthUrl: (type) => {
        return getRequest(`/oauth/render/${type}`);
    },


}

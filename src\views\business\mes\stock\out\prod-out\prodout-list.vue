<!--
  * 生产退库单
  *
  * @Author:    cjm
  * @Date:      2025-02-09 19:58:18
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="单据状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.status" style="width: 150px" placeholder="单据状态">
          <a-select-option :value="BILLS_STATUS.AUDIT.value">{{ BILLS_STATUS.AUDIT.desc }}</a-select-option>
          <a-select-option :value="BILLS_STATUS.UN_AUDIT.value">{{ BILLS_STATUS.UN_AUDIT.desc }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="出库时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.outStockTime" :presets="defaultTimeRanges" style="width: 150px" @change="onChangeOutStockTime" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2000 }"
      @change="handleTableChange"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag v-if="record.status === BILLS_STATUS.AUDIT.value" color="blue">已审核</a-tag>
          <a-tag v-else color="orange">待审核</a-tag>
        </template>
        <template v-if="column.dataIndex === 'number'">
          <a type="link" @click="showForm(record)">{{ text }}</a>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="audit(record)" type="link">审核</a-button>
            <a-button danger @click="onDelete(record)" type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <prodoutForm ref="formRef" @reloadList="queryData" />
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { stkProdOutApi } from '/@/api/business/mes/stock/stk-prodout-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { defaultTimeRanges } from '/@/lib/default-time-ranges';
  import { BILLS_STATUS } from '/@/constants/business/mes/stock/stk-status-const.js';
  import { message, Modal } from 'ant-design-vue';
  import prodoutForm from './components/product-form.vue';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '单据编号',
      dataIndex: 'number',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '单据状态',
      dataIndex: 'status',
      ellipsis: true,
      align: 'center',
    },

    {
      title: '出库时间',
      dataIndex: 'outStockTime',
      ellipsis: true,
      align: 'center',
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '仓管员名称',
      dataIndex: 'stockerName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '货主类型',
      dataIndex: 'ownerType',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '货主名称',
      dataIndex: 'ownerName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '申请时间',
      dataIndex: 'applyTime',
      ellipsis: true,
      align: 'center',
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '申请人名称',
      dataIndex: 'applicantName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      ellipsis: true,
      align: 'center',
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '审核人名称',
      dataIndex: 'auditorName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 125,
      align: 'center',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    originType: undefined, //单据来源类型
    way: undefined, //单据方式
    queryKey: undefined, //关键字查询
    status: undefined, //单据状态
    outStockTime: [], //出库时间
    outStockTimeBegin: undefined, //出库时间 开始
    outStockTimeEnd: undefined, //出库时间 结束
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }
  //处理表格时间顺序变化事件
async function handleTableChange(pagination,filters,sorter) {
      if(sorter&&sorter.field==='outStockTime'){
      queryForm.sortItemList = 
      [{
        column:'out_stock_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='auditTime'){
      queryForm.sortItemList = 
      [{
        column:'audit_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='applyTime'){
      queryForm.sortItemList = 
      [{
        column:'apply_time',
        isAsc:sorter.order==='ascend'
      }];
    }
    if(!sorter.order){
      queryForm.sortItemList=
      [{
        column:'create_time',
        isAsc:false
      }]
    }
    await queryData();
    }
  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await stkProdOutApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  function onChangeOutStockTime(dates, dateStrings) {
    queryForm.outStockTimeBegin = dateStrings[0];
    queryForm.outStockTimeEnd = dateStrings[1];
  }

  // 审核
  async function audit(record) {
    try {
      await stkProdOutApi.status(record.id);
      message.success('审核成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  onMounted(queryData);
  // ---------------------------- 删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }
  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      await stkProdOutApi.delete(data.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(record) {
    formRef.value.show(record.id);
  }
</script>

<template>
  <a-modal v-model:open="visible" :width="900" title="选择裁床单" @cancel="closeModal" @ok="onSelectItem">
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="params.queryKey" placeholder="关键字"/>
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined/>
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined/>
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <a-table
        :row-selection="{ selectedRowKeys: [selectedRowKey], onChange: onSelectChange,type: 'radio' }"
        :loading="tableLoading"
        size="small"
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        bordered
        rowKey="id"
        :scroll="{ y: 300 }"
    >
      <template #bodyCell="{ text, column,record }">
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="params.pageSize"
          v-model:current="params.pageNum"
          v-model:pageSize="params.pageSize"
          :total="total"
          @change="queryCutBedSheetList"
          @showSizeChange="queryCutBedSheetList"
          :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>


</template>
<script setup>

import {reactive, ref} from "vue";
import {PAGE_SIZE, PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import {produceInstructOrderApi} from "/@/api/business/mes/produce/produce-instruct-order-api.js";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {cutBedSheetApi} from "/@/api/business/mes/tailor/cut-bed-sheet-api.js";
import {message} from "ant-design-vue";
import {
  PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
  PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM
} from "/@/constants/business/mes/produce/produce-instruct-order-const.js";
import FilePreview from "/@/components/support/file-preview/index.vue";

const emits = defineEmits(['selectData']);
defineExpose({
  showModal,
});

const props = defineProps({})

const visible = ref(false);
const selectedRowKey = ref(null);

const tableLoading = ref(false);
const total = ref();

let defaultParams = {
  pageNum: 1,
  pageSize: PAGE_SIZE,
  queryKey: undefined,
  sortItemList: [{column: 'create_time', isAsc: false}]
};

const params = reactive({...defaultParams});

const columns = ref([
  {
    title: '单据编号',
    dataIndex: 'cutNumber',
    ellipsis: true,
  },
  {
    title: '裁床单名称',
    dataIndex: 'cutSheetName',
    ellipsis: true,
  },
  {
    title: '床次',
    dataIndex: 'cutNum',
    ellipsis: true,
  },
  {
    title: '生产指令单编号',
    dataIndex: 'instructOrderNumber',
    ellipsis: true,
  },
  {
    title: '生产指令单名称',
    dataIndex: 'instructOrderName',
    ellipsis: true,
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    ellipsis: true,
  },

  {
    title: '物料名称',
    dataIndex: 'itemName',
    ellipsis: true,
  },

  {
    title: '总裁数',
    dataIndex: 'quantity',
    ellipsis: true,
  }
]);

let tableData = ref([]);

async function showModal() {
  selectedRowKey.value = null;
  visible.value = true;
  onSearch();
}

function closeModal() {
  Object.assign(params, defaultParams);
  selectedRowKey.value = null;
  visible.value = false;
}

function onSearch() {
  params.pageNum = 1;
  queryCutBedSheetList();
}

function reset() {
  Object.assign(params, defaultParams);
  queryCutBedSheetList();
}

async function queryCutBedSheetList() {
  tableLoading.value = true;
  try {
    let res = await cutBedSheetApi.queryPage(params);
    tableData.value = res.data.list;
    total.value = res.data.total;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    tableLoading.value = false;
  }
}


function onSelectChange(selectedRowKeys) {
  if (selectedRowKeys.length > 0) {
    selectedRowKey.value = selectedRowKeys[0];
  } else {
    selectedRowKey.value = null;
  }
}

function onSelectItem() {
  if (!selectedRowKey.value) {
    message.warning('请选择裁床单');
    return;
  }
  const selectedItem = tableData.value.find(item => item.id === selectedRowKey.value);
  emits('selectData', selectedItem);
  closeModal();
}


</script>

<style lang="less" scoped>

</style>

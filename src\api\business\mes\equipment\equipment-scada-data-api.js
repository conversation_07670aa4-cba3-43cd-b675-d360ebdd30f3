/**
 * 设备监控 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-09 20:12:13
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/src/lib/axios';

export const equipmentScadaDataApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/equipmentScadaData/queryScadaDataPage', param);
  },


  /**
   * scada设备属性获取  <AUTHOR>
   */
  queryScadaProperties: (equipId) => {
      return getRequest(`/equipmentScadaData/queryScadaProperties/${equipId}`);
  },

};

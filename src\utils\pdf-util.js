import html2Canvas from 'html2canvas';
import jsPDF from 'jspdf';

/**
 * 工资条打印
 * @param fileName 文件名
 */
export async function getPayoffRecordPdf(fileName) {
  var pdfWidth =297;
  var pdfHeight = 210;
  const pdf = new jsPDF('L', 'mm', [pdfWidth, pdfHeight]);
  // 获取打印所有的元素
  const contents = document.querySelectorAll('#salary-item')
  let renderedHeight = 0; //计算渲染高度 超过则添加

  for (let i = 0; i < contents.length; i++) {
    const content = contents[i]
    await html2Canvas(content,{
      allowTaint: false,
      taintTest: false,
      logging: false,
      useCORS: true,
      dpi: window.devicePixelRatio * 4, //缩放比例
      scale : 4,
    }).then((canvas) =>{
      var imgWidth = pdfWidth - 20;
      var imgHeight = (canvas.height * imgWidth) / canvas.width;

      // 检查是否需要新页面
      if (renderedHeight + imgHeight > (pdfHeight - 20)) {
        pdf.addPage();
        renderedHeight = 0;
      }

      // 创建临时canvas
      var page = document.createElement('canvas');
      page.width = canvas.width;
      page.height = canvas.height;

      // 获取 绘制
      var ctx = page.getContext('2d');
      //绘制图片(源图像,源图像x起点,源图像y起点,裁剪宽度,裁剪高度,目标x,目标y,目标宽度,目标高度)
      ctx.drawImage(canvas,0,0,canvas.width,canvas.height,0,0,canvas.width,canvas.height);
      //添加图片到pdf(图片数据,图片格式,x坐标,y坐标,宽度,高度)
      pdf.addImage(canvas.toDataURL('image/jpeg', 1.0),'JPEG',10,renderedHeight,imgWidth,imgHeight);
      
      // 更新当前页面已使用的高度
      renderedHeight += imgHeight + 10; // 加10mm作为工资条之间的间距
    })
  }
  pdf.save(`${fileName}.pdf`);
}
/**
 * 结算记录打印
 * @param textName 文件名
 */

export async function getSettlementRecordPdf(textName) {
  var pdfWidth = 210;
  var pdfHeight = 297;
  const pdf = new jsPDF('p', 'mm', [pdfWidth, pdfHeight]);

  // 获取所有打印内容的容器
  const containers = document.querySelectorAll('#pdf-content');

  for (let i = 0; i < containers.length; i++) {
    const container = containers[i];

    await html2Canvas(container, {
      allowTaint: false,
      taintTest: false,
      logging: false,
      useCORS: true,     
      dpi: window.devicePixelRatio * 2,
      scale: 2,
    }).then((canvas) => {
      var imgWidth = pdfWidth - 20;
      var imgHeight = (canvas.height * imgWidth) / canvas.width;
      var renderedHeight = 0;

      while (renderedHeight < canvas.height) {
        var sliceHeight = Math.min((pdfHeight * canvas.width) / imgWidth, canvas.height - renderedHeight);
        var page = document.createElement('canvas');
        page.width = canvas.width;
        page.height = sliceHeight;

        var ctx = page.getContext('2d');
        ctx.drawImage(canvas, 0, renderedHeight, canvas.width, sliceHeight, 0, 0, canvas.width, sliceHeight);

        pdf.addImage(page.toDataURL('image/jpeg', 1.0), 'JPEG', 10, 0, imgWidth, (sliceHeight / canvas.height) * imgHeight);

        renderedHeight += sliceHeight;
        if (renderedHeight < canvas.height || i < containers.length - 1) {
          pdf.addPage();
        }
      }
    });
  }
  // 保存PDF
  pdf.save(`${textName}.pdf`);
}

/**
 *  货位码打印
 * @param title 文件名
 * @param id dom元素id
 * @param customPageSize 页面尺寸
 * @param pages 页数
 */
export function getLocationPdf(title, id, customPageSize, pages) {
  html2Canvas(document.querySelector(id), {
    allowTaint: false,
    taintTest: false,
    logging: false,
    useCORS: true,
    dpi: window.devicePixelRatio * 4, // 将分辨率提高到特定的DPI 提高四倍
    scale: 4, // 按比例增加分辨率
  }).then((canvas) => {
    // 检查是否提供了自定义尺寸
    var pdfWidth = customPageSize ? customPageSize[0] : 210; // 默认A4纸宽度
    var pdfHeight = customPageSize ? customPageSize[1] * pages : 297; // 默认A4纸高度
    let pdf = null;

    var imgHeight = canvas.height / pages;
    var renderedHeight = 0;

    while (renderedHeight < canvas.height) {
      var sliceHeight = Math.min(imgHeight, canvas.height - renderedHeight);
      var newCanvas = document.createElement('canvas');
      newCanvas.width = canvas.width;
      newCanvas.height = sliceHeight;

      var ctx = newCanvas.getContext('2d');
      ctx.drawImage(canvas, 0, renderedHeight, canvas.width, sliceHeight, 0, 0, canvas.width, sliceHeight);
      if (pdf === null) {
        pdf = new jsPDF('l', 'mm', [pdfWidth, (sliceHeight / canvas.height) * pdfHeight]); // 自定义纸张宽高
      }
      pdf.addImage(newCanvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, pdfWidth, (sliceHeight / canvas.height) * pdfHeight);
      renderedHeight += sliceHeight;
      if (renderedHeight < canvas.height) {
        pdf.addPage(); // 如果后面还有内容，添加一个新页面
      }
    }

    pdf.save(title + '.pdf');
  });
}

/**
 * 生产指令单 枚举
 *
 * @Author:    cyz
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */
export const PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM = {
    NORMAL: {
        value: '0',
        desc: '一般',
        label: '一般',
        color: '#46c26f',
    },
    URGENT: {
        value: '1',
        desc: '紧急',
        label: '紧急',
        color: '#f0a800',
    },
    VERY_URGENT: {
        value: '2',
        desc: '非常紧急',
        label: '非常紧急',
        color: '#eb5050',
    },
    UNKNOWN: {
        value: '3',
        desc: '未知',
        label: '未知',
        color: '#b0acac',
    },
    getEnum(value) {
        if (value === '0') {
            return PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.NORMAL;
        } else if (value === '1') {
            return PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.URGENT;
        } else if (value === '2') {
            return PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.VERY_URGENT
        }
        return PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.UNKNOWN;
    },
}

export const PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM = {
    PLAN: {
        value: '0',
        desc: '计划',
        label: '计划',
        color: '#00aed1',
    },
    ISSUED: {
        value: '1',
        desc: '下达',
        label: '下达',
        color: '#f0a800',
    },
    START: {
        value: '2',
        desc: '开工',
        label: '开工',
        color: '#46c26f',
    },
    FINISH: {
        value: '3',
        desc: '完工',
        label: '完工',
        color: '#0d0096',
    },


    getEnum(value) {
        if (value === '0') {
            return PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.PLAN;
        } else if (value === '1') {
            return PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.ISSUED;
        } else if (value === '2') {
            return PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.START;
        } else if (value === '3') {
            return PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.FINISH;
        }

    }
}
// fix-bug: 添加生产类型
export const PRODUCE_TYPE_ENUM = {
    SELF_PRODUCED: {
        value: '0',
        desc: '自产',
        label: '自产',
        color: '#CC6CE7',
    },
    SELF_TRIMMING_OUTSOURCING: {
        value: '1',
        desc: '自裁委外',
        label: '自裁委外',
        color: '#E96C84',
    },
    WHOLE_PIECE_OUTSOURCING: {
        value: '2',
        desc: '整件委外',
        label: '整件委外',
        color: '#98F5F9',
    },
    getEnum(value) {
        if (value === '0') {
            return PRODUCE_TYPE_ENUM.SELF_PRODUCED;
        } else if (value === '1') {
            return PRODUCE_TYPE_ENUM.SELF_TRIMMING_OUTSOURCING;
        } else if (value === '2') {
            return PRODUCE_TYPE_ENUM.WHOLE_PIECE_OUTSOURCING
        }
    }

}

export default {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
    PRODUCE_TYPE_ENUM
};

<!--
  * 工资发放
  *
  * @Author:    linwj
  * @Date:      2024-11-30 20:48:59
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
            <a-row class="smart-query-form-row">
              <a-form-item label="员工" class="smart-query-form-item">
                <a-select
                    v-model:value="initOptEmp"
                    show-search
                    mode="multiple"
                    :max-tag-count="1"
                    placeholder="员工选择"
                    style="width: 150px"
                    :options="operatorOptions"
                    :filter-option="filterOption"
                    @change="handleEmpChange"
                />
              </a-form-item>
              <a-form-item label="结算月份" class="smart-query-form-item">
                <a-date-picker
                    v-model:value="settlementMonth"
                    picker="month"
                    format="YYYY-MM"
                    :allowClear="false"
                />
              </a-form-item>
              <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                  <template #icon>
                    <SearchOutlined />
                  </template>
                  查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                  <template #icon>
                    <ReloadOutlined />
                  </template>
                  重置
                </a-button>
              </a-form-item>
            </a-row>
          </a-form>

    <a-card size="small" :bordered="false" :hoverable="true">

      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button @click="openCustomPayModal" type="primary" size="small" >
            <template #icon>
              <PlusOutlined />
            </template>
            自定义工资项
          </a-button>
          <a-button @click="openCustomPayValueModal" type="primary" size="small" >
            <template #icon>
              <PlusOutlined />
            </template>
            批量调整
          </a-button>
          <a-button @click="handleBatchPayoff" type="primary" size="small" >
            <template #icon>
              <PlusOutlined />
            </template>
            批量发放
          </a-button>
        </div>
      </a-row>

      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          :loading="tableLoading"
          :row-selection="rowSelection"
          :pagination="false"
          :scroll="{ x: 'max-content' }"
          row-key="employeeId"
      >
        <template #headerCell="{ column }">
          <template v-if="otherList.indexOf(column.dataIndex)!==-1">
            <span>
              {{column.title+`(${WAGE_TYPE_ENUM.getEnum(column.type).label})`}}
            </span>
          </template>
        </template>

        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button type="link" @click="handleSinglePayoff(record)">发放</a-button>
            </div>
          </template>
          <template v-if="column.dataIndex === 'countSalary'">
            {{record.countSalary+'元'}}
            <a-button type="link" @click="handleQueryDetails(record)" style="color: red;width: 40px">查看</a-button>
          </template>
          <template v-if="column.dataIndex === 'totalSalary'">
            {{record.totalSalary+'元'}}
          </template>
          <!-- 其他项数据 -->
          <template v-if="otherList.indexOf(column.dataIndex)!==-1">
            <span v-for="(item) in record.wageFieldValues" :key="item.fieldValueId">
              <span v-if="item.fieldName===column.title">
                <!--失去焦点回调-->
                <a-input-number v-model:value="item.fieldValue" @blur="handleFieldValueUpdate(record,column)" style="width: 100%"/>
              </span>
            </span>
          </template>
        </template>
      </a-table>
      <!---------- 表格 end ----------->

      <div class="smart-query-table-page">
        <a-pagination
            showSizeChanger
            showQuickJumper
            show-less-items
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :defaultPageSize="queryForm.pageSize"
            v-model:current="queryForm.pageNum"
            v-model:pageSize="queryForm.pageSize"
            :total="total"
            @change="queryData"
            @showSizeChange="queryData"
            :show-total="(total) => `共${total}条`"
        />
      </div>

      <CustomPayForm ref="customPayRef" @reloadList="queryData" @update:field="handleSaveField"/>
      <CustomPayValueForm  ref="customPayValueRef" @reloadList="queryData"/>
      <PayoffForm ref="payoffRef" @reloadList="queryData"/>
      <PayoffDetailsForm ref="payoffDetailsRef" @reloadList="queryData"/>
    </a-card>
</template>
<script setup>
    import {reactive, ref, onMounted, watch} from 'vue';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import dayjs from "dayjs";
    import _ from 'lodash';
    import {employeeApi} from "/@/api/system/employee-api.js";
    import {payoffApi} from "/@/api/business/mes/salary/payoff-api.js";
    import {WAGE_TYPE_ENUM} from "/@/constants/business/mes/salary/custom-pay-const.js";
    import {wageFieldValueApi} from "/@/api/business/mes/salary/wage-field-value-api.js";
    import {message} from "ant-design-vue";
    import CustomPayForm from "/@/views/business/mes/salary/payoff/components/Custom-Pay-Form.vue";
    import CustomPayValueForm from "/@/views/business/mes/salary/payoff/components/Custom-Pay-value-Form.vue";
    import PayoffForm from "/@/views/business/mes/salary/payoff/components/Payoff-Form.vue";
    import PayoffDetailsForm from "/@/views/business/mes/salary/payoff/components/Payoff-Details-Form.vue";

    // ---------------------------- 处理时间查询----------------------------
    //结算月份
    const settlementMonth = ref(dayjs().startOf('month'));
    //监听结算月份变化
    watch(()=>settlementMonth.value, (newValue) => {
      if(!_.isNull(newValue)){
        queryForm.belongMonth = newValue.startOf('month').format("YYYY-MM-DD")
        queryData()
      }
    })
    // ---------------------------- 处理操作人下拉 ----------------------------

    //初始化员工下拉
    const initOptEmp = ref()
    const operatorOptions = ref([])

    //输出操作者options
    function outstandingOptOptions(tableData) {
      operatorOptions.value = []
      tableData.map((e) => (
          operatorOptions.value.push({
            value: e.employeeId,
            label: e.actualName
          })
      ))
    }

    function handleEmpChange(empIds) {
      queryForm.employeeIds.length = 0
      Object.assign(queryForm.employeeIds,empIds)
    }

    //操作者下拉框搜索
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    //查询所有员工
    async function queryAllEmployee() {
      try {
        let resp = await employeeApi.queryAll();
        outstandingOptOptions(resp.data)
      } catch (e) {
        smartSentry.captureError(e);
      }
    }

    const columns = ref([
        {
            title: '员工姓名',
            dataIndex: 'actualName',
            ellipsis: true,
            align: 'center',
            width: 60
        },
        {
            title: '计件工资',
            dataIndex: 'countSalary',
            ellipsis: true,
            align: 'center',
            width: 100
        },
        {
            title: '总工资',
            dataIndex: 'totalSalary',
            ellipsis: true,
            align: 'center',
            width: 100
        },
    ]);
    const length = columns.value.length //没添加其他项前的长度

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        pageNum: 1,
        pageSize: 10,
        employeeIds:[], //员工id
        belongMonth: dayjs().startOf('month').format("YYYY-MM"), //归属月份
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        //重置所有时间
        settlementMonth.value = dayjs().startOf('month')
        queryForm.pageSize = pageSize;
        // 清空已选择的员工
        queryForm.employeeIds.length = 0
        initOptEmp.value = undefined
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await payoffApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
       if(!(_.isNil(selectedRowList.value)&&_.isEmpty(selectedRowList.value))){
         selectedRowKeyList.value.length = 0
         selectedRowList.value.length = 0
       }

// --------------------- 添加其他项目 --------------------------
    }
    async function initOtherField(){
      //没有其他项直接添加操作列
      if(!(_.isNil(tableData.value[0]) && _.isEmpty(tableData.value[0]))){
        addWageField(tableData.value[0].wageFieldValues)
      } else addOption()
    }

    const otherList = ref([])
    function addWageField(wageFieldValue) {
        wageFieldValue.map((item)=>{
          addOtherField(item)
        })
        addOption()
    }

    const actions = ref({
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        align: 'center',
        width: 80
    })

    //添加操作项
    function addOption(){
        columns.value.push(actions.value)
    }
    //添加其他项
    function addOtherField(data){
      const other = {
        title: data.fieldName,
        dataIndex: data.fieldName,
        ellipsis: true,
        type: data.type,
        width: 60
      }
      columns.value.push(other)
      otherList.value.push(other.dataIndex)
    }

// ---------------------------- 处理工资项更新 ----------------------------
    function handleFieldValueUpdate(rowData,column){
      const fieldName = column.title;
      const fieldData = rowData.wageFieldValues.find(e => e.fieldName === fieldName);

      if (fieldData) {
        const fieldValueId = fieldData.fieldValueId;
        const fieldValue = fieldData.fieldValue;
        updateWageFieldValue(fieldValueId, fieldValue);
      } else {
        
      }
    }

    function updateWageFieldValue(fieldValueId,fieldValue){
      let param = {
        fieldValueId: fieldValueId,
        value: fieldValue,
      }
      try {
        wageFieldValueApi.update(param);
        message.success("更新成功")
        queryData()
      } catch (error) {
      smartSentry.captureError(error);
      }
    }

// -------------------- 处理自定义工资项弹窗 -------------------------
    const customPayRef = ref()

    function openCustomPayModal(){
      customPayRef.value.show()
    }
    // 保存编辑工资项，清空原有工资项，重新渲染
    function handleSaveField(data){
      columns.value.splice(length, columns.value.length-length)
      data.map((item)=>{
        addOtherField(item)
      })
      addOption()
    }

// -------------------- 处理批量调整弹窗 -------------------------
    const customPayValueRef = ref()
    function openCustomPayValueModal(){
      if(_.isEmpty(selectedRowList.value)){
        message.warn("请选择数据")
        return;
      }
      customPayValueRef.value.show(selectedRowKeyList.value)
    }


// -------------------- 处理发放弹窗 -------------------
    const payoffRef = ref();
    function handleSinglePayoff(rowData){
      payoffRef.value.show(rowData)
    }
    function handleBatchPayoff(){
      if(_.isEmpty(selectedRowList.value)){
        message.warn("请选择数据")
        return;
      }
      payoffRef.value.show({
        selectedRowList: selectedRowList.value,
        selectedRowKeyList: selectedRowKeyList.value
      })
    }

// ----------------- 处理查看详情 -------------------
    const payoffDetailsRef = ref()
    function handleQueryDetails(rowData){
      payoffDetailsRef.value.show({employeeId:rowData.employeeId,actualName:rowData.actualName}, queryForm.belongMonth);
    }

// ----------------- 处理多选 ------------------------
    const selectedRowKeyList = ref([])
    const selectedRowList = ref(undefined)
    const rowSelection = {
      onChange: (selectedRowKeys,selectedRows) => {
        selectedRowKeyList.value = selectedRowKeys
        selectedRowList.value = selectedRows
      }
    };

    onMounted(async ()=>{
      await queryData()
      await queryAllEmployee()
      await initOtherField()
    });
</script>

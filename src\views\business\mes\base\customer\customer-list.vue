<!--
  * 客户表
  *
  * @Author:    xmt
  * @Date:      2024-07-03 09:43:14
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="停用标识" class="smart-query-form-item"
        ><!--0启用，1停用-->
        <a-select v-model:value="queryForm.enableFlag" placeholder="请选择">
          <a-select-option value="false">启用</a-select-option>
          <a-select-option value="true">停用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'level'">
          <div>
            <a-rate :value="Number(record.level)" disabled />
          </div>
        </template>
        <template v-if="column.dataIndex === 'enableFlag'">
          <div>
            <a-tag :color="text === 1 ? 'red' : 'green'">{{ text === 1 ? '停用' : '启用' }}</a-tag>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'number'">
          <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <CustomerForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<style>
  .ant-switch {
    border-color: rgb(98, 230, 186);
    background-color: rgb(98, 230, 186);
  }

  .ant-switch-checked {
    border-color: rgb(233, 112, 112) !important;
    background-color: rgb(233, 112, 112) !important;
  }
</style>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { customerApi } from '/@/api/business/mes/base/customer-api.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import CustomerForm from './customer-form.vue';

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '客户编号',
      dataIndex: 'number',
      ellipsis: false,
      width: 100,
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'name',
      ellipsis: false,
      width: 100,
      align: 'center',
    },
    {
      title: '公司名称',
      dataIndex: 'company',
      ellipsis: false,
      width: 200,
      align: 'center',
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      ellipsis: false,
      width: 100,
      align: 'center',
    },
    {
      title: '电话',
      dataIndex: 'telephone',
      ellipsis: false,
      width: 100,
      align: 'center',
    },
    {
      title: '停用标识',
      // 0启用，1停用
      dataIndex: 'enableFlag',
      ellipsis: false,
      width: 50,
      align: 'center',
    },
    {
      title: '等级',
      // 5星最高，无半星
      dataIndex: 'level',
      ellipsis: false,
      width: 150,
      align: 'center',
    },

    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 100,
      align: 'center',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await customerApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      await customerApi.delete(data.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }
</script>

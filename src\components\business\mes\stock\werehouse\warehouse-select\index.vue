<!--
  * 仓库选择组件
  * 返回所选仓库列表
  * @Author:    wxx
  * @Date:      2025-02-10
  * @Copyright  zscbdic
-->

<template>
  <a-select
    v-model:value="selectedValue"
    :style="{ width: width }"
    :placeholder="props.placeholder"
    :showSearch="true"
    :allowClear="true"
    :filterOption="filterOption"
    @change="handleChange"
  >
    <a-select-option v-for="item in warehouseOptions" :key="item.id" :value="item.id">
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { stkWarehouseApi } from '/@/api/business/mes/stock/stk-warehouse-api.js';

  const props = defineProps({
    value: [Number, Array],
    placeholder: {
      type: String,
      default: '请选择仓库',
    },
    width: {
      type: String,
      default: '100%',
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 过滤选项
  function filterOption(inputValue, option) {
    const item = warehouseOptions.value.find((i) => i.id === option.value);
    return item?.name?.toLowerCase().includes(inputValue.toLowerCase());
  }

  //查询仓库列表
  const warehouseOptions = ref([]);
  async function queryWarehouseList() {
    try {
      const queryData = await stkWarehouseApi.queryList();
      warehouseOptions.value = queryData.data;
    } catch (error) {
      message.error('仓库名称请求失败');
    }
  }

  // 处理选择变化
  function handleChange(value) {
    const warehouseItem = warehouseOptions.value.find((item) => item.id === value);
    emit('update:value', value);
    emit('change', warehouseItem);
  }

  // 监听props.value的变化
  const selectedValue = ref(props.value);
  watch(
    () => props.value,
    (newVal) => {
      selectedValue.value = newVal;
    }
  );

  onMounted(() => {
    queryWarehouseList();
  });
</script>

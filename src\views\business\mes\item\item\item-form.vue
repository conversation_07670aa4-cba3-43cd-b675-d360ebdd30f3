<!--
  * 主物料表
  *
  * @Author:    cjm
  * @Date:      2024-07-02 08:33:07
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="1200px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >

          <a-row>
<!--                <a-col :span="8">-->
<!--                      <a-form-item label="主键"  name="id">-->
<!--                        <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />-->
<!--                      </a-form-item>-->
<!--                </a-col>-->
                <a-col :span="12">
                      <a-form-item label="物料分类id;关联t_item_type"  name="typeId">
                        <a-input-number style="width: 100%" v-model:value="form.typeId" placeholder="物料分类id;关联t_item_type" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="物料名称"  name="name">
                        <a-input style="width: 100%" v-model:value="form.name" placeholder="物料名称" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="供应商id;关联t_item_supplier"  name="supplierId">
                        <a-input-number style="width: 100%" v-model:value="form.supplierId" placeholder="供应商id;关联t_item_supplier" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="规格型号"  name="model">
                        <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="物料编号"  name="number">
                        <a-input style="width: 100%" v-model:value="form.number" placeholder="物料编号" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="单位id;关联t_unit"  name="unitId">
                        <a-input-number style="width: 100%" v-model:value="form.unitId" placeholder="单位id;关联t_unit" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="价格"  name="price">
                        <a-input-number style="width: 100%" v-model:value="form.price" placeholder="价格" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="图片"  name="imgUrl">
<!--                    <a-input style="width: 100%" v-model:value="form.imgUrl" placeholder="图片" />-->
                    <file-upload v-model:value="form.imgUrl" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="停用标识;0启用，1停用"  name="enableFlag">
<!--                        <BooleanSelect v-model:value="form.enableFlag" style="width: 100%" />-->
                        <a-input-number style="width: 100%" v-model:value="form.enableFlag" placeholder="" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="类型;0半成品 1成品"  name="category">
                        <a-input style="width: 100%" v-model:value="form.category" placeholder="类型;0半成品 1成品" />
                      </a-form-item>
                </a-col>
                <a-col :span="12">
                      <a-form-item label="属性;0面料，1辅料"  name="attribute">
                        <a-input style="width: 100%" v-model:value="form.attribute" placeholder="属性;0面料，1辅料" />
                      </a-form-item>
                </a-col>
          </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { itemApi } from '/@/api/business/mes/item/item-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FileUpload from '/@/components/support/file-upload/index.vue';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
          id: undefined,
              // id: undefined, //主键
              typeId: undefined, //物料分类id;关联t_item_type
              name: undefined, //物料名称
              supplierId: undefined, //供应商id;关联t_item_supplier
              model: undefined, //规格型号
              number: undefined, //物料编号
              unitId: undefined, //单位id;关联t_unit
              price: undefined, //价格
              imgUrl: undefined, //图片url
              enableFlag: undefined, //停用标识;0启用，1停用
              category: undefined, //类型;0半成品 1成品
              attribute: undefined, //属性;0面料，1辅料
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  // id: [{ required: true, message: '主键 必填' }],
                  typeId: [{ required: true, message: '请选择物料分类' }],
                  name: [{ required: true, message: '请输入物料名称' }],
                  number: [{ required: true, message: '请输入物料编号' }],
                  unitId: [{ required: true, message: '请选择单位' }],
                  category: [{ required: true, message: '请选择类型' }],
                  attribute: [{ required: true, message: '请选择属性' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await itemApi.update(form);
      } else {
        await itemApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

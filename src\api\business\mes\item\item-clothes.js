/**
 * 成衣信息表 api 封装
 *
 * @Author:     fkf
 * @Date:      2024-07-02 12:03:01
 * @Copyright  zscbdic
 */
import { postRequest, getRequest,getDownload } from '/@/lib/axios';

export const itemClothesApi = {
  /**
   * 查询sku列表  <AUTHOR>
   */
  queryskuList: (itemNumber) => {
    return getRequest(`/query/sku/list/${itemNumber}`);
  },
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/item/clothes/queryPage', param);
  },
  /**
   * 成衣详情查询  <AUTHOR>
   */
  getById:(param) =>{
    return getRequest('/item/clothes/byId',param)
  },
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/item/clothes/add', param);
  },
  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/item/clothes/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/item/clothes/delete/${id}`);
  },
  //下载模版文件
  downloadTemplate(){
    return getDownload(`/item/clothes/downloadTemplate`)
  },
  //导入文件
  importfile:(file)=>{
    return postRequest('/item/clothes/import',file);
  },
};

<!--
  * 设备卡片组件
  *
  * @Author:    linwj
  * @Date:      2025-02-11 20:12:33
  * @Copyright  zscbdic
-->
<template>
  <a-card :bordered="true" style=" width: 33%; border-radius: 3px;margin: 0 0.3% 7px 0" :bodyStyle="{padding: '0px'}" :hoverable="true" >
    <!--  设备类型-->
    <div style=" height: 24px; width: 100%;display: flex;justify-content: space-between">
      <div style="display: flex;" >
        <div
            style="padding-left: 8px; height: 22px; display: flex; justify-content: center; align-items: center;background-color: #1677ff"
        >
          <span style="color: rgb(255, 255, 255); overflow: hidden; text-overflow: ellipsis; white-space: nowrap;font-size: 11px">{{props.equipmentTypeName}}</span>
        </div>
        <div
            style="width: 16px; height: 22px; border-radius: 0px 0px 100%; display: inline-block;background-color: #1677ff"
        ></div>
      </div>
      <!--  设备物联状态-->
      <div class="device-iot-status">
        <div style="font-size: 13px;">
          <span style="font-size: 12px">{{props.equipmentScadaOnlineStatus}}</span>
          <span class="connected-dot" :style="{backgroundColor:equipmentScadaOnlineStatusConst.getEnum(props.equipmentScadaOnlineStatus).color}"></span>
        </div>
      </div>
    </div>
    <!--  设备信息-->
    <div class="device">
      <div class="device-info">
        <div>设备名称：<span>{{ props.equipmentName }}</span></div>
        <div>设备编号：<span>{{ props.equipmentNumber }}</span></div>
        <div>设备状态：<span>{{props.equipmentScadaRunStatus}}</span></div>
        <div>车间信息：<span>{{props.workShopName}}</span></div>
        <div>物联平台：<span>{{props.scadaPlatform}}</span></div>
        <div>上次更新时间：<span>{{props.lastScadaDataUpdateTime}}</span></div>
        <div>最后在线时间：<span>{{props.lastScadaEquipmentUpTime}}</span></div>
<!--        <div>最后下线时间：<span>{{props.lastScadaEquipmentDownTime}}</span></div>-->
      </div>
    </div>
  </a-card>
</template>
<script setup>
const props = defineProps({
  equipmentName:{
    label: '设备名称',
    default: '设备名称',
  },
  equipmentNumber:{
    label: '设备编号',
    default: '设备编号',
  },
  equipmentTypeName:{
    label: '设备类型',
    default: '设备类型',
  },
  workShopName:{
    label: '车间信息',
    default: '车间信息',
  },
  scadaPlatform:{
    label: '物联平台',
    default: '物联平台',
  },
  equipmentScadaRunStatus:{
    label: '设备状态',
    default: '设备状态',
  },
  equipmentScadaOnlineStatus:{
    label: '物联状态',
    default: '物联状态',
  },
  lastScadaDataUpdateTime:{
    label: '上次刷新时间',
  },
  lastScadaEquipmentUpTime:{
    label: '最后在线时间',
  },
  // lastScadaEquipmentDownTime:{
  //   label: '最后下线时间',
  // }
});
// 设备物联状态
const equipmentScadaOnlineStatusConst = {
  ONLINE:{
    value: '在线',
    color: '#36ea36',
  },
  OFFLINE:{
    value: '离线',
    color: '#f43535',
  },
  UNKNOWN:{
    value: '未知',
    color: '#716f6f',
  },
  getEnum(value){
    if(value==='在线'){
      return equipmentScadaOnlineStatusConst.ONLINE;
    }else if(value==='离线'){
      return equipmentScadaOnlineStatusConst.OFFLINE;
    }else{
      return equipmentScadaOnlineStatusConst.UNKNOWN;
    }
  }
}
</script>
<style scoped lang="less">
.device{
  display: flex;
  justify-content: space-between; // 水平两端对齐
  padding: 0 9px 4px;
  white-space: nowrap;
  .device-info{
    display: flex;
    flex-direction: column;
    font-size: 12px;
  }

}
.device-iot-status{
  margin: 4px 4px 0 0;
  .connected-dot {
    width: 8px; /* 小点的宽度 */
    height: 8px; /* 小点的高度 */
    border-radius: 50%; /* 使小点成为圆形 */
    display: inline-block; /* 使小点内联显示 */
    margin-left: 4px; /* 小点与文本之间的间距 */
  }
}
</style>

/**
 * 工资发放 api 封装
 *
 * @Author:    linwj
 * @Date:      2024-11-20 20:48:59
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const payoffApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/payoff/queryPage', param);
  },

  /**
   * 发放  <AUTHOR>
   */
  payoff : (param) => {
    return postRequest('/payoff/payoff', param);
  },

  /**
   * 计件详情  <AUTHOR>
   */
  queryDetails : (param,employeeId) => {
    return postRequest(`/payoff/queryDetails/${employeeId}`, param);
  },
};


/**
 * 仓库 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-05 15:13:18
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkWarehouseApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/stkWarehouse/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/stkWarehouse/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/stkWarehouse/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/stkWarehouse/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/stkWarehouse/batchDelete', idList);
  },

  /**
   * 查询列表  <AUTHOR>
   */
  queryList: () => {
    return postRequest('/stkWarehouse/queryList', {});
  },

  /**
   * 快速建仓  <AUTHOR>
   */
  quickAdd: (param) => {
    return postRequest('/stkWarehouse/quickAdd', param);
  },
};

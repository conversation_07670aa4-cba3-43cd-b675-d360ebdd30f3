<!--
  * 单位表
  *
  * @Author:    xmt
  * @Date:      2024-07-04 11:12:45
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules"  :label-col="{ span: 5 }" >
      <a-form-item label="单位编码"  name="unitCode">
        <a-input style="width: 100%" v-model:value="form.unitCode" placeholder="单位编码" />
      </a-form-item>
      <a-form-item label="单位名称"  name="name">
        <a-input style="width: 100%" v-model:value="form.name" placeholder="单位名称" />
      </a-form-item>
      <a-form-item label="备注"  name="remark">
      <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
       </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { unitApi } from '/@/api/business/mes/base/unit-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    if(!rowData.id){
      getClientNo()
    }
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }
  //自动获取单位编号
  async function getClientNo(){
    try{
      const res = await unitApi.getUnitNo();
      form.unitCode = res.data
    } catch(err) {
      smartSentry.captureError(err);
    }
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
          id: undefined,
              remark: undefined, //备注
              unitCode: undefined, //单位编码
              name: undefined, //单位名称
  };

  let form = reactive({ ...formDefault });

  const rules = {
    name: [{ required: true, message: '单位名称 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await unitApi.update(form);
      } else {
        await unitApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

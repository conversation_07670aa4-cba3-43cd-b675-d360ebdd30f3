<!--
  * 生产领料单添加
  *
  * @Author:    fkf
  * @Copyright  zscbdic
-->
<template>
    <a-modal
        :title="form.id ? '编辑生产领料单' : '创建生产领料单'"
        width="94%"
        :open="visibleFlag"
        @cancel="onClose"
    >
      <div class="title">
        基本信息
      </div>
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" >
        <a-row>
          <a-col :span="6">
            <a-form-item label="单据编号" name="number">
              <a-input v-model:value="form.number" placeholder="请输入，忽略将自动生成" style="width: 90%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="仓库" name="warehouseId">
              <wereHouseSelect v-model:value="form.warehouseId" placeholder="请选择仓库"  style="width: 90%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="出库时间" name="outStockTime">
              <a-date-picker style="width: 90%;" show-time @change="onChangeOutStockTime" v-model:value="outStockTimeValue"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="备注" name="remark">
              <a-textarea v-model:value="form.remark" placeholder="请输入备注" style="width: 90%;" auto-size/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="仓管员" name="stockerId">
              <employeeSelect v-model:value="form.stockerId" @change="stockerChange" placeholder="请选择申请人" style="width: 90%;"/>
            </a-form-item>
          </a-col>
              <ownerSelect @update:ownerInfo="owerSelectChange"  ref="ownerSelectRef" v-model:ownerInfo="ownerInfo"/>
          <a-col :span="6">
            <a-form-item label="申请人" name="applicantId">
              <employeeSelect v-model:value="form.applicantId" @change="applicantChange" placeholder="请选择申请人" style="width: 90%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="申请时间" name="applyTime">
              <a-date-picker show-time  style="width: 90%;" @change="onChangeApplyTime" placeholder="若不填写默认当前时间" v-model:value="applyTimeValue"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="title">
       单据明细
      </div>
      <a-space style="margin-bottom: 10px;">
        <a-button type="primary" @click="handleAdd">
          <plus-outlined /> 选择单据
        </a-button>
      </a-space>
      <a-table
        :columns="columns"
        :dataSource="form.details"
        :pagination="false"
        bordered
        size="small"
        row-key="seq"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <div class="smart-table-operate">
              <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
            </div>
          </template>
          <template v-if="column.dataIndex === 'materielId'">
            <MaterialSelect v-model:value="record.materielId"  placeholder="请选择物料" @change="(value) => materielChange(value,record)" disabled/>
          </template>
          <template v-if="column.dataIndex=== 'lotNumber'">
            <lotSelect v-model:value="record.lot" placeholder="请选择批次号" @change="(value) => lotChange(value,record)" :materielId="record.materielId"/>
          </template>
          <template v-if="column.dataIndex=== 'locationNumber'">
            <locationSelect v-model:value="record.locationId" :warehouseId="form.warehouseId" placeholder="请选择货位" @change="(value) => locationChange(value,record)"/>
          </template>
          <template v-if="column.dataIndex=== 'qty'">
            <a-input v-model:value="record.qty" placeholder="请输入数量"/>
          </template>
          <template v-if="column.dataIndex=== 'price'">
            <a-input v-model:value="record.price" placeholder="请输入单价"/>
          </template>
          <template v-if="column.dataIndex=== 'amount'">
            <a-input v-model:value="record.amount" disabled/>
          </template>
          <template v-if="column.dataIndex=== 'avbQty'">
            <singleMaterialInventory :materialId="record.materielId" :warehouseId="form.warehouseId" :locationId="record.locationId" :lotId="record.lotId" />
          </template>
          <template v-if="column.dataIndex=== 'remark'">
            <a-textarea v-model:value="record.remark" placeholder="请输入备注" auto-size/>
          </template>
          <template v-if="column.dataIndex=== 'originDetailId'">
            <produceInstructOrderSelect v-model:value="record.originOrderBillId" style="width: 90%;" disabled/>
          </template>
        </template>
      </a-table>
      <template #footer>
        <a-space>
          <a-button @click="onClose">取消</a-button>
          <a-button type="primary" @click="onSubmit">提交申请</a-button>
          <a-button type="primary" @click="onSubmitStatus">提交并审核</a-button>
        </a-space>
      </template>
    </a-modal>
    <orderItemSelect ref="orderItemSelectRef" @selectValue="selectValueInfo" />
  </template>
  <script setup>
    import { ref , reactive , watch} from 'vue';
    import { useUserStore } from '/@/store/modules/system/user.js';
    import { stkProdPickMtrlApi } from '/@/api/business/mes/stock/stk-prodpickmtrl-api';
    import { unitApi } from '/@/api/business/mes/base/unit-api.js';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { smartSentry } from '/@/lib/smart-sentry';
    import { BILLS_STATUS } from '/@/constants/business/mes/stock/stk-status-const.js';
    import orderItemSelect from '/@/components/business/mes/produce/produce-material-select-modal/index.vue'
    import ownerSelect from '/@/components/business/mes/stock/owner-select/index.vue'
    import wereHouseSelect from '/@/components/business/mes/stock/werehouse/warehouse-select/index.vue';
    import MaterialSelect from '/@/components/business/mes/item/material-all-select/index.vue';
    import employeeSelect from '/@/components/system/employee-obj-select/index.vue';
    import locationSelect from '/@/components/business/mes/stock/werehouse/location-select/index.vue';
    import lotSelect from '/@/components/business/mes/stock/lot/index.vue';
    import produceInstructOrderSelect from '/@/components/business/mes/produce/produce-instruct-order-select/index.vue';
    import singleMaterialInventory from '/@/components/business/mes/stock/single-material-Inventory/index.vue';
    import dayjs from 'dayjs';
    import { message } from 'ant-design-vue';

   
  // ------------------------ 事件Ref ------------------------

    const emits = defineEmits(['reloadList']);
    const userStore = useUserStore();  //获取用户store
    const ownerSelectRef = ref();   //货主选择
    const ownerInfo = ref({}); //货主信息
    const orderItemSelectRef = ref(); //物料选择 
  // ------------------------ 显示与隐藏 ------------------------
    // 是否显示
    const visibleFlag = ref(false);

    async function show(id) {
      if (id) {
        //请求具体单据
        const res = await stkProdPickMtrlApi.byId(id);
        Object.assign(form,res.data);
        ownerInfo.value = {
          type: form.ownerType,
          id: form.ownerId,
          name: form.ownerName,
        };
        form.details.forEach(details =>{
          details.lot ={
            id: details.lotId,
            number:details.lotNumber
          }
        })
        outStockTimeValue.value = dayjs(form.outStockTime);
        applyTimeValue.value = dayjs(form.applyTime);
       
      }
      visibleFlag.value = true;
    }

    function onClose() {
      resetData();
      visibleFlag.value = false;
    }
    //重制数据
    function resetData(){
      Object.assign(form,formDefault);
      //重置货主选择
      ownerSelectRef.value.reset();
      form.details = [];
      ownerInfo.value = {};
      outStockTimeValue.value = undefined;
      applyTimeValue.value = undefined;
    }
  //------------------------ 表单 ------------------------
    const formRef = ref();
    const rules = {
      warehouseId: [
        { required: true, message: '仓库不能为空' },
      ],
    }
    const formDefault = {
      id: undefined, //id
      number: undefined, //单据编号
      remark: undefined, //备注
      warehouseId: undefined, //仓库id 组建
      outStockTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), //出库时间
      stockerId: undefined, //仓管员id 组建
      stockerName: undefined, //仓管员名称
      ownerType: undefined, //货主类型
      ownerId: undefined, //货主id
      ownerName: undefined, //货主名称
      applicantId: userStore.employeeId, //申请人id 组建 默认当前账号
      applicantName:userStore.actualName, //申请人名称
      applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), //申请时间
      originType: undefined, //单据来源类型
      originId: undefined, //单据来源id
      originNumber: undefined, //单据来源编号
      type: '', //单据类型
      way: '', //单据方式
      status:  BILLS_STATUS.UN_AUDIT.value, //单据状态
      details:[], //详细信息
    }
    let form = reactive({ ...formDefault });

// 表格列定义
    const columns = [
      {
        title: '序号',
        dataIndex: 'seq',
        width: 60,
      },
      {
        title: '物料',
        dataIndex: 'materielId',
        width: 220,
      },
      {
        title: '物料SKU',
        dataIndex: 'materielSku',
        width: 150,
      },
      {
        title: '物料SPU',
        dataIndex: 'materielSpu',
        width: 150,
      },
      {
        title: '物料类型',
        dataIndex: 'materielModel',
        width: 150,
      },
      {
        title: '单位',
        dataIndex: 'unitName',
        width: 80,
      },
      {
        title: '批次号',
        dataIndex: 'lotNumber',
        width: 150,
      },
      {
        title: '库位编号',
        dataIndex: 'locationNumber',
        width: 150,
      },
       {
        title: '可用库存',
        dataIndex: 'avbQty',
        width: 100,
      },
      {
        title: '生产指令单编号',
        dataIndex: 'originDetailId',
        width: 200,
      },
      {
        title: '数量',  
        dataIndex: 'qty', 
        width: 100,
      },
      {
        title: '单价',
        dataIndex: 'price',
        width: 100,
      },
      {
        title: '金额',
        dataIndex: 'amount',
        width: 100,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        width: 150,
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 90,
        fixed: 'right',
      }
    ];
    //------------------------ 添加删除产品 ------------------------
    function handleAdd() {
      orderItemSelectRef.value.open();
    }
    function selectValueInfo({ selectedRows,billType,orderBillType }) {
      form.details = [];
      selectedRows.forEach(async item =>{
        const details ={
          seq: form.details.length + 1,//序号
          materielId: item.itemId, //物料id
          materielSku: undefined, //物料sku
          materielSpu: undefined, //物料spu
          materielModel: undefined, //物料类型
          unitId: item.itemUnitId, //单位id
          unitName: item.itemUnitName,
          lot: undefined, //批次对象
          lotId: undefined, //批次id
          lotNumber: undefined, //批次号
          sns: undefined, //序列号
          locationId: undefined, //库位id
          locationNumber: undefined, //库位编号
          qty: undefined, //数量    
          joinQty: undefined, //累计数量  
          price: undefined, //单价
          amount: undefined, //总金额
          originDetailType: billType, //来源类型
          originDetailId: item.id, //来源id
          originDetailSeq: undefined, //来源序号
          originOrderBillType: orderBillType, //来源类型
          originOrderBillId: item.orderId, //来源生产指令单id
          originOrderBillNumber: item.orderNumber, //来源编号
        }
        form.details.push(details);
        // 强制调用物料选择
        await materielChange({
          skuNumber: item.itemSkuNumber,
          number: item.itemNumber,
          model: item.itemModel,
          unitId: item.itemUnitId,
        },details);
      });
    }
    //删除
    function handleDelete(record){
      form.details = form.details.filter(item => item.seq !== record.seq);
      form.details.forEach((item, i) => {
          item.seq = i + 1;
      });
    }
  //------------------------ 处理时间 ------------------------
    const outStockTimeValue =ref()
    const applyTimeValue = ref()

    function onChangeOutStockTime(date, dateString) {
      form.outStockTime = dateString;
    }
    function onChangeApplyTime(date, dateString) {
      form.applyTime = dateString;
    }
  //------------------------ 处理人员 ----------------------
  //绑定人员名字
    function stockerChange(employee) {
      form.stockerName = employee?.actualName;
      
    }
    //申请人
    function applicantChange(employee) {
      form.applicantName = employee?.actualName;
    }
  //------------------------ 货主选择 ------------------------
    function owerSelectChange(ownerInfo) {
        form.ownerType = ownerInfo?.type;
        form.ownerId = ownerInfo?.id;
        form.ownerName = ownerInfo?.name;
    }
  //------------------------ 处理物料 ------------------------
    //物料选择
    async function materielChange(materiel,record){
      record.unitId = materiel?.unitId;
      record.materielSku = materiel?.skuNumber;
      record.materielSpu = materiel?.number;
      record.materielModel = materiel?.model;
      //通过id查询单位名称
      const res = await unitApi.getId(record.unitId);
      record.unitName = res.data.name;
    }
    //------------------------ 处理货位 ------------------------
    function locationChange(location,record){
      record.locationNumber = location?.number;
    }
   //------------------------ 处理批次号 ------------------------
    function lotChange(lot,record){
      record.lotId = lot?.id;
      record.lotNumber = lot?.number;
    }
  //---------------------- 金额计算 ------------------------
  const calculateAmount = (record) =>{
    let qty = Number(record.qty) ||0;
    let price = Number(record.price) ||0;
    record.amount = (qty*price).toFixed(3);
  }
  //监听金额变化
  watch(
    //监听金额变化
    () => form.details.map(record => [record.qty,record.price]),
    //监听变化后计算金额
    () => form.details.forEach(calculateAmount),
    {deep:true}
  )
  //------------------------ 保存 ------------------------
  async function onSubmit() { 
    await formRef.value.validateFields();
    SmartLoading.show();
    try {
      if (form.id) {
        await stkProdPickMtrlApi.update(form);
      } else {
        await stkProdPickMtrlApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  //------------------------ 保存并审核 ------------------------
  async function onSubmitStatus() {
    form.status = BILLS_STATUS.AUDIT.value;
    await onSubmit();
  }
  defineExpose({
      show
    })
  </script>

  <style>
    .title {
      font-size: 16px;
      font-weight: bold;
      margin: 10px 0;
    }
  </style>
  
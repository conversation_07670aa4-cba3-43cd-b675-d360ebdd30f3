<!--
  * 工序信息
  *
  * @Author:    xmt
  * @Date:      2024-07-13 15:19:57
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined/>
          </template>
          新建
        </a-button>
        <a-button @click="showImportForm"  size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          导入
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData"/>
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table

        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"

    >
      <template #bodyCell="{ text,record, column }">
        <template v-if="column.dataIndex === 'processNumber'">
          <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <ProcessForm ref="formRef" @reloadList="queryData"/>
    <ImportForm ref="importFormRef" @reloadList="queryData" :downloadTemplateApi="processApi.downloadTemplate" :importfileApi="processApi.importfile"/>
  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/src/components/framework/smart-loading';
import {processApi} from '/src/api/business/mes/process/process-api';
import {PAGE_SIZE_OPTIONS} from '/src/constants/common-const';
import {smartSentry} from '/src/lib/smart-sentry';
import TableOperator from '/src/components/support/table-operator/index.vue';
import ProcessForm from './process-form.vue';
import ImportForm from '/@/components/business/mes/common/data-import-modal/index.vue'
import {dictApi} from "/@/api/support/dict-api.js";
// ---------------------------- 表格列 ----------------------------
const codeNames = ref([]);

async function getColumnsName(code) {
  const list = await dictApi.valueList(code);
  codeNames.value = list.data
}

getColumnsName("PROCESS_TYPE")
const columns = ref([
  {
    title: '工序编号',
    dataIndex: 'processNumber',
    ellipsis: true,
    align: 'center'

  },
  {
    title: '工序名称',
    dataIndex: 'name',
    ellipsis: true,
    align: 'center'

  },
  {
    title: '部位',
    dataIndex: 'position',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '工序类型',
    dataIndex: 'processType',
    ellipsis: true,
    align: 'center',
    customRender: (text) => {
      for (let i = 0; i < codeNames.value.length; i++) {
        if (text.text === codeNames.value[i].valueCode) {
          return codeNames.value[i].valueName
        }
      }
    }
  },
  {
    title: '标准工时',
    dataIndex: 'standardTime',
    ellipsis: true,
    align: 'center'
  },
  {
    title: '工价一',
    dataIndex: 'unitPrice1',
    ellipsis: true,
    align: 'center',
    customRender: (text) => {
      return "￥" + text.value.toFixed(3)
    }

  },
  {
    title: '工序控制',
    dataIndex: 'processControl',
    ellipsis: true,
    align: 'center',
    customRender: (text) => {
      if (text.text === '0') return "自制"
      else if (text.text === '1') return "委外"
      else if (text.text === '2') return "不限"
      else return "未知"
    }
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 90,
    align: 'center'
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //关键字查询
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{column: 'create_time', isAsc: false}],
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await processApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}


onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();
const importFormRef=ref(null)
function showForm(data) {
  formRef.value.show(data);
}
function showImportForm() {   
    importFormRef.value.show();
  }
// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求删除
async function requestDelete(data) {
  SmartLoading.show();
  try {
    await processApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}
</script>
<style scoped>
/* 斑马纹样式 */
.ant-table-striped :deep(.table-striped) td {
  background: rgb(244, 243, 243);
}
</style>

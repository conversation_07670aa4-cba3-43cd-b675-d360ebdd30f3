/**
 * 批号跟踪信息 api 封装
 *
 * @Author:    wxx
 * @Date:      2025-02-14
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const lotMaterialTraceApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/stkLotMaterialTrace/queryPage', param);
  },
  /**
   * 额外信息分页查询  <AUTHOR>
   */
  queryPageExtra: (param) => {
    return postRequest('/stkLotMaterialTrace/queryPageWithExtra', param);
  },
};

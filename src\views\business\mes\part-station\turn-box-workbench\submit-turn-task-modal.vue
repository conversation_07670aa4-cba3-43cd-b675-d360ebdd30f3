<template>
  <a-modal v-model:open="visible" :width="1300" title="发起转运任务" @cancel="closeModal" @ok="onSubmit">
    <a-row :gutter="16">
      <a-col span="14">
        <div style="margin-bottom: 20px;background-color: #EBEEF5;color: #909399;padding: 6px;border-radius: 4px">
          1. 扫码货位二维码与周转箱二维码自动发起任务
          <br/>
          2.成功发起任务后需重新扫码货位二维码与周转箱二维码
          <br/>
          3.点击 “重置” 按钮，重新扫码货位二维码与周转箱二维码
          <br/>
          4.点击 “撤回” 按钮，撤回任务
        </div>
        <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 ,offset:1}">
          <a-form-item>
            二维码输入框
            <a-input style="font-size: 25px;font-weight: bold" ref="qrCodeInputRef" v-model:value="qrCode"
                     @pressEnter="qrCodePressEnter" placeholder="请扫描二维码"/>
          </a-form-item>
          <a-form-item name="type">
            任务类型
            <a-select style="font-size: 25px;font-weight: bold" size="large" v-model:value="form.type"
                      placeholder="请选择任务类型" :options="taskTypeOptions" @change="taskTypeChange"/>
          </a-form-item>
          <a-form-item name="produceTeamId" v-if="form.type === TURN_TASK_TYPE_ENUM.TO_WORKSHOP.value">
            生产小组
            <ProduceTeamSelect style="font-size: 25px;font-weight: bold" size="large"
            v-model:value="form.endProduceTeamId"  @change="inputRefFocus"/>
          </a-form-item>
          <a-form-item name="syncPartDispatchFlag" v-if="form.type === TURN_TASK_TYPE_ENUM.TO_WORKSHOP.value">
            是否同步
            <a-switch v-model:checked="form.syncPartDispatchFlag"  checked-children="开" un-checked-children="关"  @change="inputRefFocus"/>
            <br/>
            <div style="color: #909399">
              注意：只有选择了生产小组后才能开启同步功能
            </div>
          </a-form-item>
          <a-form-item name="turnoverBoxId">
            裁片周转箱
            <a-input style="font-size: 25px;font-weight: bold" v-model:value="turnBox.number"
                     placeholder="请扫码周转箱二维码" disabled/>
          </a-form-item>
          <a-form-item name="startLocationId">
            起点货位
            <a-input style="font-size: 25px;font-weight: bold" v-model:value="startLocation.binCode"
                     placeholder="请扫码货位二维码" disabled/>
          </a-form-item>
        </a-form>
      </a-col>

      <!--    任务列表  -->
      <a-col span="10">
        <div style="font-weight: bold;">周转任务列表</div>
        <a-list item-layout="horizontal" :data-source="taskList" style="max-height: 420px; overflow-y: auto;">
          <template #renderItem="{ item }">
            <a-list-item>
              <template #actions>
                <a-button type="link" style="color: #F56C6C;font-size: 20px" @click="withdrawTask(item.id)"
                          v-if="item.status === TURN_TASK_STATUS_ENUM.WAIT_GET.value">撤回
                </a-button>
                <div v-else-if="item.status === TURN_TASK_STATUS_ENUM.CANCEL.value" style="color: #909399;font-size: 20px">已撤回</div>
              </template>
              <a-list-item-meta>
                <template #title>
                  <span style="font-size: 20px;"
                        v-if="item.status === TURN_TASK_STATUS_ENUM.WAIT_GET.value"> {{ item.name }}</span>
                  <span style="font-size: 20px;font-style: italic; text-decoration: line-through;"
                        v-else-if="item.status === TURN_TASK_STATUS_ENUM.CANCEL.value">{{ item.name }}</span>
                </template>
                <template #description>
                  {{ `起点货位：${item.startLocationCode}  /  时间：${item.submitTime}` }}
                </template>
              </a-list-item-meta>
            </a-list-item>
          </template>
        </a-list>
      </a-col>
    </a-row>

    <template #footer>
      <!-- 自定义按钮 -->
      <div style="">
        <a-button @click="closeModal" size="large" style="margin-right: 10px">取消</a-button>
        <a-button type="primary" @click="resetData" size="large">重置</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup>
import {nextTick,reactive, ref} from "vue";
import {QR_CODE_TYPE_ENUM} from "/src/constants/business/mes/common/qr-code-type-const.js";
import {message, notification} from "ant-design-vue";
import _ from "lodash";
import {SmartLoading} from "/src/components/framework/smart-loading/index.js";
import {partStationBinApi} from "/@/api/business/mes/part-station/bin/part-station-bin-api.js";
import {partStationTurnBoxApi} from "/@/api/business/mes/part-station/turn-box/part-station-turn-box-api.js";
import {
  TURN_TASK_STATUS_ENUM,
  TURN_TASK_TYPE_ENUM
} from "/@/constants/business/mes/part-station/turn-task/turn-task-const.js";
import {
  partStationTurnTaskActionApi
} from "/@/api/business/mes/part-station/turn-task/part-station-turn-task-action-api.js";
import ProduceTeamSelect from "/@/components/business/mes/factory/produce-team-select/index.vue";

//-----------------------------任务列表---------------------------
const taskList = ref([])

async function withdrawTask(id) {
  try {
    SmartLoading.show();
    // console.log("撤回任务", id)
    let res = await partStationTurnTaskActionApi.cancelTask([id])
    taskList.value.forEach((item) => {
      if (item.id === id) {
        item.status = TURN_TASK_STATUS_ENUM.CANCEL.value
      }
    })
    message.success("撤回成功")
  } finally {
    SmartLoading.hide();
    qrCodeInputRef.value.focus()
  }
}

//---------------------输入表单--------------------------------
// 组件ref
const formRef = ref();
const formDefault = {
  type: TURN_TASK_TYPE_ENUM.TO_WORKSHOP.value,
  startLocationId: null,
  turnoverBoxId: null,
  endProduceTeamId: null,
  syncPartDispatchFlag: false,
};
let form = reactive({...formDefault});
const rules = {
  type: [{required: true, message: '任务类型 必填'}],
  startLocationId: [{required: true, message: '起点货位 必填'}],
  turnoverBoxId: [{required: true, message: '周转箱 必填'}],
};

const taskTypeOptions = TURN_TASK_TYPE_ENUM.getOptions();

function taskTypeChange(){
  form.endProduceTeamId = null
  nextTick(() => {
    qrCodeInputRef.value.focus()
  })
}

async function onSubmit() {
  try {
    // console.log("提交", form)
    await formRef.value.validateFields();

    await submitTurnTask()
    //清除相关字段
    form.startLocationId = null;
    form.turnoverBoxId = null;
    qrCode.value = null;
    startLocation.value = Object.assign(startLocationDefault);
    turnBox.value = Object.assign(turnBoxDefault);
    qrCodeInputRef.value.focus();
    // message.success("发起任务成功")

  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

async function submitTurnTask() {
  try {
    SmartLoading.show();
    let res = await partStationTurnTaskActionApi.submitTurnTask(form)
    // console.log("res", res)
    taskList.value.unshift({
      name: "周转箱：" + turnBox.value.number + "--" + TURN_TASK_TYPE_ENUM.getOption(form.type).desc,
      submitTime: new Date().toLocaleString(),
      startLocationCode: startLocation.value.binCode,
      id: res.data,
      status: TURN_TASK_STATUS_ENUM.WAIT_GET.value
    })
    openNotification("success", "top", "提示", "发起任务成功")
  } catch (e) {
    openNotification("error", "top", "提示", e.data.msg)
  } finally {
    SmartLoading.hide();
  }
}

// ----------------------- modal  显示与隐藏 ---------------------
const emits = defineEmits(['close']);

defineExpose({
  showModal,
});

const visible = ref(false);

async function showModal() {
  visible.value = true;
  await nextTick(() => {
    qrCodeInputRef.value.focus();
  })

}

function closeModal() {
  resetData()
  taskList.value = []
  visible.value = false;
  emits('close');
}


//---------------扫码----------------

const qrCodeInputRef = ref();
const qrCode = ref('')

/**
 * 条码校验
 * @param code
 */
function codeCheck(code) {
  if (_.isEmpty(code)) {
    return false;
  }
  let codeType = QR_CODE_TYPE_ENUM.getTypeByCode(code);
  // console.log("条码校验",code)
  if (codeType === QR_CODE_TYPE_ENUM.PART_STATION_BIN || codeType === QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX) {
    return true;
  }
  openNotification("warn", 'top', '提示', '请扫描货位二维码或裁片周转箱二维码');
  return false;
}

async function qrCodePressEnter(e) {
  let code = e.target.value;
  //校验
  if (!codeCheck(code)) {
    qrCodeInputRef.value.focus();
    qrCode.value = null
    e.target.value = null
    return
  }
  //获取值和类型
  let value = QR_CODE_TYPE_ENUM.getValueByCode(code);
  let codeType = QR_CODE_TYPE_ENUM.getTypeByCode(code);
  if (_.isEmpty(value)) {
    openNotification("warn", 'top', '提示', '请扫描二维码');
    return
  }

  try {
    SmartLoading.show();
    if (codeType === QR_CODE_TYPE_ENUM.PART_STATION_BIN) {
      await getStationBin(value)
      if (form.turnoverBoxId) {
        await onSubmit()
      }
    } else if (codeType === QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX) {
      await getTurnBox(value)
      if (form.startLocationId) {
        await onSubmit()
      }
    }
  } catch (e) {
    console.log()
  } finally {
    qrCodeInputRef.value.focus();
    qrCode.value = null
    e.target.value = null
    SmartLoading.hide();
  }

}

function inputRefFocus(){
  qrCodeInputRef.value.focus()
}

//-----------------货位------------------------
const startLocationDefault = {
  id: null,
  binCode: null,
}

//货位
const startLocation = ref({
  ...startLocationDefault
})

async function getStationBin(id) {
  try {
    SmartLoading.show();
    const res = await partStationBinApi.queryBinById(id);
    startLocation.value = res.data;
    form.startLocationId = res.data.id;
    message.info('货位二维码');
  } catch (e) {
    console.log(e)
  } finally {
    SmartLoading.hide();
  }
}

//----------------周转箱----------------------
//周转箱
const turnBoxDefault = {
  id: null,
  name: null,
  number: null,
}

const turnBox = ref({
  ...turnBoxDefault
})

async function getTurnBox(id) {
  try {
    SmartLoading.show();
    const res = await partStationTurnBoxApi.queryById(id)
    turnBox.value = res.data;
    form.turnoverBoxId = res.data.id;
    message.info('周转箱二维码');
  } catch (e) {
    console.log(e)
  } finally {
    SmartLoading.hide();
  }
}

//--------------------------------------------

function resetData() {
  qrCode.value = null;
  Object.assign(form, formDefault);
  startLocation.value = Object.assign(startLocationDefault);
  turnBox.value = Object.assign(turnBoxDefault);
  qrCodeInputRef.value.focus();

  //清空输入框数据
  // qrCode.value = null
  // qrCodeInputRef.value.value = null;
  // qrCodeInputRef.value.focus();
}

function openNotification(type, placement, message, description) {
  notification[type]({
    message: message,
    description: description,
    placement: placement,
  });
}


</script>

<style scoped lang="less">

</style>

<!--
  * 批次跟踪表
  *
  * @Author:    lyq
  * @Date:      2025-02-19
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="单据类型" class="smart-query-form-item">
        <a-select v-model:value="queryForm.billType" placeholder="请选择" style="width: 120px">
          <a-select-option v-for="item in Object.values(BillTypeEnum).filter((item) => typeof item !== 'function')" :key="item.value">{{
            item.desc
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="跟踪方向" class="smart-query-form-item">
        <a-select v-model:value="queryForm.stockDirect" placeholder="请选择" style="width: 120px">
          <a-select-option v-for="item in Object.values(LOT_STOCKDIRECT).filter((item) => typeof item !== 'function')" :key="item.value">{{
            item.desc
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="单据时间" class="smart-query-form-item">
        <a-range-picker :presets="defaultTimeRanges" style="width: 240px" @change="onChangeRecordTime" v-model:value="billTime" />
      </a-form-item>
      <a-form-item label="物料名称" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielName" placeholder="物料名称" />
      </a-form-item>
      <a-form-item label="物料spu编码" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielSpuNumber" placeholder="物料spu编码" />
      </a-form-item>
      <a-form-item label="物料sku编码" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.materielSkuNumber" placeholder="物料sku编码" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2000 }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'billType'">
          <div class="smart-table-operate">
            {{ BillTypeEnum.getOptions(record.billType) }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'orderType'">
          <div class="smart-table-operate">
            {{ BillTypeEnum.getOptions(record.orderType) }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'stockDirect'">
          <div class="smart-table-operate">
            {{ LOT_STOCKDIRECT.getOptions(record.stockDirect) }}
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { lotMaterialTraceApi } from '../../../../../../api/business/mes/stock/lot-material-trace-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { LOT_STOCKDIRECT } from '/@/constants/business/mes/stock/lot-master-const';
  import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { defaultTimeRanges } from '/@/lib/default-time-ranges.js';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '物料spu编号',
      dataIndex: 'materielSpuNumber',
      width: 200,
    },
    {
      title: '物料sku编号',
      dataIndex: 'materielSkuNumber',
      width: 200,
    },
    {
      title: '物料名称',
      dataIndex: 'materielName',
      ellipsis: true,
    },
    {
      title: '规格型号',
      dataIndex: 'materielModel',
      ellipsis: true,
      width: 130,
    },
    {
      title: '跟踪方向',
      dataIndex: 'stockDirect',
      ellipsis: true,
      width: 85,
    },
    {
      title: '单据编号',
      dataIndex: 'billNumber',
      width: 200,
    },
    {
      title: '单据时间',
      dataIndex: 'billTime',
      ellipsis: true,
    },
    {
      title: '单据类型',
      dataIndex: 'billType',
      ellipsis: true,
    },
    {
      title: '数量',
      dataIndex: 'qty',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      ellipsis: true,
      width: 70,
    },
    {
      title: '订单类型',
      dataIndex: 'orderType',
      ellipsis: true,
    },

    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      ellipsis: true,
      width: 200,
    },
    {
      title: '仓库',
      dataIndex: 'warehouseName',
      ellipsis: true,
      width: 150,
    },
    {
      title: '货位编号',
      dataIndex: 'locationNumber',
    },
  ]);
  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 't_mes_stk_lot_material_trace.create_time', isAsc: false }],
    lotMasterId: undefined,
    queryKey: undefined,
    billType: undefined,
    stockDirect: undefined,
    billTimeBegin: undefined,
    billTimeEnd: undefined,
    materielName: undefined,
    materielSpuNumber: undefined,
    materielSkuNumber: undefined,
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  // 报工日期范围
  const billTime = ref([]);
  //-------------- 查询时间 ------------------
  function onChangeRecordTime(dates, dateStrings) {
    queryForm.billTimeBegin = dateStrings[0];
    queryForm.billTimeEnd = dateStrings[1];
  }
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    billTime.value = []; // 手动清空报工日期
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await lotMaterialTraceApi.queryPageExtra(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }
  onMounted(queryData);
</script>

<template>
  <a-modal
    :title="'协助报工'"
    width="1500px"
    :open="visibleFlag"
    @cancel="onClose"
    @ok="submit"
    :maskClosable="false"
    :destroyOnClose="true"
    >
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <InsItemSelect @change="onInsItemChange" />
        <a-form-item label="工序" class="smart-query-form-item">
          <a-select
            placeholder="工序"
            style="width: 170px"
            v-model:value="queryForm.orderProcessId"
            @change="queryData"
          >
            <a-select-option v-for="item in processOptions" :key="item.value" :value="item.value">
              {{ item.label }}
              <div style="color: #999; font-size: 12px;">位置: {{ item.position || '-' }}</div>
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-space>
          <a-date-picker 
            v-model:value="batchReportTime" 
            :show-time="{ format: 'HH' }" 
            format="YYYY-MM-DD HH"
            placeholder="批量修改时间"
            @change="batchUpdateReportTime"
            style="margin-left: 10%"
          />
        </a-space>
      </a-row>
    </a-form>
    <a-table
    :columns="columns"
    size="small"
    :dataSource="tableData"
    rowKey="id"
    bordered
    :loading="tableLoading"
    :pagination="false"
    :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, preserveSelectedRowKeys: true }"
    >
    <template #bodyCell="{ record, column }">
      <template v-if="column.dataIndex === 'workReportNum'">
        <a-input-number v-model:value="record.workReportNum" />
      </template>
      <template v-if="column.dataIndex === 'workerId'">
        <EmployeeSelect v-model:value="record.workerId" />
      </template>
      <template v-if="column.dataIndex === 'reportTime'">
        <a-date-picker v-model:value="record.reportTime"    :show-time="{ format: 'HH' }" format="YYYY-MM-DD HH"/>
      </template>
    </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
import { ref,reactive } from 'vue';
import { PAGE_SIZE_OPTIONS, PAGE_SIZE } from '/src/constants/common-const';
import InsItemSelect from '/src/components/business/mes/produce/ins-item-select/index.vue';
import EmployeeSelect from '/src/components/system/employee-select/index.vue';
import { workRepoetApi } from '/src/api/business/mes/work/work-report-api';
import { produceInstructOrderProcessApi } from '/src/api/business/mes/produce/produce-instruct-order-process-api';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

// ---------------------------- 事件 ----------------------------
const emit = defineEmits(['reloadList'])
// ---------------------------- 显示 ----------------------------
const visibleFlag = ref(false)

function onClose() {
    reset()
    visibleFlag.value = false
    emit('reloadList')
}

function show() {
    visibleFlag.value = true
}
function reset() {
  Object.assign(queryForm, queryFormState)
  selectedRows.value = []
  selectedRowKeys.value = []
  tableData.value = []
  total.value = 0
  processOptions.value = [] // 清空工序选项
}
// ---------------------------- 表单 ----------------------------
//查询信息
const queryFormState ={
  pageNum: 1,
  pageSize: PAGE_SIZE,
  orderProcessId: undefined, //工序id
  instructOrderId: undefined, //指令单id
}
const queryForm = reactive({...queryFormState})
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);

// 批量修改时间相关
const batchReportTime = ref(null);

const columns = [
  {
    title: '生产指令单编号',
    dataIndex: 'instructOrderNumber',
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
  },
  {
    title: '物料名称',
    dataIndex: 'itemName',
  },
  {
    title: '物料型号',
    dataIndex: 'model',
  },
  {
    title: '单位名称',
    dataIndex: 'unitName',
  },
  {
    title: '颜色',
    dataIndex: 'styleColor',

  },
  {
    title: '尺寸',
    dataIndex: 'size',
  },
  {
    title: '位置',
    dataIndex: 'positions',

  },
  {
    title: '扎数',
    dataIndex: 'tieNum',

  },
  {
    title: '数量',
    dataIndex: 'num',
  },
  {
    title: '报工数量',
    dataIndex: 'workReportNum',
  },
  {
    title: '报工人员',
    dataIndex: 'workerId',
    width: 200,
  },
  {
    title: '报工时间',
    dataIndex: 'reportTime',
    width: 200,
  }
]

//--------------------------- 选择事件 ----------------------------
//工序列表选项
const processOptions = ref([])
//获取指令单id
function onInsItemChange(data) {
  //清空工序id
  queryForm.orderProcessId = undefined
  processOptions.value = []
  queryForm.instructOrderId = data.instructOrderId
  getProcessList()
}
//选择事件
const onSelectChange = (keys, rows) => {
  selectedRows.value = rows;
  selectedRowKeys.value = keys;
  // console.log(selectedRowKeys.value,selectedRows.value)
};

/**
 * 批量更新选中行的报工时间
 */
function batchUpdateReportTime() {
  if (!batchReportTime.value) {
    message.warning('请选择报工时间');
    return;
  }
  
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择需要修改的数据');
    return;
  }
  
  // 更新选中行的报工时间，确保使用 dayjs 对象
  selectedRows.value.forEach(row => {
    // 直接使用 batchReportTime.value（它本身就是一个 dayjs 对象）
    row.reportTime = batchReportTime.value;
  });
  
  message.success(`成功批量修改 ${selectedRowKeys.value.length} 条数据的报工时间`);
  batchReportTime.value='';
}
//获取工序列表
async function getProcessList() {
  const res = (await produceInstructOrderProcessApi.getProcessList(queryForm.instructOrderId)).data
  processOptions.value = res.map(item => ({
    label: item.name,
    value: item.id,
    position: item.position
  }))
}
//获取工序未报工费票
async function queryData() {
  if(!queryForm.orderProcessId) {
    message.warning('请先选择工序')
    return
  }
  if (selectedRows.value.some(row => !row.workerId)) {
  message.error('请选择报工人员');
  return;
  }
  tableLoading.value = true
  try {
    const res = (await workRepoetApi.unReport(queryForm)).data
    tableData.value = res.list.map(item => ({
      ...item,
      workReportNum: item.num
    }))
    total.value = res.total
    selectedRowKeys.value = []
    selectedRows.value = []
  } catch (error) {
    console.log(error)
    message.error('获取数据失败')
  } finally {
    tableLoading.value = false
  }
}
//提交
async function submit() {
  // 添加更多验证
  if (!selectedRows.value.length) {
    message.warning('请选择需要报工的数据')
    return
  }
  const formData = selectedRows.value.map(row => ({
  ticketId: row.id,
  processId: queryForm.orderProcessId,
  workQuantity: row.workReportNum,
  workerId: row.workerId,
  reportTime: dayjs(row.reportTime || new Date()).format('YYYY-MM-DD HH:mm:ss')
}))
  try {
   await workRepoetApi.assistReport(formData)
   message.success('提交成功')

   onClose()
  } catch (error) {
    message.error('提交失败')
  } finally {
    tableLoading.value = false
  }
}
//获取工序
defineExpose({
    show
})
</script>

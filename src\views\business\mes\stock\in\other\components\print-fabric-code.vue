<!--
  * 打印面料卡
-->
<template>
  <a-modal :open="visibleFlag" title="打印面料卡" width="800px" :footer="null" @cancel="onClose">
    <a-form class="smart-query-form">
      <a-button @click="handleExport" type="primary">导出</a-button>
    </a-form>
    <div>
      <div id="pdf-content" style="width: 500px; margin: 0 auto">
        <table class="table">
          <tr>
            <th class="title-head" colspan="5">易裁剪绩东一店面料卡</th>
          </tr>
          <tr>
            <td class="title-col">客户</td>
            <td class="content-col">依慕思</td>
            <td rowspan="4" colspan="2">
              <div class="qrInfo">
                <a-qrcode
                  :size="150"
                  type='svg'
                  error-level='L'
                  :bordered = false
                  value="https://www.antdv.com"
                />
              </div>
            </td>
          </tr>
          <tr>
            <td class="title-col">Spu</td>
            <td class="content-col">201113433GD502</td>
          </tr>
          <tr>
            <td class="title-col">Sku</td>
            <td class="content-col">201113433GD502.1</td>
          </tr>
          <tr>
            <td class="title-col">日期</td>
            <td class="content-col">2025-04-18</td>
          </tr>
          <tr>
            <td class="title-col" >布种</td>
            <td class="content-col" >170G挺阔单面布-吸湿速干</td>
            <td class="title-col">缸号</td>
            <td class="content-col">250707112</td>
          </tr>
          <tr>
            <td class="title-col">规格</td>
            <td class="content-col">绿色调00344</td>
            <td class="title-col">卷数</td>
            <td class="content-col">15</td>
          </tr>
        </table>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { getLocationPdf } from '/src/utils/pdf-util.js';
  import vueQr from 'vue-qr/src/packages/vue-qr.vue';
  import { ref } from 'vue';
  import _ from 'lodash';

  // 字体大小
  const fontSize = ref(81);
  // 纸张尺寸
  const pageSize = ref([200, 50]);
  // 文件名
  const fileName = ref('货位码');

  const qrInfoObject = ref(); //二维码信息 key:货位码 value:二维码内容
  const qrKeys = ref(); //货位码

  function handleExport() {
    // getLocationPdf(fileName.value,"#pdf-content",pageSize.value,qrKeys.value.length)
    // onClose()
  }

  // ------------------------ 显示与隐藏 ------------------------
  // // 是否显示
  const visibleFlag = ref(false);

  function show(data) {
    visibleFlag.value = true;
  }

  function onClose() {
    visibleFlag.value = false;
  }

  defineExpose({
    show,
  });
</script>
<style scoped lang="less">
  //th,td {
  //  border: 1px solid #000;
  //  border-collapse: collapse;
  //  text-align: center;
  //  padding: 0 5px;
  //  height: 20px;
  //  font-size: inherit;
  //  word-break: break-all; /* 添加文字换行 */
  //}
  .table {
    width: 100%;
    border: 1px solid #000;
    margin: 2px 0;
  }
  .title-head{
    border: 1px solid #000;
    font-size: 20px;
    font-family: cursive;
  }
  .title-col{ //标题列
    border: 1px solid #000;
    width: 50px;
    height: 40px;
    padding: 2px;
    text-align: center;
    font-size: 17px;
    font-family: cursive;
  }
  .content-col{ // 内容列
    border: 1px solid #000;
    border-collapse: collapse;
    padding: 0 5px;
    width: 200px;
    height: 20px;
    font-size: 17px;
    font-family: cursive;
    word-break: break-all;  /* 添加文字换行 */
  }
  .qrInfo{
    width: 150px;
    margin: 0 auto;
  }
</style>

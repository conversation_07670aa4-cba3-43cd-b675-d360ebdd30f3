<template>
  <div style="padding: 10px; min-height: calc(-50px + 100vh)">
    <board-title :title="'生产仪表盘'" />

    <div id="processWrap">
      <div class="processWrap">
        <div class="process-title">
          <div style="font-size: 14px">
            <span style="font-size: 18px; font-weight: 600">工序任务</span>
          </div>
          <div style="display: flex; align-items: center">
            <a-radio-group button-style="solid" v-model:value="timeChoose">
              <a-radio-button value="today">今天</a-radio-button>
              <a-radio-button value="yesterday">昨天</a-radio-button>
              <a-radio-button value="month">本月</a-radio-button>
            </a-radio-group>
          </div>
        </div>
        <div class="processCardArea">
          <div v-if="processList.length != 0" style="display: flex; width: 100%; flex-wrap: wrap">
            <a-card
              style="
                margin: 0 16px 16px 0;
                cursor: pointer;
                border: 0;
                border-radius: 3px;
                background-color: rgb(250, 250, 250);
                overflow: hidden;
              "
              v-for="(item, index) in processList"
              :key="item.name"
              :bordered="false"
              :body-style="{padding: '0px', height: '120px', width: '248px', display: 'flex', flexWrap: 'wrap'}"
            >
              <div
                style="
                  border-right: 1px dashed rgb(239, 239, 239);
                  width: 45%;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  padding-top: 20px;
                "
              >
                <div style="font-size: 26px">{{ item.count }}</div>
                <div style="color: rgb(153, 153, 153)">工序任务数</div>
              </div>
              <div style="display: flex; width: 100%; flex: 1 1 0; flex-direction: column; justify-content: center; padding-top: 16px">
                <div style="font-size: 14px; display: flex; justify-content: center; align-items: center">
                  <div style="flex: 1 1 0; text-align: right; color: rgb(153, 153, 153); margin-right: 6px">应生产数</div>
                  <div style="flex: 1 1 0; text-align: left; color: rgb(2, 185, 128)">{{ item.produceShouldNum }}</div>
                </div>
                <div style="font-size: 14px; display: flex; justify-content: center; align-items: center">
                  <div style="flex: 1 1 0; text-align: right; color: rgb(153, 153, 153); margin-right: 6px">已生产数</div>
                  <div style="flex: 1 1 0; text-align: left; color: rgb(250, 173, 20)">{{ item.produceFinishNum }}</div>
                </div>
                <div style="font-size: 14px; display: flex; justify-content: center; align-items: center">
                  <div style="flex: 1 1 0; text-align: right; color: rgb(153, 153, 153); margin-right: 6px">超数数量</div>
                  <div style="flex: 1 1 0; text-align: left; color: rgb(0, 0, 0)">{{ item.overflowNum }}</div>
                </div>
              </div>
              <div style="width: 100%; border-top: 1px solid rgb(242, 242, 242); text-align: center">
                <div style="display: flex; width: 90%; margin-left: 6px; height: 100%; align-items: center">
                  <span style="width: 30px; color: rgb(153, 153, 153); font-size: 12px; margin-right: 5px">进度</span>
                  <a-progress :percent="item.processBar" />
                </div>
              </div>

              <div style="position: absolute; height: 24px; width: 100%">
                <div style="display: flex">
                  <div
                    :style="{ backgroundColor: colors[index % 8] }"
                    style="padding-left: 8px; height: 22px; display: flex; justify-content: center; align-items: center"
                  >
                    <span style="color: rgb(255, 255, 255); overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ item.name }}</span>
                  </div>
                  <div
                    :style="{ backgroundColor: colors[index % 8] }"
                    style="width: 16px; height: 22px; border-radius: 0px 0px 100%; display: inline-block"
                  ></div>
                </div>
              </div>
            </a-card>
          </div>
          <div v-else>
            <div class="no-data">
              <span>暂无数据</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="otherWrap">
      <div class="leftCardArea">
        <div
          style="
            background-color: rgb(255, 255, 255);
            padding: 20px;
            border-radius: 3px;
            box-shadow: rgba(0, 0, 0, 0.06) 2px 2px 6px 0px;
            margin-bottom: 10px;
          "
        >
          <div style="display: flex; align-items: center; justify-content: space-between">
            <div style="display: flex; align-items: center">
              <span style="font-size: 16px; margin-bottom: 10px">工单进度报表 </span>
            </div>
          </div>
          <div class="tableContainer">
            <a-table :scroll="{ x: 1400 }" :columns="processProgressColumns" :data-source="processProgressList" :pagination="paginationProp">
              <template #bodyCell="{ column, text, record }">
                <template v-if="column.dataIndex === 'produceStatus'">
                  <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color">
                    {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
                  </a-tag>
                </template>
                <template v-if="column.dataIndex === 'processProgress'">
                  <a-steps class="step" label-placement="vertical">
                    <a-step v-for="(item,index) in record.processList" :key="index" :disabled="true" status="process">
                      <template #icon>
                        <div>
                          <a-progress
                            style="margin-top: 0px; margin-left: -5px"
                            type="circle"
                            :percent="precentCount(item.finishNum, item.shouldNum)"
                            :size="40"
                          />
                        </div>
                      </template>
                      <template #description>
                        <div style="margin-top: -10px">
                          {{ item.name }}
                        </div>
                      </template>
                    </a-step>
                  </a-steps>
                </template>
              </template>
            </a-table>
          </div>
          <br />
        </div>
      </div>
      <div class="performanceCardArea">
        <div style="background-color: white; border: 0px; border-radius: 3px; box-shadow: rgba(0, 0, 0, 0.06) 2px 2px 6px 0px; margin-bottom: 10px">
          <div style="display: flex; align-items: center"><span style="font-size: 16px; padding: 10px 0px 0px 15px">绩效指标</span></div>
          <div class="performanceCardArea-indicators">
            <div class="cardContainer-indicators">
              <div class="value-indicators">{{ planCount }}</div>
              <div class="label-indicators">计划数</div>
            </div>
            <div class="cardContainer-indicators">
              <div class="value-indicators" style="color: rgb(2, 185, 128)">{{ finishCount }}</div>
              <div class="label-indicators">完成数量</div>
            </div>
            <div class="cardContainer-indicators">
              <div class="value-indicators">{{ productCount }}</div>
              <div class="label-indicators">工单数量</div>
            </div>
            <div class="cardContainer-indicators">
              <div class="value-indicators">
                <div style="font-size: 16px">
                  <span class="value-indicators">{{ averageProduceCycle }}</span
                  >天
                </div>
              </div>
              <div class="label-indicators">指令单</div>
              <div class="label-indicators">平均生产周期</div>
            </div>
            <div class="cardContainer-indicators">
              <div class="value-indicators" style="color: rgb(255, 89, 80)">{{ unFinishedCount }}</div>
              <div class="label-indicators">超时</div>
              <div class="label-indicators">未完成指令单</div>
            </div>
          </div>
        </div>

        <div
          style="
            margin-bottom: 15px;
            background-color: white;
            border: 0px solid;
            border-radius: 3px;
            box-shadow: rgba(0, 0, 0, 0.06) 2px 2px 6px 0px;
            overflow: auto;
            overflow-x: hidden;
          "
        >
          <div style="display: flex; align-items: center"><span style="font-size: 16px; padding: 10px 0px 0px 15px">生产安排情况播报</span></div>
          <div style="height: 400px; background-color: rgb(255, 255, 255); overflow: scroll; padding: 15px">
            <a-timeline>
              <a-timeline-item v-for="item in produceArrangeList" :key="item.id">
                <div style="color: rgb(0, 0, 0); display: flex; justify-content: space-between">
                  <div style="margin-bottom: 8px">{{ item.createTime }}</div>
                </div>
                <div style="color: rgb(155, 155, 155); background-color: rgb(250, 250, 250); border-radius: 3px; padding: 10px 3px 10px 10px">
                  <span>
                    <span class="broadCastStr" style="color: black">{{ item.finishName }} {{ item.arrangeName }}</span>
                    <br />
                    <span class="broadCastStr">生产指令单号： {{ item.produceInstructOrderNumber }}</span>

                    <span class="broadCastStr">物料编号： {{ item.itemNumber }}</span>

                    <span class="broadCastStr">物料名： {{ item.itemName }}</span>
                  </span>
                </div>
              </a-timeline-item>
            </a-timeline>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { ref, onMounted, watch, computed, reactive } from 'vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { produceInstructOrderArrangeApi } from '/@/api/business/mes/produce/produce-instruct-order-arrange-api.js';
  import { produceInstructOrderProcessApi } from '/@/api/business/mes/produce/produce-instruct-order-process-api.js';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api.js';
  import { PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
  import BoardTitle from '/@/components/business/mes/board/board-title.vue';
  import dayjs from 'dayjs';

  const timeChoose = ref('month');
  // 开始时间
  const beginTime = ref('');
  // 结束时间
  const endTime = ref('');
  // 计划数
  const planCount = ref(0);
  // 完成数量
  const finishCount = ref(0);

  // 工单数量
  const productCount = ref(0);
  // 平均生产周期
  const averageProduceCycle = ref(0);
  // 超时未完成
  const unFinishedCount = ref(0);
  //工序列表
  const processList = ref([]);
  // 工单进度列表
  const processProgressList = ref([]);
  // 生产安排列表
  const produceArrangeList = ref([]);
  const queryFormState = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 分页功能
  const page = ref(1);
  const pageSize = ref(5);
  const totals = ref(0);
  const paginationProp = ref({
    showSizeChanger: true, // 是否展示 pageSize 切换器，当 total 大于 50 时默认为 true
    showQuickJumper: true, // 是否可以快速跳转至某页
    pageSize: pageSize, // 每页条数
    current: page, // 当前页数
    total: totals, // 数据总数
    showTotal: (totals) => `总共 ${totals} 条数据`, // 用于显示数据总量和当前数据顺序
    onChange: pageChange, // 页码或 pageSize 改变的回调，参数是改变后的页码及每页条数
    onShowSizeChange: pageSizeChange, // pageSize 变化的回调
    pageSizeOptions: PAGE_SIZE_OPTIONS, // 指定每页可以显示多少条
  });

  function pageChange(pageValue, pageSizeValue) {
    page.value = pageValue;
    pageSize.value = pageSizeValue;
    queryProcessProgressData();
  }

  function pageSizeChange(current, size) {
    page.value = current;
    pageSize.value = size;
    queryProcessProgressData();
  }
  // 工序进度条items
  const progressBarItems = ref([]);
  // 计算工序进度条百分比
  // 计算工序进度条百分比
  const precentCount = (finishNum, shouldNum) => {
    return Math.floor((finishNum / shouldNum) * 100);
  };
  const queryForm = reactive({ ...queryFormState });
  // 总数
  const total = ref(0);
  // 表格加载loading
  const tableLoading = ref(false);
  const processProgressColumns = ref([
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      height: 10,
      width: 50,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '指令单编号',
      dataIndex: 'instructNumber',
      key: 'instructNumber',
      width: 120,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      key: 'itemNumber',
      width: 140,
      align: 'center',
    },

    {
      title: '物料名称',
      dataIndex: 'itemName',
      width: 150,
      key: 'itemName',
      align: 'center',
    },
    {
      title: '生产数量',
      dataIndex: 'produceNum',
      key: 'produceNum',
      width: 60,
      align: 'center',
    },
    {
      title: '生产状态',
      dataIndex: 'produceStatus',
      key: 'produceStatus',
      width: 60,
      align: 'center',
    },
    {
      title: '工序进度',
      dataIndex: 'processProgress',
      key: 'processProgress',
      // width: 130,
      align: 'center',
    },
  ])

  const colors = [
    'rgb(40, 194, 228)',
    'rgb(116, 130, 229)',
    'rgb(250, 173, 20)',
    'rgb(61, 210, 126)',
    'rgb(0, 185, 190)',
    'rgb(24, 144, 255)',
    'rgb(255, 89, 80)',
    'rgb(228, 191, 83)',
  ];

  // 工序进度报表
  async function queryProcessProgressData() {
    let param = {
      pageNum: page.value,
      pageSize: pageSize.value,
    };
    try {
      let queryData = await produceInstructOrderProcessApi.queryPage(param);
      processProgressList.value = queryData.data.list;
      totals.value = queryData.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 工序任务
  const produceMark = async () => {
    try {
      let queryData = await produceInstructOrderProcessApi.processCountStats(beginTime.value, endTime.value);
      let arr = [];
      queryData.data.forEach((item) => {
        let obj = {
          name: item.name,
          count: item.processCount,
          produceShouldNum: item.shouldNum,
          produceFinishNum: item.finishNum,
          overflowNum: item.overflowNum,
          processBar: Math.floor((item.finishNum / item.shouldNum) * 100),
        };
        arr.push(obj);
      });
      processList.value = arr;
    } catch (e) {
      smartSentry.captureError(e);
    }
  };
  // 指令单生产安排播报
  const produceOrderAnnouncement = async () => {
    let param = {
      pageNum: 1,
      pageSize: 10,
      finishFlag: true,
      sortItemList: [
        {
          isAsc: false,
          column: 'real_end_time',
        },
      ],
    };
    try {
      let queryData = await produceInstructOrderArrangeApi.queryPage(param);
      let arr = [];
      queryData.data.list.forEach((item) => {
        let obj = {
          id: item.id,
          createTime: item.createTime,
          finishTime: item.finishTime,
          finishName: item.finish,
          arrangeName: item.nodeName,
          produceInstructOrderNumber: item.instructNumber,
          itemName: item.itemName,
          itemNumber: item.itemNumber,
        };
        arr.push(obj);
      });
      produceArrangeList.value = arr;
    } catch (e) {
      smartSentry.captureError(e);
    }
  };

  // 计划数
  async function queryPlanCount() {
    let param = {
      startTime: beginTime.value,
      endTime: endTime.value,
    };
    try {
      let queryData = await produceInstructOrderApi.queryPlanCount(param);
      planCount.value = queryData.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 完成数量
  async function queryfinishCount() {
    let param = {
      startTime: beginTime.value,
      endTime: endTime.value,
    };
    try {
      let queryData = await produceInstructOrderApi.queryDoneCount(param);
      finishCount.value = queryData.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 工单数量
  async function queryProductCount() {
    let param = {
      startTime: beginTime.value,
      endTime: endTime.value,
    };
    try {
      let queryData = await produceInstructOrderApi.queryOrderCount(param);
      productCount.value = queryData.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 平均生产周期
  async function queryAverageCyrcle() {
    let param = {
      startTime: beginTime.value,
      endTime: endTime.value,
    };
    try {
      let queryData = await produceInstructOrderApi.queryAverageProductionCycle(param);
      averageProduceCycle.value = queryData.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 超时未完成
  async function queryunfinishedCount() {
    let param = {
      startTime: beginTime.value,
      endTime: endTime.value,
    };
    try {
      let queryData = await produceInstructOrderApi.queryOverTimeUnfinishedCount(param);
      unFinishedCount.value = queryData.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  onMounted(() => {
    beginTime.value = dayjs().startOf('month').format('YYYY-MM-DD');
    endTime.value = dayjs().endOf('month').format('YYYY-MM-DD');
    produceMark();
    produceOrderAnnouncement();
    queryProcessProgressData();

    // 计划
    queryPlanCount();
    // 超时未完成数量
    queryunfinishedCount();
    // 完成数量
    queryfinishCount();
    // 平均周期
    queryAverageCyrcle();
    // 工单数量
    queryProductCount();
  });
  //
  watch(
    () => timeChoose.value,
    (newValue) => {
      if (newValue === 'today') {
        //   获取当前时间
        let time = dayjs().format('YYYY-MM-DD');
        beginTime.value = endTime.value = time;
      } else if (newValue === 'yesterday') {
        let time = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
        beginTime.value = endTime.value = time;
      } else {
        beginTime.value = dayjs().startOf('month').format('YYYY-MM-DD');
        endTime.value = dayjs().endOf('month').format('YYYY-MM-DD');
      }
      produceMark();
      // 计划
      queryPlanCount();
      // 超时未完成数量
      queryunfinishedCount();
      // 完成数量
      queryfinishCount();
      // 平均周期
      queryAverageCyrcle();
      // 工单数量
      queryProductCount();
    }
  );
</script>
<style lang="less" scoped>
  .processWrap {
    background-color: #fff;
    border: 0;
    border-radius: 3px;
    max-height: calc(100vh - 60vh);
    min-height: 300px;
    margin-bottom: 10px;
    padding-bottom: 20px;
    position: relative;
    //overflow-y: scroll;
    overflow: auto;
    overflow-x: hidden;

    //border: 1px solid #000;
    box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.06);

    .process-title {
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      display: flex;
    }

    .processCardArea {
      //display: flex;
      //flex-wrap: wrap;
      //justify-content: space-between; /* 确保卡片之间有均匀的间距 */
      //align-items: stretch; /* 确保卡片高度一致 */
      margin: 0 16px;
      //min-width: 1680px;
      display: flex;
      align-content: flex-start;
      flex-flow: row wrap;
      //border: 1px solid #000;
      .no-data {
        //width: 150vh;
        width: 85vw;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        //border: 1px solid #000;

        margin: auto;
        color: rgba(0, 0, 0, 0.33);
        font-family: Inter;
        font-size: 36px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }

    .processCardArea .ant-card {
      //flex: 0 0 calc(25% - 16px); /* 每个卡片占据25%的宽度，减去间距 */
      //margin: 8px; /* 设置卡片之间的间距 */
      //width: calc(25% - 16px); /* 确保卡片宽度减去间距后正确 */
      //height: 120px;
      //display: flex;
      //flex-wrap: wrap;
      height: 120px;
      width: 248px;
      //flex: 0 0 20%;
    }

    /* 每行的最后一个卡片不需要右边距 */

    .processCardArea .ant-card:nth-child(4n) {
      margin-right: 0;
    }
  }

  .otherWrap {
    margin-bottom: 20px;
    display: flex;

    .leftCardArea {
      flex: 1;
      width: calc(100% - 300px);
      margin-right: 10px;

      .tableContainer {
        border: 1px solid #e8e8e8;
        margin-top: 10px;
      }
    }

    .performanceCardArea {
      width: 300px;
      padding-right: 10px;

      .performanceCardArea-indicators {
        background-color: #fff;
        flex-wrap: wrap;
        justify-content: space-between;
        height: calc(100% - 214px);
        padding: 10px 10px 0;
        display: flex;

        .cardContainer-indicators {
          background-color: #fafafa;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 48%;
          margin-bottom: 10px;
          padding: 5px 0;
          display: flex;

          .value-indicators {
            font-size: 24px;
          }

          .label-indicators {
            opacity: 0.3;
            font-size: 12px;
          }
        }
      }
    }
  }

  .broadCastStr {
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    display: inline-block;
    overflow: hidden;
  }
  //进度条
  ///deep/ .ant-steps .ant-steps-dot .ant-teps-item-process .ant-steps-item-icon,
  ///deep/ .ant-steps .ant-steps-dot .ant-steps-small .ant-steps-item-process .ant-steps-item-icon {
  //  top: -15px;
  //  width: 44px;
  //  height: 20px;
  //  margin-inline-start: 49px;
  //  border: 1px solid #000;
  //}
  .step {
    :deep(.ant-steps .ant-steps-item) {
      //:deep(.ant-steps-item){
      //  color: #0D366F;
      //}
      border: 1px solid #000;

      :deep(.ant-steps .ant-steps-dot .ant-steps-small .ant-steps-item-process .ant-steps-item-icon) {
        border: 1px solid #000;
      }

      :deep(.ant-steps-item-icon .ant-steps-icon) {
        top: 10px;
        border: 1px solid #000;
      }
    }
  }
  :deep(.ant-table-tbody > tr > td) {
    padding: 3px !important;
  }
</style>

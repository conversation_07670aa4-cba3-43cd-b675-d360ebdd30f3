/**
 * 款式品类表 api 封装
 *
 * @Author:    hzx
 * @Date:      2024-07-05 15:45:34
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const styleApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/style/queryPage', param);
  },
  // 添加树表查询
  queryTree:(param) => {
    return postRequest('/style/queryTree', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/style/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/style/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/style/delete/${id}`);
  },

};

<!--
  * 货位
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:14:20
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" >
          <!-- <a-row> -->
                    <!-- <a-form-item label="主键"  name="id">
                      <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />
                    </a-form-item> -->
                    <a-form-item label="货位编号"  name="number">
                      <a-input style="width: 100%" v-model:value="form.number" placeholder="货位编号" />
                    </a-form-item>
                    <a-form-item label="货架"  name="rackId">
                      <RackSelect v-model:value="form.rackId"/>
                    </a-form-item>
                    <a-form-item label="备注"  name="remark">
                      <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
                    </a-form-item>
                    
                    
                    <!-- <a-form-item label="拣货优先级 保留"  name="pickPriority" >
                      <a-input-number style="width: 100%" v-model:value="form.pickPriority" placeholder="拣货优先级;拣货优先级 保留" />
                    </a-form-item> -->
          <!-- </a-row> -->

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick, onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { stkLocationApi } from '../../../../../../api/business/mes/stock/stk-location-api';
  // import {stkRackApi}from'../../../../../../api/business/mes/stock/stk-rack-api'
  // 选择货架列表
  import RackSelect from '/@/components/business/mes/stock/werehouse/rack-select/index.vue';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
              remark: undefined, //备注
              rackId: undefined, //货架id
              number: undefined, //货位编号
              pickPriority: undefined, //拣货优先级;拣货优先级 保留
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  rackId: [{ required: true, message: '货架 必填' }],
                  number: [{ required: true, message: '货位编号 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await stkLocationApi.update(form);
      } else {
        await stkLocationApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  defineExpose({
    show,
  });
</script>

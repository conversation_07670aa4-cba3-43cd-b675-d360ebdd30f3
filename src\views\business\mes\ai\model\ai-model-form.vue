<template>
  <a-modal :title="form.id ? '编辑' : '添加'" v-model:open="showForm" :width="900" :destroyOnClose="true" @cancel="handleCancel">
    <a-form :model="form" :rules="rules" ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="模型名称" name="modelName">
        <a-input v-model:value="form.modelName" placeholder="请输入模型名称" />
      </a-form-item>

      <a-form-item label="模型昵称" name="modelNickname">
        <a-input v-model:value="form.modelNickname" placeholder="请输入模型昵称" />
      </a-form-item>

      <a-form-item label="baseUrl" name="baseUrl">
        <a-input v-model:value="form.baseUrl" placeholder="请输入baseUrl" />
      </a-form-item>

      <a-form-item label="apiKey" name="apiKey">
        <a-input v-model:value="form.apiKey" placeholder="请输入apiKey" />
      </a-form-item>

      <a-form-item label="模型类型" name="modelType">
        <a-select v-model:value="form.modelType" placeholder="请选择模型类型">
          <a-select-option v-for="item in modelTypeOptions" :key="item.value" :value="item.value">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="优先级" name="priority">
        <a-input v-model:value="form.priority" placeholder="请输入优先级" />
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark"></a-textarea>
      </a-form-item>

      <a-form-item label="是否启用" name="enableFlag">
        <a-switch v-model:checked="form.enableFlag" />
      </a-form-item>

      <a-form-item label="使用工具" name="useToolFlag">
        <a-switch v-model:checked="form.useToolFlag" />
      </a-form-item>

      <a-form-item label="使用知识库" name="knowledgeUseFlag">
        <a-switch v-model:checked="form.knowledgeUseFlag" />
      </a-form-item>

      <a-form-item label="视觉能力" name="visionUseFlag">
        <a-switch v-model:checked="form.visionUseFlag" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-button @click="onClose">取消</a-button>
      <a-button type="primary" @click="onSubmit">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, defineExpose } from 'vue';
  import { message } from 'ant-design-vue';
  import { AI_MODEL_TYPE_ENUM } from '/@/constants/business/mes/ai/ai-model-const';
  import { aiModelApi } from '/@/api/business/mes/ai/ai-model-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emits = defineEmits(['reloadList']);

  const formRef = ref(null);

  // 表单默认值
  const formDefault = {
    id: undefined,
    modelName: undefined, //模型名称
    modelNickname: undefined, //模型昵称
    modelType: undefined, //模型类型
    remark: undefined, //备注
    priority: undefined, //优先级
    baseUrl: undefined, //baseUrl
    apiKey: undefined, //apiKey
    enableFlag: true, //是否启用
    useToolFlag: false, //是否使用工具
    knowledgeUseFlag: false, //是否使用知识库
    visionUseFlag: false, //是否使用视觉能力
  };

  // 表单数据
  const form = reactive({ ...formDefault });

  // 表单校验规则
  const rules = {
    modelName: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
    modelNickname: [{ required: true, message: '请输入模型昵称', trigger: 'blur' }],
    modelType: [{ required: true, message: '请选择模型类型', trigger: 'change' }],
    baseUrl: [{ required: true, message: '请输入baseUrl', trigger: 'blur' }],
    apiKey: [{ required: true, message: '请输入apiKey', trigger: 'blur' }],
  };

  // 模型类型选项数据
  const modelTypeOptions = Object.values(AI_MODEL_TYPE_ENUM);

  // 打开模态框
  const showForm = ref(false);
  function show(data) {
    showForm.value = true;
    resetForm(); // 重置表单
    if (data && data.id) {
      Object.assign(form, data);
    }
  }

  // 重置表单
  function resetForm() {
    formRef.value && formRef.value.resetFields();
    Object.assign(form, formDefault);
  }

  //取消
  function onClose() {
    showForm.value = false;
    resetForm();
  }

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await aiModelApi.update(form);
      } else {
        await aiModelApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

<style scoped>
  .ant-form-item {
    margin-bottom: 16px;
  }
</style>

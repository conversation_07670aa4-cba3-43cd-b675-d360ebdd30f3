<!--
  * 裁片驿站操作日志
  *
  * @Author:    cjm
  * @Date:      2024-10-07 19:47:06
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
          <a-row>
                    <a-form-item label="主键"  name="id">
                      <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />
                    </a-form-item>
                    <a-form-item label="菲票id"  name="feTicketId">
                      <a-input-number style="width: 100%" v-model:value="form.feTicketId" placeholder="菲票id" />
                    </a-form-item>
                    <a-form-item label="操作类型"  name="optType">
                      <a-input style="width: 100%" v-model:value="form.optType" placeholder="操作类型" />
                    </a-form-item>
                    <a-form-item label="操作描述"  name="optDesc">
                      <a-input style="width: 100%" v-model:value="form.optDesc" placeholder="操作描述" />
                    </a-form-item>
                    <a-form-item label="操作人"  name="operator">
                      <a-input style="width: 100%" v-model:value="form.operator" placeholder="操作人" />
                    </a-form-item>
                    <a-form-item label="操作人id"  name="operatorId">
                      <a-input-number style="width: 100%" v-model:value="form.operatorId" placeholder="操作人id" />
                    </a-form-item>
                    <a-form-item label="操作时间"  name="optTime">
                      <a-input style="width: 100%" v-model:value="form.optTime" placeholder="操作时间" />
                    </a-form-item>
          </a-row>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationOptLogApi } from '/@/api/business/mes/part-station/opt-log/part-station-opt-log-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
              id: undefined, //主键
              feTicketId: undefined, //菲票id
              optType: undefined, //操作类型
              optDesc: undefined, //操作描述
              operator: undefined, //操作人
              operatorId: undefined, //操作人id
              optTime: undefined, //操作时间
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  id: [{ required: true, message: '主键 必填' }],
                  feTicketId: [{ required: true, message: '菲票id 必填' }],
                  optType: [{ required: true, message: '操作类型 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await partStationOptLogApi.update(form);
      } else {
        await partStationOptLogApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

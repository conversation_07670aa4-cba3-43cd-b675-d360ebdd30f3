/**
 * 工序库 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-13 15:19:57
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const processLibraryApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/processLibrary/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/processLibrary/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/processLibrary/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/processLibrary/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/processLibrary/batchDelete', idList);
  },

};

/**
 * 生产指令单 api 封装
 *
 * @Author:    cyz
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const produceInstructOrderApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/produceInstructOrder/queryPage', param);
  },

  queryPageWithProcess: (param) => {
    return postRequest('/produceInstructOrderProcess/produceInstructOrder/queryPage', param);
  },

  /**
   *  获取基础表单 <AUTHOR>
   */
  queryBaseInfo: (id) => {
    return getRequest(`/produceInstructOrder/getBaseInfo/${id}`);
  },
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/produceInstructOrder/add', param);
  },

  /**
   * 修改、更新  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/produceInstructOrder/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/produceInstructOrder/delete/${id}`);
  },
  /**
   * 获取详情  <AUTHOR>
   */
  getDetails: (id) => {
    return getRequest(`/produceInstructOrder/get/${id}`);
  },
  /**
   * 批量下达指令单  <AUTHOR>
   */
  batchOrder: (param) => {
    return postRequest('/produceInstructOrder/batchIssued', param);
  },
  /**
   * 单个下达指令单 <AUTHOR>
   */
  singleOrder: (id) => {
    return getRequest(`/produceInstructOrder/issued/${id}`);
  },
  /*
   *单个反下达指令单
   */
  cancelOrder: (id) => {
    return getRequest(`/produceInstructOrder/reIssued/${id}`);
  },

  updateOrder: (param) => {
    return postRequest('/produceInstructOrder/changePriority', param);
  },
  /**
   * 自动生成指令单编号
   */

  autoGetOrderCode: (param) => {
    return getRequest('/produceInstructOrder/getProduceInstructOrderNo', param);
  },
  /**
   * 查询计划数
   */
  queryPlanCount: (param) => {
    return postRequest('/produceInstructOrderStats/planNum', param);
  },

  /**
   * 查询完成数
   */
  queryDoneCount: (param) => {
    return postRequest('/produceInstructOrderStats/finishedNum', param);
  },
  /**
   * 查询工单数
   */
  queryOrderCount: (param) => {
    return postRequest('/produceInstructOrderStats/instructNum', param);
  },

  /**
   * 查询平均生产周期
   */
  queryAverageProductionCycle: (param) => {
    return postRequest('/produceInstructOrderStats/averageDays', param);
  },
  /**
   * 查询超时未完成数量
   */
  queryOverTimeUnfinishedCount: (param) => {
    return postRequest('/produceInstructOrderStats/timeout', param);
  },
  /**
   * 查询今日新单
   */
  querynewOrder: (param) => {
    return postRequest('/produceInstructOrderStats/instructNum', param);
  },
  /**
   * 查询进行中
   */
  queryDoingOrder: (param) => {
    return postRequest('/produceInstructOrderStats/onGoingNum', param);
  },
  /**
   * 查询今日下达
   */
  queryIssueOrder: (param) => {
    return postRequest('/produceInstructOrderStats/issuedNum', param);
  },
  /**
   * 查询指令单变化趋势
   */
  queryOrderChange: (param) => {
    return postRequest('/produceInstructOrderStats/perDayOrdersNum', param);
  },
  /**
   * 查询指令单饼状图
   */
  queryOrderPieChart: (param) => {
    return postRequest('/produceInstructOrderStats/priorityNum', param);
  },

  /**
   * 指令单强制完工
   */

  forceComplete: (ids) => {
    return postRequest('/produceInstructOrder/forceComplete', ids);
  },

  /**
   * 全查询  <AUTHOR>
   */
  queryInsOrItem: (param) => {
    return postRequest(`/produceInstructOrder/getInsOrItemNumber`, param);
  },
};

/**
 * 自定义工资项 枚举
 *
 * @Author:    linwj
 * @Date:      2024-11-21 10:22:24
 * @Copyright  zscbdic
 */

//加减项类型
export const WAGE_TYPE_ENUM = {
    ADD: {
        value: '0',
        label: '加项',
    },
    SUB: {
        value: '1',
        label: '减项',
    },
    getEnum(value) {
        if (value==='0'){
            return WAGE_TYPE_ENUM.ADD;
        }else if (value==='1'){
            return WAGE_TYPE_ENUM.SUB;
        }
    },
};

export default {
    WAGE_TYPE_ENUM,
};
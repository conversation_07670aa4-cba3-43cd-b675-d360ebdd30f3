/*
 * 配置
 *
 * @Author:    卓大
 * @Date:      2022-09-03 21:51:54
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const configApi = {
  // 分页查询 <AUTHOR>
  queryList: (param) => {
    return postRequest('/support/config/query', param);
  },
  // 添加配置参数 <AUTHOR>
  addConfig: (param) => {
    return postRequest('/support/config/add', param);
  },
  // 修改配置参数 <AUTHOR>
  updateConfig: (param) => {
    return postRequest('/support/config/update', param);
  },
  // 查询配置详情 <AUTHOR>
  queryByKey: (param) => {
    return getRequest(`/support/config/queryByKey?configKey=${param}`);
  },
};

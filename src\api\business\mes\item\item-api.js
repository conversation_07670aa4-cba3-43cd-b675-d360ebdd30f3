/**
 * 主物料表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-02 08:33:07
 * @Copyright  zscbdic
 */
import { postRequest, getRequest,getDownload } from '/@/lib/axios';

export const itemApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/item/queryPage', param);
  },

  /**
   * 全查询  <AUTHOR>
   */
  queryList : (param) => {
    return postRequest('/item/queryList',param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/item/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/item/update', param);
  },
  // 导入文件
  importfile:(file)=>{
    return postRequest('/item/import',file);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/item/delete/${id}`);
  },
  //  查询单个数据
  queryById(id) {
    return getRequest(`/item/query/${id}`);
  },
  //查询详情
  getById:(id) =>{
    return getRequest(`/item/byId/${id}`)
  },
  // 自动获取编号
  getItemNo(){
    return getRequest(`/item/getItemNo`);
  },
  // 下载基础物料导入模板
  downloadTemplate(){
    return getDownload(`/item/downloadTemplate`);
  }
  
};

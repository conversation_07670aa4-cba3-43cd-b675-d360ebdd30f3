<!--
  * 尺寸模板表
  *
  * @Date:      2024-07-05 15:45:35
  * @Copyright  zscbdic
-->
<!--* @Author:    lwt-->
<template>
  <a-row :gutter="16" >
    <!--  尺码模板列表  -->
    <a-col :span="6">
      <a-card title="尺码模板列表" :bordered="false" hoverable body-style="height:45rem;">
        <!--   右上角添加按钮     -->
        <template #extra>
          <a-button type="primary" @click="handleAddTemplate">+添加模板</a-button>
        </template>
        <!--    主内容列表    -->
        <a-table :columns="sidebarColumns" :dataSource="mainData" rowKey="id" :customRow="rowClick" :pagination="false">
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'templateName'">
              <span v-if="!record.isEditing">{{ record.templateName }}</span>
            </template>
            <a-input v-if="record.isEditing  && column.dataIndex !== 'action'" v-model:value="record.editingTemplateName"
                     @blur=" templateSaveEdit(record)"/>
            <CheckOutlined v-if="record.isEditing  && column.dataIndex !== 'action'" @click=" templateSaveEdit(record)"
                           style="margin-left: 10px"/>
            <CloseOutlined v-if="record.isEditing  && column.dataIndex !== 'action'" @click=" cancelEdit(record)"
                           style="margin-left: 30px"/>
            <template v-if="column.dataIndex === 'action'">
              <div class="smart-table-operate" style="width: 60px;">
                <FormOutlined @click="handleEdit(record)"/>
                <DeleteOutlined @click="onTemplateDelete(record.id)" style="margin-left: 5px"/>
              </div>
            </template>
          </template>
        </a-table>

        <template #actions>
          <a-pagination :current="currentPageTemplate" :pageSize="pageSize" :total="totalTemplateData"
                        @change="handleTemplatePageChange" style="margin-top: 10px;"/>
        </template>
      </a-card>
    </a-col>
    <!--  尺码大小列表  -->
    <a-col :span="18">
<!--      <a-card title="尺码大小列表" :bordered="false" body-style="height:70vh;">-->
      <a-card title="尺码大小列表" :bordered="false" body-style="height:45rem;">

      <!--     头部   -->
        <template #extra>
          <a-button type="primary" @click="handleAddSize()">+添加码数</a-button>
        </template>
        <!--  内容区      -->
        <a-table :columns="sizeColumns" :dataSource="currentPageData" rowKey="id" :pagination="false">
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'size'">
              <!-- 使用 v-if 控制显示文本或输入框 -->
              <span v-if="!record.isEditing">{{ record.sizeMessage }}</span>
            </template>
            <a-input v-if="record.isEditing  && column.dataIndex !== 'action'" v-model:value="record.editingSizeMessage"
                     @blur="sizeSaveEdit(record)" style="width: 50%"/>
            <a-button v-if="record.isEditing  && column.dataIndex !== 'action'" type="primary"
                      @click="sizeSaveEdit(record)" style="margin-left: 10px">保存
            </a-button>
            <a-button v-if="record.isEditing  && column.dataIndex !== 'action'" type="primary"
                      @click=" cancelEdit(record)" style="margin-left: 10px">取消
            </a-button>
            <template v-if="column.dataIndex === 'action'">
              <div class="smart-table-operate">
                <a-button type="link" @click="handleEdit(record)">编辑</a-button>
                <a-button type="link" danger @click="onDelete(record.id)">删除</a-button>
              </div>
            </template>
          </template>
        </a-table>
        <!-- 尾部分页       -->
        <template #actions>
          <a-pagination :current="currentPageTemplate" :pageSize="pageSize" :total="totalTemplateData"
                        @change="handleTemplatePageChange" style="margin-top: 10px;"/>
        </template>

      </a-card>
    </a-col>

  </a-row>
      <!-- 添加模板对话框 -->
  <a-modal bodyStyle="justify-content: center;align-items:center;"  style="top: 200px;" v-model:open="templateOpen" title="添加模板" @ok="handleTempaleOk" >
  <div style="display:flex;justify-content: center;align-items:center;">
    <div style="width: 70%">
      <a-row :gutter="[16,24]">
        <a-col :span="24">
          <a-input type="text" v-model:value="newTemplateName" placeholder="输入模板名称" style="width:100%"/>
        </a-col>
      </a-row>
      <a-row :gutter="[16,24]">
        <a-col :span="24">
          <a-input type="text" v-model:value="sizeCode" placeholder="输入尺寸编码" style="width:100%;margin-top:10px"/>
        </a-col>
      </a-row>
    </div>
  </div>

  </a-modal>
  <!-- 添加码数对话框 -->
  <a-modal  style="top: 200px" v-model:open="sizeOpen" title="添加码数" @ok="handleSizeOk">
    <a-input type="text" v-model:value="newSize" placeholder="输入新的尺码"/>
  </a-modal>

</template>
<script setup>
import {ref, onMounted, reactive} from 'vue';
import {sizeTemplateApi} from '/@/api/business/mes/base/size-template-api.js';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {smartSentry} from '/@/lib/smart-sentry';
import {CheckOutlined, CloseOutlined, DeleteOutlined, FormOutlined} from '@ant-design/icons-vue';
import {message, Modal} from "ant-design-vue";
import {sizeApi} from "/@/api/business/mes/base/size-api.js";


const mainData = ref([])
const currentPageTemplate = ref(1);
const pageSize = ref(10);
const totalTemplateData = ref(0);
const currentPageData = ref([]); // 当前页面应显示的数据
const totalSizeData = ref(0);
const tableCurrentPage = ref(1);// 增加一个状态变量来存储表格的分页信息
const selectedTemplateId = ref(null);
const showAddTemplateInput = ref(false); // 控制输入框显示的状态变量
const newTemplateName = ref(''); // 存储新模板名称的输入值
const templateOpen=ref(false) // 添加模板对话框变量
const sizeOpen=ref(false) // 添加码数对话框变量
const sidebarColumns = ref([
  {
    title: '尺码模板',
    dataIndex: 'templateName',
    key: 'templateName',
    scopedSlots: {customRender: 'templateName'},

  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    scopedSlots: {customRender: 'action'},
    align: 'center'
  },
]);
const sizeColumns = ref([
  {
    title: '尺码大小',
    dataIndex: 'sizeMessage',
    key: 'sizeMessage',
    scopedSlots: {customRender: 'size'},
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    scopedSlots: {customRender: 'action'},
    align: 'center'
  },
]);
const queryFormState = {
  pageNum: 1,
  pageSize: 10,
};
// 查询表单form
const queryForm = reactive({...queryFormState});
const showInputBox = ref(false);// 添加状态变量控制输入框显示和用户输入
const newSize = ref('');
const sizeCode = ref(null); // 存储从rowClick中获取的sizeCode

// 左侧列表统一的数据获取函数
async function fetchTemplateData(page, pageSize) {
  try {
    const params = {pageNum: page, pageSize: pageSize};
    const response = await sizeTemplateApi.queryPage(params);
    mainData.value = response.data.list;
    totalTemplateData.value = response.data.total; // 更新总数据量
  } catch (error) {
    console.error('API请求失败:', error);
  }
}

// 右侧表格数据获取函数
async function fetchSizeData(page, pageSize) {
  try {
    const params = {pageNum: page, pageSize: pageSize};
    const response = await sizeApi.queryPage(params);
    currentPageData.value = response.data.list;
    totalSizeData.value = response.data.total;
  } catch (error) {
    console.error('API请求失败:', error);
    message.error('更新当前页数据失败，请稍后重试');
  }
}

// 左侧列表分页处理函数
function handleTemplatePageChange(page) {
  console.log("fasfaf:",page)
  currentPageTemplate.value = page;
  update()
}

function update() {
  fetchTemplateData(currentPageTemplate.value, pageSize.value).then(() => {
    if (mainData.value && mainData.value.length > 0) {
      const firstTemplate = mainData.value[0];// 获取左侧表格第一个模板的数据
      selectedTemplateId.value = firstTemplate.id; // 更新 selectedTemplateId
      // 根据新模板ID获取尺码数据
      updateSizeData(firstTemplate.id);
    }
  })
}

// 右侧表格分页处理函数
function handleSizePageChange(page) {
  tableCurrentPage.value = page;
  fetchSizeData(tableCurrentPage.value, pageSize.value);
}

// 列表组件挂载后加载第一页数据
onMounted(async () => {
  // await fetchData(currentPage.value, pageSize.value);
  await fetchTemplateData(currentPageTemplate.value, pageSize.value);
  await fetchSizeData(tableCurrentPage.value, pageSize.value);
  if (mainData.value && mainData.value.length > 0) {
    const firstTemplateId = mainData.value[0].id; // 假设 mainData 已经有了数据，我们选择第一个
    selectedTemplateId.value = firstTemplateId;// 更新 selectedTemplateId 并获取其尺码数据
    await updateSizeData(firstTemplateId);
  }
});

function rowClick(record) {
  return {
    onClick: async () => { // 使用 async 来处理异步操作
      if (record.id) {
        try {
          // 假设 fetchSizesByTemplateId 是异步的
          sizeCode.value = record.sizeCode; // 存储sizeCode
          selectedTemplateId.value = record.id;
          await updateSizeData(record.id);
        } catch (error) {
          // 处理错误
          console.error('选择尺码模板时发生错误:', error);
        }
      }
    }
  };
}

// 定义一个更新尺码表数据的函数
async function updateSizeData(templateId) {
  try {
    const sizeMessages = await fetchSizesByTemplateId(templateId);
    currentPageData.value = sizeMessages;
  } catch (error) {
    // 错误处理，例如打印错误信息和/或向用户显示错误消息
    console.error('更新尺码数据失败:', error);
    message.error('更新尺码数据失败，请稍后重试');
  }
}

// 新的 API 调用函数，根据尺码模板 ID 获取尺码数据
async function fetchSizesByTemplateId(templateId) {
  try {
    const form = {
      "pageNum": 1,
      "pageSize": 47,
    };
    const response = await sizeApi.queryPage(form);
    const sizesData = response.data.list;
    // 过滤出具有相同 templateId 的数据
    const filteredSizes = sizesData.filter(item => item.templateId === templateId);
    return filteredSizes; // 返回过滤后的尺码数据数组
  } catch (error) {
    console.error('获取尺码数据失败:', error);
    throw error;
  }
}

currentPageData.value.forEach(item => {
  item.isEditing = false;
});

function handleEdit(record) {
  record.isEditing = !record.isEditing;
}

async function sizeSaveEdit(record) {
  const editingSizeMessage = record.editingSizeMessage.trim();
  if (editingSizeMessage) {
    const sizeId = record.id;
    try {
      const form = {
        "id": sizeId,
        "templateId": selectedTemplateId.value,
        "sizeMessage": editingSizeMessage,
      };
      const response = await sizeApi.update(form);
      console.log(response);
      await updateSizeData(selectedTemplateId.value);
      message.success('尺码更改成功');
      record.isEditing = false;
    } catch (error) {
      message.error('更新尺码失败: ' + error.message);
      console.error('更新尺码失败:', error);
    }
  } else {
    message.warning('请输入尺码信息');
  }
}

async function templateSaveEdit(record) {
  const editingTemplateName = record.editingTemplateName.trim();
  if (editingTemplateName) {
    const sizeId = record.id;
    try {
      const form = {
        "id": sizeId,
        "sizeCode": sizeCode.value,
        "templateName": editingTemplateName,
      };
      const response = await sizeTemplateApi.update(form);
      console.log(response);
      await fetchTemplateData(currentPageTemplate.value, pageSize.value);
      message.success('尺码更改成功');
      record.isEditing = false;
    } catch (error) {
      message.error('更新尺码失败: ' + error.message);
      console.error('更新尺码失败:', error);
    }
  } else {
    message.warning('请输入尺码信息');
  }
}

function cancelEdit(record) {
  record.isEditing = false;// 退出编辑状态
}

//尺码表的删除
function onDelete(record) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该数据吗?',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      console.log(record)
      await requestDelete(record);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//尺码表的删除
async function requestDelete(id) {
  try {
    SmartLoading.show();
    await sizeApi.delete(id);
    message.success('删除成功');
    await updateSizeData(selectedTemplateId.value);
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

//尺码模板的删除
function onTemplateDelete(record) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该数据吗?',
    okText: '删除',
    okType: 'danger',
    async onOk() {
      console.log(record)
      await templateDelete(record);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//尺码模板的删除
async function templateDelete(id) {
  try {
    SmartLoading.show();
    await sizeTemplateApi.delete(id);
    message.success('删除成功');
    // 重新加载尺码模板数据
    await fetchTemplateData(currentPageTemplate.value, pageSize.value);
    // 如果当前页数据量小于每页显示数量，且当前页是最后一页，则跳转到上一页
    if (mainData.value.length === 0 && currentPageTemplate.value > 1) {
      currentPageTemplate.value -= 1; // 跳转到上一页
      await fetchTemplateData(currentPageTemplate.value, pageSize.value);
    }
    // 如果当前选中的模板ID不在当前数据列表中，重置为null或者选择一个新的模板
    if (!mainData.value.some(item => item.id === selectedTemplateId.value)) {
      selectedTemplateId.value = mainData.value.length > 0 ? mainData.value[0].id : null;
    }

    // 如果有选中的模板ID，更新尺码数据
    if (selectedTemplateId.value) {
      await updateSizeData(selectedTemplateId.value);
    }
    console.log(currentPageTemplate.value, pageSize.value);
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

function handleAddSize() {
  // showInputBox.value = true; // 显示输入框
  // 显示对话框
  sizeOpen.value=true
}

// 添加addSize函数来处理用户提交的尺码数据
async function addSize() {
  if (newSize.value.trim()) {
    try {
      const form = {
        "templateId": selectedTemplateId.value,
        "sizeMessage": newSize.value.trim(),
      };
      console.log("提交的尺码信息:", form);
      const response = await sizeApi.add(form);
      console.log(response);
      message.success('尺码添加成功');
      newSize.value = '';    // 重置输入框和显示状态
      showInputBox.value = false;
      await updateSizeData(selectedTemplateId.value); // 刷新页面数据
    } catch (error) {
      message.error('添加尺码失败');
      console.error('添加尺码失败:', error);
    }
  } else {
    message.warning('请输入尺码');
  }
}

function handleAddTemplate() {
  templateOpen.value=true
  // showAddTemplateInput.value = true; // 显示输入框
  newTemplateName.value = ''; // 清空输入值
}
// 添加尺码模板对话框确认
const handleTempaleOk = e => {
  // 添加尺码模板
  addTemplate()
  templateOpen.value = false;
};
// 添加尺码模板对话框确认
const handleSizeOk = e => {
  // 添加码数模板
  addSize()
  sizeOpen.value = false;
};

async function addTemplate() {
  if (newTemplateName.value.trim()) {
    try {
      const form = {
        "sizeCode": sizeCode.value,
        "templateName": newTemplateName.value,
      };
      const response = await sizeTemplateApi.add(form);
      selectedTemplateId.value = response.id;
      console.log(response);
      message.success('模板添加成功');
      showAddTemplateInput.value = false; // 隐藏输入框
      newTemplateName.value = ''; // 清空输入值
      sizeCode.value = '' // 清空输入值
      await fetchTemplateData(currentPageTemplate.value, pageSize.value);// 刷新页面数据
      await updateSizeData(selectedTemplateId.value);
    } catch (error) {
      message.error('添加模板失败');
      console.error('添加模板失败:', error);
    }
  } else {
    message.warning('请输入模板名称');
  }
}

function cancelAddTemplate() {
  showAddTemplateInput.value = false; // 隐藏输入框
  newTemplateName.value = ''; // 清空输入值
}
</script>
<style scoped>
.container {
  display: flex;
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

.sidebar {
  width: 250px;
  background-color: #ffffff;
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 80px);
}

.main-content {
  flex-grow: 1;
  padding: 10px;
  overflow-y: auto;
  position: relative;
}

.button-container {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  align-items: center;
}

.left-button-container {
  position: absolute;
  bottom: 20px;
  left: 50px;
  display: flex;
  align-items: center;
}
</style>

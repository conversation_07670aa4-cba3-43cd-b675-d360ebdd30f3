<template>
  <a-modal
      :open="visible"
      :title="form.id ? '编辑周转箱' : '新增周转箱'"
      :width="400"
      @cancel="handleCancel"
  >
 <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="周转箱编号" name="number">
        <a-input style="width: 100%" v-model:value="form.number" placeholder="周转箱编号" />
      </a-form-item>
      <a-form-item label="名称" name="name">
        <a-input style="width: 100%" v-model:value="form.name" placeholder="名称" />
      </a-form-item>
      <a-form-item label="容量" name="capacity">
        <a-input style="width: 100%" v-model:value="form.capacity" placeholder="容量" />
      </a-form-item>
      <a-form-item label="备注" name="remark">  
        <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>
  <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { partStationTurnBoxApi } from '/@/api/business/mes/part-station/turn-box/part-station-turn-box-api';
import { smartSentry } from '/@/lib/smart-sentry';
const visible = ref(false);
const emit = defineEmits(['reloadList']);

const formState = {
  number: undefined,
  id: undefined,
  remark: undefined,
  name: undefined,
  capacity: undefined,
};
const form = reactive({ ...formState });

const formRef = ref();
const rules = {
//   name: [{ required: true, message: '请输入名称' }],
//   capacity: [{ required: true, message: '请输入容量' }],
};

async function onSubmit() {
    // await formRef.value.validate();
    try {
        if (form.id) {
            await partStationTurnBoxApi.update(form);
        } else {
            await partStationTurnBoxApi.save(form);
        }
        visible.value = false;
        emit('reloadList');
    } catch (error) {
        smartSentry.captureError(error);
    }
}
const handleCancel = () => {
  visible.value = false;
  Object.assign(form, formState);
};

function showForm(data) {
  visible.value = true;
  if (data) {
   Object.assign(form, data);
  }
}
defineExpose({showForm}
);
</script>

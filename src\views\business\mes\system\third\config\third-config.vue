<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input
          v-model:value="form.queryKey"
          style="width: 150px"
          placeholder="关键字查询"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="handleSearch">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
<a-card size="small" :bordered="false" :hoverable="true">
  <a-row :gutter="16" v-if="filteredAppList.length > 0">
    <a-col :span="6" v-for="item in filteredAppList" :key="item.appId">
      <a-card
        :title="item.title"
        :bordered="false"
        hoverable
        class="card"
      >
        <img :src="item.icon" style="float: left;width: 64px;height: 64px;"/>
        <div class="info">
          <span style="padding-right: 30px;margin-bottom: 5px;width: 100%;">类型：{{ item.type }}</span>
          <span class="introduction">{{item.introduction}}</span>
        </div>
        <template #extra>
          <a-button @click="item.showMethod" type="link" style="padding-right: 0px;">编辑</a-button>
        </template>
      </a-card>
    </a-col>
  </a-row>
  <a-empty v-else />

  <div class="smart-query-table-page">
    <a-pagination
      showSizeChanger
      show-less-items
      :pageSizeOptions="PAGE_SIZE_OPTIONS"
      :defaultPageSize="form.pageSize"
      v-model:current="form.pageNum"
      v-model:pageSize="form.pageSize"
      :total="total"
      @change="handleSearch"
      @showSizeChange="handleSearch"
      :show-total="(total) => `共${total}条`"
    />
  </div>
</a-card>


  <FeishuModal ref="FeishuModalRef"/>
  <WorkWechatModal ref="WorkWechatModalRef"/>
  <YuanyiModal ref="YuanyiModalRef"/>
</template>


<script setup>
import { onMounted, ref, reactive } from 'vue';
import FeishuModal from './components/feishu-config-form.vue';
import WorkWechatModal from './components/workwechat-config-form.vue';
import YuanyiModal from './components/yuanyi-config-form.vue';
import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
import feiShuIcon from '/src/assets/images/login/feishu-icon.png';
import workWechatIcon from '/src/assets/images/login/work-wechat-icon.png';
import yuanyiIcon from '/src/assets/images/yuanyi/yuanyi-logo-2.png';
//飞书模态框
const FeishuModalRef = ref();
//企业微信模态框
const WorkWechatModalRef = ref();
//元一模态框
const YuanyiModalRef = ref();

const form = reactive({
  queryKey: undefined,
  pageNum: 1,
  pageSize: 10
});
const total = ref(0);
const allData = ref([]);
const filteredAppList = ref([]);

// 第三方应用配置数据
const thirdPartyApps = [
  {
    type:'第三方应用',
    title: '飞书',
    introduction: '字节跳动旗下的一站式企业协作与管理平台',
    icon: feiShuIcon,
    showMethod: showFeishuModal
  },
  {
    type:'第三方应用',
    title: '企业微信',
    introduction: '腾讯微信团队为企业打造的一套高效、安全的沟通工具',
    icon: workWechatIcon,
    showMethod: showWorkWechatModal
  },
  {
    type:'合作伙伴',
    title: '元一科技',
    introduction: '智能铺布机和高精度自动裁床物联网平台对接',
    icon: yuanyiIcon,
    showMethod: showYuanyiModal
  }
];

const handleSearch = () => {
  const filteredData = form.queryKey
    ? allData.value.filter(app => app.title.includes(form.queryKey))
    : allData.value;

  total.value = filteredData.length;

  const startIndex = (form.pageNum - 1) * form.pageSize;
  const endIndex = startIndex + form.pageSize;
  filteredAppList.value = filteredData.slice(startIndex, endIndex);
};

const resetQuery = () => {
  form.queryKey = undefined;
  form.pageNum = 1;
  handleSearch();
};

function showFeishuModal(){
  FeishuModalRef.value.open();
}

function showWorkWechatModal(){
  WorkWechatModalRef.value.open();
}

function showYuanyiModal(){
  YuanyiModalRef.value.open();
}

onMounted(() => {
  allData.value = thirdPartyApps;
  handleSearch();
});
</script>


<style>
.info{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    padding-left:20px;
    margin-bottom: 15px;
}
.introduction{
    /* 使用webkit弹性盒子布局 */
    display: -webkit-box;
    /* 设置弹性盒子的主轴方向为垂直方向 */
    -webkit-box-orient: vertical;
    /* 设置文本最多显示2行 */
    -webkit-line-clamp: 2;
    /* 固定高度为44px */
    height: 44px;
    overflow: hidden;
}
.card{
    margin-bottom: 5px;
}
</style>

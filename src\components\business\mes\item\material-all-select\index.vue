<!--
  * 物料下拉选择框 
  * 
  * @Author:    fkf
-->
<template>
    <a-select
      v-model:value="selectValue"
      :style="`width: ${width}`"
      :placeholder="props.placeholder"
      :showSearch="true"
      :allowClear="true"
      :disabled="props.disabled"
      :filterOption="filterOption"
      :size="size"
      @change="onChange"
    >
      <a-select-option v-for="item in itemList" :key="item.id" :value="item.id" >
        <div>{{ item.name }} <span style="font-size: 12px; color: #999;">{{ item.model }}</span>
          <br/>
          <span style="font-size: 12px; color: #999;">{{ item.skuNumber }}</span>
        </div>
      </a-select-option>
    </a-select>
  </template>
  
  <script setup>
    import { onMounted, ref, watch } from 'vue';
    import { itemApi } from '/@/api/business/mes/item/item-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    
    // =========== 属性定义 和 事件方法暴露 =============
  
    const props = defineProps({
      value: [Number, Array],
      placeholder: {
        type: String,
        default: '请选择',
      },
      width: {
        type: String,
        default: '100%',
      },
      size: {
        type: String,
        default: 'default',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    });

    const emit = defineEmits(['update:value', 'change']);
    // 过滤选项
    function filterOption(inputValue, option) {
      const item = itemList.value.find((i) => i.id === option.value);
      if (!item || !inputValue) return true;

      // 转换为小写进行比较
      const searchText = inputValue.toLowerCase();
      const name = (item.name || '').toLowerCase();
      const skuNumber = (item.skuNumber || '').toLowerCase();

      // 返回名称或SKU编号包含搜索文本的选项
      return name.includes(searchText) || skuNumber.includes(searchText);
    }
  
    // =========== 查询数据 =============
  
    //物料列表数据
    const itemList = ref([]);
    async function query() {
      try {
        let resp = await itemApi.queryList({});
        // 物料列表数据对象
        itemList.value = resp.data;
      } catch (e) {
        smartSentry.captureError(e);
      }
    }
    onMounted(query);
  
    // =========== 选择 监听、事件 =============
    
    const selectValue = ref(props.value);
    watch(
      () => props.value,
      (newValue) => {
        selectValue.value = newValue;
      }
    );
    // 监听物料列表和选中值
    watch(
      //监听物料列表和选中值
      [() => itemList.value, () => props.value],

      ([items, newValue]) => {
        if (newValue && items.length > 0) {
          const selectedItem = items.find(item => item.id === newValue);
          if (selectedItem) {
            emit('change', selectedItem);
          }
        }
      },
      {immediate:true}
    );
    function onChange(value) {
      //查找物料id对应的物料对象
      const selectedItem = itemList.value.find(item => item.id === value);
      emit('update:value', value);
      // 触发 change 事件，传入选中的物料对象
      emit('change', selectedItem);
    }

  </script>
  
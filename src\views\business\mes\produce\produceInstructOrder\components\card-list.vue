<!--
  * 卡片视图
  *
  * @Author:    wxx
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <div class="card-list">
    <a-row :gutter="20">
      <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-for="item in tableData" :key="item.id">
        <a-card :bordered="false" hoverable class="card-item">
          <div>
            <!-- 优先级标签 -->
            <a-tag class="priority-tag" :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(item.priority).color">
              {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(item.priority).label }}
            </a-tag>

            <!-- 内容 -->
            <div class="card-content">
              <!-- 图片部分 -->
              <div class="card-img">
                <div v-if="item.imgUrl && item.imgUrl.length > 0" class="img-container">
                  <a-image-preview-group>
                    <template v-for="(img, index) in item.imgUrl" :key="index">
                      <a-image v-if="index === 0" :width="120" :src="img.fileUrl" class="preview-image" />
                      <a-image v-else style="display: none" :src="img.fileUrl" />
                    </template>
                  </a-image-preview-group>
                </div>
                <div v-else class="img-container">
                  <a-image
                    :width="120"
                    class="preview-image"
                    src="https://www.antdv.com/#error"
                    fallback="data:image/png;base64,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"
                    :preview="false"
                  />
                </div>
              </div>

              <div class="card-content-item">
                <ul>
                  <li>
                    <label>单据编号：</label> <span class="card-content-item-value">{{ item.instructNumber }}</span>
                  </li>
                  <li>
                    <label>客户名称：</label> <span class="card-content-item-value">{{ item.customerName }}</span>
                  </li>
                  <li>
                    <label>物料编号：</label> <span class="card-content-item-value">{{ item.itemNumber }}</span>
                  </li>
                  <li>
                    <label>物料名称：</label> <span class="card-content-item-value">{{ item.itemName }}</span>
                  </li>
                  <li>
                    <label>生产数量：</label> <span class="card-content-item-value">{{ item.produceNum }} </span>
                  </li>
                  <li>
                    <label>生产状态：</label>
                    <span
                      class="card-content-item-value"
                      :style="{ color: PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(item.produceStatus).color }"
                    >
                      {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(item.produceStatus).label }}
                    </span>
                  </li>
                  <li>
                    <label>交货日期：</label> <span class="card-content-item-value">{{ item.deliverTime }}</span>
                  </li>
                </ul>
              </div>

              <!-- 操作按钮 -->
              <div class="card-content-actions">
                <ul>
                  <li><a-button type="link" @click="showForm(item)">详情</a-button></li>
                  <li><a-button type="link" @click="showForm(item)">编辑</a-button></li>
                  <li>
                    <a-dropdown>
                      <a class="ant-dropdown-link" style="color: orange;margin-left: 22%;" @click.prevent>
                        下达
                        <DownOutlined />
                      </a>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item @click="issuedOrder(record,0)">
                            <a href="javascript:;">下达</a>
                          </a-menu-item>
                          <a-menu-item @click="issuedOrder(record,1)">
                            <a href="javascript:;">反下达</a>
                          </a-menu-item>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </li>
                  <li><a-button type="link" danger @click="onDelete(item)">删除</a-button></li>
                </ul>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 分页 -->
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="handlePageChange"
        @showSizeChange="handlePageChange"
        :show-total="(total) => `共${total}条`"
      />
    </div>
    <ProduceInstructOrderForm ref="formRef" @reloadList="queryData" />
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import ProduceInstructOrderForm from '../produce-instruct-order-form.vue';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
    PRODUCE_TYPE_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';

  // 定义props
  const props = defineProps({
    queryForm: {
      type: Object,
      required: true,
    },
    queryData: {
      type: Function,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    tableLoading: {
      type: Boolean,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  });

  // 定义emit
  const emit = defineEmits(['update:query-form']);
  const newQueryForm = JSON.parse(JSON.stringify(props.queryForm));

  //编辑
  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }

  // 分页变化
  const handlePageChange = async () => {
    emit('update:query-form', newQueryForm);
    await props.queryData();
  };

  // 下达和反下达生产
  async function issuedOrder(record,option) {
    try {
      SmartLoading.show();
      if(option == 0){
        await produceInstructOrderApi.singleOrder(record.id);
        message.success('下达成功');
      }else{
        await produceInstructOrderApi.cancelOrder(record.id);
        message.success('反下达成功');
      }
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // 删除确认
  const onDelete = (record) => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  };

  // 删除请求
  const requestDelete = async (id) => {
    try {
      SmartLoading.show();
      await produceInstructOrderApi.delete(id);
      message.success('删除成功');
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  };
</script>

<style scoped>
  li {
    list-style: none;
  }

  .card-item {
    position: relative;
    margin-top: 20px;
  }

  .priority-tag {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 10px;
  }

  .card-img {
    width: 120px;
    height: 190px; /* 固定容器高度 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 10px;
  }

  .img-container {
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .card-content {
    display: flex;
    padding-top: 10px;
    justify-content: space-between;
  }

  .card-content-item {
    flex: 1;
  }

  .card-content-item-value {
    /* 文本溢出显示省略号 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    display: inline-block;
    word-break: break-all;
  }

  /* 控制图片样式 */
  .preview-image {
    max-width: 100% !important;
    max-height: 100% !important; /* 限制最大高度 */
    object-fit: contain !important; /* 保持比例完整显示 */
  }
  .card-content-item ul {
    margin: 0;
    padding: 0;
  }

  .card-content-item li {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    line-height: 22px;
  }
</style>

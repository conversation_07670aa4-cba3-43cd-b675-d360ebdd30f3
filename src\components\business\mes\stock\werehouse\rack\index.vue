<template>
    <a-select
    v-model:value="selectedRackId"
    show-search
    :filter-option="filterOption"
    placeholder="请选择货架"
    style="width: 100%"
    @change="handleChange"
  >
    <a-select-option v-for="rack in rackList" :key="rack.id" :value="rack.id" :label="rack.label">
      {{ rack.label }}
    </a-select-option>
  </a-select>
</template>
<script setup>
import { onMounted, ref,watch, } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';
import {stkRackApi}from'../../../../../../api/business/mes/stock/stk-rack-api'

//接收编辑下已有选中项
const props = defineProps({
  modelValue: {
    type: Number,
    default: null,
  },
});
const emit = defineEmits(['update:modelValue', 'change']);
//货架列表数据
const rackList = ref([]);
  async function getRackList() {
    try {
      let result = await stkRackApi.queryList({});
      rackList.value = result.data.map(item=>{
        return {
          id:item.id,
          value:item.id,
          label:item.number
        }
      });
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // =========== 选择 监听、事件 =============
  
  const selectedRackId  = ref(props.modelValue);
  //当父组件传值变化时，更新selectedRackId
  watch(
  () => props.modelValue,
  (newValue) => {
    selectedRackId.value = newValue;
  }
);
  //当父组件传值变化时，更新selectedRackId
watch(
  selectedRackId,
  (newValue) => {
    emit('update:modelValue', newValue);
    emit('change', newValue);
  }
);
//处理选择变化
function handleChange(value) {
    selectedRackId.value = value;
// 根据 selectedRackId 查找对应的 label
    const selectedRack = rackList.value.find(rack => rack.id === value);
    if(selectedRack){
        const label=selectedRack.label;
        emit('update:modelValue', value);
        emit('change', value, label); // 传递 rackId 和 label给父组件
    }

}
// 自定义number搜索逻辑
function filterOption(input, option) {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}
  onMounted(getRackList);
</script>
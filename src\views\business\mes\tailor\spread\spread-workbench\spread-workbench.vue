<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item class="smart-query-form-item">
        <a-radio-group v-model:value="taskFinishStatusChoice" button-style="solid"
                       @change="taskFinishStatusChoiceChange">
          <a-radio-button :value="false">待完成</a-radio-button>
          <a-radio-button :value="true">已完成</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
      <a-form-item label="车间" class="smart-query-form-item">
        <workshop-select @change="selectWorkshopChange" :value="queryForm.workshopId"/>
      </a-form-item>
      <a-form-item label="生产小组" class="smart-query-form-item">
        <produce-team-select @change="selectTeamChange" :value="queryForm.teamId"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>

    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true" style="background-color: #fafaff">

    <a-list :grid="{ gutter: 4, xs: 1, sm: 2, md: 3, lg: 3, xl: 3, xxl: 4 }" :data-source="tableData"
            :loading="tableLoading">
      <template #renderItem="{ item }">
        <a-list-item>
          <a-card
              @click="openSpreadWorkModal(item.id)"
              size="small"
              :style="{
                  backgroundColor: item.taskStatus === SPREAD_TASK_STATUS_ENUM.DOING.value ? '#ffeb60' : '#fff'
              }"
              :class="['spread-task-card']"
              :bordered="false"
          >
            <div class="card-header">
              <div class="card-title">{{ item.number }}
                <a-tag :bordered="false" style="font-size: 14px"
                       :color="SPREAD_TASK_STATUS_ENUM.getEnum(item.taskStatus).color">
                  {{ SPREAD_TASK_STATUS_ENUM.getEnum(item.taskStatus).label }}
                </a-tag>
              </div>
              <div>任务编号</div>

            </div>
            <div class="card-row">
              <span class="label">生产指令单编号</span>
              <span class="value">{{ item.produceInstructOrderNumber }}</span>
            </div>
            <div class="card-row">
              <span class="label">款号</span>
              <span class="value" :style="{ color: '#1890ff' }">{{ item.materialSpuNumber }}</span>
              <span class="label">款名</span>
              <span class="value" :style="{ color: '#1890ff' }">{{ item.materialName }}</span>
            </div>
            <div class="card-row">
              <span class="label">床次</span>
              <span class="value">{{ item.cutNum }}</span>
            </div>
            <div class="card-row">
              <span class="label">车间</span>
              <span class="value">{{ item.workshopName }}</span>
              <span class="label">生产小组</span>
              <span class="value">{{ item.teamName }}</span>
            </div>
            <div class="card-row">
              <span class="label">设备</span>
              <span class="value">{{ item.equipmentName }}</span>
            </div>
            <div class="card-row">
              <span class="label">计划开始时间</span>
              <span class="value">{{ item.planBeginTime }}</span>
            </div>
            <div class="card-footer">
              <div class="plan">计划 {{ item.planTotalLayer }}层</div>
              <a-button
                  v-if="item.taskStatus===SPREAD_TASK_STATUS_ENUM.DOING.value"
                  type="primary"
                  size="large"
                  style="width: 100%;font-weight: bold"
              >继续作业
              </a-button>
              <a-button
                  v-else-if="item.taskStatus===SPREAD_TASK_STATUS_ENUM.ISSUE.value"
                  type="primary"
                  size="large"
                  style="width: 100%;background-color: #fca062;font-weight: bold"
              >开始作业
              </a-button>
            </div>
          </a-card>
        </a-list-item>
      </template>
    </a-list>

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

  </a-card>

  <spread-work-modal ref="spreadWorkModalRef"  @reloadList="queryData"/>

</template>
<script setup>
import {onMounted, reactive, ref} from "vue";
import workshopSelect from "/@/components/business/mes/factory/workshop-select/index.vue"
import produceTeamSelect from "/@/components/business/mes/factory/produce-team-select/index.vue"

import {smartSentry} from "/@/lib/smart-sentry.js";
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import {spreadTaskApi} from "/@/api/business/mes/tailor/spread-task-api.js";
import {SPREAD_TASK_STATUS_ENUM} from "/@/constants/business/mes/tailor/spread-task-const.js";
import SpreadWorkModal from "/@/views/business/mes/tailor/spread/spread-workbench/components/spread-work-modal.vue";
import PartStationRackForm from "/@/views/business/mes/part-station/rack/part-station-rack-form.vue";

//---------------------作业模态框------
const spreadWorkModalRef = ref(null);

function openSpreadWorkModal(taskId) {
  spreadWorkModalRef.value.showModal(taskId);
}


// ---------------------------- 查询数据表单和方法 ----------------------------

const UN_FINISH_STATUS_LIST = [SPREAD_TASK_STATUS_ENUM.DOING.value, SPREAD_TASK_STATUS_ENUM.ISSUE.value]
const FINISH_STATUS_LIST = [SPREAD_TASK_STATUS_ENUM.COMPLETE.value]

const queryFormState = {
  queryKey: undefined, //关键字查询
  workshopId: undefined,
  teamId: undefined,
  equipmentId: undefined,
  taskStatusList: UN_FINISH_STATUS_LIST,
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{column: 'create_time', isAsc: false}],
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await spreadTaskApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    // console.log(tableData.value)
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

onMounted(queryData);

//------------------------

const taskFinishStatusChoice = ref(false)

function taskFinishStatusChoiceChange(e) {
  // console.log(e.target.value)
  if (e.target.value === true) {
    queryForm.taskStatusList = FINISH_STATUS_LIST
  } else {
    queryForm.taskStatusList = UN_FINISH_STATUS_LIST
  }
  queryData()
}

//-----------------------------------

function selectWorkshopChange(e) {
  // console.log(e)
  queryForm.workshopId = e.workshopId
}

function selectTeamChange(e) {
  // console.log(e)
  queryForm.teamId = e.teamId
}

</script>
<style scoped lang="less">

.spread-task-card {
  border-radius: 12px;
  overflow: hidden;
  min-height: 320px;

  .card-header {
    margin-bottom: 8px;

    .card-title {
      font-weight: 600;
      font-size: 24px;
      color: #222;
    }
  }

  .card-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4px;

    .label {
      color: #999;
      min-width: 48px;
      margin-right: 4px;
      margin-bottom: 4px;
      text-align: center;
      background-color: #f1f5fb;
      padding: 1px 8px;
      border-radius: 10px;
    }

    .value {
      color: #333;
      margin-right: 16px;
      font-weight: 500;
    }
  }

  .card-footer {
    margin-top: 12px;

    .plan {
      font-size: 16px;
      color: #666;
      font-weight: 500;
      background-color: #f2f6fc;
      padding: 4px 12px;
      border-radius: 12px;
      text-align: center;
      margin-bottom: 8px;
    }

    .btn {
      font-size: 22px;
      border-radius: 6px;
      width: 100%;

    }
  }

}
</style>

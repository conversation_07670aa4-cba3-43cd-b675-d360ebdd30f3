<!--
  * 其他入库单添加
  *
  * @Author:    pxz
  * @Copyright  zscbdic
-->
<template>
  <a-modal
    :title="form.id ? '编辑入库单' : '创建入库单'"
    width="1500px"
    :open="visibleFlag" 
    @cancel="onClose"
  >
    <div class="title">
      基本信息
    </div>
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" >
      <a-row>
        <a-col :span="6">
          <a-form-item label="单据编号" name="number">
            <a-input v-model:value="form.number" placeholder="请输入，忽略将自动生成" style="width: 90%;"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="仓库" name="warehouseId">
            <wereHouseSelect v-model:value="form.warehouseId" placeholder="请选择仓库" style="width: 90%;"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="入库时间" name="inStockTime">
            <a-date-picker style="width: 90%;" show-time @change="onChangeInStockTime" v-model:value="inStockTimeValue"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="form.remark" placeholder="请输入备注" style="width: 90%;" auto-size/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="仓管员" name="stockerId">
            <employeeSelect v-model:value="form.stockerId" @change="stockerChange" placeholder="请选择申请人" style="width: 90%;"/>
          </a-form-item>
        </a-col>
        <ownerSelect @update:ownerInfo="owerSelectChange" ref="ownerSelectRef" v-model:ownerInfo="ownerInfo"/>
        <a-col :span="6">
          <a-form-item label="申请人" name="applicantId">
            <employeeSelect v-model:value="form.applicantId" @change="applicantChange" placeholder="请选择申请人" style="width: 90%;"/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="申请时间" name="applyTime">
            <a-date-picker show-time style="width: 90%;" @change="onChangeApplyTime" placeholder="若不填写默认当前时间" v-model:value="applyTimeValue"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="title">
      入库明细
    </div>
    <a-space style="margin-bottom: 10px;">
      <a-button type="primary" @click="handleAdd">
        <plus-outlined /> 添加物料
      </a-button>
    </a-space>
    <a-table
      :columns="columns"
      :dataSource="form.details"
      :pagination="false"
      bordered
      size="small"
      row-key="seq"
      :scroll="{ x: 1500 }"
    >
      <template #bodyCell="{ text,column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'materielId'">
          <MaterialSelect v-model:value="record.materielId" placeholder="请选择物料" @change="(value) => materielChange(value,record)"/>
        </template>
        <template v-if="column.dataIndex=== 'lotNumber'">
          <lotSelect v-model:value="record.lot" placeholder="请选择批次号" @change="(value) => lotChange(value,record)" :materielId="record.materielId"/>
        </template>
        <template v-if="column.dataIndex=== 'locationNumber'">
          <locationSelect v-model:value="record.locationId" :warehouseId="form.warehouseId" placeholder="请选择货位" @change="(value) => locationChange(value,record)"/>
        </template>
        <template v-if="column.dataIndex=== 'qty'">
          <a-input v-model:value="record.qty" placeholder="请输入数量"/>
        </template>
        <template v-if="column.dataIndex=== 'price'">
          <a-input v-model:value="record.price" placeholder="请输入单价"/>
        </template>
        <template v-if="column.dataIndex=== 'amount'">
          <a-input v-model:value="record.amount" disabled/>
        </template>
        <template v-if="column.dataIndex=== 'remark'">
          <a-textarea v-model:value="record.remark" placeholder="请输入备注" auto-size/>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">提交申请</a-button>
        <a-button type="primary" @click="onSubmitStatus">提交并审核</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { useUserStore } from '/@/store/modules/system/user.js';
import { unitApi } from '/@/api/business/mes/base/unit-api.js';
import { stkOtherInStockApi } from '/@/api/business/mes/stock/stk-in-other-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { BILLS_STATUS } from '/@/constants/business/mes/stock/stk-status-const.js';
import ownerSelect from '/@/components/business/mes/stock/owner-select/index.vue'
import wereHouseSelect from '/@/components/business/mes/stock/werehouse/warehouse-select/index.vue';
import MaterialSelect from '/@/components/business/mes/item/material-all-select/index.vue';
import employeeSelect from '/@/components/system/employee-obj-select/index.vue';
import locationSelect from '/@/components/business/mes/stock/werehouse/location-select/index.vue';
import lotSelect from '/@/components/business/mes/stock/lot/index.vue';
import dayjs from 'dayjs';
import { message } from 'ant-design-vue';

const rules = {
  warehouseId: [
    { required: true, message: '仓库不能为空' },
  ],
}
const ownerInfo = ref({});

// ------------------------ 事件 ------------------------
const emits = defineEmits(['reloadList']);
//获取用户store
const userStore = useUserStore();
//货主选择
const ownerSelectRef = ref();

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

async function showForm(id) {
  if (id) {
    //请求具体单据
    const res = await stkOtherInStockApi.getById(id);
    Object.assign(form,res.data);
    ownerInfo.value = {
      type: form.ownerType,
      id: form.ownerId,
      name: form.ownerName,
    };
    form.details.forEach(details =>{
      details.lot ={
        id: details.lotId,
        number:details.lotNumber
      }
    })
    inStockTimeValue.value = dayjs(form.inStockTime);
    applyTimeValue.value = dayjs(form.applyTime);
  }
  visibleFlag.value = true;
}

function onClose() {
  resetData();
  visibleFlag.value = false;
}

//重制数据
function resetData(){
  Object.assign(form,formDefault);
  //重置货主选择
  ownerSelectRef.value.reset();
  form.details = [];
  ownerInfo.value = {};
  inStockTimeValue.value = undefined;
  applyTimeValue.value = undefined;
}

//------------------------ 表单 ------------------------
const formRef = ref();

const formDefault = {
  id: undefined, //id
  number: undefined, //单据编号
  remark: undefined, //备注
  warehouseId: undefined, //仓库id 组建
  inStockTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), //入库时间
  stockerId: undefined, //仓管员id 组建
  stockerName: undefined, //仓管员名称
  ownerType: undefined, //货主类型
  ownerId: undefined, //货主id
  ownerName: undefined, //货主名称
  applicantId: userStore.employeeId, //申请人id 组建 默认当前账号
  applicantName:userStore.actualName, //申请人名称
  applyTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), //申请时间
  originType: undefined, //单据来源类型
  originId: undefined, //单据来源id
  originNumber: undefined, //单据来源编号
  type: '', //单据类型
  way: '', //单据方式
  status: BILLS_STATUS.UN_AUDIT.value, //单据状态
  details:[], //详细信息
}

let form = reactive({ ...formDefault });

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'seq',
    width: 50,
  },
  {
    title: '物料',
    dataIndex: 'materielId',
    width: 200,
  },
  {
    title: '物料SKU',
    dataIndex: 'materielSku',
    width: 150,
  },
  {
    title: '物料SPU',
    dataIndex: 'materielSpu',
    width: 150,
  },
  {
    title: '规格型号',
    dataIndex: 'materielModel',
    width: 100,
  },
  {
    title: '单位',
    dataIndex: 'unitName',
    width: 100,
  },
  {
    title: '批次号',
    dataIndex: 'lotNumber',
    width: 180,
  },
  {
    title: '库位编号',
    dataIndex: 'locationNumber',
     width: 180,
  },
  {
    title: '数量',
    dataIndex: 'qty',
    width: 100,
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 100,
  },
  {
    title: '金额',
    dataIndex: 'amount',
        width: 100,

  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 90,
    fixed: 'right',
  }
];

//------------------------ 处理时间 ------------------------
const inStockTimeValue = ref()
const applyTimeValue = ref()

function onChangeInStockTime(date, dateString) {
  form.inStockTime = dateString;
}

function onChangeApplyTime(date, dateString) {
  form.applyTime = dateString;
}

//------------------------ 处理人员 ----------------------
//绑定人员名字
function stockerChange(employee) {
  form.stockerName = employee?.actualName;
}

//申请人
function applicantChange(employee) {
  form.applicantName = employee?.actualName;
}

//------------------------ 货主选择 ------------------------
function owerSelectChange(ownerInfo) {
  form.ownerType = ownerInfo?.type;
  form.ownerId = ownerInfo?.id;
  form.ownerName = ownerInfo?.name;
}

//------------------------ 添加删除产品 ------------------------
function handleAdd() {
  form.details.push({
    seq: form.details.length + 1, //序号
    materielId: undefined, //物料id
    materielSku:undefined,//物料sku
    materielSpu:undefined,//物料spu
    materielModel:undefined,//物料类型
    lot:undefined, //批号对象
    lotId: undefined, //批次id  
    lotNumber: undefined, //批次号
    locationId: undefined, //货位id
    locationNumber: undefined, //货位编号
    unitId: undefined, //单位id
    unitName: undefined, //单位名称
    qty: undefined, //数量    
    joinQty: undefined, //累计数量  
    price: undefined, //单价
    amount: undefined, //总金额
    originDetailType: undefined, //来源类型
    originDetailId: undefined, //来源id
    originDetailSeq: undefined, //来源序号
    remark: undefined //备注
  });
}

function handleDelete(record){
  form.details = form.details.filter(item => item.seq !== record.seq);
  form.details.forEach((item, i) => {
    item.seq = i + 1;
  });
}

//------------------------ 处理事件 ------------------------
//物料选择
async function materielChange(materiel,record){
  if (!materiel) {
      // 清空相关字段
      record.unitId = undefined;
      record.materielSku = undefined;
      record.materielSpu = undefined;
      record.materielModel = undefined;
      record.unitName = undefined;
      return;
    }
  record.unitId = materiel?.unitId;
  record.materielSku = materiel?.skuNumber;
  record.materielSpu = materiel?.number;
  record.materielModel = materiel?.model;
  //通过id查询单位名称
  const res = await unitApi.getId(record.unitId);
  record.unitName = res.data.name;
}

//货位选择
function locationChange(location,record){
  record.locationNumber = location?.number;
}

//批次号选择
function lotChange(lot,record){
  record.lotId = lot?.id;
  record.lotNumber = lot?.number;
}

//---------------------- 金额计算 ------------------------
const calculateAmount = (record) =>{
  let qty = Number(record.qty) ||0;
  let price = Number(record.price) ||0;
  record.amount = (qty*price).toFixed(3);
}

//监听金额变化
watch(
  //监听金额变化
  () => form.details.map(record => [record.qty,record.price]),
  //监听变化后计算金额
  () => form.details.forEach(calculateAmount),
  {deep:true}
)

//------------------------ 保存 ------------------------
async function onSubmit() { 
  await formRef.value.validateFields();
  SmartLoading.show();
  try {
    if (form.id) {
      await stkOtherInStockApi.update(form);
    } else {
      await stkOtherInStockApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

//------------------------ 保存并审核 ------------------------
async function onSubmitStatus() {
  form.status = BILLS_STATUS.AUDIT.value;
  await onSubmit();
}

defineExpose({
  showForm
})
</script>

<style>
.title {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
}
</style>
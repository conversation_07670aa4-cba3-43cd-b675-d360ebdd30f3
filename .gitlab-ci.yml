variables: #变量
  #网关
  MES_FRONT_CONTAINER: "yuanyi-mes-front-gitlab"
  MES_FRONT_IMAGE: "yuanyi-mes-front-gitlab"
  MES_FRONT_CONTAINER_TEST: "yuanyi-mes-front-gitlab-test"
  MES_FRONT_IMAGE_TEST: "yuanyi-mes-front-gitlab-test"
  #端口
  MES_FRONT_PORT: 8088


stages:
  - deploy
  - sonarqube-check
  - deploy-test
  - deploy-prod


deploy:
  stage: deploy
  image: docker:20.10.15  #目的是为了在docker环境下的runner能执行docker命令
  script:
    #服务
    - docker ps -a|grep $MES_FRONT_CONTAINER &&  docker stop $MES_FRONT_CONTAINER && docker rm $MES_FRONT_CONTAINER || echo "not exist"
    - docker images |grep  $MES_FRONT_IMAGE && docker rmi -f $MES_FRONT_IMAGE || echo  "not exist"
    - docker build -f Dockerfile-Dev --build-arg NODE_ENV=dev -t $MES_FRONT_IMAGE .
    - docker run -d --restart=always -p $MES_FRONT_PORT:80 --log-opt max-size=50m --log-opt max-file=3 --name $MES_FRONT_CONTAINER $MES_FRONT_IMAGE
  only:
    - dev
  tags:
    - yuanyi-mes-front


sonarqube-check:
  image:
    name: sonarsource/sonar-scanner-cli:11
    entrypoint: [ "" ]
  stage: sonarqube-check
  script:
    - sonar-scanner -Dsonar.host.url="${SONAR_HOST_URL}" -Dsonar.token="${SONAR_TOKEN}"
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'dev'
  tags:
    - yuanyi-mes-front


deploy-test:
  stage: deploy
  image: docker:20.10.15  #目的是为了在docker环境下的runner能执行docker命令
  script:
    #服务
    - docker ps -a|grep $MES_FRONT_CONTAINER_TEST &&  docker stop $MES_FRONT_CONTAINER_TEST && docker rm $MES_FRONT_CONTAINER_TEST || echo "not exist"
    - docker images |grep  $MES_FRONT_IMAGE_TEST && docker rmi -f $MES_FRONT_IMAGE_TEST || echo  "not exist"
    - docker build -f Dockerfile-Test --build-arg NODE_ENV=test -t $MES_FRONT_IMAGE_TEST .
    - docker run -d --restart=always -p 8898:80 --log-opt max-size=50m --log-opt max-file=3 --name $MES_FRONT_CONTAINER_TEST $MES_FRONT_IMAGE_TEST
  only:
    - dev
  tags:
    - yuanyi-mes-front


deploy-prod:
  stage: deploy
  image: docker:20.10.15  #目的是为了在docker环境下的runner能执行docker命令
  script:
    #服务
    - docker ps -a|grep $MES_FRONT_CONTAINER &&  docker stop $MES_FRONT_CONTAINER && docker rm $MES_FRONT_CONTAINER || echo "not exist"
    - docker images |grep  $MES_FRONT_IMAGE && docker rmi -f $MES_FRONT_IMAGE || echo  "not exist"
    - docker build -f Dockerfile-Prod --build-arg NODE_ENV=prod -t $MES_FRONT_IMAGE .
    - docker run -d --restart=always -p $MES_FRONT_PORT:80 --log-opt max-size=50m --log-opt max-file=3 --name $MES_FRONT_CONTAINER $MES_FRONT_IMAGE
  only:
    - master
  tags:
    - yuanyi-mes-front-prod

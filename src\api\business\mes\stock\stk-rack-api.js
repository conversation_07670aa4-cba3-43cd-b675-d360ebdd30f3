/**
 * 货架 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-05 15:13:43
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkRackApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/stkRack/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/stkRack/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/stkRack/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/stkRack/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/stkRack/batchDelete', idList);
  },
  //查询列表
  queryList: () => {
    return postRequest('/stkRack/queryList',{});
  },
};

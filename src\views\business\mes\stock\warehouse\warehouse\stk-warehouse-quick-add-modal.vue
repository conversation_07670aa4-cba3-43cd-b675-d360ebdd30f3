<!--
  * 仓库快速建仓
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:13:18
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="'快速建仓'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 }" >
          
          <a-form-item label="仓库编号" name="number">
            <a-input style="width: 100%" v-model:value="form.number" placeholder="仓库编号" />
          </a-form-item>
          <a-form-item label="仓库名称" name="name">
            <a-input style="width: 100%" v-model:value="form.name" placeholder="仓库名称" />
          </a-form-item>
          <a-form-item label="仓库地址" name="address">
            <a-input style="width: 100%" v-model:value="form.address" placeholder="仓库地址" />
          </a-form-item>
          <!-- <a-form-item label="负责人id" name="principalId">
            <a-input-number style="width: 100%" v-model:value="form.principalId" placeholder="负责人id" disabled/>
          </a-form-item> -->
         <a-form-item label="负责人名称"  name="principalName">
                        <employee-obj-select
                        v-model:value="form.principalId"
                         @change="handleEmployeeChange"
                        style="width: 100%"
                        placeholder="请选择负责人"
                        />
                    </a-form-item>
          <a-form-item label="负责人电话" name="tel">
            <a-input style="width: 100%" :value="form.tel" placeholder="负责人电话" disabled/>
          </a-form-item>
          <a-form-item label="货架数量" name="rackNum">
            <a-input-number style="width: 100%" v-model:value="form.rackNum" placeholder="货架数量" />
          </a-form-item>
          <a-form-item label="层数" name="layerNum">
            <a-input-number style="width: 100%" v-model:value="form.layerNum" placeholder="层数" />
          </a-form-item>
           <a-form-item label="是否启用仓位管理" name="openLocationFlag">
            <a-radio-group v-model:value="form.openLocationFlag">
              <a-radio :value="true">是</a-radio>
              <a-radio :value="false">否</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="是否允许负库存" name="allowNegativeFlag">
            <a-radio-group v-model:value="form.allowNegativeFlag">
              <a-radio :value="true">是</a-radio>
              <a-radio :value="false">否</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
          </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { stkWarehouseApi } from '/@/api/business/mes/stock/stk-warehouse-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import EmployeeObjSelect from '/@/components/system/employee-obj-select/index.vue';

  const emits = defineEmits(['reloadList']);

  const visibleFlag = ref(false);
  const formRef = ref();

  const formDefault = {
    remark: undefined,
    number: undefined,
    name: undefined,
    address: undefined,
    principalId: undefined,
    principalName: undefined,
    tel: undefined,
    openLocationFlag: undefined,
    allowNegativeFlag: undefined,
    rackNum: undefined,//货架数量
    layerNum: undefined,//层数
  };

  let form = reactive({ ...formDefault });

  const rules = {
    number: [{ required: true, message: '仓库编号必填' }],
    name: [{ required: true, message: '仓库名称必填' }],
    openLocationFlag: [{ required: true, message: '是否启用仓位管理必填' }],
    allowNegativeFlag: [{ required: true, message: '是否允许负库存必填' }],
    rackNum: [{ required: true, message: '货架数量必填' }],
    layerNum: [{ required: true, message: '层数必填' }],
  };

  // 处理员工选择变化
  function handleEmployeeChange(employee) {
    form.principalName = employee.actualName;
      form.principalId = employee.employeeId;
      form.tel = employee.phone;
  }

  function show() {
    Object.assign(form, formDefault);
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  async function save() {
    SmartLoading.show();
    try {
      await stkWarehouseApi.quickAdd(form);
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
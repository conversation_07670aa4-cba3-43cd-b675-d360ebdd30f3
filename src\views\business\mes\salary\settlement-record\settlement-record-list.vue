<!--
  * 薪酬结算记录
  *
  * @Author:    linwj
  * @Date:      2024-11-20 20:48:59
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="员工" class="smart-query-form-item">
              <a-select
                  v-model:value="initOptEmp"
                  show-search
                  placeholder="员工选择"
                  style="width: 150px"
                  :options="operatorOptions"
                  :filter-option="filterOption"
                  @change="handleEmpChange"
              />
            </a-form-item>
            <a-form-item label="结算方式" class="smart-query-form-item">
            <a-select
                ref="initSettlementType"
                v-model:value="queryForm.settlementWay"
                style="width: 100px"
                :options="settlementTypeOptions"
                @change="handleSettlementTypeChange"
                placeholder="不区分"
            />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
              <a-button @click="batchCancel" type="primary" size="small" danger>
                      <span>批量取消</span>
              </a-button>
              <a-button @click="handleExport" type="primary" size="small">
                      <span>批量导出</span>
              </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
                :row-selection="rowSelection"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                      <a-button @click="showForm(record)" type="link">查看</a-button>
                      <a-button @click="settlementCancel(record)" type="link" danger>取消</a-button>
                    </div>
                </template>
              <template v-else-if="column.dataIndex === 'payoffFlag'">
                <span v-if="text === true"><a-tag color="green">已发放</a-tag></span>
                <span v-else><a-tag color="red">未发放</a-tag></span>
              </template>
            </template>

        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>
        <SettlementRecordForm  ref="formRef" @reloadList="queryData"/>
        <SettlementRecordPrintModal ref="printRef"/>

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { message } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { settlementRecordApi } from '/@/api/business/mes/salary/settlement-record-api.js';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import SettlementRecordForm from './settlement-record-form.vue';
    import _ from "lodash";
    import {SETTLEMENT_WAY_ENUM} from "/@/constants/business/mes/salary/settlementWay-const.js";
    import {employeeApi} from "/@/api/system/employee-api.js";
    import dayjs from "dayjs";
    import {exportSettlementRecord} from "/src/utils/salary-pdf-util.js";
    import {getLocationPdf} from "/src/utils/pdf-util.js";
    import SettlementRecordPrintModal from './components/settlement-record-print-modal/index.vue';

    const printRef = ref();

    async function handleExport() {
    if (_.isEmpty(selectedRowList.value)) {
        message.warn("请选择数据")
        return;
    }

    try {

        const resArray = await Promise.all(selectedRowList.value.map(e=>settlementRecordApi.queryDetailsByRecordId(e.id)))
        const belongMonth = selectedRowList.value.map(e=>e.belongMonth)
        printRef.value.open(resArray,belongMonth);

    } catch (error) {
        smartSentry.captureError(error);
        message.error(error.message);
    }
}

    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        {
            title: '员工姓名',
            dataIndex: 'actualName',
            ellipsis: true,
        },
        {
            title: '归属月份',
            dataIndex: 'belongMonth',
            ellipsis: true,
        },
        {
            title: '总金额',
            dataIndex: 'totalAmount',
            ellipsis: true,
            customRender: (text) => {
              return "￥"+text.value
            }
        },
        {
            title: '总件数',
            dataIndex: 'totalNum',
            ellipsis: true,
            customRender: (text) => {
              return text.value+" 件"
            }
        },
        {
            title: '总次数',
            dataIndex: 'totalCount',
            ellipsis: true,
        },
        {
          title: '结算时间',
          dataIndex: 'settlementTime',
          ellipsis: true,
        },
        {
          title: '发放状态',
          dataIndex: 'payoffFlag',
          ellipsis: true,
        },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 90,
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------
    const settlementTypeOptions = ref([])

    const queryFormState = {
        employeeId: undefined, //员工id
        settlementWay: undefined, //结算方式
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        initOptEmp.value = undefined
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await settlementRecordApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }

    async function outstandingOptionsByEnum(enums,options){
      const settlementWayEnums =  Object.values(enums);
      for(let i = 0;i<settlementWayEnums.length-1;i++){
        options.value.push({
          value:settlementWayEnums[i].value,
          label:settlementWayEnums[i].label
        })
      }
    }

    async function handleSettlementTypeChange(value){
      queryForm.settlementWay = value;
    }

    onMounted(()=>{
      queryData();
      outstandingOptionsByEnum(SETTLEMENT_WAY_ENUM,settlementTypeOptions)
      queryAllEmployee()
    });

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 处理操作人下拉 ----------------------------

    //初始化员工下拉
    const initOptEmp = ref()
    const operatorOptions = ref([])

    //输出操作者options
    function outstandingOptOptions(tableData) {
      operatorOptions.value = []
      tableData.map((e) => (
          operatorOptions.value.push({
            value: e.employeeId,
            label: e.actualName
          })
      ))
    }

    function handleEmpChange(empId) {
      queryForm.employeeId = empId
    }

    //操作者下拉框搜索
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    //查询所有员工
    async function queryAllEmployee() {
      try {
        let resp = await employeeApi.queryAll();
        outstandingOptOptions(resp.data)
      } catch (e) {
        smartSentry.captureError(e);
      }
    }

    //请求结算取消
    async function settlementCancel(data){
      SmartLoading.show();
      try {
        await settlementRecordApi.settlementCancel(data.id);
        message.success('结算取消成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }

    // ----------------- 处理多选 ------------------------
    const selectedRowKeyList = ref([])
    const selectedRowList = ref(undefined)
    const rowSelection = {
      onChange: (selectedRowKeys,selectedRows) => {
        selectedRowKeyList.value = selectedRowKeys
        selectedRowList.value = selectedRows
      }
    };

    //批量取消
    async function batchCancel () {
      if (_.isNil(selectedRowKeyList.value) || _.isEmpty(selectedRowKeyList.value)) {
        return message.warn("请选择结算记录");
      }
      SmartLoading.show();
      try {
        await Promise.all(  // 并行处理每个取消操作
            selectedRowKeyList.value.map((id) => settlementRecordApi.settlementCancel(id))
        );
        message.success('批量取消成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
        message.error('批量取消失败');
      } finally {
        SmartLoading.hide();
      }
    }

</script>

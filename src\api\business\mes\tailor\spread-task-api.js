import { postRequest, getRequest } from '/@/lib/axios';

export const spreadTaskApi = {
  /**
   * 分页查询  <AUTHOR>
  queryPage: (param) => {
    return postRequest('/spreadTask/queryPage', param);
  },

  /**
   * 添加  <AUTHOR>
  add: (param) => {
    return postRequest('/spreadTask/add', param);
  },

  /**
   * 修改  <AUTHOR>
  update: (param) => {
    return postRequest('/spreadTask/update', param);
  },

  /**
   * 根据id查询  <AUTHOR> @param id
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  queryById: (id) => {
    return getRequest(`/spreadTask/get/${id}`);
  },

  start: (id) => {
    return getRequest(`/spreadTask/start/${id}`);
  },

  complete: (param) => {
    return postRequest(`/spreadTask/complete`,param);
  },

  cancel: (id) => {
    return getRequest(`/spreadTask/cancel/${id}`);
  },

  delete: (id) => {
    return getRequest(`/spreadTask/delete/${id}`);
  },

  checkProcess: (param) => {
    return postRequest(`/spreadTask/checkProcess`,param);
  },
  //下发任务单
  issue: (ids) => {
    return postRequest(`/spreadTask/issue`,ids);
  },
  //反下达
  reIssue: (ids) => {
    return postRequest(`/spreadTask/reIssue`,ids);
  },
  //批量删除任务单
  batchDelete: (ids) => {
    return postRequest(`/spreadTask/batchDelete`,ids);
  },
}

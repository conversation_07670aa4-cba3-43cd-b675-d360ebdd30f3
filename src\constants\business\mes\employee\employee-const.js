/**
 * 部门员工枚举
 *
 * @Author:    fkf
 * @Date:      2025-01-05 15:37:57
 * @Copyright  zscbdic
 */

export const EMPLOYEE_APPLY_STATUS_ENUM={
    CHECKING: {
        value: '0',
        desc: '待审核',
        label: '待审核',
        color: 'blue'
    },
    AGREE: {        
        value: '1',
        desc: '已同意',
        label: '已同意',
        color: 'green'
    },
    REFUSE: {
        value: '2',
        desc: '已拒绝',
        label: '已拒绝',
        color: 'red'
    },
    getEnum(value) {
        for (const key in this) {
        if (typeof this[key] === 'object' && this[key].value === value) {
        return this[key];
        }
        }
    }
}
  
export default {
    EMPLOYEE_APPLY_STATUS_ENUM,
};
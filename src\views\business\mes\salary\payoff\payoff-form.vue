<!--
  * 薪酬结算
  *
  * @Author:    linwj
  * @Date:      2024-11-20 20:48:59
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="'员工：'+ workerName"
      width="1000px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="时间" class="smart-query-form-item">
          <a-range-picker v-model:value="timeRangeValue"
                          :allowClear="false"
                          :defaultValue="[dayjs().startOf('month'), dayjs().endOf('month')]"
                          disabled  />
        </a-form-item>
      </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->
    <!-- 工资汇总 -->
    <a-card size="small" :bordered="false" :hoverable="true">
      <div style="background: #ffffff; padding: 5px">
        <a-row :gutter="8">
          <a-col :span="12">
              <a-statistic
                  title="已结"
                  :value="0"
                  :precision="2"
                  suffix="￥"
                  :value-style="{ color: '#29cf13' }"
              />
          </a-col>
          <a-col :span="12">
              <a-statistic
                  title="未结"
                  :value="0"
                  :precision="2"
                  suffix="￥"
                  :value-style="{ color: '#cf1322' }"
              />
          </a-col>
        </a-row>
      </div>

      <!---------- 表格 begin ----------->
      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          bordered
          :loading="tableLoading"
          :pagination="false"
      >
        <template #bodyCell="{ text, record, column }">
          <template v-if="column.dataIndex === 'actualName'">
            <a @click="showModal(record.employeeId)">
              {{ record.actualName }}
            </a>
          </template>
          <template v-else-if="column.dataIndex === 'settlementFlag'">
            <a-tag :color="SETTLEMENT_STATUS_ENUM.getEnum(text).color">
              {{SETTLEMENT_STATUS_ENUM.getEnum(text).desc}}
            </a-tag>
          </template>
        </template>
      </a-table>
      <!---------- 表格 end ----------->

      <div class="smart-query-table-page">
        <a-pagination
            showSizeChanger
            showQuickJumper
            show-less-items
            :pageSizeOptions="PAGE_SIZE_OPTIONS"
            :defaultPageSize="queryForm.pageSize"
            v-model:current="queryForm.pageNum"
            v-model:pageSize="queryForm.pageSize"
            :total="total"
            @change="queryData"
            @showSizeChange="queryData"
            :show-total="(total) => `共${total}条`"
        />
      </div>
    </a-card>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick, watch, onMounted, computed} from 'vue';
import _ from 'lodash';
import { smartSentry } from '/@/lib/smart-sentry';
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import SmartEnumSelect from "/@/components/framework/smart-enum-select/index.vue";
import dayjs from "dayjs";
import {SETTLEMENT_WAY_ENUM} from "/@/constants/business/mes/salary/settlementWay-const.js";
import {settlementApi} from "/@/api/business/mes/salary/settlement-api.js";
import {SETTLEMENT_STATUS_ENUM} from "/@/constants/business/mes/salary/settlementStatus-const.js";

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);


  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    if(!_.isNil(rowData)){
      visibleFlag.value = true;
    }
    initVal(rowData)
    queryData()
  }

  const employeeId = ref(null)
  const workerName = ref(null)
  const timeRangeValue = ref([])

// 初始化值
function initVal(rowData){
  // 初始化员工信息
  employeeId.value = rowData.employeeId
  workerName.value = rowData.workerName
  // 初始化查询表单时间
  queryForm.startDate = _.isEmpty(rowData.settlementMonth)?
      rowData.timeRange[0].startOf('day').format("YYYY-MM-DD"):
      rowData.settlementMonth.startOf('month').format("YYYY-MM-DD");
  queryForm.endDate = _.isEmpty(rowData.settlementMonth)?
      rowData.timeRange[1].endOf('day').format("YYYY-MM-DD"):
      rowData.settlementMonth.endOf('month').format("YYYY-MM-DD");
  // 初始化结算时间范围
  timeRangeValue.value.length = 0
  if(!_.isNull(rowData.timeRange)){
    timeRangeValue.value.push(rowData.timeRange[0].startOf('day'))
    timeRangeValue.value.push(rowData.timeRange[1].endOf('day'))
  }else {
    timeRangeValue.value.push(rowData.settlementMonth.startOf('month'));
    timeRangeValue.value.push(rowData.settlementMonth.endOf('month'));
  }
}

function onClose() {
  visibleFlag.value = false;
}

// ---------------------------- 员工薪资汇总栏 ------------------------------
// 工资
const totalSalary = ref(0);
// 已结
const paySalary = ref(0);
// 未结
const unPaySalary = ref(0);



const columns = ref([
  {
    title: '生产单号',
    dataIndex: 'produceInstructOrderNumber',
    ellipsis: true,
  },
  {
    title: '款号',
    dataIndex: 'itemName',
    ellipsis: true,
  },
  {
    title: '颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
  },
  {
    title: '床号',
    dataIndex: 'cutNum',
    ellipsis: true,
  },
  {
    title: '工序',
    dataIndex: 'processName',
    ellipsis: true,
  },
  {
    title: '件数',
    dataIndex: 'workQuantity',
    ellipsis: true,
    customRender: (text) => {
      return text.value+" 件"
    }
  },
  {
    title: '单价',
    dataIndex: 'price',
    ellipsis: true,
    customRender: (text) => {
      return "￥"+text.value
    }
  },
  {
    title: '结算状态',
    dataIndex: 'settlementFlag',
    ellipsis: true,
  },
  {
    title: '计件时间',
    dataIndex: 'reportTime',
    ellipsis: true,
    width: 150,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  pageNum: 1,
  pageSize: 10,
  settlementWay: SETTLEMENT_WAY_ENUM.BY_MONTH.value, //结算方式 默认月结
  startDate: dayjs().startOf('month').format("YYYY-MM-DD"), // 开始日期 默认本月
  endDate: dayjs().endOf('month').format("YYYY-MM-DD"), // 结束日期 默认本月
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  //重置所有时间
  queryForm.pageSize = pageSize;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await settlementApi.settlementDetails(employeeId.value,queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}


defineExpose({
  show,
});
</script>

/**
 * 裁床单 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-13 10:53:07
 * @Copyright  cjm
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const cutBedSheetApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/cutBedSheet/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/cutBedSheet/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/cutBedSheet/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/cutBedSheet/delete/${id}`);
  },
  /**
   * 获取指令单下新裁床单床次  <AUTHOR>
   */
  queryNewBedNum: (id) => {
    return getRequest(`/cutBedSheet/newBedNum/${id}`);
  },

  /**
   *  查看详情
   */
  viewDetails:(id)=>{
      return getRequest(`/cutBedSheet/detail/${id}`);
  },

  /**
   * 获取裁床单编号  <AUTHOR>
   */
  getCBNo: () => {
    return getRequest('/cutBedSheet/getCBNo');
  },
};







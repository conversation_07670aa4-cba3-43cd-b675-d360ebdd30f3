<template>
    <cut-plan-form 
     ref="formRef"
     @submit="handleSubmit"
    />
</template>
<script setup>
import cutPlanForm from '/@/views/business/mes/tailor/cut-plan/cut-plan-form.vue'
import { ref } from 'vue'
import {cutPlanApi} from '/@/api/business/mes/tailor/cut-plan.js'
import { message } from 'ant-design-vue';
// ---------------------------- 获取表单数据 ----------------------------
const emit = defineEmits(['success'])
const formRef = ref()
async function handleSubmit() {
  try {
    const formData = formRef.value.getFormData();
    const cutBedsData = formRef.value.getCutBedsData();

    // 转换裁床数据
    const beds = cutBedsData.map(bed => {
      // 获取总裁数
      const qty = bed.leftTableData.reduce((sum, row) => {
         // 获取所有尺码列的值并求和
        const rowTotal = bed.leftColumns
        .filter(col => col.dataIndex.startsWith('size')) // 过滤出尺码列
        .reduce((sizeSum, col) => sizeSum + (row[col.dataIndex] || 0), 0); // 在这一行里面求和
        return sum + rowTotal;
      }, 0);
      // 转换尺码数据
      const sizes = bed.leftColumns
        .filter(col => col.dataIndex.startsWith('size'))
        .map(col => ({
          size: col.title,
          ratio: col.ratio || 0
        }));

      // 转换颜色数据
      const colors = bed.leftTableData.map(row => ({
        color: row.styleColor,
        planLayer: row.clothPutNum || 0,
        unitDosage: bed.unitDosage || 0
      }));

      // 计算总裁数
      const layer = colors.reduce((sum, color) => sum + color.planLayer, 0);
      return {
        remark: bed.remark,
        seq: bed.seq,
        cutNum: bed.cutNum,
        parts: bed.parts,
        qty,
        clothLen: bed.clothLen,
        layer,
        sizes,
        colors
      };
    });
    console.log('beds', beds)
    //提交数据
    const submitData = {
      ...formData,
      beds
    };
    // 验证必填字段
    if (!submitData.produceInstructOrderId) {
      message.error('请选择生产指令单');
      return;
    }
    if (!beds.length) {
      message.error('请至少添加一个裁床');
      return;
    }
    if (beds.some(bed => !bed.cutNum)) {
      message.error('请输入床次');
      return;
    }
    if (beds.some(bed => !bed.parts.length)) {
      message.error('请选择部位');
      return;
    }
    if (beds.some(bed => !bed.clothLen)) {
      message.error('请输入铺布长度');
      return;
    }
    // 提交数据
    await cutPlanApi.add(submitData);
    emit('success');
    message.success('添加成功');
    formRef.value.onClose();
  } catch (e) {
    message.error('提交失败');
  }
}
// ---------------------------- 暴露方法 ----------------------------
defineExpose({
  show: () => formRef.value.show()
})
</script>

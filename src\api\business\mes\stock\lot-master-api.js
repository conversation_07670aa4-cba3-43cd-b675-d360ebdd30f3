/**
 * 批号主档 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-03 19:28:09
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkLotMasterApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/stkLotMaster/queryPage', param);
  },
  /**
   * 查询列表  <AUTHOR>
   */
  queryLotList:(params)=>{
    return postRequest('/stkLotMaster/queryList',params);
  },
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/stkLotMaster/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/stkLotMaster/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/stkLotMaster/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/stkLotMaster/batchDelete', idList);
  },

};

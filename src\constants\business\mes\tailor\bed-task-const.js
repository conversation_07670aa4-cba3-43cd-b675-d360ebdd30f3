/**
 * 床次任务类型枚举常量
 * 
 * @Author:    assistant
 * @Date:      2025-07-22
 * @Copyright  zscbdic
 */

// 床次任务类型枚举
export const BED_TASK_TYPE_ENUM = {
  // 铺布任务
  SPREAD: {
    value: 'spreadTask',
    label: '铺',
    name: '铺布任务',
    color: 'blue',
    cssClass: 'bed-block-spread',
    style: {
      background: '#e6f7ff',
      border: '#1890ff',
      text: '#1890ff'
    }
  },
  // 计划
  PLAN: {
    value: 'plan',
    label: '计',
    name: '计划',
    color: 'orange',
    cssClass: 'bed-block-plan',
    style: {
      background: '#fff7e6',
      border: '#fa8c16',
      text: '#fa8c16'
    }
  },
  // 裁床单
  CUT: {
    value: 'cutBedSheet',
    label: '裁',
    name: '裁床单',
    color: 'green',
    cssClass: 'bed-block-cut',
    style: {
      background: '#f6ffed',
      border: '#52c41a',
      text: '#52c41a'
    }
  },
  // 获取方法
  getEnum(value) {
    if (value === 'spreadTask') {
      return BED_TASK_TYPE_ENUM.SPREAD;
    } else if (value === 'plan') {
      return BED_TASK_TYPE_ENUM.PLAN;
    } else if (value === 'cutBedSheet') {
      return BED_TASK_TYPE_ENUM.CUT;
    }
  },
  // 根据label获取枚举
  getEnumByLabel(label) {
    if (label === '铺') {
      return BED_TASK_TYPE_ENUM.SPREAD;
    } else if (label === '计') {
      return BED_TASK_TYPE_ENUM.PLAN;
    } else if (label === '裁') {
      return BED_TASK_TYPE_ENUM.CUT;
    }
  },
  // 获取所有枚举值
  getAllEnums() {
    return [
      BED_TASK_TYPE_ENUM.SPREAD,
      BED_TASK_TYPE_ENUM.PLAN,
      BED_TASK_TYPE_ENUM.CUT
    ];
  }
};

// 默认方块样式配置
export const DEFAULT_BLOCK_STYLE_ENUM = {
  DEFAULT: {
    value: 'default',
    label: '默认',
    style: {
      background: '#fafafa',
      border: '#d9d9d9',
      text: '#1890ff'
    }
  }
};

// 方块颜色类型枚举
export const BLOCK_COLOR_TYPE_ENUM = {
  // 单一类型
  SINGLE: {
    value: 'single',
    label: '单一类型',
  },
  // 多种类型
  MULTIPLE: {
    value: 'multiple',
    label: '多种类型',
  },
  // 获取方法
  getEnum(value) {
    if (value === 'single') {
      return BLOCK_COLOR_TYPE_ENUM.SINGLE;
    } else if (value === 'multiple') {
      return BLOCK_COLOR_TYPE_ENUM.MULTIPLE;
    }
  }
};

export default {
  BED_TASK_TYPE_ENUM,
  DEFAULT_BLOCK_STYLE_ENUM,
  BLOCK_COLOR_TYPE_ENUM,
};

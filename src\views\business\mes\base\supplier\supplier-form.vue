<!--
  * 供应商表
  *
  * @Author:    xmt
  * @Date:      2024-07-03 09:43:14
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="600px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="供应商名称" name="name">
        <a-input style="width: 100%" v-model:value="form.name" placeholder="供应商名称" />
      </a-form-item>
      <a-form-item label="供应商简称" name="fastName">
        <a-input style="width: 100%" v-model:value="form.fastName" placeholder="供应商简称" />
      </a-form-item>
      <a-form-item label="供应商类型" name="type">
        <dict-select v-model:value="form.type" keyCode="SUPPLIER_TYPE" width="100%" />
      </a-form-item>
      <a-form-item label="联系人" name="contact">
        <a-input style="width: 100%" v-model:value="form.contact" placeholder="联系人" />
      </a-form-item>
      <a-form-item label="电话" name="telephone">
        <a-input style="width: 100%" v-model:value="form.telephone" placeholder="电话" />
      </a-form-item>
      <a-form-item label="地址" name="address">
        <a-input style="width: 100%" v-model:value="form.address" placeholder="地址" />
      </a-form-item>
      <a-form-item label="结算方式" name="way">
        <a-select
            v-model:value="form.way"
            style="width: 100%"
            placeholder="結算方式"
            :allowClear="true"
            @change="settlementWayChange"
            :options="settlementWayOptions"
        />
      </a-form-item>
      <a-form-item label="等级" name="level">
        <a-rate v-model:value="form.level" :count="5" />
      </a-form-item>
      <a-form-item label="停用标识" name="enableFlag">
        <a-switch v-model:checked="form.enableFlag" :checkedValue="true" :unCheckedValue="false" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea style="width: 350px;height: 100px" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import {reactive, ref, nextTick, onMounted} from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import {dictApi} from "/@/api/support/dict-api.js";

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);
  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      // 浅拷贝
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }
  // -------------------------------------------
  const settlementWayOptions = ref()
  async function outstandingWayOptions(){
    let res = await dictApi.valueList('SUPPLIER_WAY');
    settlementWayOptions.value = res.data.map((item)=>{
      return {
        label: item.valueName,
        value: item.valueCode
      }
    })
  }

  function settlementWayChange(value){
    if(!value){
      form.way = ''
    }
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined,
    name: undefined, //供应商名称
    fastName: undefined, //供应商简称
    type: undefined, //供应商类型
    contact: undefined, //联系人
    telephone: undefined, //电话
    address: undefined, //地址
    way: undefined, //结算方式
    level: undefined, //等级;5星最高，无半星
    enableFlag: 0, //停用标识;0启用，1停用
    remark: undefined, //备注
  };

  let form = reactive({ ...formDefault });
  const rules = {
    name: [{ required: true, message: '供应商名称 必填' }],
    type: [{ required: true, message: '供应商类型 必填' }],
    level: [{ required: true, message: '等级 必填' }],
    enableFlag: [{ required: true, message: '停用标识 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await supplierApi.update(form);
      } else {
        await supplierApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });

  onMounted(()=>{
    outstandingWayOptions()
  })
</script>

<style>
  .ant-switch {
    border-color: rgb(132, 247, 166);
    /* 开关边框颜色 */
    background-color: rgb(132, 247, 166);
    /* 开关背景颜色 */
  }

  .ant-switch-checked {
    border-color: rgb(233, 112, 112) !important;
    /* 开启状态的开关边框颜色 */
    background-color: rgb(233, 112, 112) !important;
    /* 开启状态的开关背景颜色 */
  }
</style>

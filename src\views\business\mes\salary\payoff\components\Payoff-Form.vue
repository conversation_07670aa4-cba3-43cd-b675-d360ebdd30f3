<!--
  * 工资发放弹窗
  * @Author:    linwj
  * @Date:      2024-12-09 22:48:59
  * @Copyright  zscbdic
-->
<template>
  <a-modal v-model:open="payoffOpen"
           title="工资发放"
           width="400px"
           @ok="handlepayoffOpenOk">
    <a-divider />
    <a-row>
      <a-col :span="8">发放金额</a-col>
      <a-col :span="8" :offset="8" align="right">{{payoffAmount+" ￥"}}</a-col>
    </a-row>
    <br/>
    <a-row>
      <a-col :span="8">发放人数</a-col>
      <a-col :span="8" :offset="8" align="right">{{numberOfPayoff}}</a-col>
    </a-row>
    <a-divider />
    <a-form-item label="结算月份" class="smart-query-form-item">
      <a-date-picker v-model:value="belongDate"
                     picker="month"
                     format="YYYY-MM"/>
    </a-form-item>
  </a-modal>
</template>
<script setup>
import {ref, watch} from 'vue';
import _ from "lodash";
import {payoffApi} from "/@/api/business/mes/salary/payoff-api.js";
import {smartSentry} from '/@/lib/smart-sentry';
import {message} from "ant-design-vue";
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// -------------------- 处理发放弹窗 -------------------
const payoffOpen = ref(false)
//发放金额
const payoffAmount = ref(0)
//发放人数
const numberOfPayoff = ref(0)
//归属月份
const belongDate = ref(undefined)

watch(()=>belongDate.value, (newValue)=>{
  if(!_.isNull(newValue)){
    param.value.belongMonth = newValue
  }
})
function handlePayoff(rowData){
  if(_.isNil(rowData.totalSalary)){
    //若为批量发放
    payoffAmount.value = 0
    rowData.selectedRowList.map((item)=>{
      payoffAmount.value += item.totalSalary
    })
    numberOfPayoff.value = rowData.selectedRowKeyList.length
  }else {
    //若为单个发放
    payoffAmount.value = rowData.totalSalary
    numberOfPayoff.value = rowData.actualName
  }
  param.value.employeeIds = _.isNil(rowData.employeeId) ?
      rowData.selectedRowKeyList: [rowData.employeeId]
}

const param = ref({})
async function handlepayoffOpenOk() {
  try {
    await payoffApi.payoff(param.value).then(()=>{
      payoffOpen.value = false
      message.success('发放成功')
      emits('reloadList')
    })
  } catch (error) {
    smartSentry.captureError(error);
  }
}

function show(rowData) {
  if(!_.isNil(rowData)){
    payoffOpen.value = true;
    handlePayoff(rowData)
  }
}

defineExpose({
  show,
});
</script>
<template>
  <board-title :title="'报工仪表盘'" style="line-height: 70px; height: 70px"/>

  <a-card :body-style="{ padding: 0 }" style="padding: 12px; margin-bottom: 8px">
    <a-row>
      <a-col span="4">
        <span style="margin-right: 10px">车间:</span>
        <WorkshopSelect placeholder="请选择" style="width: 60%" v-model:value="queryForm.workShopId"/>
      </a-col>
      <a-col span="4">
        <span style="margin-right: 10px">小组:</span>
        <ProduceTeamSelect placeholder="请选择" style="width: 60%" v-model:value="queryForm.produceTeamId"/>  
      </a-col>
      <a-col span="16" style="display: flex; justify-content: flex-end; ">
        <a-radio-group button-style="solid" @change="onClickDate" v-model:value="dayChoice">
          <a-radio-button value="yesterday">昨天</a-radio-button>
          <a-radio-button value="today">今天</a-radio-button>
          <a-radio-button value="7days">近7天</a-radio-button>
          <a-radio-button value="30days">近30天</a-radio-button>
          <a-radio-button value="60days">近60天</a-radio-button>
        </a-radio-group>
        <a-range-picker :presets="defaultTimeRanges" style="width: 260px; margin-left: 6px" @change="onChangeDate" v-model:value="queryForm.reportTime" />
        <a-button type="primary" style="margin-left: 6px; width: 100px" @click="search">查询</a-button>
        <a-button  style="margin-left: 6px; width: 100px" @click="reset">重置</a-button>
      </a-col>
    </a-row>
  </a-card>

  <a-card style="margin-bottom: 8px">
    <div class="card-title">数据总览</div>
    <a-row :gutter="16">
      <a-col :span="6">
        <valueCard :title="'报工次数'" :value="workRecordCount" :unit="'次'"/>
      </a-col>
      <a-col :span="6">
        <valueCard :title="'工序产出'" :value="workRecordProduceNum" :unit="''"/>
      </a-col>
      <a-col :span="6">
        <valueCard :title="'平均产出'" :value="workAverageNum" :unit="''"/>
      </a-col>
      <a-col :span="6">
        <valueCard :title="'待审核'" :value="workRecordUnAuditNum" :unit="''"/>
      </a-col>
    </a-row>
  </a-card>


  <a-row :gutter="16" style="margin-bottom: 8px">
    <a-col :span="12">
      <process-pie ref="processPieRef"/>
    </a-col>
    <a-col :span="12">
      <output-ranking ref="outputRankingRef"/>
    </a-col>
  </a-row>

  <a-row :gutter="16">
    <a-col :span="24">
      <report-trend ref="reportTrendRef"/>
    </a-col>
  </a-row>
</template>


<script setup>

import BoardTitle from "/@/components/business/mes/board/board-title.vue";
import valueCard from "/src/views/business/mes/work/reprot-dashboard/components/value-card.vue";
import ProcessPie from "/@/views/business/mes/work/reprot-dashboard/components/process-pie.vue";
import OutputRanking from "/@/views/business/mes/work/reprot-dashboard/components/output-ranking.vue";
import ReportTrend from "/@/views/business/mes/work/reprot-dashboard/components/report-trend.vue";
import {defaultTimeRanges} from "/@/lib/default-time-ranges.js";
import ProduceTeamSelect from "/@/components/business/mes/factory/produce-team-select/index.vue";
import WorkshopSelect from "/@/components/business/mes/factory/workshop-select/index.vue";
import { ref, reactive, onMounted } from "vue";
import dayjs from "dayjs";
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';
import { workReportRecordApi } from "/src/api/business/mes/work/work-report-record-api.js";

const dayChoice = ref('30days');// 选择日期

const queryFormState = {
  workShopId: undefined,//车间id
  produceTeamId: undefined,//小组id
  reportTime: [], //报工时间
  reportBeginTime: getDateBeginAndEndTime(dayChoice.value).beginTime.format('YYYY-MM-DD 00:00:00'),//报工开始时间
  reportEndTime: getDateBeginAndEndTime(dayChoice.value).endTime.format('YYYY-MM-DD 23:59:59'),//报工结束时间
  workerId: undefined,//工人id
  workRecordStatus: undefined,//报工状态
  auditStatus: undefined,//审核状态
};

const RangeTimeState = {
  startTime: getDateBeginAndEndTime(dayChoice.value).beginTime.format('YYYY-MM-DD'),    
  endTime: getDateBeginAndEndTime(dayChoice.value).endTime.format('YYYY-MM-DD'),
}

const queryForm = reactive({...queryFormState});
const RangeTime = reactive({...RangeTimeState});



const workRecordCount = ref(0);//报工次数
const workRecordProduceNum = ref(0);//工序产出
const workRecordUnAuditNum = ref(0);//待审核数
const workAverageNum = ref(0);//平均产出

const processPieRef = ref();//工序产出饼图
const outputRankingRef = ref();//工序产出排名
const reportTrendRef = ref();//报工趋势

function queryAllData() {
    processPieRef.value.query(queryForm);
    outputRankingRef.value.query(queryForm);
    reportTrendRef.value.query(queryForm);
  Promise.all([
    workReportRecordApi.getWorkRecordCount(queryForm),
    workReportRecordApi.getWorkRecordProduceNum(queryForm),
    workReportRecordApi.getWorkRecordUnAuditedNum(RangeTime)
    
  ]).then(([workRecordCountRes, workRecordProduceNumRes, workRecordUnAuditNumRes]) => {
    workRecordCount.value = workRecordCountRes.data;
    workRecordProduceNum.value = workRecordProduceNumRes.data;
    workRecordUnAuditNum.value = workRecordUnAuditNumRes.data;
    workAverageNum.value =  workRecordCount.value > 0 ? (workRecordProduceNum.value / workRecordCount.value).toFixed(2) : 0;  
  });
}


 function search() { 
  try {
    SmartLoading.show();
    queryAllData();
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    SmartLoading.hide();
  }
}

function reset() {
  Object.assign(queryForm, queryFormState);
  dayChoice.value = '30days';
  queryForm.reportTime = [];
  queryAllData();
}


/**
 * 选择日期
 * @param dates
 * @param dateStrings
 */
function onChangeDate(dates, dateStrings) {
 queryForm.reportBeginTime = dayjs(dates[0]).format('YYYY-MM-DD 00:00:00');
  queryForm.reportEndTime = dayjs(dates[1]).format('YYYY-MM-DD 23:59:59');
  RangeTime.startTime = dayjs(dates[0]).format('YYYY-MM-DD');
  RangeTime.endTime = dayjs(dates[1]).format('YYYY-MM-DD');
}



async function onClickDate(e) {
  const date = getDateBeginAndEndTime(e.target.value);
  queryForm.reportBeginTime = date.beginTime.format('YYYY-MM-DD 00:00:00');
  queryForm.reportEndTime = date.endTime.format('YYYY-MM-DD 23:59:59');
  RangeTime.startTime = date.beginTime.format('YYYY-MM-DD');
  RangeTime.endTime = date.endTime.format('YYYY-MM-DD');
  search();
}



function getDateBeginAndEndTime(type) {
  if (type === 'yesterday') {
    return {
      beginTime: dayjs().subtract(1, 'day'),
      endTime: dayjs().subtract(1, 'day'),
    };
  } else if (type === 'today') {
    return {
      beginTime: dayjs(),
      endTime: dayjs(),
    };
  } else if (type === '7days') {
    return {
      beginTime: dayjs().subtract(7, 'day'),
      endTime: dayjs(),
    };
  } else if (type === '30days') {
    return {
      beginTime: dayjs().subtract(30, 'day'),
      endTime: dayjs(),
    };
  } else if (type === '60days') {
    return {
      beginTime: dayjs().subtract(60, 'day'),
      endTime: dayjs(),
    };
  }
}

onMounted(() => {
  queryAllData();
})
</script>

<style scoped lang="less">
.card-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: bold
}
</style>

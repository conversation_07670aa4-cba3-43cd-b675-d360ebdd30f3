<!--
  * app菜单
  *
  * @Author:    cjm
  * @Date:      2024-11-04 11:19:34
  * @Copyright  zscbdic
-->
<template>
  <a-drawer
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @close="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="类型" name="menuType">
        <!--        <a-input-number style="width: 100%" v-model:value="form.menuType" placeholder="类型"/>-->
        <a-radio-group v-model:value="form.menuType" button-style="solid">
          <a-radio-button :value="MENU_TYPE_ENUM.MENU.value">
            {{ MENU_TYPE_ENUM.MENU.desc }}
          </a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="菜单编码" name="menuCode">
        <a-input style="width: 100%" v-model:value="form.menuCode" placeholder="菜单编码"/>
      </a-form-item>
      <a-form-item label="菜单名称" name="menuName">
        <a-input style="width: 100%" v-model:value="form.menuName" placeholder="菜单名称"/>
      </a-form-item>
      <a-form-item label="菜单描述" name="menuDesc">
        <a-input v-model:value="form.menuDesc" placeholder="请输入菜单描述"/>
      </a-form-item>
      <a-form-item label="路由地址" name="path">
        <a-input v-model:value="form.path" placeholder="请输入路由地址"/>
      </a-form-item>
      <a-form-item label="菜单分类" name="classify">
        <dict-select v-model:value="form.classify" keyCode="APP_MENU_CLASSIFY" width="100%"/>
      </a-form-item>
      <a-form-item label="图标" name="iconSrc" help="图片项目内路径/base64编码/图片链接">
        <a-input v-model:value="form.iconSrc" placeholder="请输入图标"/>
      </a-form-item>
      <a-form-item label="显示状态" name="visibleFlag">

        <a-switch v-model:checked="form.visibleFlag" checked-children="显示" un-checked-children="不显示"/>
      </a-form-item>
      <a-form-item label="排序" name="sort" help="值越小越靠前">
        <a-input-number v-model:value="form.sort" :min="0" placeholder="请输入排序" style="width: 100px"/>
      </a-form-item>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import DictSelect from '/@/components/support/dict-select/index.vue';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {menuAppApi} from '/@/api/business/mes/system/menu/menu-app-api.js';
import {smartSentry} from '/@/lib/smart-sentry';
import {MENU_TYPE_ENUM} from "/@/constants/system/menu-const.js";



// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  menuCode: undefined, //菜单编码
  menuName: undefined, //菜单名称
  menuDesc: undefined,//菜单描述
  menuType: MENU_TYPE_ENUM.MENU.value, //类型
  path: undefined, //路由地址
  visibleFlag: true, //显示状态
  classify: undefined,//菜单分类
  sort: 0,//排序
  iconSrc: undefined,//图标
};

let form = reactive({...formDefault});

const rules = {

  menuCode: [{required: true, message: '菜单编码 必填'}],
  menuName: [{required: true, message: '菜单名称 必填'}],
  menuType: [{required: true, message: '类型 必填'}],
  classify: [{required: true, message: '菜单分类 必填'}],
  path: [{required: true, message: '路由地址 必填'}],
  visibleFlag: [{required: true, message: '显示状态 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await menuAppApi.update(form);
    } else {
      await menuAppApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

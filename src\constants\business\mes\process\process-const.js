/**
 * 工序库停用标识 枚举
 *
 * @Author:    xmt
 * @Date:      2024-07-13 15:19:57
 * @Copyright  zscbdic
 */


export const PROCESS_STATUS_ENUM = {
    ACTIVE : {
        value: false,
        desc: '启用',
        label: '启用',
        color: '#46c26f',
    },
    DISABLE: {
        value: true,
        desc: '停用',
        label: '停用',
        color: 'error',
    },
    getEnum(value) {
        if (value === false) {
            return  PROCESS_STATUS_ENUM.ACTIVE;
        } else if (value === true) {
            return PROCESS_STATUS_ENUM.DISABLE;
        }
    }
};

export default {
    PROCESS_STATUS_ENUM,
};

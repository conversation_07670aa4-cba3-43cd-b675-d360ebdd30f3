<template>
  <a-card>
    <div>报工分析 | 报工次数与报工产出趋势</div>
    <div style="color: #C0C4CC">{{ desc }}</div>
    <div class="echarts-box">
      <div class="report-trend-line" id="report-trend-line"></div>
    </div>
  </a-card>
</template>

<script setup>
import * as echarts from 'echarts';
import { onMounted, reactive } from "vue";
import { workReportRecordApi } from "/src/api/business/mes/work/work-report-record-api.js";
import dayjs from "dayjs";
import { smartSentry } from '/@/lib/smart-sentry';

let option = reactive({});
let myChart = null;

const props = defineProps({
  desc: {
    default: '更新于：' + dayjs().format('YYYY-MM-DD HH:mm:ss'),
  },
});

onMounted(() => {
  init();
});

async function query(params = {}) {
  try {
    const res = await workReportRecordApi.getWorkRecordCountAndNumLine(params);
    if (res.data) {
      // 提取日期作为 x 轴数据
      const dates = res.data.numLine.map(item => item.x);
      
      // 提取两条线的数据
      const numValues = res.data.numLine.map(item => item.y);
      const countValues = res.data.countLine.map(item => item.y);
      
      // 更新图表数据
      option.xAxis.data = dates;
      option.series[0].data = countValues; // 报工次数
      option.series[1].data = numValues;   // 报工产出数量
      myChart && myChart.setOption(option);
    }
  } catch (error) {
    smartSentry.captureError(error);
  }
}

function init() {
  option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['报工次数', '报工产出数量'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [] // 初始化为空数组，等待接口数据
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '报工次数',
        type: 'line',
        stack: 'Total',
        symbol: 'none',
        data: [], // 初始化为空数组，等待接口数据
        areaStyle: {
          color: 'rgba(82,168,250,0.4)'
        },
        lineStyle: {
          color: '#4ea9f9',
          width: 3,
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#4ea9f9',
        },
      },
      {
        name: '报工产出数量',
        type: 'line',
        stack: 'Total',
        data: [], // 初始化为空数组，等待接口数据
        symbol: 'none',
        lineStyle: {
          color: '#6dcb1f',
          width: 3,
        },
        areaStyle: {
          color: '#d4ecde'
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          color: '#6dcb1f',
        },
      },
    ]
  };
  
  let chartDom = document.getElementById('report-trend-line');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    option && myChart.setOption(option);
  }
  
  window.addEventListener("resize", () => {
    myChart && myChart.resize();
  });
}

defineExpose({
  query
})
</script>

<style scoped lang="less">
.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .report-trend-line {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

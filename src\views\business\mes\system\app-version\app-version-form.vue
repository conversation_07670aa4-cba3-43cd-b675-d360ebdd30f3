<!--
  * app版本管理
  *
  * @Author:    cjm
  * @Date:      2025-02-25 14:44:40
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 }">


      <a-form-item label="APP_ID" name="appId">
        <a-input style="width: 100%" v-model:value="form.appId" placeholder="app_id"/>
      </a-form-item>
      <a-form-item label="app名称" name="appName">
        <a-input style="width: 100%" v-model:value="form.appName" placeholder="app名称"/>
      </a-form-item>
      <a-form-item label="版本名称(号)" name="versionName">
        <a-input style="width: 100%" v-model:value="form.versionName" placeholder="版本名称" addon-before="V"/>
      </a-form-item>
      <a-form-item label="版本号排序" name="versionSort">
        <a-input-number style="width: 100%" v-model:value="form.versionSort" placeholder="版本号排序"  :min="0"/>
      </a-form-item>
      <a-form-item label="描述" name="versionDescription" help="富文本">
        <a-textarea style="width: 100%" v-model:value="form.versionDescription" placeholder="描述"/>
      </a-form-item>
      <a-form-item label="升级包类型" name="packageType">
        <a-select v-model:value="form.packageType" placeholder="升级包类型">
          <a-select-option v-for="item in PACKAGE_TYPE_ENUM.getOptions()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="是否强制更新" name="forceUpdate">
        <!--          <a-input-number style="width: 100%" v-model:value="form.forceUpdate" placeholder="是否强制更新;0否1是"/>-->
        <a-switch v-model:checked="form.forceUpdate"/>
      </a-form-item>
      <a-form-item label="安卓下载链接" name="androidUrl">
        <a-input style="width: 100%" v-model:value="form.androidUrl" placeholder="链接"/>
<!--        <file-upload v-model:value="form.url" :list-type="'text'" :multiple="false" :maxUploadSize="1" @change="form.url = $event" :accept="'.apk'" :max-size="150"/>-->
      </a-form-item>
<!--      <a-form-item label="备注" name="remark">-->
<!--        <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注"/>-->
<!--      </a-form-item>-->


    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {nextTick, reactive, ref} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {appVersionApi} from '/@/api/business/mes/system/app-version/app-version-api';
import {smartSentry} from '/@/lib/smart-sentry';
import {PACKAGE_TYPE_ENUM, PLATFORM_ENUM} from "/@/constants/business/mes/system/app-version-const.js";
import fileUpload from '/@/components/support/file-upload/index.vue'

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  remark: undefined, //备注
  appId: undefined, //app_id
  appName: undefined, //app名称
  androidUrl: undefined, //链接
  versionCode: undefined, //版本号
  versionName: undefined, //版本名称
  versionDescription: undefined, //描述
  packageType: '0', //包类型;0是整包升级，1是wgt升级
  forceUpdate: true, //是否强制更新;0否1是
  status: undefined, //状态;published（已发布）、draft（草稿）
  releaseDate: undefined, //发布日期
};

let form = reactive({...formDefault});

const rules = {
  appId: [{required: true, message: 'app_id 必填'}],
  appName: [{required: true, message: 'app名称 必填'}],
  androidUrl: [{required: true, message: '安卓下载链接 必填'}],
  versionName: [{required: true, message: '版本名称（号） 必填'}],
  versionSort: [{required: true, message: '版本号排序 必填'}],
  // versionDescription: [{required: true, message: '描述 必填'}],
  packageType: [{required: true, message: '升级包类型 必填'}],
  forceUpdate: [{required: true, message: '是否强制更新 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await appVersionApi.update(form);
    } else {
      await appVersionApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

<!--
  * 车间信息
  *
  * @Author:    xmt
  * @Date:      2024-07-09 10:31:51
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="400px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-form-item label="所属工厂"  name="factoryId">
          <a-select placeholder="所属工厂"
                ref="selectFactoryid"
                v-model:value="form.factoryId"
                 style="width: 100%"
                 :options="factoryIds"
            />
         </a-form-item>
         <a-form-item label="车间名称"  name="name">
           <a-input style="width: 100%" v-model:value="form.name" placeholder="车间名称" />
         </a-form-item>
         <a-form-item label="车间位置"  name="location">
           <a-input style="width: 100%" v-model:value="form.location" placeholder="车间位置" />
         </a-form-item>
         <a-form-item label="负责人"  name="managerId">
             <a-select placeholder="负责人"
                 ref="selectManagerid"
                 v-model:value="form.managerId"
                 style="width: 100%"
                 :options="managerIds"
            />
         </a-form-item>
         <a-form-item label="备注"  name="remark">
           <a-textarea style="width: 100%;height: 100px;" v-model:value="form.remark" placeholder="备注" />
         </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import {employeeApi} from "/@/api/system/employee-api.js";
  import { factoryApi} from "/@/api/business/mes/factory/factory-api.js";

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);
  //存放负责人信息和工厂信息
  const managerIds = ref([])
  const factoryIds = ref([])
  function mergeString(data,value,label) {
    return data.map((e)=>({
      value:e[value],
      label:e[label]
    }))
  }

  async function defaultList(){
    try{
      //请求参数
      let queryForm = {
        queryKey:""
      };
      let responseModel1 = await employeeApi.queryAll(queryForm);
      managerIds.value = mergeString(responseModel1.data,"employeeId","actualName")
      let responseModel2 = await factoryApi.queryList();
      factoryIds.value = mergeString(responseModel2.data,"id","name")
    } catch (e) {
      message.error('请求失败！');
      smartSentry.captureError("this is the:",e);
    }
  }
  defaultList()
  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);
  
  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
      
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
          id: undefined,
              remark: undefined, //备注
              factoryId: undefined, //所属工厂id
              name: undefined, //车间名称
              location: undefined, //车间位置
              managerId: undefined, //负责人id
              manager: undefined, //负责人姓名
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  factoryId: [{ required: true, message: '所属工厂 必填' }],
                  name: [{ required: true, message: '车间名称 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await workshopApi.update(form);
      } else {
        await workshopApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

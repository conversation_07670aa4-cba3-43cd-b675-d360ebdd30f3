<!--
  * 
  *角色 大模型能力
  *
-->
<template>
  <div>
    <div class="tree-header">
      <p>设置角色对应的大模型工具能力</p>
      <a-button v-if="selectRoleId" type="primary" @click="saveChange" v-privilege="'system:role:llmtool:update'"> 保存 </a-button>
    </div>
    <!-- 大模型工具分类展示 -->
    <div class="tool-container">
      <a-checkbox-group v-model:value="selectedToolIds">
        <ul class="category-list">
          <li v-for="category in categorizedTools" :key="category.type">
            <div style="border-bottom: 1px solid #f0f0f0; margin-bottom: 1.5%"><EditOutlined /> {{ category.typeName }}</div>
            <div class="tool-items">
              <div v-for="tool in category.tools" :key="tool.id" class="tool-item">
                <a-checkbox :value="tool.id">{{ tool.description }}</a-checkbox>
              </div>
            </div>
          </li>
        </ul>
      </a-checkbox-group>
    </div>
  </div>
</template>

<script setup>
  import { inject, ref, watch, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { roleLLMToolApi } from '/@/api/system/role-llmtool-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { dictApi } from '/@/api/support/dict-api.js';
  import { EditOutlined } from '@ant-design/icons-vue';

  let toolList = ref([]);
  let selectedToolIds = ref([]); // 存放所选工具ID
  let selectRoleId = inject('selectRoleId');
  let typeDict = ref([]); // 存放分类字典数据

  // 按类型分组工具，并添加类型中文名
  const categorizedTools = computed(() => {
    // 使用reduce直接分组并获取中文名
    const groupedByType = toolList.value.reduce((result, tool) => {
      // 如果这个type没有初始化，则创建一个对象
      if (!result[tool.type]) {
        // 找到对应的字典项获取中文名
        const typeItem = typeDict.value.find((item) => item.valueCode === tool.type);
        result[tool.type] = {
          type: tool.type,
          typeName: typeItem ? typeItem.valueName : tool.type,
          tools: [],
        };
      }

      // 添加工具到对应分类
      result[tool.type].tools.push(tool);
      return result;
    }, {});

    // 转换为数组并返回
    return Object.values(groupedByType);
  });

  watch(selectRoleId, () => getRoleLLMToolAbility(), {
    immediate: true,
  });

  // 获取角色的大模型工具能力
  async function getRoleLLMToolAbility() {
    if (!selectRoleId.value) {
      return;
    }
    try {
      // 先加载字典数据
      await loadTypeDict();

      let res = await roleLLMToolApi.getRoleLLMToolAbility(selectRoleId.value);
      let data = res.data;
      toolList.value = data.toolList || [];
      selectedToolIds.value = data.selectedToolIds || [];
    } catch (error) {
      smartSentry.captureError(error);
    }
  }

  // 加载分类字典
  async function loadTypeDict() {
    try {
      const res = await dictApi.valueList('AI_LLM_TOOL_TYPE');
      typeDict.value = res.data || [];
    } catch (error) {
      smartSentry.captureError(error);
    }
  }

  //保存
  async function saveChange() {
    let params = {
      roleId: selectRoleId.value,
      tools: selectedToolIds.value,
    };
    SmartLoading.show();
    try {
      await roleLLMToolApi.updateRoleLLMToolAbility(params);
      message.success('保存成功');
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

<style scoped lang="less">
  .tree-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0;
  }

  .tool-container {
    padding: 0 15px;
  }

  .category-list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 20px;
    }
  }

  .tool-items {
    padding: 5px 0;
  }

  .tool-item {
    margin: 10px 0;
    padding: 8px;
  }

  :deep(.ant-checkbox-group) {
    width: 100%;
  }
</style>

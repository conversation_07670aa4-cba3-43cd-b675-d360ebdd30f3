<!--
  * 薪酬发放记录表
  *
  * @Author:    cjm
  * @Date:      2024-12-03 21:37:41
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="员工" class="smart-query-form-item">
              <a-select
                  v-model:value="initOptEmp"
                  show-search
                  placeholder="员工选择"
                  style="width: 150px"
                  :options="operatorOptions"
                  :filter-option="filterOption"
                  @change="handleEmpChange"
              />
            </a-form-item>
            <a-form-item label="发放时间" class="smart-query-form-item">
              <a-range-picker v-model:value="queryForm.payoffTime"
                              :presets="defaultTimeRanges"
                              style="width: 150px"
                              @change="onChangePayoffTime" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
      <a-row class="smart-table-btn-block">
        <div class="smart-table-operate-block">
          <a-button @click="handleBatchCancel" type="primary" size="small" >
            <template #icon>
              <PlusOutlined />
            </template>
            批量取消发放
          </a-button>
          <a-button @click="handlePrint" type="primary" size="small" >
            <template #icon>
              <PlusOutlined />
            </template>
            导出工资条
          </a-button>
        </div>
      </a-row>
        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
                :scroll="{ x: 'max-content' }"
                :row-selection="rowSelection"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="handleSingleCancel(record)" type="link" style="color: red">取消发放</a-button>
                    </div>
                </template>
                <template v-if="column.dataIndex === 'belongMonth'">
                  {{record.belongMonth.split(' ')[0]}}
                </template>
              <template v-if="column.dataIndex === 'pieceAmount'">
                {{record.pieceAmount}}
                <a-button @click="handleQueryDetails(record)" type="link">查看</a-button>
              </template>
              <template v-if="column.dataIndex === 'otherAmount'">
                {{text}}
                <a-button @click="handleQueryOtherDetails(record)" type="link">查看</a-button>
              </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>
        <!-- 计件金额明细-->
        <PayoffRecordForm  ref="formRef" @reloadList="queryData"/>
        <!-- 其他项明细-->
        <PayoffRecordOtherField ref="otherFormRef" />
    </a-card>
    <PayoffRecordPrintModel ref="printRef"/>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { payoffRecordApi } from '/@/api/business/mes/salary/payoff-record-api.js';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import PayoffRecordForm from './payoff-record-form.vue';
import { defaultTimeRanges } from '/@/lib/default-time-ranges';
import {employeeApi} from "/@/api/system/employee-api.js";
import _ from "lodash";
import PayoffRecordPrintModel from './components/payoff-record-print-model/index.vue';
import PayoffRecordOtherField from "/@/views/business/mes/salary/payoff-record/components/payoff-record-otherField.vue";
// ---------------------------- 表格列 ----------------------------

const columns = ref([
    {
        title: '员工姓名',
        dataIndex: 'actualName',
        ellipsis: true,
        width: 60
    },
    {
        title: '归属月份',
        dataIndex: 'belongMonth',
        ellipsis: true,
        width: 100
    },
    {
        title: '总金额',
        dataIndex: 'totalAmount',
        ellipsis: true,
        width: 80
    },
    {
        title: '计件金额',
        dataIndex: 'pieceAmount',
        ellipsis: true,
        width: 100
    },
    {
        title: '其他项金额',
        dataIndex: 'otherAmount',
        ellipsis: true,
        width: 60
    },
    {
        title: '发放时间',
        dataIndex: 'payoffTime',
        ellipsis: true,
        width: 150
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      align: 'center',
      width: 80
    }
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
    payoffTime: [], //发放时间
    payoffTimeBegin: undefined, //发放时间 开始
    payoffTimeEnd: undefined, //发放时间 结束
    employeeId: undefined, //员工id
    pageNum: 1,
    pageSize: 10,
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    initOptEmp.value = undefined
    queryData();
}

// 查询数据
async function queryData() {
    tableLoading.value = true;
    try {
        let queryResult = await payoffRecordApi.queryPage(queryForm);
        tableData.value = queryResult.data.list;
        total.value = queryResult.data.total;
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        tableLoading.value = false;
    }
    if(!(_.isNil(selectedRowList.value)&&_.isEmpty(selectedRowList.value))){
      selectedRowKeyList.value.length = 0
      selectedRowList.value.length = 0
    }
}

function onChangePayoffTime(dates, dateStrings){
    queryForm.payoffTimeBegin = dateStrings[0];
    queryForm.payoffTimeEnd = dateStrings[1];
}
// ---------------------------- 处理取消发放 ----------------------------
//单个取消
function handleSingleCancel(rowData){
  showCancelConfirmation(rowData.id)
}
//批量取消
function handleBatchCancel(){
  if(_.isEmpty(selectedRowList.value)){
    message.warn("请选择数据")
    return;
  }
  showCancelConfirmation()
}
//弹窗提示
function showCancelConfirmation(recordId){
  Modal.confirm({
      title: '提示',
      content: '确定要取消发放这些数据吗?',
      okText: '确定',
      okType: 'danger',
      onOk() {
          requestCancel(recordId);
      },
      cancelText: '取消',
      onCancel() {},
    });
}

function requestCancel(recordId){
  let ids = _.isNil(recordId)? selectedRowKeyList.value : [recordId]

  payoffRecordApi.cancelPayoff(ids).then(()=>{
      message.success("取消发放成功")
      queryData()
  })
}
// ----------------- 处理多选 ------------------------
const selectedRowKeyList = ref([])
const selectedRowList = ref(undefined)
const rowSelection = {
  onChange: (selectedRowKeys,selectedRows) => {
    selectedRowKeyList.value = selectedRowKeys
    selectedRowList.value = selectedRows
  }
};

// --------------------------- 查看计件金额详情 --------------------------------
const formRef = ref();
function handleQueryDetails(rowData){
  formRef.value.show(rowData.id);
}

// --------------------------- 查看其他项金额详情 --------------------------------
const otherFormRef = ref()
function handleQueryOtherDetails(rowData){
  otherFormRef.value.show(rowData);
}
// ---------------------------- 处理操作人下拉 -------------------------------

//初始化员工下拉
const initOptEmp = ref()
const operatorOptions = ref([])

function handleEmpChange(empId) {
  queryForm.employeeId = empId
}

//操作者下拉框搜索
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

//查询所有员工
async function queryAllEmployee() {
  try {
    let res = await employeeApi.queryAll();
    operatorOptions.value = res.data.map((item) => {
      return  {
        value: item.employeeId,
        label: item.actualName
      }
    })
  } catch (e) {
    smartSentry.captureError(e);
  }
}


// ---------------------------- 打印工资条 ----------------------------
const printRef = ref();
function handlePrint(){
  if(_.isEmpty(selectedRowList.value)){
    message.warn("请选择数据")
    return;
  }
  try{
    printRef.value.downloadPdf(selectedRowList.value)
  }
  catch(error){
    message.error("导出失败，请稍后再试");
  }
}

onMounted(async () => {
  await queryData()
  await queryAllEmployee()
});
</script>

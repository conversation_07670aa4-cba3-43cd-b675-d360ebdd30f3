<!--
  * 季度表
  *
  * @Author:    lwt
  * @Date:      2024-07-05 15:45:34
  * @Copyright  zscbdic
-->
<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="addCategory()" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">

      </div>
    </a-row>

    <a-table :scroll="{ x: 1000 }" size="small" :dataSource="tableDataList" :columns="columns" rowKey="id" bordered
      :pagination="false" @expandedRowsChange="changeExand" :expanded-row-keys="expandedRowKeys"
      :expandRowByClick="true">
      <!-- 操作按钮 -->
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="addCategory(record.id, record)" type="link"
              v-privilege="`${privilegePrefix}category:addChild`">
              增加子分类
            </a-button>
            <a-button @click="addCategory(undefined,record)" type="link"
              >编辑
            </a-button>
            <a-button @click="confirmDeleteCategory(record.id)" danger type="link"
              >删除
            </a-button>

          </div>
        </template>
      </template>
    </a-table>
    <SeasonForm ref="formModal" @reloadList="reloadList" />
  </a-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { seasonApi } from '/@/api/business/mes/base/season-api';
import { smartSentry } from '/@/lib/smart-sentry';
import SeasonForm from './season-form.vue';

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '季度名称',
    dataIndex: 'seasonName',
    ellipsis: true,
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 200,
    align: 'center',
  },
]);

// 定义树表各项方法变量
// 定义树表列表数组
const tableLoading = ref(false);
const tableDataList = ref([])
const props = defineProps({
  // 分组类型
  categoryType: {
    type: String,
    default: '', // 设置默认值为 42
  },

  privilegePrefix: {
    type: String,
    default: '',
  },
});

// 发送异步获取树表数组
async function queryList() {
  try {
    tableLoading.value = true;
    // 请求树表参数
    let queryForm = {
      queryKey: "",
      parentId: 0,
      categoryType: props.categoryType,
    };
    let responseModel = await seasonApi.queryTree(queryForm);
    // 遍历 data 数组中的每个对象
    tableDataList.value = responseModel.data;
  } catch (e) {
    smartSentry.captureError("this is the:", e);
  } finally {
    tableLoading.value = false;
  }
}

const expandedRowKeys = ref([]);

function reloadList(parentId) {
  queryList();
  if (parentId) {
    expandedRowKeys.value.push(parentId);
  }
}

onMounted(queryList);
defineExpose({
  queryList,
});

function changeExand(val) {
  expandedRowKeys.value = val;
}
// ------------------------------ 添加 ------------------------------

const formModal = ref([]);
// 递归函数：根据 id 查找 fullName
function findFullNameById(tree, parentId) {
  for (const node of tree) {
    if (node.id === parentId) {
      return node.fullName;
    }
    if (node.children && node.children.length > 0) {
      const fullName = findFullNameById(node.children, parentId);
      if (fullName) {
        return fullName;
      }
    }
  }
  return null;
}
function addCategory(parentId,record) {
  let categoryType = props.categoryType;
  if (parentId == undefined && record !== undefined) {
    // 获取父级分类的 fullName
    const fullName = findFullNameById(tableDataList.value, record.parentId);
    if (fullName) {
      categoryType = fullName;
    } else {
      categoryType = '';
    }
  }
    formModal.value.show(categoryType, parentId, record);
}

// ------------------------------- 删除 -----------------------------

function confirmDeleteCategory(categoryId) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除当前分类吗?',
    okText: '确定',
    okType: 'danger',
    async onOk() {
      deleteCategory(categoryId);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}
async function deleteCategory(categoryId) {
  try {
    SmartLoading.show();
    await seasonApi.delete(categoryId);
    message.success('删除成功');
    queryList();
    formModal.value.getSelectValues(); // 重新渲染表单上级分类下拉选项
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

</script>

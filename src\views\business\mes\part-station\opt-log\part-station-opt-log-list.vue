<!--
  * 裁片驿站操作日志
  *
  * @Author:    cjm
  * @Date:      2024-10-07 19:47:06
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <!--  物料编号 指令单编号 操作类型 操作人-->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange"/>
      <!--操作者-->
      <a-form-item label="操作人" class="smart-query-form-item">
        <a-select
            v-model:value="initOptEmp"
            show-search
            placeholder="操作人"
            style="width: 100px"
            :options="operatorOptions"
            :filter-option="filterOption"
            @change="handleEmpChange"
        />
      </a-form-item>
      <!--操作类型-->
      <a-form-item label="操作类型" class="smart-query-form-item">
        <a-select
            v-model:value="initOptType"
            show-search
            placeholder="操作类型"
            style="width: 100px"
            :options="optTypeOptions"
            :filter-option="filterOption"
            @change="handleOptTypeChange"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :scroll="{x:1300}"
        :loading="tableLoading"
        :pagination="false"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
        @change="handleTableChange"
    >
      <template #bodyCell="{ text, record, column }">
        <!--操作类型-->
        <template v-if="column.dataIndex==='optType'">
          <a-tag :color=OPT_LOG_TYPE_ENUM.getEnum(text).color style="width: 65px;text-align: center">
            {{ OPT_LOG_TYPE_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartStationOptLogForm ref="formRef" @reloadList="queryData"/>

  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {partStationOptLogApi} from '/@/api/business/mes/part-station/opt-log/part-station-opt-log-api';
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';
import {smartSentry} from '/@/lib/smart-sentry';
import {OPT_LOG_TYPE_ENUM} from '/@/constants/business/mes/part-station/opt-log/opt-log-const.js';
import PartStationOptLogForm from './part-station-opt-log-form.vue';
import InsItemSelect from '/src/components/business/mes/produce/ins-item-select/index.vue';
import {employeeApi} from "/@/api/system/employee-api.js";
// ---------------------------- 表格列 ----------------------------
const columns = ref([
  {
    title: '指令单编号',
    dataIndex: 'instructOrderNumber',
    width: 135,
    ellipsis: true,
    // width: 130
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    width: 135,
    ellipsis: true,
    // ellipsis: false,
  },
  // {
  //   title: '物料名称',
  //   dataIndex: 'itemName',
  //   ellipsis: true,
  //   width: 135
  // },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    width: 50,
    align: "center",
  },
  {
    title: '颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    width: 80
  },
  {
    title: '尺寸',
    dataIndex: 'size',
    ellipsis: true,
    width: 80
  },
  {
    title: '部位',
    dataIndex: 'positions',
    ellipsis: true,
    width: 80
  },
  {
    title: '数量',
    dataIndex: 'num',
    ellipsis: true,
    width: 50,
    align: "center",
  },
  {
    title: '操作类型',
    dataIndex: 'optType',
    ellipsis: true,
    align: "center",
    width: 100,
  },
  {
    title: '操作描述',
    dataIndex: 'optDesc',
    ellipsis: true,
    width: 200
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    ellipsis: true,
  },
  {
    title: '操作时间',
    dataIndex: 'optTime',
    ellipsis: true,
    sorter: true,//启用排序
    sortDirections: ['ascend', 'descend'], // 允许升序和降序
  },
]);
const insItemSelectRef = ref()
//初始化操作人
const initOptEmp = ref(null)
//初始化操作类型
const initOptType = ref(null)
// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  instructOrderNumber: undefined, //指令单编号
  itemNumber: undefined, //物料编号
  optType: undefined, //操作类型
  operatorId: undefined, //操作人id
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{column: 'opt_time', isAsc: false}],

};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  //清空两个编号
  insItemSelectRef.value.clearAllNumber()
  initOptEmp.value = undefined
  initOptType.value = undefined
  queryForm.pageSize = pageSize;
  queryData();
}
//处理表格时间顺序变化事件
async function handleTableChange(pagination,filters,sorter){
    if(sorter&&sorter.field==='optTime'){
      queryForm.sortItemList = 
      [{
        column:'opt_time',
        isAsc:sorter.order==='ascend'
      }];
    }
    if(!sorter.order){
      queryForm.sortItemList=
      [{
        column:'opt_time',
        isAsc:false
      }]
    }
    await queryData();
  }
// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await partStationOptLogApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

//查询所有员工
async function queryAllEmployee() {
  try {
    let resp = await employeeApi.queryAll();
    outstandingOptOptions(resp.data)
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// ---------------------------- 处理两个编号 ----------------------------
function insItemChange(data) {
  // 回传数据
  queryForm.instructOrderNumber = data.instructOrderNumber
  queryForm.itemNumber = data.itemNumber
}

// ---------------------------- 处理操作人下拉 ----------------------------
const operatorOptions = ref([])

//输出操作者options
function outstandingOptOptions(tableData) {
  operatorOptions.value = []
  tableData.map((e) => (
      operatorOptions.value.push({
        value: e.employeeId,
        label: e.actualName
      })
  ))
}

function handleEmpChange(empId) {
  queryForm.operatorId = empId
}

//操作者下拉框搜索
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

// ---------------------------- 处理操作类型下拉 ----------------------------
const optTypeOptions = ref([])

//输出操作者options
function outstandingOptTypeOptions() {
  const optTypeEnums = Object.values(OPT_LOG_TYPE_ENUM);
  for (let i = 0; i < optTypeEnums.length - 1; i++) {
    optTypeOptions.value.push({
      value: optTypeEnums[i].value,
      label: optTypeEnums[i].label
    })
  }
}

function handleOptTypeChange(optType) {
  queryForm.optType = optType
}

onMounted(() => {
  queryData()
  queryAllEmployee()
  outstandingOptTypeOptions()
});

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求删除
async function requestDelete(data) {
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await partStationOptLogApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await partStationOptLogApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
</script>

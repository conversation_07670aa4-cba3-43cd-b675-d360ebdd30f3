/**
 * 薪酬发放记录表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-12-03 21:37:41
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const payoffRecordApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/payoffRecord/queryPage', param);
  },

  /**
   * 计件详情  <AUTHOR>
   */
  queryDetails: (id) => {
      return postRequest(`/payoffRecord/queryDetails/${id}`);
  },


  /**
   * 取消发放  <AUTHOR>
   */
  cancelPayoff: (param) => {
      return getRequest(`/payoffRecord/cancelPayoff?ids=${param}`);
  },

};

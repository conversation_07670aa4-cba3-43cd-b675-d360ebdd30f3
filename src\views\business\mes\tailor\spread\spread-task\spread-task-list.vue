<!--
  * 铺布任务
  *
  * @Author:    cjm
  * @Date:      2025-06-21 15:24:27
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.queryKey" placeholder="关键字查询"/>
      </a-form-item>
        <a-form-item label="生产指令单"  class="smart-query-form-item">
            <produce-instruct-order-select v-model:value="queryForm.produceInstructOrderId" :placeholder="请选择生产指令单" style="width: 200px" />
          </a-form-item>
      <!-- <a-form-item label="款号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.materialId" placeholder="款号"/>
      </a-form-item> -->
      <a-form-item label="床次" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.cutNum" placeholder="床次"/>
      </a-form-item>
      <a-form-item label="车间" class="smart-query-form-item">
         <workshop-select v-model:value="queryForm.workshopId" placeholder="请选择车间" style="width: 200px" />
      </a-form-item>
      <a-form-item label="小组" class="smart-query-form-item">
       <produce-team-select v-model:value="queryForm.teamId" placeholder="生产小组" style="width: 200px"/>
      </a-form-item>
      <a-form-item label="设备" class="smart-query-form-item">
       <equipment-select v-model:value="queryForm.equipmentId" placeholder="设备" style="width: 200px" :workshopId="queryForm.workshopId"/>
      </a-form-item>
      <a-form-item label="任务状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.taskStatus" style="width: 150px" placeholder="任务状态">
          <a-select-option v-for="item in SPREAD_TASK_STATUS_ENUM.getOptions()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="计划铺布开始时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.planBeginTime" :presets="defaultTimeRanges" style="width: 200px"
                        @change="onChangePlanBeginTime"/>
      </a-form-item>
      <a-form-item label="开始时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.realBeginTime" :presets="defaultTimeRanges" style="width: 200px"
                        @change="onChangeRealBeginTime"/>
      </a-form-item>
      <a-form-item label="完成人" class="smart-query-form-item">
        <employee-select v-model:value="queryForm.realCompleterId" placeholder="完成人"  :width="'200px'"/>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined/>
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined/>
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm()" type="primary" size="small">
          <template #icon>
            <PlusOutlined/>
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" danger size="small"
                  :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined/>
          </template>
          批量删除
        </a-button>
        <a-button @click="confirmBatchIssue" type="primary" size="small"
                  :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <SendOutlined/>
          </template>
          下发任务单
        </a-button>
        <a-button @click="confirmBatchReIssue" type="default" size="small"
                  :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <RollbackOutlined/>
          </template>
          反下达
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData"/>
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">

        <!-- 有图片预览时 注释解开并把下面的'picture'修改成自己的图片字段名即可 -->
        <!-- <template v-if="column.dataIndex === 'picture'">
            <FilePreview :fileList="text" type="picture" />
          </template> -->

        <!-- 使用字典时 注释解开并把下面的'dict'修改成自己的字典字段名即可 有多个字典字段就复制多份同理修改 不然不显示字典 -->
        <!-- 方便修改tag的颜色 orange green purple success processing error default warning -->
        <!-- <template v-if="column.dataIndex === 'dict'">
          <a-tag color="cyan">
            {{ text && text.length > 0 ? text.map((e) => e.valueName).join(',') : '暂无' }}
          </a-tag>
        </template> -->

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />
    </div>

    <SpreadTaskForm ref="formRef" @reloadList="queryData"/>

  </a-card>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {spreadTaskApi} from '/@/api/business/mes/tailor/spread-task-api.js';
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';
import {smartSentry} from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import SpreadTaskForm from './spread-task-form.vue';
import {defaultTimeRanges} from '/@/lib/default-time-ranges';
import {SPREAD_TASK_STATUS_ENUM} from "/@/constants/business/mes/tailor/spread-task-const.js";
import produceInstructOrderSelect from '/@/components/business/mes/produce/produce-instruct-order-select/index.vue'
import workshopSelect from '/@/components/business/mes/factory/workshop-select/index.vue';
import produceTeamSelect from '/@/components/business/mes/factory/produce-team-select/index.vue';
import equipmentSelect from '/@/components/business/mes/equipment/equipment-select/index.vue';
import employeeSelect from '/@/components/system/employee-obj-select/index.vue';
import { SendOutlined, RollbackOutlined } from '@ant-design/icons-vue';
//import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '编号',
    dataIndex: 'number',
    ellipsis: true,
  },
  {
    title: '生产指令单',
    dataIndex: 'produceInstructOrderNumber',
    ellipsis: true,
  },
  {
    title: '款号',
    dataIndex: 'materialSpuNumber',
    ellipsis: true,
  },
  {
    title: '款名',
    dataIndex: 'materialName',
    ellipsis: true,
  },

  {
    title: '床次',
    dataIndex: 'cutNumz',
    ellipsis: true,
  },
  {
    title: '车间',
    dataIndex: 'workshopName',
    ellipsis: true,
  },
  {
    title: '设备',
    dataIndex: 'equipmentName',
    ellipsis: true,
  },
  {
    title: '小组',
    dataIndex: 'teamName',
    ellipsis: true,
  },
  {
    title: '计划铺布开始时间',
    dataIndex: 'planBeginTime',
    ellipsis: true,
  },
  {
    title: '计划铺布结束时间',
    dataIndex: 'planEndTime',
    ellipsis: true,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    ellipsis: true,
    customCell: (record) => {
      let backgroundColor = '';
      let option = SPREAD_TASK_STATUS_ENUM.getEnum(record.taskStatus);
      if (option) {
        backgroundColor = option.color;
      } else {
        backgroundColor = '#909399';
      }
      return {
        style: {
          backgroundColor: backgroundColor,
          color: '#fff',
        },
      };
    },
    customRender: (text) => {
      let option = SPREAD_TASK_STATUS_ENUM.getEnum(text.value);
      if (option) {
        return option.label;
      }
      return text;
    },
  },
  {
    title: '开始时间',
    dataIndex: 'realBeginTime',
    ellipsis: true,
  },
  {
    title: '完成时间',
    dataIndex: 'realEndTime',
    ellipsis: true,
  },
  {
    title: '完成人名称',
    dataIndex: 'realCompleterName',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 90,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //关键字查询
  produceInstructOrderId: undefined, //生产指令单
  materialId: undefined, //款号
  cutNum: undefined, //床次
  workshopId: undefined, //车间
  teamId: undefined, //小组
  equipmentId: undefined, //设备ID
  taskStatus: undefined, //任务状态
  planBeginTime: [], //计划铺布开始时间
  planBeginTimeBegin: undefined, //计划铺布开始时间 开始
  planBeginTimeEnd: undefined, //计划铺布开始时间 结束
  realBeginTime: [], //开始时间
  realBeginTimeBegin: undefined, //开始时间 开始
  realBeginTimeEnd: undefined, //开始时间 结束
  realCompleterId: undefined, //完成人ID
  pageNum: 1,
  pageSize: 10,
  sortItemList: [{ column: 'create_time', isAsc: false }],
};
// 查询表单form
const queryForm = reactive({...queryFormState});
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await spreadTaskApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

function onChangePlanBeginTime(dates, dateStrings) {
  queryForm.planBeginTimeBegin = dateStrings[0];
  queryForm.planBeginTimeEnd = dateStrings[1];
}

function onChangeRealBeginTime(dates, dateStrings) {
  queryForm.realBeginTimeBegin = dateStrings[0];
  queryForm.realBeginTimeEnd = dateStrings[1];
}


onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data) {
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求删除
async function requestDelete(data) {
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await spreadTaskApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await spreadTaskApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量下发任务单 ----------------------------

// 批量下发任务单
function confirmBatchIssue() {
  Modal.confirm({
    title: '提示',
    content: '确定要下发选中的任务单吗?',
    okText: '下发',
    okType: 'primary',
    onOk() {
      requestBatchIssue();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量下发任务单
async function requestBatchIssue() {
  try {
    SmartLoading.show();
    await spreadTaskApi.issue(selectedRowKeyList.value);
    message.success('下发成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量反下达 ----------------------------

// 批量反下达
function confirmBatchReIssue() {
  Modal.confirm({
    title: '提示',
    content: '确定要反下达选中的任务单吗?',
    okText: '反下达',
    okType: 'default',
    onOk() {
      requestBatchReIssue();
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

//请求批量反下达
async function requestBatchReIssue() {
  try {
    SmartLoading.show();
    await spreadTaskApi.reIssue(selectedRowKeyList.value);
    message.success('反下达成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
</script>

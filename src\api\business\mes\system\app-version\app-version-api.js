/**
 * app版本管理 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-25 14:44:40
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const appVersionApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/appVersion/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/appVersion/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/appVersion/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/appVersion/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/appVersion/batchDelete', idList);
  },
  /**
   * 修改状态  <AUTHOR>
   */
  updateStatus: (param) => {
      return postRequest('/appVersion/updateStatus', param);
  },
};

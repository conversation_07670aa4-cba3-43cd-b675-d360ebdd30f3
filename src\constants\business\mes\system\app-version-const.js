/**
 * app版本管理 枚举
 *
 * @Author:    cjm
 * @Date:      2025-02-25 14:44:40
 * @Copyright  zscbdic
 */
export const PLATFORM_ENUM = {
    ANDROID: {
        value: 'android',
        label: '安卓',
    },

    getEnum(value) {
        return Object.values(PLATFORM_ENUM).filter(item => typeof item === 'object').find(item => item.value === value);
    },

    getOptions() {
        return Object.values(PLATFORM_ENUM).filter(item => typeof item === 'object')
    },
};

export const VERSION_STATUS_ENUM = {
    PUBLISHED: {
        value: 'published',
        label: '已发布',
    },

    UN_PUBLISHED: {
        value: 'un_publish',
        label: '未发布',
    },

    getEnum(value) {
        return Object.values(VERSION_STATUS_ENUM).filter(item => typeof item === 'object').find(item => item.value === value);
    },

    getOptions() {
        return Object.values(VERSION_STATUS_ENUM).filter(item => typeof item === 'object')
    },
};

export const PACKAGE_TYPE_ENUM = {
    FULL_UPGRADE: {
        value: '0',
        label: 'APK(全量升级)',
    },


    getEnum(value) {
        return Object.values(PACKAGE_TYPE_ENUM).filter(item => typeof item === 'object').find(item => item.value === value);
    },

    getOptions() {
        return Object.values(PACKAGE_TYPE_ENUM).filter(item => typeof item === 'object')
    },
};

export default {};

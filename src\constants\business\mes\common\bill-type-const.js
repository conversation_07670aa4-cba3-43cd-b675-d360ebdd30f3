/**
 * 单据类型枚举常量
 * <AUTHOR>
 * @date 2025-02-14
 */

export const BillTypeEnum = {
  /**
   * 其他入库单
   */
  OTHER_IN: {
    value: 'STOCK_OTHER_IN',
    desc: '其他入库单',
  },

  /**
   * 其他出库单
   */
  OTHER_OUT: {
    value: 'STOCK_OTHER_OUT',
    desc: '其他出库单',
  },

  /**
   * 生产用料清单
   */
  PRODUCE_MATERIAL: {
    value: 'PRODUCE_NEED_MATERIAL',
    desc: '生产用料清单',
  },

  /**
   * 生产领料单
   */
  STOCK_PICK_MATERIAL_OUT: {
    value: 'STOCK_PICK_MATERIAL_OUT',
    desc: '生产领料单',
  },

  /**
   * 生产退料单
   */
  STOCK_RETURN_MATERIAL_IN: {
    value: 'STOCK_RETURN_MATERIAL_IN',
    desc: '生产退料单',
  },

  /**
   * 生产入库单
   */
  STOCK_PRODUCE_IN: {
    value: 'STOCK_PRODUCE_IN',
    desc: '生产入库单',
  },

  /**
   * 生产退库单
   */
  STOCK_PRODUCE_OUT: {
    value: 'STOCK_PRODUCE_OUT',
    desc: '生产退库单',
  },

  /**
   * 生产指令单
   */
  PRODUCE_INSTRUCT_ORDER: {
    value: 'PRODUCE_INSTRUCT_ORDER',
    desc: '生产指令单',
  },

  PRODUCE_INSTRUCT_ORDER_CLOTHES:{
    value:'PRODUCE_INSTRUCT_ORDER_CLOTHES',
    desc:'生产成衣清单'
  },
  /**
   * 裁床单
   */
  CUT_BED_SHEET:{
    value:'CUT_BED_SHEET',
    desc:'裁床单'
  },
  getOptions(value) {
    if(value){
      for (const key in this) {
        if (this[key] && this[key].value === value) {
          return this[key].desc;
        }
      }
      return '';
    }
  },
};

export default {
  BillTypeEnum,
};

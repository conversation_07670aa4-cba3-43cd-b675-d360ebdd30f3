/**
 * 工资发放 api 封装
 *
 * @Author:    linwj
 * @Date:      2024-11-20 20:48:59
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const wageFieldApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryList : () => {
    return postRequest('/wageField/queryList');
  },

  /**
   * 添加  <AUTHOR>
   */
  add : (param) => {
    return postRequest('/wageField/add',param);

  },

  /**
   * 更新  <AUTHOR>
   */
  update : (param) => {
    return postRequest('/wageField/update',param);
  },

  /**
   * 单个删除  <AUTHOR>
   */
  delete : (id) => {
    return getRequest(`/wageField/delete/${id}`);
  },
};

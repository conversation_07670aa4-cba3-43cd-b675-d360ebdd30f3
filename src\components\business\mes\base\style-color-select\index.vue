<!--
  * 款式颜色选择组件
  * 返回所选颜色
  * @Author:    assistant
  * @Date:      2025-01-17
  * @Copyright  zscbdic
-->
<template>
  <a-select
    :options="options"
    :showSearch="true"
    :allowClear="true"
    v-model:value="selectValue"
    @change="onChange"
    :style="`width: ${width}`"
    placeholder="请选择颜色"
    :listHeight="150"
  >
    <template #dropdownRender="{ menuNode: menu }">
      <v-nodes :vnodes="menu" />
      <a-divider style="margin: 4px 0" />
      <a-space style="padding: 4px 8px;width:100%;display: flex;justify-content: flex-end;">
        <a-button @click.stop="showForm" class="smart-margin-left10">
          <template #icon>
            <PlusOutlined />
          </template>
          添加颜色
        </a-button>
      </a-space>
    </template>
  </a-select>

  <styleColorForm ref="formRef" @reloadList="queryData"/>
</template>

<script setup>
import { defineComponent, onMounted, ref, watch } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue';
import { styleColorApi } from '/@/api/business/mes/base/style-color-api.js';
import styleColorForm from "/@/views/business/mes/base/style-color/style-color-form.vue";

// 定义 VNodes 组件用于渲染下拉菜单
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

const formRef = ref();
function showForm(data) {
  formRef.value.show(data);
}

// =========== 属性定义 和 事件方法暴露 =============

const props = defineProps({
  value: {
    type: String,
    default: undefined
  },
  width: {
    type: String,
    default: '100%',
  },
  colorList: {
    type: Array,
    default: () => []
  },
});

const emit = defineEmits(['update:value', 'change']);

// =========== 请求颜色数据 =============
const options = ref([])

async function queryData() {
  try {
    const queryForm = {
      pageNum: 1,
      pageSize: 100,
    };
    let res = await styleColorApi.queryPage(queryForm);
    options.value = [];
    if (res.data?.list) {
      let filteredList = res.data.list;

      // 如果传入了 colorList，则只显示 colorList 中包含的颜色
      if (props.colorList && props.colorList.length > 0) {
        filteredList = res.data.list.filter(item =>
          props.colorList.includes(item.styleColor)
        );
      }

      options.value = filteredList.map((item) => {
        return {
          value: item.styleColor,
          label: item.styleColor,
          id: item.id,
        }
      });
    }
  } catch (error) {
    console.error('查询颜色数据失败:', error);
  }
}

onMounted(queryData)

// 监听 colorList 变化，重新查询数据
watch(
  () => props.colorList,
  (newValue) => {
    console.log('colorList changed:', newValue);
    queryData();
  },
  { deep: true }
);

// =========== 选择 监听、事件 =============

const selectValue = ref(props.value);
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = newValue;
  }
);

function onChange(value) {
  emit('update:value', value);
  emit('change', value);
}
</script>

/**
 * 松布计划物料明细 api 封装
 *
 * @Author:    linwj
 * @Date:      2025-06-22 21:42:26
 * @Copyright  weavewise.zscbdic.cn
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const loosenPlanDetailsApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/loosenPlanDetails/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/loosenPlanDetails/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/loosenPlanDetails/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/loosenPlanDetails/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/loosenPlanDetails/batchDelete', idList);
  },

};

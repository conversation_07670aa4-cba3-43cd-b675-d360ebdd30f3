<!--
  * 菲票
  *
  * @Author:    cjm
  * @Date:      2024-07-14 16:55:01
  * @Copyright  cjm
-->
<template>
  <a-modal
      :title="form.id ? '菲票信息' : '添加'"
      width="1000px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :label-col="{ span: 8}" :wrapperCol="{ span: 14 }" disabled>
          <a-row>
            <a-col :span="8">
                    <a-form-item label="指令单"  name="instructOrderName">
                      <a-input style="width: 100%" v-model:value="form.instructOrderName" placeholder="生产指令单名称" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="裁床单编号"  name="cutBedSheetNumber">
                      <a-input style="width: 100%" v-model:value="form.cutBedSheetNumber" placeholder="裁床单编号" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="裁床单名称"  name="cutBedSheetName">
                      <a-input style="width: 100%" v-model:value="form.cutBedSheetName" placeholder="裁床单名称" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="物料编号"  name="itemNumber">
                      <a-input style="width: 100%" v-model:value="form.itemNumber" placeholder="物料编号" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="规格型号"  name="model">
                      <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="物料名称"  name="itemName">
                      <a-input style="width: 100%" v-model:value="form.itemName" placeholder="物料名称" />
                    </a-form-item>
            </a-col>
<!--            <a-col :span="8">-->
<!--              <a-form-item label="物料属性"  name="attribute">-->
<!--                <a-input style="width: 100%" v-model:value="form.attribute" placeholder="0面料，1其他，2成衣" />-->
<!--              </a-form-item>-->
<!--            </a-col>-->
            <a-col :span="8">
              <a-form-item label="单位名称"  name="unitName">
                <a-input style="width: 100%" v-model:value="form.unitName" placeholder="物料单位名称" />
              </a-form-item>
            </a-col>
<!--            <a-col :span="8">-->
<!--                    <a-form-item label="物料类型"  name="category">-->
<!--                      <a-input style="width: 100%" v-model:value="form.category" placeholder="0半成品 1成品" />-->
<!--                    </a-form-item>-->
<!--            </a-col>-->
            <a-col :span="8">
                    <a-form-item label="扎号"  name="tieNum">
                      <a-input-number style="width: 100%" v-model:value="form.tieNum" placeholder="扎号" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="款式颜色"  name="styleColor">
                      <a-input style="width: 100%" v-model:value="form.styleColor" placeholder="款式颜色" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="尺码"  name="size">
                      <a-input style="width: 100%" v-model:value="form.size" placeholder="尺码" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="床次"  name="cutNum">
                      <a-input-number style="width: 100%" v-model:value="form.cutNum" placeholder="床次" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="数量"  name="num">
                      <a-input-number style="width: 100%" v-model:value="form.num" placeholder="数量" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="缸号"  name="lotNo">
                      <a-input style="width: 100%" v-model:value="form.lotNo" placeholder="缸号" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="部位"  name="positions">
                      <a-input style="width: 100%" v-model:value="form.positions" placeholder="部位" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="最后状态"  name="lastStatus">
                      <a-input style="width: 100%" v-model:value="form.lastStatus" placeholder="最后状态;保留" />
                    </a-form-item>
            </a-col>
            <a-col :span="8">
                    <a-form-item label="流转状态"  name="flowStatus">
                      <a-input style="width: 100%" v-model:value="form.flowStatus" placeholder="流转状态;保留" />
                    </a-form-item>
            </a-col>
          </a-row>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
          id: undefined,
              id: undefined, //主键
              instructOrderNumber: undefined, //生产指令单编号
              instructOrderName: undefined, //生产指令单名称
              instructOrderId: undefined, //生产指令单id
              cutBedSheetId: undefined, //裁床单id
              cutBedSheetNumber: undefined, //裁床单编号
              cutBedSheetName: undefined, //裁床单名称
              itemId: undefined, //物料id
              itemNumber: undefined, //物料编号
              model: undefined, //规格型号
              itemName: undefined, //物料名称
              unitId: undefined, //物料单位id
              unitName: undefined, //物料单位名称
              attribute: undefined, //物料属性;0面料，1其他，2成衣
              category: undefined, //物料类型;0半成品 1成品
              tieNum: undefined, //扎号
              styleColor: undefined, //款式颜色
              size: undefined, //尺码
              cutNum: undefined, //床次
              num: undefined, //数量
              lotNo: undefined, //缸号
              positions: undefined, //部位
              lastStatus: undefined, //最后状态;保留
              flowStatus: undefined, //流转状态;保留
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  // id: [{ required: true, message: '主键 必填' }],
                  instructOrderNumber: [{ required: true, message: '生产指令单编号 必填' }],
                  instructOrderId: [{ required: true, message: '生产指令单id 必填' }],
                  cutBedSheetId: [{ required: true, message: '裁床单id 必填' }],
                  cutBedSheetNumber: [{ required: true, message: '裁床单编号 必填' }],
                  itemId: [{ required: true, message: '物料id 必填' }],
                  itemNumber: [{ required: true, message: '物料编号 必填' }],
                  model: [{ required: true, message: '规格型号 必填' }],
                  itemName: [{ required: true, message: '物料名称 必填' }],
                  unitId: [{ required: true, message: '物料单位id 必填' }],
                  unitName: [{ required: true, message: '物料单位名称 必填' }],
                  attribute: [{ required: true, message: '物料属性;0面料，1其他，2成衣 必填' }],
                  category: [{ required: true, message: '物料类型;0半成品 1成品 必填' }],
                  tieNum: [{ required: true, message: '扎号 必填' }],
                  styleColor: [{ required: true, message: '款式颜色 必填' }],
                  size: [{ required: true, message: '尺码 必填' }],
                  cutNum: [{ required: true, message: '床次 必填' }],
                  num: [{ required: true, message: '数量 必填' }],
  };


  defineExpose({
    show,
  });
</script>

/**
 * 铺布任务 枚举
 *
 */


export const SPREAD_TASK_STATUS_ENUM = {
    PLAN: {
        value: 'PLAN',
        label: '计划中',
        color: '#409EFF'
    },
    ISSUE: {
        value: 'ISSUE',
        label: '下发',
        color: '#E6A23C'
    },
    DOING:{
        value: 'DOING',
        label: '进行中',
        color: '#409EFF'
    },
    COMPLETE: {
        value: 'COMPLETE',
        label: '完成',
        color: '#67C23A'
    },
    CANCEL: {
        value: 'CANCEL',
        label: '取消',
        color: 'red'
    },
    UNKNOWN: {
        value: 'UNKNOWN',
        label: '未知',
        color: 'red'
    },
    getOptions() {
        return Object.values(this).filter(item => typeof item === 'object')
    },
    getEnum(value) {
        // 遍历枚举对象的所有属性
        for (const key in this) {
            // 跳过原型链上的属性
            if (!Object.hasOwn(this, key)) continue;
            // 如果属性值是一个对象且包含 value 属性
            const item = this[key];
            if (typeof item === 'object' && item.value === value) {
                return item;
            }
        }
        // 如果未找到匹配的值，返回 UNKNOWN
        return this.UNKNOWN;
    }
}
export default {
    SPREAD_TASK_STATUS_ENUM
};

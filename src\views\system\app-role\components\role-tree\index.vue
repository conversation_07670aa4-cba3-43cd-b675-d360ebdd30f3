<!--
  * 角色 树形结构
  *
  * @Author:    linwj
  * @Date:      2024-11-14 22:34:00
  * @Copyright  zscbdic
  *
-->
<template>
    <div class="tree-header">
      <p>设置角色对应的功能操作、后台管理权限</p>
      <a-button v-if="selectRoleId" type="primary" @click="saveChange" v-privilege="'system:role:menu:update'"> 保存 </a-button>
    </div>
    <!-- 功能权限勾选部分 -->
    <li v-for="(module) in tree" :key="module.classify">
      <div style="border-bottom: 1px solid #f0f0f0;margin-bottom: 1.5%"><EditOutlined /> {{module.classifyLabel}}</div>
        <!--第二级-->
        <div v-for="item in module.menuList" :key="item.id"
             style="margin:0 0 1% 3%;border-bottom: 1px solid #f0f0f0;">
          <a-checkbox @change="selectSecondCheckbox(item)"
                      class="checked-box-label"
                      :value="item.id" :checked="selectedMenuId.indexOf(item.id)!==-1">{{item.menuName}}</a-checkbox>
          <!-- 如果有第三级按钮-->
          <div v-if="item.menuType === MENU_TYPE_ENUM.POINTS.value">
            <div v-for="item in item.menuList" :key="item.id"
                 style="margin-left: 4%">
              <a-checkbox @change="selectFirstCheckbox(module)" class="checked-box-label" :value="item.id">{{item.menuName}}</a-checkbox>
            </div>
          </div>
        </div>
    </li>
</template>
<script setup>
  import { inject, ref, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { roleMenuApi } from '/@/api/system/appRole-menu-api.js';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { smartSentry } from '/@/lib/smart-sentry';
  import {MENU_TYPE_ENUM} from "/@/constants/system/menu-const.js";



  let tree = ref();
  let selectRoleId = inject('selectRoleId');
  //存放所选菜单id
  const selectedMenuId = ref([])

  function selectFirstCheckbox(module){
    console.log(module)
  }

  function selectSecondCheckbox(module){
    if (selectedMenuId.value.indexOf(module.id)!==-1){
      let index = selectedMenuId.value.indexOf(module.id)
      console.log(index)
      selectedMenuId.value.splice(index,1)
    }else {
      selectedMenuId.value.push(module.id)
    }
    console.log(selectedMenuId.value)
  }

  watch(selectRoleId, () => getRoleSelectedMenu(), {
    immediate: true,
  });

  async function getRoleSelectedMenu() {
    if (!selectRoleId.value) {
      return;
    }
    let res = await roleMenuApi.getRoleSelectedMenu(selectRoleId.value);
    let data = res.data;
    selectedMenuId.value = data.selectedMenuId
    tree.value = data.menuList;
    console.log(selectedMenuId.value)
  }

  async function saveChange() {
    let params = {
      roleId: selectRoleId.value,
      menuIdList: selectedMenuId.value,
    };
    SmartLoading.show();
    try {
      await roleMenuApi.updateRoleMenu(params);
      message.success('保存成功');
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }
</script>
<style scoped lang="less">
  @import './index.less';
</style>

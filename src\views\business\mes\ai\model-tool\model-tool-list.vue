<!--
  * 大模型工具列表
  *
  * @Author:    lyq
  * @Date:      2025-04-27
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="分类" class="smart-query-form-item">
        <dict-select v-model:value="queryForm.type" keyCode="AI_LLM_TOOL_TYPE" width="150px" />
      </a-form-item>
      <a-form-item label="停用标识" class="smart-query-form-item"
        ><!--0启用，1停用-->
        <a-select v-model:value="queryForm.enableFlag" placeholder="请选择">
          <a-select-option value="true">启用</a-select-option>
          <a-select-option value="false">停用</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="confirmBatchDelete" type="primary" size="small" danger :disabled="selectedRowKeyList.length === 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'enableFlag'">
          <div>
            <a-tag :color="text === false ? 'red' : 'green'">{{ text === false ? '停用' : '启用' }}</a-tag>
          </div>
        </template>
        <template v-if="column.dataIndex === 'type'">
          <div>
            {{ getTypeDesc(text) }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <ModelToolForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { modelToolApi } from '/@/api/business/mes/ai/model-tool/model-tool-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import ModelToolForm from './model-tool-form.vue';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { dictApi } from '/@/api/support/dict-api.js';

  //------------------------------------字典 英译中------------------------------------
  const typeDict = ref([]);

  // 获取分类描述文字
  function getTypeDesc(typeValue) {
    if (!typeValue || !typeDict.value || typeDict.value.length === 0) {
      return typeValue;
    }
    const item = typeDict.value.find((item) => item.valueCode === typeValue);
    return item ? item.valueName : typeValue;
  }

  // 加载字典数据
  async function loadTypeDict() {
    try {
      const res = await dictApi.valueList('AI_LLM_TOOL_TYPE');
      typeDict.value = res.data || [];
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: 'Bean名称',
      dataIndex: 'beanName',
      ellipsis: false,
      width: 150,
      align: 'center',
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: false,
      width: 200,
      align: 'center',
    },
    {
      title: '分类',
      dataIndex: 'type',
      ellipsis: false,
      width: 200,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: false,
      width: 200,
      align: 'center',
    },

    {
      title: '停用标识',
      dataIndex: 'enableFlag',
      ellipsis: false,
      width: 100,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      ellipsis: false,
      width: 100,
      align: 'center',
      fixed: 'right',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    type: undefined, //大模型工具类型
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await modelToolApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(() => {
    queryData();
    loadTypeDict();
  });

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      await modelToolApi.delete(data.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await modelToolApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      selectedRowKeyList.value = [];
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

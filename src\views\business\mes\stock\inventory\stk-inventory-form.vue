<!--
  * 即时库存
  *
  * @Author:    cjm
  * @Date:      2025-01-14 15:20:23
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
          <a-row>
                    <a-form-item label="主键"  name="id">
                      <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" />
                    </a-form-item>
                    <a-form-item label="版本号"  name="version">
                      <a-input-number style="width: 100%" v-model:value="form.version" placeholder="版本号" />
                    </a-form-item>
                    <a-form-item label="仓库id"  name="warehouseId">
                      <a-input-number style="width: 100%" v-model:value="form.warehouseId" placeholder="仓库id" />
                    </a-form-item>
                    <a-form-item label="货位id"  name="locationId">
                      <a-input-number style="width: 100%" v-model:value="form.locationId" placeholder="货位id" />
                    </a-form-item>
                    <a-form-item label="物料id"  name="materielId">
                      <a-input-number style="width: 100%" v-model:value="form.materielId" placeholder="物料id" />
                    </a-form-item>
                    <a-form-item label="批次id"  name="lotId">
                      <a-input-number style="width: 100%" v-model:value="form.lotId" placeholder="批次id" />
                    </a-form-item>
                    <a-form-item label="sn ID"  name="snId">
                      <a-input-number style="width: 100%" v-model:value="form.snId" placeholder="sn ID" />
                    </a-form-item>
                    <a-form-item label="货主类型;保留"  name="ownerType">
                      <a-input style="width: 100%" v-model:value="form.ownerType" placeholder="货主类型;保留" />
                    </a-form-item>
                    <a-form-item label="货主id;保留"  name="ownerId">
                      <a-input-number style="width: 100%" v-model:value="form.ownerId" placeholder="货主id;保留" />
                    </a-form-item>
                    <a-form-item label="单位id"  name="unitId">
                      <a-input-number style="width: 100%" v-model:value="form.unitId" placeholder="单位id" />
                    </a-form-item>
                    <a-form-item label="实际库存"  name="qty">
                      <a-input-number style="width: 100%" v-model:value="form.qty" placeholder="实际库存" />
                    </a-form-item>
                    <a-form-item label="可用库存"  name="avbQty">
                      <a-input-number style="width: 100%" v-model:value="form.avbQty" placeholder="可用库存" />
                    </a-form-item>
                    <a-form-item label="锁定库存"  name="lockQty">
                      <a-input-number style="width: 100%" v-model:value="form.lockQty" placeholder="锁定库存" />
                    </a-form-item>
                    <a-form-item label="预计库存"  name="predictQty">
                      <a-input-number style="width: 100%" v-model:value="form.predictQty" placeholder="预计库存" />
                    </a-form-item>
          </a-row>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { stkInventoryApi } from '/@/api/business/mes/stock/stk-inventory-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
              id: undefined, //主键
              version: undefined, //版本号
              warehouseId: undefined, //仓库id
              locationId: undefined, //货位id
              materielId: undefined, //物料id
              lotId: undefined, //批次id
              snId: undefined, //sn ID
              ownerType: undefined, //货主类型;保留
              ownerId: undefined, //货主id;保留
              unitId: undefined, //单位id
              qty: undefined, //实际库存
              avbQty: undefined, //可用库存
              lockQty: undefined, //锁定库存
              predictQty: undefined, //预计库存
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  id: [{ required: true, message: '主键 必填' }],
                  warehouseId: [{ required: true, message: '仓库id 必填' }],
                  materielId: [{ required: true, message: '物料id 必填' }],
                  unitId: [{ required: true, message: '单位id 必填' }],
                  qty: [{ required: true, message: '实际库存 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await stkInventoryApi.update(form);
      } else {
        await stkInventoryApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

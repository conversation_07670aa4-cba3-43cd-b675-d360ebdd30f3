/**
 * ai 大模型工具api 封装
 *
 * @Author:    lyq
 * @Date:      2025
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const modelToolApi={
/*
分页查询
*/
queryPage: (param) => {
  return postRequest('/ai/LLMTool/queryPage', param);
},
/*
添加
*/
add: (param) => {
  return postRequest('/ai/LLMTool/add', param);
},
/*
更新
*/
update: (param) => {
  return postRequest('/ai/LLMTool/update', param);
},
/*
删除
*/
delete: (id) => {
  return getRequest(`/ai/LLMTool/delete/${id}`);
},
/*
批量删除
*/
batchDelete: (idList) => {
  return postRequest('/ai/LLMTool/batchDelete',idList);
}
}

<template>
<a-card title="模型路由设置">
  <a-form :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
    <a-form-item label="提示词" name="prompt">
      <a-textarea v-model:value="form.prompt" placeholder="请输入提示词" :rows="4" />
    </a-form-item>
    <a-form-item label="路由决策模型" name="modelId">
      <model-select v-model:value="form.modelId" placeholder="请选择路由决策模型" />
    </a-form-item>
    <a-form-item label="启用状态" name="enableFlag">
      <a-switch v-model:checked="form.enableFlag" />
    </a-form-item>

    <a-divider orientation="left">路由项配置</a-divider>

    <div v-for="(item, index) in form.items" :key="index" class="route-item">
      <a-card class="route-item-card" size="small" :title="`序号：${index+1}`">
        <template #extra>
          <a-button type="primary" danger size="small" @click="removeRouteItem(index)">删除</a-button>
        </template>
        <a-form-item label="路由条件" :name="['items', index, 'condition']">
          <a-textarea v-model:value="item.value" placeholder="请输入路由条件" />
        </a-form-item>
        <a-form-item label="目标模型" :name="['items', index, 'targetModelId']">
          <model-select v-model:value="item.modelId" placeholder="请选择目标模型" />
        </a-form-item>
      </a-card>
    </div>

    <a-form-item>
      <a-button type="dashed" block @click="addRouteItem">
        <plus-outlined /> 添加路由项
      </a-button>
    </a-form-item>
  </a-form>

  <template #extra>
    <a-button type="primary" @click="handleSubmit">保存</a-button>
    <a-button style="margin-left: 10px" @click="handleReset">重置</a-button>
  </template>
</a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from "vue";
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { aiSettingApi } from '/@/api/business/mes/ai/ai-setting-api.js';
import ModelSelect from '/@/components/business/mes/ai/model-select.vue';

const formState = {
  configId: undefined,
  prompt: undefined,
  modelId: undefined,
  enableFlag: false,
  items: []
}

const form = ref({...formState});

// 获取模型路由设置
const getModelRouteSetting = async () => {
  const res = await aiSettingApi.getModelRouteSetting();
  form.value = res.data;
  // 确保启用状态是布尔值
  form.value.enableFlag = Boolean(form.value.enableFlag);
  // 如果items为空，初始化为空数组
  if (!form.value.items) {
    form.value.items = [];
  }
}

// 添加路由项
const addRouteItem = () => {
  form.value.items.push({
    condition: '',
    targetModelId: ''
  });
}

// 删除路由项
const removeRouteItem = (index) => {
  form.value.items.splice(index, 1);
}

// 提交表单
const handleSubmit = async () => {
  const res = await aiSettingApi.updateModelRouteSetting(form.value);
  message.success('保存成功');
  await getModelRouteSetting(); // 重新获取最新数据
}

// 重置表单
const handleReset = () => {
  form.value = {...formState};
  getModelRouteSetting(); // 重新获取数据
}

// 组件挂载时获取模型路由设置
onMounted(() => {
  getModelRouteSetting();
})
</script>

<style scoped lang="less">
.route-item {
  margin-bottom: 16px;

  .route-item-card {
    background-color: #fafafa;
  }
}
</style>

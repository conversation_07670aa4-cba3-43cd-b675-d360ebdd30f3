/**
 * 客户表 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-03 09:43:14
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const customerApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/customer/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/customer/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/customer/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/customer/delete/${id}`);
  },
  /**
   * 获取客户编号  <AUTHOR>
   */
  getClientNo: (param) => {
    return getRequest('/customer/getClientNo', param);
  },

  /**
   * 获取客户名称下拉  <AUTHOR>
   */
  getCustomerName: (param) => {
    return postRequest('/customer/queryList', param);
  },
};

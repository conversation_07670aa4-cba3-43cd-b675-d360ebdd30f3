/**
 * 物料BOM表 api 封装
 *
 * @Author:    cyz
 * @Date:      2024-07-09 10:25:22
 * @Copyright  zscbdic
 */
import { create } from 'lodash';
import { postRequest, getRequest } from '/@/lib/axios';

export const bomApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/bom/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/bom/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/bom/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/bom/delete/${id}`);
  },
  /**
  * 更新 根据id查询  
  */
  queryById: (id) => {
    return getRequest(`/bom/${id}`);
  },
  /**
   * 根据id和版本号查询
   */
  queryIdAndVersion: (param) => {
    return postRequest(`/bom/query`,param);
  },
   /**
   * 根据bom编号查询所有版本号
   */
  queryVersionOfBom: (bomNumber) => {
    return getRequest(`/bom/query/${bomNumber}`);
  },
   /**
   * 新建版本
   */
  createNewVersion: (param) => {
    return postRequest(`/bom/copy`,param);
  },

  /**
   * 获取BOM编号  <AUTHOR>
   */
  getBOMNo: (param) => {
    return getRequest('/bom/getBOMNo',param);
  },
  /**
   * 获取BOM下拉列表  <AUTHOR>
   */
  queryList: (param) => {
    return getRequest('/bom/queryAll',param);
  },
  /**
   * 获取bom内物料列表  <AUTHOR>
   */
  queryBomItemList: (id) => {
    return getRequest(`/bomDetail/getBomDetailList/${id}`);
  },
};

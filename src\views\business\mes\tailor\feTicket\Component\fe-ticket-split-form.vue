<template>
  <a-modal title="拆分菲票" :open="visibleFlag" @cancel="onClose" @ok="onSubmit" :confirmLoading="loading" width="900px">
    <a-spin :spinning="loading">
      <a-form :model="form" :rules="rules" ref="formRef">
        <!-- 原菲票信息 -->
        <a-row :gutter="16" style="margin-bottom: 16px">
          <a-col :span="24">
            <a-descriptions title="原菲票信息" bordered size="small">
              <a-descriptions-item label="生产指令单编号" span="1">{{ originalTicket.instructOrderNumber }}</a-descriptions-item>
              <a-descriptions-item label="裁床单编号" span="1">{{ originalTicket.cutBedSheetNumber }}</a-descriptions-item>
              <a-descriptions-item label="物料编号" span="1">{{ originalTicket.itemNumber }}</a-descriptions-item>
              <a-descriptions-item label="物料名称" span="1">{{ originalTicket.itemName }}</a-descriptions-item>
              <a-descriptions-item label="颜色" span="1">{{ originalTicket.styleColor }}</a-descriptions-item>
              <a-descriptions-item label="数量" span="1">{{ originalTicket.num }}</a-descriptions-item>
              <a-descriptions-item label="部位" span="1">{{ originalTicket.positions }}</a-descriptions-item>
              <a-descriptions-item label="尺码" span="1">{{ originalTicket.size }}</a-descriptions-item>
            </a-descriptions>
          </a-col>
        </a-row>

        <a-row style="margin-bottom: 16px">
          <a-col :span="12">
            <a-button type="primary" size="small" @click="addSplitItem">
              <template #icon><PlusOutlined /></template>
              添加拆分项
            </a-button>
          </a-col>
          <a-col :span="12" style="text-align: right">
            <span>剩余未拆分数量：{{ remainingQty }}</span>
          </a-col>
        </a-row>

        <a-table size="small" :dataSource="form.items" :columns="columns" :pagination="false" bordered rowKey="key">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">
              {{ index + 1 }}
            </template>

            <template v-else-if="column.dataIndex === 'qty'">
              <a-form-item :name="['items', index, 'qty']" :rules="[{ required: true, message: '请输入数量' }]" style="margin-bottom: 0">
                <a-input-number v-model:value="record.qty" :min="1" :max="originalTicket.num" style="width: 100%" />
              </a-form-item>
            </template>

            <template v-else-if="column.dataIndex === 'size'">
              <a-form-item :name="['items', index, 'size']" :rules="[{ required: true, message: '请选择尺码' }]" style="margin-bottom: 0">
                <a-select v-model:value="record.size" style="width: 100%" placeholder="请选择尺码" :options="sizeOptions" allowClear />
              </a-form-item>
            </template>

            <template v-else-if="column.dataIndex === 'part'">
              {{ originalTicket.positions }}
            </template>

            <template v-else-if="column.dataIndex === 'action'">
              <a-button type="link" danger @click="removeSplitItem(record)"> 删除 </a-button>
            </template>
          </template>
        </a-table>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api.js';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emit = defineEmits(['reload-list']);

  // ---------------------------------------- 模态框显示 ----------------------------------------
  const visibleFlag = ref(false);
  const loading = ref(false);

  //关闭模态框
  const onClose = () => {
    visibleFlag.value = false;
    reset();
  };

  //显示模态框并初始化数据
  const show = async (record) => {
    loading.value = true;
    try {
      // 清空之前的数据
      reset(record);

      await fetchSizeOptions(record.instructOrderId);
      visibleFlag.value = true;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      loading.value = false;
    }
  };

  const formRef = ref(null);
  const originalTicket = ref({}); // 原菲票信息
  const form = reactive({
    id: null,
    items: [],
  });

  //------------------------------------获取尺码信息----------------
  const sizeOptions = ref([]);
  const fetchSizeOptions = async (instructOrderId) => {
    try {
      const res = await produceInstructOrderClothesApi.queryClothesSizeList(instructOrderId);
      sizeOptions.value = res.data?.map((size) => ({ label: size, value: size })) || [];
    } catch (error) {
      smartSentry.captureError(error);
      message.error('获取尺码列表失败');
    }
  };

  // ---------------------------------------- 拆分项管理 ----------------------------------------
  // 计算剩余数量
  const remainingQty = computed(() => {
    const totalAssigned = form.items.reduce((sum, item) => sum + (item.qty || 0), 0);
    return originalTicket.value.num - totalAssigned;
  });

  //添加拆分项
  const addSplitItem = () => {
    form.items.push({
      key: Date.now().toString(),
      qty: remainingQty.value > 0 ? remainingQty.value : 0,
      size: originalTicket.value.size || null,
      part: originalTicket.value.positions || '',
    });
  };

  //删除拆分项
  const removeSplitItem = (index) => {
    form.items.splice(index, 1);
  };

  // ---------------------------------------- 表格配置 ----------------------------------------
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: '8%',
      align: 'center',
    },
    {
      title: '数量',
      dataIndex: 'qty',
      width: '25%',
      align: 'center',
    },
    {
      title: '尺码',
      dataIndex: 'size',
      width: '25%',
      align: 'center',
    },
    {
      title: '部位',
      dataIndex: 'part',
      width: '30%',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: '12%',
      align: 'center',
    },
  ];

  // ---------------------------------------- 表单提交 ----------------------------------------
  //表单验证
  const rules = {
    items: [{ required: true, message: '请添加至少一个拆分项' }],
  };
  //提交表单
  const onSubmit = async () => {
    try {
      await formRef.value.validate();

      // 检查总数量是否匹配
      if (remainingQty.value !== 0) {
        message.error('拆分数量必须等于原菲票数量');
        return;
      }

      loading.value = true;

      //提交数据
      const submitData = {
        id: form.id,
        items: form.items.map((item) => ({
          qty: item.qty,
          size: item.size,
          part: originalTicket.value.positions,
        })),
      };

      const res = await feTicketApi.splitTicket(submitData);
      emit('reload-list');
      onClose();
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('提交失败，请检查表单');
    } finally {
      loading.value = false;
    }
  };

  //重置
  const reset = (record) => {
    originalTicket.value = record ? { ...record } : {};
    form.id = record?.id || null;
    form.items = [];
  };

  defineExpose({
    show,
  });
</script>

<style scoped>
  .ant-form-item {
    margin-bottom: 12px;
  }
</style>

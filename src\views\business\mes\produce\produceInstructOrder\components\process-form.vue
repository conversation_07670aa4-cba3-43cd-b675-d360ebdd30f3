<!--
  * 工序计划信息
  *
  * @Author:    fkf
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
    <div><strong>工序计划信息</strong></div>
  </a-row>
  <a-row>
    <a-col :span="2">
      <a-button type="primary" style="margin-right: 20px" @click="showItemModal()">添加</a-button>
    </a-col>
    <a-col :span="7">
      <a-form>
        <a-form-item label="选择工序计划" name="username">
          <a-select
            v-model:value="selectOrderProcess"
            style="width: 100%"
            :options="options"
            placeholder="请选择工序计划信息"
            @select="changeOption"
          />
        </a-form-item>
      </a-form>
    </a-col>
  </a-row>
  <process-select-modal ref="processModal" @select-data="selectItemData" />
  <br />
  <a-form ref="formPackage" :model="processList" :rules="rules">
    <a-table size="small" :dataSource="processList" :columns="columns" :pagination="false" bordered :scroll="{ x: 120 }">
      <template #bodyCell="{ text, record, column, index }">
        <template v-if="column.dataIndex === 'name'">
          <a-form-item class="tableItem" :name="[index, 'name']" :rules="rules.name">
            <a-input placeholder="请输入工序名称" v-model:value="record[column.dataIndex]" disabled />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'serialNumber'">
          {{ record.serialNumber }}
        </template>
        <template v-else-if="column.dataIndex === 'position'">
          <a-form-item class="tableItem" :name="[index, 'position']" :rules="rules.position">
            <a-input disabled placeholder="请输入工序部位" v-model:value="record[column.dataIndex]" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'processType'">
          <a-form-item class="tableItem" :name="[index, 'processType']" :rules="rules.processType">
            <dict-select disabled key-code="PROCESS_TYPE" v-model:value="record[column.dataIndex]" width="100%" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'overflowWorkFlag'">
          <a-switch v-model:checked="record.overflowWorkFlag" checked-children="允许" un-checked-children="不允许" />
        </template>
        <template v-if="column.dataIndex === 'shouldNum'">
          <a-form-item class="tableItem" :name="[index, 'shouldNum']" :rules="rules.shouldNum">
            <a-input placeholder="请输入数量" v-model:value="record[column.dataIndex]" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'standardTime'">
          <a-form-item class="tableItem" :name="[index, 'standardTime']" :rules="rules.standardTime">
            <a-input-number placeholder="请输入标准工时" v-model:value="record[column.dataIndex]" min="1" :precision="0">
              <template #addonAfter> 秒 </template>
            </a-input-number>
          </a-form-item>
        </template>
        <template v-else-if="['unitPrice1'].includes(column.dataIndex)">
          <a-input-number placeholder="请输入工价一" v-model:value="record[column.dataIndex]" min="0" prefix="￥" :precision="3" />
        </template>
        <template v-else-if="column.dataIndex === 'workshopId'">
          <a-form-item class="tableItem" :name="[index, 'workshopId']" :rules="rules.workshopId">
            <a-select v-model:value="record[column.dataIndex]" style="width: 100%" :options="workshopList" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'auditFlag'">
          <a-switch v-model:checked="record.auditFlag" checked-children="审核" un-checked-children="不审核" />
        </template>
        <template v-else-if="column.dataIndex === 'processControl'">
          <a-select v-model:value="record.processControl" style="width: 100%">
            <a-select-option value="0">自制</a-select-option>
            <a-select-option value="1">委外</a-select-option>
            <a-select-option value="2">不限</a-select-option>
          </a-select>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate">
            <a-button @click="deleteItem(record.key)" danger type="link">删除</a-button>
            <a-button type="link" @click="moveUp(record, index)" :disabled="index === 0">上移</a-button>
            <a-button type="link" @click="moveDown(record, index)" :disabled="index === (processList?.length || 0) - 1">下移</a-button>
          </div>
        </template>
      </template>
    </a-table>
  </a-form>
</template>
<script setup>
  import { onMounted, ref, watch } from 'vue';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { processLibraryApi } from '/@/api/business/mes/process/process-library-api';
  import ProcessSelectModal from '/@/components/business/mes/process/process-info-select-modal/index.vue';

  // -------------------定义通信事件--------------------------------
  const props = defineProps({
    value: [Array],
    totalNum: Number,
  });
  const emit = defineEmits(['update:value', 'change']);
  // -----------------------表单--------------------------------
  // 表单标识
  const formPackage = ref();
  const columns = ref([
    {
      title: '序号',
      dataIndex: 'serialNumber',
      width: 50,
      fixed: 'left',
    },
    {
      title: '工序名称(必填)',
      dataIndex: 'name',
      width: 120,
    },
    {
      title: '部位',
      dataIndex: 'position',
      width: 100,
    },
    {
      title: '工序类型',
      dataIndex: 'processType',
      width: 100,
    },
    {
      title: '车间',
      dataIndex: 'workshopId',
      width: 120,
    },
    {
      title: '是否审核',
      dataIndex: 'auditFlag',
      width: 100,
    },
    {
      title: '超数生产',
      dataIndex: 'overflowWorkFlag',
      width: 100,
    },
    {
      title: '工序控制',
      dataIndex: 'processControl',
      width: 100,
    },
    {
      title: '应生产数量',
      dataIndex: 'shouldNum',
      width: 100,
    },
    {
      title: '标准工时',
      dataIndex: 'standardTime',
      width: 100,
    },
    {
      title: '工价一',
      dataIndex: 'unitPrice1',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 150,
    },
  ]);
  const validateNum = async (_rule, value) => {
    if (value === '' || value === undefined || value === 0) {
      return Promise.reject('请输入数量');
    }
    return Promise.resolve();
  };
  // 表单校验规则
  const rules = ref({
    // 工序名称
    name: [{ required: true, message: '请输入工序名称', trigger: 'blur' }],
    // 工序类型
    processType: [{ required: true, message: '请输入工序类型', trigger: 'blur' }],
    // 车间
    workshopId: [{ required: true, message: '请输入车间', trigger: 'blur' }],
    // 工序控制
    processControl: [{ required: true, message: '请输入工序控制', trigger: 'blur' }],
    // 生产数量
    shouldNum: [{ required: true, message: '请输入生产数量', trigger: 'blur', validator: validateNum }],
    // 标准工时
    standardTim: [{ required: true, message: '请输入标准工时', trigger: 'blur', validator: validateNum }],
  });
  // 校验方法
  const handleValidate = async () => {
    let checkRules = await formPackage.value.validate();
  };
  // 重置表单
  function reset() {
    // 清空所有数据
    processList.value = [];
    selectOrderProcess.value = null;
    options.value = [];
    // 重置表单验证状态
    if (formPackage.value) {
      formPackage.value.resetFields();
    }
    // 重新初始化下拉选项
    queryData();
    // 触发更新
    emit('change', processList.value);
    emit('update:value', processList.value);
  }
  // ---------------------- 请求事件 ----------------------------
  // 查询下拉工序
  const selectOrderProcess = ref(null);
  const options = ref([]);
  async function queryData() {
    try {
      let data = await processLibraryApi.queryList();
      data.data.forEach((item) => {
        options.value.push({
          id: item.id,
          value: item.id,
          label: item.name,
        });
      });
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // 查询工序库id
  const processList = ref([]);
  async function queryId(id) {
    try {
      let data = await processLibraryApi.queryById(id);
      //  获取信息，全部赋值表单中 应生产数量继承props.totalNum
      processList.value = data.data.processLibraryDetailsVOList.map((item) => ({
        ...item,
        shouldNum: props.totalNum,
      }));
      emit('change', processList.value);
      emit('update:value', processList.value);
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // 下拉工序选择请求
  const changeOption = (value, option) => {
    queryId(option.id);
  };
  // 查询车间列表
  const workshopList = ref([]); //车间
  async function queryWorkshopList() {
    try {
      let queryResult = await workshopApi.queryList({});
      workshopList.value = queryResult.data.map((item) => ({
        value: item['id'],
        label: item['name'],
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  // ---------------------------删除--------------------------------
  function deleteItem(key) {
    // 查找真正的索引位置
    const index = processList.value.findIndex((item) => item.key === key);
    if (index !== -1) {
      processList.value.splice(index, 1);
      // 重新排序 key
      processList.value = processList.value.map((item, idx) => ({
        ...item,
        key: idx,
      }));
      emit('change', processList.value);
    }
  }

  //-----------------------------行上下移功能---------------------------
  //上移
  function moveUp(record, index) {
    // 交换当前行与上一行
    [processList.value[index], processList.value[index - 1]] = [processList.value[index - 1], processList.value[index]];

    updateSerialNumbers();
    emit('change', processList.value);
  }

  //下移
  function moveDown(record, index) {
    // 交换当前行与下一行
    [processList.value[index], processList.value[index + 1]] = [processList.value[index + 1], processList.value[index]];

    updateSerialNumbers();
    emit('change', processList.value);
  }

  // 重新排序序号和key
  function updateSerialNumbers() {
    processList.value.forEach((item, index) => {
      item.serialNumber = index + 1;
      item.key = index;
    });
  }
  //--------------------------- 工序库引用逻辑 ---------------------
  const processModal = ref();
  // 显示工序库引用弹窗
  function showItemModal(record) {
    processModal.value.showModal();
  }
  // 选择工序
  function selectItemData(selectedItems) {
    if (!selectedItems.length) return;
    selectedItems.forEach((data) => {
      const newData = {
        ...data,
        key: processList.value.length,
        processId: data.id,
        shouldNum: props.totalNum,
        auditFlag: false,
        overflowWorkFlag: false,
        serialNumber: processList.value.length + 1,
      };
      processList.value.push(newData);
    });
    // 重新排序序号和key
    processList.value.forEach((item, index) => {
      item.key = index;
      item.serialNumber = index + 1;
    });
    // 触发更新
    emit('change', processList.value);
  }
  // ---------------------------挂载监听事件--------------------------------
  // 监听工序列表
  watch(
    () => props.value,
    (value) => {
      processList.value = value ? [...value] : [];
      queryWorkshopList();
    },
    { deep: true, immediate: true }
  );
  // 监听工序列表
  watch(
    () => processList.value.length,
    () => {
      const plainArray = processList.value;
      emit('change', plainArray);
    },
    { immediate: true }
  );
  // 监听数量总数
  watch(
    () => props.totalNum,
    () => {
      processList.value.forEach((item) => {
        item.shouldNum = props.totalNum;
      });
    }
  );
  onMounted(() => {
    queryData();
    // 监听props.value的变化，确保新数据也有key
    watch(
      () => props.value,
      (value) => {
        if (value) {
          processList.value = value.map((item, index) => {
            if (item.key === undefined) {
              return { ...item, key: index };
            }
            return item;
          });
        } else {
          processList.value = [];
        }
      },
      { deep: true, immediate: true }
    );
  });
  // 暴露方法
  defineExpose({
    handleValidate,
    reset,
  });
</script>
<style lang="less" scoped>
  // 表单元素样式
  .tableItem {
    margin-top: 30px;
  }
  .toRequired {
    align-items: center;
    color: #ff4949;
    padding-right: 10px;
  }
</style>

/**
 * 生产指令单统计 api
 *
 * @Author:    linwj
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */
import { postRequest } from '/@/lib/axios';

export const produceInstructOrderStatsApi = {

    /**
     * 查询今日新单
     */
    queryNewOrder: (param) => {
        return postRequest('/produceInstructOrderStats/instructNum', param);
    },

    /**
     * 查询今日下达
     */
    queryIssueOrder : (param) => {
        return postRequest('/produceInstructOrderStats/issuedNum', param);
    },

    /**
     * 查询完成数
     */
    queryFinishOrder: (param) => {
        return postRequest('/produceInstructOrderStats/finishedNum', param);
    },

    /**
     * 查询工单数/现有指令单数量
     */
    queryPlanOrder: (param) => {
        return postRequest('/produceInstructOrderStats/instructNum', param);
    },

    /**
     * 查询进行中指令单数
     */
    queryDoingOrder: (param) => {
        return postRequest('/produceInstructOrderStats/onGoingNum', param);
    },

    /**
     * 查询超时指令单数量
     */
    queryOverTimeOrder: (param) => {
        return postRequest('/produceInstructOrderStats/timeout', param);
    },

    /**
     * 查询指令单每日变化趋势
     */
    queryOrderChange : (param) => {
        return postRequest('/produceInstructOrderStats/perDayOrdersNum', param);
    },

    /**
     * 查询指令单优先级
     */
    queryOrderPieChart : (param) => {
        return postRequest('/produceInstructOrderStats/priorityNum', param);
    },
};

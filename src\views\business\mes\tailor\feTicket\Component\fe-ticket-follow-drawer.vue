<template>
  <a-drawer
    v-model:open="drawerOpen"
    class="custom-class"
    root-class-name="root-class-name"
    style="color: red; background-color: #F5F6F8;"
    title="菲票追踪"
    placement="right"
    ref="FllowDrawerRef"
    :bodyStyle="{ marginLeft: '-40px' }"
  >
    <a-list
      item-layout="horizontal"
      :data-source="drawerData"
      style="width: 110%"
      :split="false"
      :loading="loading"
    >
     <!-- 定义 loadMore 插槽 -->
     <template #loadMore>
        <div
          v-if="!loading && total > drawerData.length"
          style="text-align: center; margin-top: 12px; height: 32px; line-height: 32px"
        >
          <a-button @click="onLoadMore">加载更多</a-button>
        </div>
      </template>
      <!-- 定义 renderItem 插槽 -->
      <template #renderItem="{ item }">
        <a-list-item style="margin-top: -5%;">
          <a-row style="margin-top: 10px;">
            <a-col :span="24">
              <a-list-item-meta>
                <template #title>
                  <div style="display: flex; justify-content: space-between;">
                    <span style="font-size: 16px; font-weight: 700;">{{ item.optContent }}</span>
                    <span style="font-size: 15px; color: rgb(151, 146, 152)">{{ item.createTime }}</span>
                  </div>
                </template>
                <template #avatar>
                  <CaretUpOutlined style="font-size: 16px; color: #1890ff" />
                </template>
              </a-list-item-meta>
            </a-col>
            <a-col :span="24">
              <a-card style="margin-bottom: 20px; color: rgb(151, 146, 152); width: 350px">
                <template #title>
                  <div style="display: flex; align-items: center;">
                    <a-avatar style="background-color: #87d068; margin-right: 8px;" size="small">
                      <template #icon>
                        <UserOutlined />
                      </template>
                    </a-avatar>
                    {{ item.operatorName }}
                  </div>
                </template>
                <p>{{ item.optDesc }}</p>
                <p v-if="item.bizNumber !== null">业务编号：{{ item.bizNumber }}</p>
                <p v-if="item.bizType !== null">业务类型：{{ BillTypeEnum.getOptions(item.bizType) }}</p>
                <p>操作时间:{{ item.optTime }}</p>
              </a-card>
            </a-col>
          </a-row>
        </a-list-item>
      </template>
    </a-list>
  </a-drawer>
</template>

<script lang="ts" setup>
import { nextTick, reactive, ref } from 'vue';
import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const';
import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api';

const queryFormState = {
  pageNum: 1,
  pageSize: 10,
  feTicketId: undefined,
  sortItemList: [{ column: 'create_time', isAsc: false }],
};

// 查询表单form
const queryForm = reactive({ ...queryFormState });

// 卡片数据数组
let drawerData = reactive([]);

// 是否显示
const drawerOpen = ref(false);

// 卡片总数
const total = ref(0);

// 加载状态
const loading = ref(false);

const show = async (data) => {
  // 初始化卡片数据数组
  drawerData.splice(0, drawerData.length);
  Object.assign(queryForm, queryFormState);
  queryForm.feTicketId = data.id;
  queryData();
  nextTick(() => {
    drawerOpen.value = true;
  });
};

async function queryData() {
  const result = await feTicketApi.trace(queryForm);
  result.data.list.forEach((item) => {
    const newData = {
      optContent: item.optContent,
      operatorName: item.operatorName,
      optDesc: item.optDesc,
      optTime: item.optTime,
      createTime: formatRelativeTime(new Date(item.createTime)),
      bizNumber: item.bizNumber,
      bizType: item.bizType,
    };
    drawerData.push(newData);
  });
  total.value = result.data.total;
}
// 时间转换函数
function formatRelativeTime(date) {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) {
    return `${seconds}秒前`;
  } else if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else {
    return `${days}天前`;
  }
}

// 加载更多功能
const onLoadMore = () => {
  queryForm.pageNum += 1;
  queryData();
};
defineExpose({
  show,
});
</script>
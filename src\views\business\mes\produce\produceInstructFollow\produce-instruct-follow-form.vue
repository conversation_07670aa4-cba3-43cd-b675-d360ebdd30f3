<!--
  * 生产跟单
  *
  * @Author:    lwj
  * @Date:      2024-07-26 21:36:36
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑跟单' : '添加'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :label-col="{ span: 5, offset: 1 }">
      <a-form-item label="跟单员" name="employeeId">
        <a-select
          ref="memberInfos"
          v-model:value="form.employeeId"
          mode="multiple"
          style="width: 100%"
          placeholder="请选择"
          :options="employees"
          :filter-option="filterOption"
          @change="onChange"
        />
      </a-form-item>

      <a-form-item label="委派人" name="delegateId">
        <a-select
          ref="memberInfos"
          v-model:value="form.delegateId"
          style="width: 100%"
          placeholder="请选择"
          :allowClear="true"
          :options="employees"
          :filter-option="filterOption"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { produceInstructFollowApi } from '/@/api/business/mes/produce/produce-instruct-follow-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { employeeApi } from '/@/api/system/employee-api.js';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api.js';

  // ------------------------ 获取所有员工信息 ------------------------
  const employees = ref([]);

  // 拼装员工信息函数
  function mergeString(data, value, label) {
    return data.map((e) => ({
      value: e[value],
      label: e[label],
    }));
  }

  async function defaultList() {
    try {
      // 请求参数
      let queryForm = {
        queryKey: '',
      };
      let responseModel1 = await employeeApi.queryAll(queryForm);
      employees.value = mergeString(responseModel1.data, 'employeeId', 'actualName');
    } catch (e) {
      message.error('请求失败！');
      smartSentry.captureError('this is the:', e);
    }
  }
  defaultList();

  function onChange(data) {
    form.employeeId = [];
    data.map((item) => {
      form.employeeId.push(item);
    });
    console.log(form.employeeId);
  }

  //新增，编辑搜索功能
  const filterOption = (input, option) => {
    return option.label.indexOf(input) >= 0;
  };
  // ------------------------ 委派人 ------------------------

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    console.log('传入的rowData:', rowData);
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      console.log('合并前的form:', form);
      Object.assign(form, rowData);
      console.log('合并后的form:', form);
      //先清空再添加
      form.employeeId = [];
      if (rowData.employeeId != null) {
        rowData.employeeId.map((item) => {
          form.employeeId.push(item);
        });
      }
    }

    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    // id: undefined,
    id: undefined, //主键
    employeeId: [], //跟单员id
    instructOrderId: undefined, //指令单id
    delegateId: undefined, //委派人id
  };

  let form = reactive({ ...formDefault });

  // 点击确定，验证表单
  async function onSubmit() {
    console.log(form);
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      await produceInstructFollowApi.update(form);
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

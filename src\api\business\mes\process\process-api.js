/**
 * 工序信息 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-13 15:19:57
 * @Copyright  zscbdic
 */
import { postRequest, getRequest,getDownload } from '/@/lib/axios';

export const processApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/process/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/process/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/process/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/process/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/process/batchDelete', idList);
  },
   // 下载基础物料导入模板
    downloadTemplate(){
      return getDownload(`/process/downloadTemplate`);
    },
    // 导入文件
  importfile:(file)=>{
    return postRequest('/process/import',file,);
  },
};

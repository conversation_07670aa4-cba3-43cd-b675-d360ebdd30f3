<!--
  * 工厂信息表
  *
  * @Author:    cyz
  * @Date:      2024-07-04 11:11:50
  * @Copyright  zscbdic
-->
<template>
  <a-drawer
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @close="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
        <a-form-item label="工厂名称"  name="name">
          <a-input style="width: 100%" v-model:value="form.name" placeholder="工厂名称"/>
        </a-form-item>
        <a-form-item label="邮政编码"  name="postalCode">
          <a-input style="width: 100%" v-model:value="form.postalCode" placeholder="邮政编码"/>
        </a-form-item>
          <a-form-item label="联系人"  name="contact">
            <a-input style="width: 100%" v-model:value="form.contact" placeholder="联系人"/>
        </a-form-item>
        <a-form-item label="电话"  name="telephone">
           <a-input style="width: 100%" v-model:value="form.telephone" placeholder="电话"/>
        </a-form-item>
        <a-form-item label="地址"  name="address">
           <a-input style="width: 100%" v-model:value="form.address" placeholder="地址"/>
         </a-form-item>
        <a-form-item label="备注"  name="remark">
           <a-textarea style="width: 100%" v-model:value="form.remark" placeholder="备注"/>
       </a-form-item>
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
  import {nextTick, reactive, ref } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { factoryApi } from '/@/api/business/mes/factory/factory-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      id: undefined, //主键
      remark: undefined, //备注
      name: undefined, //工厂名称
      address: undefined, //地址
      postalCode: undefined, //邮政编码
      contact: undefined, //联系人
      telephone: undefined, //电话
  };

  let form = reactive({ ...formDefault });

  const rules = {
        name: [{ required: true, message: '工厂名称 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await factoryApi.update(form);
      } else {
        await factoryApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>


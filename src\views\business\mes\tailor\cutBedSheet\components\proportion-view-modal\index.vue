<template>
  <a-modal
    :open="visible"
    title="按比例编菲预览"
    width="800px"
    @ok="ok"
    @cancel="cancel"
    :maskClosable="false"
  >
    <!-- 头部信息区域 -->
    <div class="header-info">
      <a-row :gutter="[16, 16]">
        <a-col :span="12">
          <span>指令单号：{{ headerInfo.instructOrderNumber }}</span>
        </a-col>
        <a-col :span="12">
          <span>颜色：{{ headerInfo.colors }}</span>
        </a-col>
        <a-col :span="12">
          <span>款号：{{ headerInfo.itemNumber }}</span>
        </a-col>
        <a-col :span="12">
          <span>尺寸：{{ headerInfo.sizes }}</span>
        </a-col>
        <a-col :span="12">
          <span>款名：{{ headerInfo.itemName }}</span>
        </a-col>
        <a-col :span="12">
          <span>部位：{{ headerInfo.positions }}</span>
        </a-col>
        <a-col :span="12">
          <span>菲票数：{{ feTickets.length }}</span>
        </a-col>
        <a-col :span="12">
          <span>裁片数：{{ totalPieces }}</span>
        </a-col>
      </a-row>
    </div>

    <!-- 表格区域 -->
    <a-table 
      :columns="columns" 
      :data-source="tableData"
      :pagination="false"
      size="small"
      :scroll="{ y: 400 }"
      bordered
    />
  </a-modal>
</template>

<script setup>
  import { ref, computed } from 'vue';

  //父子通信
  const emit = defineEmits(['submit']);

  // 模态框显示状态
  const visible = ref(false);

  // 菲票数据
  const feTickets = ref([]);

  // 表格列定义
  const columns = [
    {
      title: '扎号',
      dataIndex: 'tieNum',
      width: 80,
      align: 'center',
    },
    {
      title: '颜色',
      dataIndex: 'styleColor',
      width: 100,
      align: 'center',
    },
    {
      title: '尺码',
      dataIndex: 'size',
      width: 80,
      align: 'center',
    },
    {
      title: '数量',
      dataIndex: 'num',
      width: 80,
      align: 'center',
    },
    {
      title: '部位',
      dataIndex: 'positions',
      width: 100,
      align: 'center',
    }
  ];

  // 计算表格数据
  const tableData = computed(() => {
    return feTickets.value.map(ticket => ({
      key: ticket.tieNum,
      tieNum: ticket.tieNum, 
      styleColor: ticket.styleColor,
      size: ticket.size,
      num: ticket.num,
      positions: ticket.positions
    }));
  });

  // 计算头部信息
  const headerInfo = computed(() => {
    if (feTickets.value.length === 0) {
      return {
        instructOrderNumber: '',
        colors: '',
        itemNumber: '',
        sizes: '',
        itemName: '',
        positions: ''
      };
    }

    // 获取第一条记录的基本信息
    const firstTicket = feTickets.value[0];
    
    // 提取不重复的颜色和尺寸和部位
    const uniqueColors = [...new Set(feTickets.value.map(item => item.styleColor))];
    const uniqueSizes = [...new Set(feTickets.value.map(item => item.size))];
    const uniquePositions = [...new Set(feTickets.value.map(item => item.positions))];

    //返回数据
    return {
      instructOrderNumber: firstTicket.instructOrderNumber,
      colors: uniqueColors.join(','),
      itemNumber: firstTicket.itemNumber.trim(),
      sizes: uniqueSizes.join(','),
      itemName: firstTicket.itemName,
      positions: uniquePositions.join(',')
    };
  });

  // 计算总裁片数
  const totalPieces = computed(() => {
    //使用reduce方法对feTickets中的num进行累加
    return feTickets.value.reduce((total, ticket) => {
      return total + (ticket.num || 0);
    }, 0);
  });

  // 打开模态框的方法
  const show = (data) => {
    if (data && data.feTickets && Array.isArray(data.feTickets)) {
      feTickets.value = data.feTickets;
      visible.value = true;
    }
  };

  // 处理确认
  const ok = () => {
    visible.value = false;
    emit('submit');
    resetForm();
  };

  // 处理取消
  const cancel = () => {
    visible.value = false;
    resetForm();
  };

  //重制表单
  const resetForm = () => {
    feTickets.value = [];
    headerInfo.value = {};
    totalPieces.value = 0;  
  }
  // 暴露方法给父组件
  defineExpose({
    show
  });
</script>

<style scoped>
  .header-info {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 16px;
  }

  .header-info span {
    display: inline-block;
    font-size: 14px;
    line-height: 22px;
    color: #333;
  }

  .ant-modal-title {
    text-align: center;
    font-size: 18px;
    font-weight: 500;
  }
</style>

/**
 * 生产指令单安排 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-10 10:22:24
 * @Copyright  zscbdic
 */

import { postRequest, getRequest } from '/@/lib/axios';

export const produceInstructOrderArrangeApi = {
    /**
     * 指令单安排分页查询  <AUTHOR>
     */
    queryPage : (param) => {
        return postRequest('/produceInstructOrderArrange/queryPage', param);
    },

    /**
     * 指令单安排分页查询  <AUTHOR>
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    queryProduceInstructOrderArrangePage : (param) => {
        return postRequest('/produceInstructOrderArrange/queryArrangePage', param);
    }


}

<template>
  <div style="display: flex;flex-direction: column;align-items: center;">
    <div style="text-align: center" >
    </div>
    <div style="text-align: center" >
      <div style="font-size: 22px;color: #1f2329;font-weight: 600;">扫码登录</div>
      <br>
      请使用飞书移动端扫描二维码
    </div>

    <div style="position: relative;">
      <div id="login_container"></div>
    </div>
  </div>
</template>

<script setup>
import {onMounted, ref, watch} from "vue";
import { useRouter } from "vue-router";
import {feishuApi } from "/@/api/system/feishu-api.js";

const router = useRouter()
const loginTmpCode = ref()
async function init() {
  // 清空之前的二维码容器内容，防止生成多个二维码
  const loginContainer = document.getElementById("login_container");
  loginContainer.innerHTML = "";

  let res = await feishuApi.getFeishuQrCodeConfig().catch(res => { console.error(res) })
  const queryData = res.data
  const appId = queryData.appId;
  const redirectUri = encodeURIComponent(queryData.redirectUrl);
  const state = queryData.state;
  const goto =  `https://passport.feishu.cn/suite/passport/oauth/authorize?client_id=${appId}&redirect_uri=${redirectUri}&response_type=code&state=${state}`
  const QRLoginObj = QRLogin({
    id:"login_container",
    goto: goto,
    width: "255",
    height: "255",
    style:"border: none;"
  });
  var handleMessage = function (event) {
    // 使用 matchOrigin 和 matchData 方法来判断 message 和来自的页面 url 是否合法
    if (
        QRLoginObj.matchOrigin(event.origin) &&
        QRLoginObj.matchData(event.data)
    ) {
      loginTmpCode.value = event.data.tmp_code;
      // 在授权页面地址上拼接上参数 tmp_code，并跳转
      window.location.href = ` ${goto}&tmp_code=${loginTmpCode.value}`;
    }
  };
  if (typeof window.addEventListener != "undefined") {
    window.addEventListener("message", handleMessage, false);
  } else if (typeof window.attachEvent != "undefined") {
    window.attachEvent("onmessage", handleMessage);
  }
}

onMounted(() => {
  init()
});
</script>
<style scoped lang="less">
.qr-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  z-index: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  color: white;
  font-size: 16px;
  border-radius: 10px;
}

.name-circle {
  background-color: #00a0e9;
  color: white;
  font-size: 18px;
  font-weight: bold;
  width: 66px;
  height: 66px;
  border-radius: 50%;
  line-height: 66px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

}
</style>

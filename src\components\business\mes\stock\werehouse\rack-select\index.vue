<template>
  <a-select
    v-model:value="selectedRackId"
    :filter-option="filterOption"
    placeholder="请选择货架"
    style="width: 100%"
    :showSearch="true"
    :allowClear="true"
    @change="handleChange"
  >
    <a-select-option v-for="rack in rackOptions" :key="rack.id" :value="rack.id" :label="rack.label">
      {{ rack.label }}
    </a-select-option>
  </a-select>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { stkRackApi } from '../../../../../../api/business/mes/stock/stk-rack-api';

// 接收编辑下已有选中项
const props = defineProps({
  value: {
    type: Number,
    default: null,
  },
});
const emit = defineEmits(['update:value', 'change']);
// 自定义 number 搜索逻辑
function filterOption(input, option) {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
}
// 货架列表所有数据
const rackList = ref([]);
//货架列表下拉框选项
const rackOptions = ref([]);
//获取货架列表下拉框数据
async function getRackList() {
  try {
    let result = await stkRackApi.queryList({});
    //获取货架列表下拉框所需数据
    rackOptions.value = result.data.map(item => ({
      id: item.id,
      value: item.id,
      label: item.number
    }));
    //获取货架列表全部数据
    rackList.value = result.data;
  } catch (error) {
    smartSentry.captureError(error);
  }
}
// 处理选择变化
function handleChange(value) {
  const selectedRack = rackList.value.find(rack => rack.id === value);
  if (selectedRack) {
    emit('update:value', value);
    emit('change', selectedRack);
  }
}
//获取选中的货架id
const selectedRackId = ref(props.value);

// 当父组件传值变化时，更新 selectedRackId
watch(
  () => props.value,
  (newValue) => {
    selectedRackId.value = newValue;
  }
);

// 监听 selectedRackId 变化并触发事件
watch(selectedRackId, (newValue) => {
  emit('update:value', newValue);
  emit('change', newValue);
});
onMounted(getRackList);
</script>
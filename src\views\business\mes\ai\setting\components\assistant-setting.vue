<template>
<a-card title="智能体设置">
  <a-form :model="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
    <a-form-item label="智能体名称" name="agentName">
      <a-input v-model:value="form.agentName" placeholder="请输入助手名称" />
    </a-form-item>
    <a-form-item label="智能体头像" name="agentAvatar">
      <a-input v-model:value="form.agentAvatar" placeholder="请输入助手头像URL" />
    </a-form-item>
    <a-form-item label="系统提示词" name="systemPrompt">
      <a-textarea v-model:value="form.systemPrompt" placeholder="请输入系统提示词" :rows="4" />
    </a-form-item>
    <a-form-item label="用户推荐功能" name="userRecommendPromptFlag">
      <a-switch v-model:checked="form.userRecommendPromptFlag" />
    </a-form-item>
    <a-form-item label="推荐提示词模型" name="userRecommendPromptModelId" v-if="form.userRecommendPromptFlag">
      <model-select v-model:value="form.userRecommendPromptModelId" placeholder="请选择推荐提示词模型" />
    </a-form-item>
    <a-form-item label="用户推荐系统提示词" name="userRecommendSystemPrompt" v-if="form.userRecommendPromptFlag">
      <a-textarea v-model:value="form.userRecommendSystemPrompt" placeholder="请输入用户推荐系统提示词" :rows="4" />
    </a-form-item>
  </a-form>

  <template #extra>
    <a-button type="primary" @click="handleSubmit">保存</a-button>
    <a-button style="margin-left: 10px" @click="handleReset">重置</a-button>
  </template>
</a-card>
</template>
<script setup>
import { ref, reactive,onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { aiSettingApi } from '/@/api/business/mes/ai/ai-setting-api.js'
import ModelSelect from '/@/components/business/mes/ai/model-select.vue'

const formState = {
  configId: undefined,
  agentName: undefined,
  agentAvatar: undefined,
  systemPrompt: undefined,
  userRecommendPromptFlag: false,
  userRecommendPromptModelId: undefined,
  userRecommendSystemPrompt: undefined,
}

const form = ref({...formState})

// 获取助手设置
const getAssistantSetting = async () => {
  try {
    const res = await aiSettingApi.getAssistantSetting()
    form.value = res.data
    console.log('res.data', form.value)
  } catch (error) {
    console.error('获取助手设置失败:', error)
    message.error('获取助手设置失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  const res = await aiSettingApi.updateAssistantSetting(form.value)
  message.success('保存成功')
  await getAssistantSetting() // 重新获取最新数据
}

// 重置表单
const handleReset = () => {
  form.value = {...formState}
  getAssistantSetting() // 重新获取数据
}

// 组件挂载时获取助手设置
onMounted(() => {
  getAssistantSetting()
})
</script>

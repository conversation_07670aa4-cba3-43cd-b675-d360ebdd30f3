<!--
  * 快速建仓
  *
  * @Author:    cjm
  * @Date:      2024-10-06 16:48:11
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules"  :label-col="{ span:6 }" >
      <a-row>
        <a-col span="24">
          <a-form-item label="仓库名称" name="name">
            <a-input style="width: 100%" v-model:value="form.name" placeholder="仓库名称"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="仓库地址" name="address">
            <a-input style="width: 100%" v-model:value="form.address" placeholder="仓库地址"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="仓库编码" name="warehouseCode">
            <a-input style="width: 100%" v-model:value="form.warehouseCode" placeholder="仓库编码"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="货架数量" name="rackNum">
            <a-input style="width: 100%" v-model:value="form.rackNum" placeholder="货架数量"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="层数" name="layerNum">
            <a-input style="width: 100%" v-model:value="form.layerNum" placeholder="层数"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="容量" name="layerNum">
            <a-input style="width: 100%" v-model:value="form.capacity" placeholder="容量"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="每层数量" name="binNum">
            <a-input-number style="width: 100%" v-model:value="form.binNum" placeholder="每层数量" :default-value="1"/>
          </a-form-item>
        </a-col>
        <a-col span="24">
          <a-form-item label="备注" name="remark">
            <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {partStationWarehouseApi} from '/@/api/business/mes/part-station/warehouse/part-station-warehouse-api';
import {partStationRackApi} from "/@/api/business/mes/part-station/rack/part-station-rack-api.js";
import {smartSentry} from '/@/lib/smart-sentry';

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  name: undefined, //仓库名称
  warehouseCode: undefined, //仓库编码
  address: undefined, //仓库地址
  rackNum: undefined, //货架数量
  layerNum: undefined, //层数
  capacity: undefined, //容量 不能为空
  remark: undefined,//备注
  binNum: undefined, //每层数量
};

let form = reactive({...formDefault});

const rules = {
  name: [{required: true, message: '仓库名称 必填'}],
  warehouseCode: [{required: true, message: '仓库编码 必填'}],
  rackNum: [{required: true, message: '货架数量 必填'}],
  layerNum: [{required: true, message: '层数 必填'}],
  capacity: [{required: true, message: '容量 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await partStationWarehouseApi.update(form);
    } else {
      await partStationRackApi.quickAdd(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

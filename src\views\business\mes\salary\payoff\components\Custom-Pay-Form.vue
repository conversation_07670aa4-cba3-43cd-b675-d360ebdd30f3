<!--
  * 自定义工资项
  * 编辑工资项名称、类型
  * @Author:    linwj
  * @Date:      2024-12-05 22:48:59
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      title="自定义工资项"
      width="600px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <div class="smart-table-operate-block">
      <a-button type="link" @click="addField">
        <template #icon>
          <PlusOutlined />
        </template>
        添加工资项
      </a-button>
    </div>
    <a-table :columns="columns"
             :data-source="tableData"
             size="small"
             :pagination="false"
             row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex==='action'">
          <a-button type="link" @click="deleteField(record)">删除</a-button>
        </template>
        <template v-else-if="column.dataIndex === 'fieldName'">
          <a-input v-model:value="record.fieldName" @change="handleTypeChange(record)"/>
        </template>
        <template v-else-if="column.dataIndex === 'type'">
          <a-radio-group v-model:value="record.type" @change="handleTypeChange(record)">
            <a-radio :value="WAGE_TYPE_ENUM.ADD.value">{{WAGE_TYPE_ENUM.ADD.label}}</a-radio>
            <a-radio :value="WAGE_TYPE_ENUM.SUB.value">{{WAGE_TYPE_ENUM.SUB.label}}</a-radio>
          </a-radio-group>
        </template>
      </template>
    </a-table>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button @click="saveField" type="primary">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {onMounted, reactive, ref} from 'vue';
import {smartSentry} from "/@/lib/smart-sentry.js";
import {wageFieldApi} from "/@/api/business/mes/salary/wage-field-api.js";
import {WAGE_TYPE_ENUM} from "/@/constants/business/mes/salary/custom-pay-const.js";
import {message, Modal} from "ant-design-vue";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import _ from "lodash";
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList','update:field']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show() {
  if(_.isEmpty(tableData.value)){
   queryData()
  }
  visibleFlag.value = true;
}

function onClose() {
  emits('reloadList')
  // 关闭弹窗，清空内容
  tableData.value =  _.drop(tableData.value,tableData.value.length)
  visibleFlag.value = false;
}

const formDefault = {
  id: undefined, //工资项id
  fieldName: undefined, //工资项名称
  fieldId:undefined,
  type: '0', //工资项类型 默认为加项
};

let form = reactive({ ...formDefault });

const columns = [

  {
    title: '自定义工资项',
    dataIndex: 'fieldName',
    align: 'center',
    width: '120px'
  },
  {
    title: '加项/减项',
    dataIndex: 'type',
    key: 'address',
    align: 'center'
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center'
  },
];

const tableData = ref([]);
// 查询数据
async function queryData() {
  try {
    let queryResult = await wageFieldApi.queryList();
    tableData.value = queryResult.data;
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// ------------------- 新增工资项 --------------------

function addField() {
  const newItem = {
    fieldName: undefined, //工资项名称
    type: '0', //工资项类型 默认为加项
    isNew: '1', //是否是新增
  }
  tableData.value.push(newItem)
}

// -------------------- 保存工资项 --------------------
async function saveField(){
  try {
    // 过滤出需要新增的数据
    let insertData = tableData.value.filter(item => item.isNew === '1');
    // 执行所有新增操作
    await Promise.all(insertData.map(async (item) => {
      try {
        await wageFieldApi.add(item);
      } catch (error) {
         smartSentry.captureError(error);
      }
    }));

    // 执行所有更新操作
    await Promise.all(updateDataList.value.map(async (item) => {
      try {
        await wageFieldApi.update(item);
      } catch (error) {
        smartSentry.captureError(error);
      }
    }));
  } catch (error) {
    smartSentry.captureError(error);
  }finally {
    emits('update:field',tableData.value)
    // 所有操作完成后关闭
    onClose();
  }
}

// -------------------- 删除工资项 --------------------

// 删除工资项,对于已存在的给予提示，对于新加的直接过滤
function deleteField(rowData){
  if(_.isNil(rowData.id)){
    tableData.value = tableData.value.
      filter(item => item.fieldName !== rowData.fieldName )
    return;
  }
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(rowData.id) // 工资项id
    },
    cancelText: '取消',
    onCancel() {},
  });
}

async function requestDelete(fieldId){
  SmartLoading.show();
  try {
    await wageFieldApi.delete(fieldId);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
//------------------ 更新工资项 --------------------
const updateDataList = ref([])

function handleTypeChange(rowData){
  //区分新增数据和修改数据
  if(rowData.isNew === '1'){
    return;
  }
  // 如果已经存在则直接修改，不存在则直接添加
  const index = updateDataList.value.findIndex(item => item.id === rowData.id);
  if (index !== -1){
    updateDataList.value[index] = rowData
  } else {
    updateDataList.value.push(rowData)
  }
}




onMounted(()=>{
  queryData()
})

defineExpose({
  show,
});
</script>
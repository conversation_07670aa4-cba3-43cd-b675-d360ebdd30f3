<template>
  <div style="display: flex;flex-direction: column;align-items: center;">
    <div style="text-align: center">
      <div style="font-size: 22px;color: #1f2329;font-weight: 600;">扫码登录</div>
      <br>
      请使用企业微信移动端扫描二维码
    </div>
    <div style="position: relative;">
      <div id="ww_login"></div>
    </div>
  </div>

<!--  <ThirdAuthBind ref="thirdForm"/>-->
</template>

<script setup>
import { onMounted, onUnmounted, ref } from "vue";
import { workWechatApi } from "/@/api/system/work-wechat-api.js";
import { useRouter } from 'vue-router';
import { oauth2Api } from "/@/api/system/oauth2-api";
import ThirdAuthBind from "/@/views/system/login/components/third-bind.vue";

const router = useRouter();
const thirdForm = ref()
let wwLogin = null;

async function init() {
  try {
    const res = await workWechatApi.getWorkWechatQrCodeConfig();
    const config = res.data;
    console.log('企业微信配置数据:', config);
    
    wwLogin = ww.createWWLoginPanel({
      el: '#ww_login',
      params: {
        login_type: 'CorpApp',
        appid: config.appId,
        agentid: config.agentId,
        redirect_uri: config.redirectUrl,
        state: config.state,
        redirect_type: 'callback'
      },
      onCheckWeComLogin({ isWeComLogin }) {
        console.log('是否在企业微信环境中：', isWeComLogin);
      },
      async onLoginSuccess({ code }) {
        console.log('登录成功，获取到的code：', code);
        window.location.href = `${config.redirectUrl}?code=${code}&state=${config.state}`;
        // let res = await oauth2Api.thirdLogin(code, config.state);
        // thirdForm.value.show(res.data);
      },
      onLoginFail(err) {
        console.error('登录失败：', err);
      }
    });
  } catch (error) {
    console.error('初始化企业微信登录失败:', error);
  }
}

onMounted(() => {
  init();
});

onUnmounted(() => {
  if (wwLogin) {
    wwLogin.unmount();
  }
});
</script>

<style scoped>
#ww_login {
  margin-top: 20px;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
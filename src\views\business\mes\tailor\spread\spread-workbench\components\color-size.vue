<!--
  * 裁床单比例添加
-->
<template>
  <a-row>
    <span class="partstitle"> 添加部位: </span>
    <part-select width="300px" v-model:value="selectParts" :produceInstructOrderId="props.produceInstructOrderId" />
  </a-row>
  <!-- 合并的计划数据表格 -->
  <a-row>
    <a-col :span="24">
      <div class="box">
        <div class="titlebox">计划颜色尺码配置:</div>
        <a-table
            :columns="dynamicColumns"
            :data-source="dynamicTableData"
            :pagination="false"
            bordered
            size="small"
            :scroll="{ x: 480 + sizeRow.length * 100 }"
            row-key="color"
        >
          <template #headerCell="{ column }">
            <template v-if="column.dataIndex.startsWith('size_') && sizeRow?.[column.sizeIndex]">
              <div style="text-align: center;">
                <div style="margin-bottom: 4px;">
                  <a-input-number
                      v-model:value="sizeRow[column.sizeIndex].planRatio"
                      addon-before="比例:"
                      :controls="false"
                      :disabled="true"
                      :min="0"
                      style="width: 100%"
                  />

                </div>
                <div>{{ column.title }}</div>
              </div>
            </template>
          </template>
          <template #bodyCell="{ column, index }">
            <template v-if="['planLayers', 'planLength'].includes(column.dataIndex) && planColorData?.[index]">
              <a-input-number
                  v-model:value="planColorData[index][column.dataIndex]"
                  :min="0"
                  style="width: 100%"
                  :disabled="true"
              />
            </template>
          </template>
        </a-table>
      </div>
    </a-col>
  </a-row>

  <!-- 实际颜色数据表格 -->
  <a-row>

    <a-col :span="24">
      <div class="box">

        <!-- 实际数据表格 -->
        <div class="titlebox" style="margin-top: 16px;">
          点击颜色增加一行数据:
          <span class="autoFill">
            <a-switch v-model:checked="autoFill" />
            自动填充
          </span>
        </div>
        <div class="color-buttons">
          <a-tag color="processing" class="color-tag" v-for="item in colorTagOptions" :key="item.value" @click="addColorRow(item)">
            {{ item.value }}
          </a-tag>
        </div>
        <a-table size="small" :dataSource="colorRow" :columns="colorColumns" :pagination="false">
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'realLayers'">
              <!-- <a-input-number :min="0" v-model:value="record.realLayers" @change="(value) => handleLayerChange(index, value)" />
                 -->
                <input-number-keyboard v-model:value="record.realLayers" @change="(value) => handleLayerChange(index, value)" :unit="'层'" />
            </template>
            <template v-if="column.dataIndex === 'planLayers'">
              <span v-if="!record.planLayers"> 暂无 </span>
            </template>
            <template v-if="column.dataIndex === 'planLength'">
              <span v-if="!record.planLength"> 暂无 </span>
            </template>
            <template v-if="column.dataIndex === 'realLength'">
              <input-number-keyboard v-model:value="record.realLength" @change="(value) => handleLengthChange(index, value)" :unit="'厘米'" />
            </template>

            <template v-if="column.dataIndex === 'operation'">
              <a-button type="link" danger @click="deleteColorRow(record)"> 删除 </a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-col>
  </a-row>
</template>

<script setup>
  import { ref, watch, computed, onMounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';
  import inputNumberKeyboard from '/@/components/business/mes/common/number-input-keyboard/index.vue';
  //----------------------------------父子通信----------------------------------
  const props = defineProps({
    sizeList: {
      type: Array,
      default: () => [],
    },
    colorList: {
      type: Array,
      default: () => [],
    },
    partList: {
      type: Array,
      default: () => [],
    },
    produceInstructOrderId: {
      type: Number,
    },
    taskStatus: {
      type: String,
    },
  });
  //----------------------------------添加部位事件---------------------------------
  const selectParts = ref([]);

  watch(
    () => props.partList,
    (newVal) => {
      selectParts.value = newVal || [];
    }
  );

  //----------------------------------尺寸表格数据----------------------------------
  const sizeRow = ref([]); //尺码比例数据
  const sizeColumns = [
    {
      title: '尺码',
      dataIndex: 'size',
      align: 'center',
    },
    {
      title: '计划比例',
      dataIndex: 'planRatio',
      align: 'center',
    },
    {
      title: '实际比例',
      dataIndex: 'realRatio',
      align: 'center',
    },
  ];
  // 监听输入的尺码列表并转换为表格数据
  watch(
    () => props.sizeList,
    (newVal) => {
      sizeRow.value = newVal.map(size => ({
        id:size.id,
        size: size.size,
        planRatio: size.planRatio,
        realRatio: size.realRatio ? size.realRatio : size.planRatio,
        key: size.size,
      }));
    },
    { immediate: true, deep: true }
  );
  //颜色标签选项
  const colorTagOptions = ref([]);
  watch(
    () => props.produceInstructOrderId,
    (newVal) => {
      queryOrderColor();
    },
    { immediate: true, deep: true }
  );

  async function queryOrderColor() {
    if (!props.produceInstructOrderId) {
      return;
    }

    let res = await produceInstructOrderClothesApi.queryClothesColorList(props.produceInstructOrderId);
    colorTagOptions.value = res.data.map((item) => ({
      label: item,
      value: item,
    }));
  }

  onMounted(() => {
    queryOrderColor();
  });

//-----------------------------------颜色表格数据-----------------------------------
// 计划数据表格配置
const planColorData = ref([]);
const planColorColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
  },
  {
    title: '颜色',
    dataIndex: 'color',
    align: 'center',
  },
  {
    title: '计划层数',
    dataIndex: 'planLayers',
    align: 'center',
  },
  {
    title: "计划长度",
    dataIndex: 'planLength',
    align: 'center',
  }
];

// 合并的计划数据表格配置
const combinedPlanData = ref([]);
const combinedPlanColumns = [
 {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    align: 'center',
    width: 100,
  },
  {
    title: '尺码/颜色',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '计划比例/层数',
    dataIndex: 'planValue',
    align: 'center',
  },
  {
    title: '实际比例/计划长度',
    dataIndex: 'realValue',
    align: 'center',
  }
];

// 动态列配置
const dynamicColumns = computed(() => {
  const columns = [{title: '颜色', dataIndex: 'color', width: 80, fixed: 'left'}];

  // 添加尺码列
  sizeRow.value.forEach((size, index) => {
    columns.push({
      title: size.size,
      dataIndex: `size_${size.size}`,
      width: 120,
      sizeIndex: index
    });
  });

  // 添加计划数据列
  columns.push(
      {title: '计划层数', dataIndex: 'planLayers', width: 100},
      {title: '计划长度', dataIndex: 'planLength', width: 100}
  );

  return columns;
});

// 动态表格数据
const dynamicTableData = computed(() => {
  if (!planColorData.value.length) return [];

  return planColorData.value.map((colorItem) => {
    const row = {color: colorItem.color};

    // 计算尺码数量
    sizeRow.value.forEach((size, sizeIndex) => {
      const planLayer = colorItem.planLayers || 0;
      const planRatio = sizeRow.value?.[sizeIndex]?.planRatio || 0;
      row[`size_${size.size}`] = Math.round(planLayer * planRatio);
    });

    // 添加计划层数和长度
    row.planLayers = colorItem.planLayers;
    row.planLength = colorItem.planLength;

    return row;
  });
});

// 实际数据表格配置
const colorRow = ref([]); //颜色表格数据
const colorColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    align: 'center',
    width: 80,
  },
  {
    title: '颜色',
    dataIndex: 'color',
    align: 'center',
  },
  {
    title: "实际层数",
    dataIndex: 'realLayers',
    align: 'center',
  },
  {
    title: "实际长度",
    dataIndex: 'realLength',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    align: 'center',
    width: 100,
  }
];
//监听传来的颜色数据
watch(
    () => props.colorList,
    (newVal) => {
      if (!newVal?.length) {
        planColorData.value = [];
        colorRow.value = [];
        combinedPlanData.value = [];
        return;
      }

      // 处理合并的计划数据表格
      const combinedData = [];

      // 添加尺码数据
      sizeRow.value.forEach((sizeItem, index) => {
        combinedData.push({
          key: `size-${index}`,
          dataType: '尺码',
          name: sizeItem.size,
          planValue: sizeItem.planRatio || '暂无',
          realValue: sizeItem.realRatio || '暂无'
        });
      });

      // 添加颜色数据
      const planColorsForCombined = newVal.filter(item => item.originType === 'PLAN' || !item.originType);
      planColorsForCombined.forEach((colorItem, index) => {
        combinedData.push({
          key: `color-${index}`,
          dataType: '颜色',
          name: colorItem.color,
          planValue: colorItem.planLayer || 0,
          realValue: colorItem.planLength || 0
        });
      });

      combinedPlanData.value = combinedData;

      // 处理计划数据 - 只显示PLAN类型的数据
      const planColors = newVal.filter(item => item.originType === 'PLAN' || !item.originType);
      planColorData.value = planColors.map((item, index) => ({
        key: `plan-${item.color}-${Date.now()}`,
        index: index + 1,
        color: item.color,
        planLayers: item.planLayer,
        planLength: item.planLength
      }));

      // 处理实际数据 - 根据任务状态决定显示哪些数据
      const targetColors = props.taskStatus === 'COMPLETE'
        ? newVal.filter(item => item.originType === 'REAL')  // 完成状态只显示实际数据
        : newVal;

      colorRow.value = targetColors.map((item, index) => ({
        key: `${item.color}-${Date.now()}`,
        index: index + 1,
        color: item.color,
        realLayers: item.realLayer,
        realLength: item.realLength
      }));
    },
    { immediate: true, deep: true }
  );
  //添加颜色方法
  const addColorRow = (item) => {
    colorRow.value.push({
      key: `${item.value}-${Date.now()}`,
      index: colorRow.value.length + 1,
      color: item.value,
      realLayers: autoFill.value && colorRow.value.length > 0 ? colorRow.value[0].realLayers : 0,
      realLength: autoFill.value && colorRow.value.length > 0 ? colorRow.value[0].realLength : 0,
    });
  };
  // 删除颜色行方法
  const deleteColorRow = (record) => {
    console.log(colorRow.value);
    colorRow.value = colorRow.value.filter((item) => item.index !== record.index);
    // 重新计算序号
    colorRow.value = colorRow.value.map((item, index) => ({
      ...item,
      index: index + 1,
    }));
  };
  //----------------------------------自动填充----------------------------------
  const autoFill = ref(true);
  // 处理任意行数值变化
  const handleLayerChange = (index, value) => {
    if (autoFill.value && colorRow.value.length > index + 1) {
      // 如果开启了自动填充，将当前行的值填充到后面所有行
      for (let i = index + 1; i < colorRow.value.length; i++) {
        colorRow.value[i].realLayers = value;
      }
    }
  };

  const handleLengthChange = (index, value) => {
    if (autoFill.value && colorRow.value.length > index + 1) {
      // 如果开启了自动填充，将当前行的值填充到后面所有行
      for (let i = index + 1; i < colorRow.value.length; i++) {
        colorRow.value[i].realLength = value;
      }
    }
  };

//----------------------------------清除表单数据-------------------------------------
//清除表单数据
const resetDate = () => {
  colorRow.value = [];
  sizeRow.value = [];
  partOptions.value = []
  selectParts.value = []
}
//初始化
const initData = () => {
  colorRow.value = [];
  partOptions.value = []
  selectParts.value = []
  colorRow.value = props.colorList.map((item, index) => ({
    key: `${item.value}-${Date.now()}`,
    index: index + 1,
    color: item.value,
    planLayers: 0
  }));
  sizeRow.value = [];
  sizeRow.value = props.sizeList.map(size => ({
    id: size.id,
    size: size.size,
    planRatio: 1,
    key: size.size,
  }));
  autoFill.value = false;
}
//暴露方法和数据给父组建
defineExpose({
  colorRow,
  sizeRow,
  selectParts,
  resetDate,
  initData
})
</script>
<style>
  .partstitle {
    font-size: 14px;
    margin: 5px 10px;
  }

  .box {
    margin: 15px;
  }

  .titlebox {
    width: 100%;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
    overflow: hidden;
  }

  .autoFill {
    margin-right: 40px;
    font-size: 14px;
    float: right;
  }

  .color-buttons {
    margin: 8px;
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .color-tag {
    height: 24px;
    font-size: 14px;
    line-height: 24px;
    transition: all 0.3s;
    cursor: pointer;
  }

  .color-tag:hover {
    opacity: 0.8;
  }
</style>

/**
 * 物料库存属性 api 封装
 *
 * @Author:    fkf
 * @Date:      2025-02-10 15:13:43
 * @Copyright  zscbdic
 */
import { postRequest } from '/@/lib/axios';

export const stkMaterialStockApi = {
  /**
   * 分页查询  
   */
  queryPage: (param) => {
    return postRequest('/stkMaterialStock/queryPage', param);
  },

  /**
   * 增加  
   */
  add: (param) => {
    return postRequest('/stkMaterialStock/add', param);
  },

  /**
   * 修改 
   */
  update: (param) => {
    return postRequest('/stkMaterialStock/update', param);
  },

};
<!--
  * 编辑布料
  *
  * @Author:    wxx
  * @Date:      2025-01-10
  * @Copyright  zscbdic
-->

<template>
  <a-modal width="1000px" :open="visibleFlag" @cancel="onClose" @ok="onSubmit" :maskClosable="false" :destroyOnClose="true" title="编辑布料">
    <div style="height: 600px; overflow-y: scroll; overflow-x: hidden">
      <hr style="border-color: rgba(5, 5, 5, 0.1)" />
      <a-row class="smart-table-btn-block" style="margin: 20px">
        <strong>物料基本信息</strong>
      </a-row>
      <!-- 基本信息表单 -->
      <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="物料编号" name="number">
              <a-input style="width: 100%" v-model:value="form.number" placeholder="请输入,忽略将自动生成" :disabled="true" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="物料分类" name="typeId">
              <a-cascader v-model:value="form.typeId" :options="typeOptions" placeholder="请选择物料分类" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="物料名称" name="name">
              <a-input style="width: 100%" v-model:value="form.name" placeholder="物料名称" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="供应商" name="supplierId">
              <a-select ref="select" v-model:value="form.supplierId" placeholder="请选择" :options="supplierOptions" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="规格型号" name="model">
              <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号" />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="单位" name="unitId">
              <a-select ref="select" placeholder="请选择" v-model:value="form.unitId" :options="unitOptions" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="停用标识" name="enableFlag">
              <a-radio-group v-model:value="form.enableFlag" button-style="solid" style="width: 90%">
                <a-radio-button :value="false">启用</a-radio-button>
                <a-radio-button :value="true">停用</a-radio-button>
              </a-radio-group>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row class="smart-table-btn-block" style="margin: 20px">
        <strong>布料属性</strong>
      </a-row>
      <!-- 属性信息 -->
      <a-form ref="formRef" :model="form" :label-col="{ span: 6 }" :rules="rules">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="sku编号" name="skuNumber">
              <a-input style="width: 100%" v-model:value="form.skuNumber" placeholder="sku编号"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="克重" name="gramWeight">
              <a-input style="width: 100%" v-model:value="form.gramWeight" placeholder="单位：g/m²" />
            </a-form-item>
          </a-col>

        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="重量" name="weight">
              <a-input style="width: 100%" v-model:value="form.weight" placeholder="单位：g" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="长度" name="length">
              <a-input style="width: 100%" v-model:value="form.length" placeholder="单位：mm" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="成分" name="ingredient">
              <a-input style="width: 100%" v-model:value="form.ingredient" placeholder="请输入成分" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="净价" name="netPrice">
              <a-input style="width: 100%" v-model:value="form.netPrice" placeholder="单位：元" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="米净价" name="metreNetPrice">
              <a-input style="width: 100%" v-model:value="form.metreNetPrice" placeholder="单位：元/m" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="千克净价" name="kgNetPrice">
              <a-input style="width: 100%" v-model:value="form.kgNetPrice" placeholder="单位：元/kg" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="颜色" name="colorName">
              <a-select :options="colorOptions" v-model:value="form.colorName" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="价格" name="price">
              <a-input style="width: 100%" v-model:value="form.price" placeholder="单位：元/kg" />
            </a-form-item>
          </a-col>
        </a-row>


        <a-row :gutter="24">
          <a-col :span="12"
            ><a-form-item label="图片"
              ><file-upload :default-file-list="form.imgUrl" @change="handleFileChange($event, record, column)" /></a-form-item
          ></a-col>
          <a-col :span="12">
            <a-form-item label="宽度" name="width">
              <a-input style="width: 100%" v-model:value="form.width" placeholder="单位：mm" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
  <!--款式尺寸组件-->
  <StyleColorSelect ref="colorVisible" />
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import _ from 'lodash';
  import { reactive, ref, onMounted } from 'vue';
  import { unitApi } from '/@/api/business/mes/base/unit-api';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { itemTypeApi } from '/@/api/business/mes/item/item-type-api';
  import { itemClothApi } from '/@/api/business/mes/item/item-cloth-api';
  import StyleColorSelect from '/@/components/business/mes/base/style-color-modal/index.vue';
  import FileUpload from '/@/components/support/file-upload/index.vue';
  import { styleColorApi } from '/@/api/business/mes/base/style-color-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';

  onMounted(() => {
    queryTypeOptions();
    querySupplierOptions();
    queryUnitOptions();
    queryCorlorList();
  });

  //-----------------------------事件---------------------
  const emits = defineEmits(['reloadList']);
  const visibleFlag = ref(false);

  async function show(id) {
    const res = await itemClothApi.getById({ id: id });

    const orginalData = res.data;
    if (orginalData.typeId) {
      orginalData.typeId = await findCascaderPath(typeOptions.value, orginalData.typeId);
    }
    Object.assign(form, res.data);

    visibleFlag.value = true;
  }

  function onClose() {
    visibleFlag.value = false;
  }

  //-----------------------------布料基本信息---------------------
  //------定义布料数据-------
  const formRef = ref();

  const formDefault = {
    id: undefined,
    typeId: undefined, //物料分类id;关联t_item_type
    number: undefined, //物料编号
    name: '', //物料名称
    supplierId: undefined, //供应商id;关联t_item_supplier
    model: '', //规格型号
    unitId: undefined, //单位id;关联t_unit
    price: undefined, //价格
    imgUrl: undefined, //图片url
    enableFlag: false, //停用标识;0启用，1停用
    category: undefined, //类型;0半成品 1成品
    attribute: undefined, //属性;0面料，1辅料
    gramWeight: undefined, //克重
    width: undefined, //宽度
    weight: undefined, //重量
    length: undefined, //长度
    ingredient: undefined, //成分
    netPrice: undefined, //净价
    metreNetPrice: undefined, //米净价
    kgNetPrice: undefined, //千克净价
    skuColors: undefined, //颜色
    skuNumber: undefined, //sku编号
  };
  const form = reactive({ ...formDefault });

  //布料基本信息表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入物料名称' }],
    unitId: [{ required: true, message: '请选择单位' }],
    enableFlag: [{ required: true, message: '请选择启用状态' }],
    skuNumber: [{ required: true, message: '请输入sku编号' }],
  };

  //--------------------------------获取选项数据----------------------------------
  //获取颜色列表
  const colorOptions = ref([]);
  async function queryCorlorList() {
    let queryResult = await styleColorApi.queryAll({});
    colorOptions.value = queryResult.data.map((item) => {
      return {
        value: item.styleColor,
        label: item.styleColor,
      };
    });
  }

  //获取物料分类
  const typeOptions = ref([]);

  async function queryTypeOptions() {
    let queryForm = {};
    const res = await itemTypeApi.queryCategoryTree(queryForm);
    typeOptions.value = res.data;
  }

  //递归查找物料级分类联选择器的完整路径
  function findCascaderPath(tree, id, path = []) {
    for (const node of tree) {
      const current = [...path, node.value];
      if (node.value === id) return current;
      if (node.children?.length) {
        const found = findCascaderPath(node.children, id, current);
        if (found) return found;
      }
    }
    return null;
  }

  //获取供应商名称
  const supplierOptions = ref([]);

  async function querySupplierOptions() {
    let queryForm = {};
    const res = await supplierApi.queryAll(queryForm);
    supplierOptions.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  }

  //获取单位
  const unitOptions = ref([]);

  async function queryUnitOptions() {
    let queryForm = {};
    const res = await unitApi.querySelect(queryForm);
    unitOptions.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  }

  // -------------------------上传图片---------------------------
  const handleFileChange = (event) => {
    form.imgUrl = Array.isArray(event) ? event : [event];
  };

  //----------------------------------------表单提交功能----------------------------------

  //点击确认，验证表单
  async function onSubmit() {
    await formRef.value.validateFields();

    const submitData = {
      ...form,
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
    };
    await itemClothApi.update(submitData);
    message.success('操作成功');
    emits('reloadList');
    onClose();
  }

  defineExpose({
    show,
  });
</script>

<style scoped>
  .upload-wrapper {
    position: relative;
  }

  .edit-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.45);
    color: white;
    opacity: 0;
  }

  .upload-wrapper:hover .edit-overlay {
    opacity: 1;
  }
</style>

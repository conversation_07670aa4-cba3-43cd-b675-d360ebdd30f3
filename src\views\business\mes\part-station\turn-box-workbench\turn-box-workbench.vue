<template>
  <a-row :gutter="8">
    <a-col :span="6">
      <a-card title="裁片周转箱工作台">
        <div style="margin-bottom: 20px;background-color: #EBEEF5;color: #909399;padding: 6px;border-radius: 4px">
          1. 扫码输入框需处于输入状态
          <br/>
          2. 网页模式下,输入法需切换为英文输入法
        </div>

        <div style="margin-bottom: 30px">
          <div class="title-line"> 料箱操作</div>
          <div class="sub-item">
            <a-radio-group v-model:value="turnBoxOptType" button-style="solid" size="large"
                           @change="onChangeTurnBoxOptType">
              <a-radio-button value="IN">入箱</a-radio-button>
              <a-radio-button value="OUT">出箱</a-radio-button>
              <a-radio-button value="MOVE">移箱</a-radio-button>
            </a-radio-group>
          </div>
        </div>


        <div style="margin-bottom: 30px">
          <div class="title-line">当前周转箱</div>
          <div class="sub-item">
            <span>编号：</span>
            <a-input style="font-size: 25px;font-weight: bold" disabled :value="turnBoxInfo.number"/>
          </div>
          <div class="sub-item">
            <span>名称：</span>
            <a-input style="font-size: 25px;font-weight: bold" disabled :value="turnBoxInfo.name"/>
          </div>
          <div class="sub-item">
            <span>存放扎数：</span>
            <a-input style="font-size: 25px;font-weight: bold" disabled :value="nowTicketCount"/>
          </div>
        </div>
      </a-card>
    </a-col>
    <a-col :span="18">
      <a-card title="菲票信息">
        <template #extra><a-button type="primary" size="large" @click="submitTurnTaskModalRef.showModal()" >发起周转任务</a-button></template>

        <a-input-group compact style="margin-bottom: 20px">
          <a-input style="font-size: 20px;width: calc(100% - 100px)" placeholder="请扫描菲票二维码或料箱二维码"
                   v-model:value="qrCode"
                   ref="qrCodeInputRef"
                   @pressEnter="qrCodePressEnter"/>
          <a-button style="height: 41px;font-size: 20px" type="primary" @click="resetData">重置</a-button>
        </a-input-group>
        <a-row>
          <a-col :span="6">
            <a-image width="80%"
                     v-if="feiTicketInfo.imgUrl && feiTicketInfo.imgUrl[0] && feiTicketInfo.imgUrl[0].fileUrl"
                     :src="feiTicketInfo.imgUrl[0].fileUrl"/>
            <div v-else class="no-image">
              无图片
            </div>
          </a-col>
          <a-col :span="13">
            <div style="font-size: 25px;margin-bottom: 10px">SPU编号(款号)：<span
                style="font-weight: bold">{{ feiTicketInfo.itemNumber }}</span></div>
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="床次" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.cutNum }}
              </a-descriptions-item>
              <a-descriptions-item label="扎号" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.tieNum }}
              </a-descriptions-item>
              <a-descriptions-item label="款式颜色" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.styleColor }}
              </a-descriptions-item>
              <a-descriptions-item label="尺寸" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.size }}
              </a-descriptions-item>
              <a-descriptions-item label="部位" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.positions }}
              </a-descriptions-item>
              <a-descriptions-item label="数量" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.num }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="5">
            <div
                style="padding: 20px;width: 100%;height:100%;background-color: #F2F6FC;display: flex;flex-direction: column;justify-content: space-around;align-items: center">
              <div style="font-size: 25px">数量</div>
              <div style="font-size: 60px;color: #005bf4;font-weight: bold;">{{ feiTicketInfo.num }}</div>
              <div style="font-size: 18px;color: #005bf4">
                请扫描二维码
                <br/>
              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>


      <a-card title="周转箱内裁片" style="margin-top: 14px">
        <template #extra><span style="font-weight: bold">共 {{ nowTicketCount }} 扎</span></template>
        <!--滚动        -->
        <a-table :columns="feiTicketColumns" :dataSource="nowTicketRecords" bordered size="small" :pagination="false"
                 :scroll="{y: '400px'}" :row-class-name="getRowClassName" />
      </a-card>
    </a-col>
  </a-row>

  <submit-turn-task-modal ref="submitTurnTaskModalRef" @close="closeSubmitTaskModal"/>
</template>
<script setup>

import {nextTick, onMounted, ref} from "vue";
import {QR_CODE_TYPE_ENUM} from "/@/constants/business/mes/common/qr-code-type-const.js";
import _ from "lodash";
import {message, Modal, notification} from "ant-design-vue";
import {feTicketApi} from "/@/api/business/mes/tailor/fe-ticket-api.js";
import {produceInstructOrderProcessApi} from "/@/api/business/mes/produce/produce-instruct-order-process-api.js";
import {partStationTurnBoxApi} from "/@/api/business/mes/part-station/turn-box/part-station-turn-box-api.js";
import {turnBoxInsideApi} from "/@/api/business/mes/part-station/turn-box-inside/turn-box-inside-api.js";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import SubmitTurnTaskModal from "/src/views/business/mes/part-station/turn-box-workbench/submit-turn-task-modal.vue";
import { get } from "lodash";

//----------------------------------------------
const submitTurnTaskModalRef = ref()

function closeSubmitTaskModal(){
  console.log('closeSubitTaskModal')
  nextTick(()=>{
    qrCodeInputRef.value.focus()
  })
  // setTimeout(() => {
  //
  // },200)
}

//----------------操作相关--------------------------------

/**
 * 扫描类型
 * IN 入箱
 * OUT 出箱
 * MOVE 移箱
 */
const IN = 'IN'
const OUT = 'OUT'
const MOVE = 'MOVE'

const turnBoxOptType = ref(IN)

async function onChangeTurnBoxOptType(e) {
  console.log("改变料箱操作", turnBoxOptType.value)
  // await queryTurnBoxInfo(turnBoxInfo.value.id)
  turnBoxInfo.value = Object.assign(turnBoxState)
  nowTicketRecords.value = []
  nowTicketCount.value = 0
  qrCode.value = null
  qrCodeInputRef.value.focus()
  openNotification("info", "top", "操作提示", `请重新扫描周转箱二维码`)
}

//-------------周转箱相关--------------------------------
const turnBoxState = {
  id: null,
  name: null,
  number: null,
}

const turnBoxInfo = ref({...turnBoxState})

async function queryTurnBoxInfo(id) {
  let turnBoxRes = await partStationTurnBoxApi.queryById(id)
  turnBoxInfo.value = turnBoxRes.data
}


//------------------菲票相关------------------------------

const feiTicketInfoSate = {
  id: null,
  imgUrl: null,
  instructOrderId: null,
  instructOrderNumber: null,
  issuedTime: null,
  planFinishTime: null,
  deliverTime: null,
  itemNumber: null,
  itemName: null,
  tieNum: null,
  styleColor: null,
  size: null,
  positions: null,
  num: null,
  cutNum: null,
  producePriority: null,

}
const feiTicketInfo = ref({...feiTicketInfoSate})


async function queryTicket(id) {
  let ticketRes = await feTicketApi.queryById(id)
  feiTicketInfo.value = ticketRes.data

}

//----------------扫码输入-------------------------------

const qrCode = ref('')
const qrCodeInputRef = ref(null);

/**
 * 条码校验
 * @param code
 */
function codeCheck(code) {
  if (_.isEmpty(code)) {
    return false;
  }

  let codeType = QR_CODE_TYPE_ENUM.getTypeByCode(code);
  // console.log("条码校验",code)
  if (codeType === QR_CODE_TYPE_ENUM.FE_TICKET || codeType === QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX) {
    return true;
  }
  openNotification("warn", 'top', '提示', '请扫描菲票二维码或裁片周转箱二维码');
  return false;
}

async function qrCodePressEnter(e) {
  let code = e.target.value;
  //校验
  if (!codeCheck(code)) {
    qrCodeInputRef.value.focus();
    qrCode.value = null
    e.target.value = null
    return
  }
  //获取值和类型
  let value = QR_CODE_TYPE_ENUM.getValueByCode(code);
  let codeType = QR_CODE_TYPE_ENUM.getTypeByCode(code);
  if (_.isEmpty(value)) {
    openNotification("warn", 'top', '提示', '请扫描二维码');
    return
  }

  try {
    if (codeType === QR_CODE_TYPE_ENUM.FE_TICKET) {
      //扫描菲票需先扫箱码
      if (!turnBoxInfo.value.id) {
        openNotification("warn", 'top', '提示', '请先扫描裁片周转箱二维码');
        return
      }
      //查询菲票信息
      await queryTicket(value)
      //入箱、出箱、移箱
      await actionBox();
    } else if (codeType === QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX) {
      await queryTurnBoxInfo(value)
      await queryBoxInside(value)
    }
  } catch (e) {
    console.log()
  } finally {
    qrCodeInputRef.value.focus();
    qrCode.value = null
    e.target.value = null
    SmartLoading.hide();
  }

}

//入箱、出箱、移箱
async function actionBox() {
  try {
    let res = null;
    if (turnBoxOptType.value === IN) {
      res = await inToBox()
      getInToBoxTableData()
    } else if (turnBoxOptType.value === OUT) {
      res = await outBox()
      deleteOutBoxTableData()
    } else if (turnBoxOptType.value === MOVE) {
      res = await moveBox()
      getInToBoxTableData()
    }
    if(res){
      message.success(res.data)
    }
    // console.log("入箱、出箱、移箱", res)
  } catch (e) {
    let optType = e.data.data;
    let msg = e.data.msg
    if(!optType){
      return
    }
    if(optType === IN){
      Modal.confirm({
        title: '入箱操作提示',
        content: msg,
        okText: '确认入箱',
        cancelText: '忽略',
        onOk() {
          inToBox()
          getInToBoxTableData()
          qrCodeInputRef.value.focus();
        },
        onCancel() {
          console.log('Cancel');
          qrCodeInputRef.value.focus();
        },
      });
    }else if(optType === OUT){
      Modal.confirm({
        title: '出箱操作提示',
        content: msg,
        okText: '确认出箱',
        cancelText: '忽略',
        onOk() {
          outBox()
          deleteOutBoxTableData()
          qrCodeInputRef.value.focus();
        },
        onCancel() {
          console.log('Cancel');
          qrCodeInputRef.value.focus();
        },
      });

    }else if(optType === MOVE){
      Modal.confirm({
        title: '移箱操作提示',
        content: msg,
        okText: '确认移箱',
        cancelText: '忽略',
        onOk() {
          moveBox()
          getInToBoxTableData()
          qrCodeInputRef.value.focus();
        },
        onCancel() {
          console.log('Cancel');
          qrCodeInputRef.value.focus();
        },
      });
    }
    qrCodeInputRef.value.focus();
  }
}

//入箱
async function inToBox() {
  console.log("入箱", turnBoxInfo.value, feiTicketInfo.value)
  return await turnBoxInsideApi.inToBox({
    turnBoxId: turnBoxInfo.value.id,
    feTicketId: feiTicketInfo.value.id,
  })
  // await queryBoxInside(turnBoxInfo.value.id)
}


//出箱
async function outBox() {
  return await turnBoxInsideApi.outToBox({
    turnBoxId: turnBoxInfo.value.id,
    feTicketId: feiTicketInfo.value.id,
  })
}


async function moveBox() {
  return await turnBoxInsideApi.moveToBox({
    turnBoxId: turnBoxInfo.value.id,
    feTicketId: feiTicketInfo.value.id,
  })
}

//入箱填入表格数据
function getInToBoxTableData(){
 //过滤掉重复的菲票
 const ticketIds = nowTicketRecords.value.some(item=>item.id === feiTicketInfo.value.id)
 if(!ticketIds){
  nowTicketRecords.value.forEach(item => {
    item.isNewScan = false;
  });
  nowTicketRecords.value.unshift({
    ...feiTicketInfo.value,
    inTime: new Date().toLocaleString(),
    isNewScan: true,
  })
 }
}
//出箱删除表格数据
function deleteOutBoxTableData(){
  const ticketIds = nowTicketRecords.value.some(item=>item.id === feiTicketInfo.value.id)
  if(ticketIds){
    nowTicketRecords.value = nowTicketRecords.value.filter(item=>item.id !== feiTicketInfo.value.id)
  }
}
//添加颜色
function getRowClassName(record){
  return record.isNewScan ? 'new-scanned-row' : '';
}
//-----------------------箱内相关------------------------
const feiTicketColumns = [
  {
    title: '生产指令单编号',
    dataIndex: 'instructOrderNumber',
    ellipsis: true,
    // width: 100,
  },
  {
    title: 'SPU编号(款号)',
    dataIndex: 'itemNumber',
    ellipsis: true,
    // width: 100,
  },
  // {
  //   title: '物料名称',
  //   dataIndex: 'itemName',
  //   ellipsis: true,
  //   // width: 100,
  // },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    // width: 50,
  },

  {
    title: '款式颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    // width: 80,
  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    // width: 50,
  },
  {
    title: '部位',
    dataIndex: 'positions',
    ellipsis: true,
    // width: 50,
  },
  {
    title: '数量',
    dataIndex: 'num',
    ellipsis: true,
    // width: 80,
  },
  {
    title: '入箱时间',
    dataIndex: 'inTime',
    ellipsis: true,

  }
]

const nowTicketCount = ref(0)//当前扎数
const nowTicketRecords = ref([])//当前箱内裁片

async function queryBoxInside(id) {
  let res = await turnBoxInsideApi.queryList({turnBoxId: id})
  nowTicketRecords.value = res.data
}


//------------------通同----------------------------
function resetData() {
  feiTicketInfo.value = Object.assign(feiTicketInfoSate)
  turnBoxInfo.value = Object.assign(turnBoxState)
  nowTicketRecords.value = []
  nowTicketCount.value = 0
  //清空输入框数据
  qrCode.value = null
  qrCodeInputRef.value.value = null;
  qrCodeInputRef.value.focus();
}

function openNotification(type, placement, message, description) {
  notification[type]({
    message: message,
    description: description,
    placement: placement,
  });
}

onMounted(() => {
  qrCodeInputRef.value.focus();
})
</script>
<style scoped lang="less">
.title-line {
  padding-left: 6px;
  font-size: 15px;
  font-weight: bold;
  border-left: #00a0e9 3px solid;
  margin-bottom: 12px;
}

.sub-item {
  margin-bottom: 8px;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80%;
  height: 100%;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.04);
  color: #888;
}

:deep(.new-scanned-row) {
  background-color: #e8f5e9; // 浅绿色背景
}
</style>

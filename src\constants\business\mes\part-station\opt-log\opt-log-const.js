/**
 * 操作日志 操作类型 枚举
 *
 * @Author:    Linwj
 * @Date:      2024-10-18
 * @Copyright  zscbdic
 */

export const OPT_LOG_TYPE_ENUM = {
    MOVE : {
        value: 'MOVE',
        desc: '移库',
        label: '移库',
        color: '#1d73d4',
    },
    IN: {
        value: 'IN',
        desc: '入库',
        label: '入库',
        color: '#46c26f',
    },
    OUT: {
        value: 'OUT',
        desc: '出库',
        label: '出库',
        color: '#e55151',
    },
    STOCK_TAKE:{
        value: 'TAKE',
        desc: '盘库',
        label: '盘库',
        color: '#d88d4f',
    },

    getEnum(value) {
        if (value === 'MOVE') {
            return  OPT_LOG_TYPE_ENUM.MOVE;
        } else if (value === 'IN') {
            return OPT_LOG_TYPE_ENUM.IN;
        } else if(value === 'OUT'){
            return OPT_LOG_TYPE_ENUM.OUT
        } else if (value === 'TAKE'){
            return OPT_LOG_TYPE_ENUM.STOCK_TAKE
        } else{
            return "类型错误"
        }
    }
};

export default {
    OPT_LOG_TYPE_ENUM,
};

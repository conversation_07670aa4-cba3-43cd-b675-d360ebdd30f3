/**
 * 生产领料单 api 封装
 *
 * @Author:    fkf
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkProdPickMtrlApi = {

  /**
   * 分页查询  
   */
  queryPage : (param) => {
    return postRequest('/stkProdPickMtrl/queryPage', param);
  },
  /**
   * 增加  
   */
  add: (param) => {
      return postRequest('/stkProdPickMtrl/add', param);
  },

  /**
   * 修改  
   */
  update: (param) => {
      return postRequest('/stkProdPickMtrl/update', param);
  },

  /**
   * 根据id查询其他出库单  
   */
  byId: (id) => {
      return getRequest(`/stkProdPickMtrl/byId?id=${id}`);
  },

  /**
   * 修改单据状态
   */
  status: (id) => {
      return getRequest(`/stkProdPickMtrl/status?id=${id}`);
  },

  /**
   * 删除
   */
  delete: (id) => {
      return getRequest(`/stkProdPickMtrl/delete?id=${id}`);
  },
};

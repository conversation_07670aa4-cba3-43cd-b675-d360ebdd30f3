<!--
  * 结算记录
  *
  * @Author:    linwj
  * @Date:      2024-11-20 20:48:59
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="_.isNil(actualName)?'':'员工： '+actualName"
      width="1200px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
      <!---------- 表格 begin ----------->
      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          bordered
          :loading="tableLoading"
      />
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {ref} from 'vue';
import _ from 'lodash';
import { smartSentry } from '/@/lib/smart-sentry';
import {settlementRecordApi} from "/@/api/business/mes/salary/settlement-record-api.js";

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);
//员工姓名
const actualName = ref(undefined)
// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  if(!_.isNil(rowData)){
    visibleFlag.value = true;
  }
  actualName.value = rowData.actualName
  queryData(rowData.id)
}
function onClose() {
  visibleFlag.value = false;
}

const columns = ref([
  {
    title: '生产指令单编号',
    dataIndex: 'produceInstructOrderNumber',
    ellipsis: true,
    width: 150
  },

  {
    title: '裁床单编号',
    dataIndex: 'cutBedSheetNumber',
    ellipsis: true,
    width: 150
  },
  {
    title: '床次',
    dataIndex: 'cutNum',
    ellipsis: true,
    width: 50
  },

  {
    title: '物料名称',
    dataIndex: 'itemName',
    ellipsis: true,
    width: 150
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    ellipsis: true,
    width: 100
  },
  {
    title: '款式颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    width: 80
  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    width: 50
  },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    width: 50
  },

  {
    title: '工序名称',
    dataIndex: 'processName',
    ellipsis: true,
    width: 80
  },
  {
    title: '报工数量',
    dataIndex: 'workQuantity',
    ellipsis: true,
    width: 80
  },
  {
    title: '单价价格',
    dataIndex: 'price',
    ellipsis: true,
    width: 80

  },
  {
    title: '报工时间',
    dataIndex: 'reportTime',
    ellipsis: true,
    defaultSortOrder: 'descend',
    width: 150

  },
]);


// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 查询数据
async function queryData(recordId) {
  tableLoading.value = true;
  try {
    let queryResult = await settlementRecordApi.queryDetailsByRecordId(recordId);
    tableData.value = queryResult.data;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

defineExpose({
  show,
});
</script>

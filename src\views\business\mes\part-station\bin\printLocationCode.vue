<!--
  * 打印货位码
-->
<template>
  <a-modal :open="visibleFlag" title="打印货位码" width="800px" :footer="null" @cancel="onClose" >
    <a-form class="smart-query-form">
      <a-button @click="handleExport" type="primary">导出</a-button>
    </a-form>
    <div style=" max-height: 170px; overflow-x: hidden; overflow-y: auto;">
      <div id="pdf-content" style="width: 655px;">
        <div v-for="(item,index) in qrKeys" :key="index" class="background" style="background: #037dc3; padding: 15px;width: 655px" >
          <div class="QRCode" style="width: 180px;height: 140px">
            <div class="qrInfo">
              <vue-qr
                  :text="qrInfoObject[`${item}`]"
                  :size="140"
              />
            </div>
          </div>
          <div class="locationInfo" style="width: 450px;height: 140px">
            <!--货架号-->
            <div class="shelf-number" :style="{fontSize: fontSize + 'px'}">
            {{ item }}
            </div>
            <div class="shelf-number">
              <ArrowUpOutlined  v-if="arrowDirection === 0" :style="{fontSize: '75px'}"/>
              <ArrowDownOutlined  v-if="arrowDirection === 1" :style="{fontSize: '75px'}"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="tools">
      <a-row>
        <a-col :span="4">
          <span class="tools-title">文件名:</span>
        </a-col>
        <a-col :span="4">
          <a-input v-model:value="fileName" style=" width:88px; margin-left: 16px" />
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="4">
          <span class="tools-title">纸张宽度:</span>
        </a-col>
        <a-col :span="4">
          <a-input-number v-model:value="pageSize[0]" :min="0" :max="300" style="margin-left: 16px" />
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="4">
          <span class="tools-title">纸张高度:</span>
        </a-col>
        <a-col :span="4">
          <a-input-number v-model:value="pageSize[1]" :min="0" :max="300" style="margin-left: 16px" />
        </a-col>
      </a-row>
      <a-row> 
        <a-col :span="4">
          <span class="tools-title">字号:</span>
        </a-col>
        <a-col :span="4">
          <a-slider v-model:value="fontSize" :min="12" :max="100"/>
        </a-col>
        <a-col :span="4">
          <a-input-number v-model:value="fontSize" :min="12" :max="100" style="margin-left: 16px" />
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="4">
          <span class="tools-title">箭头方向:</span>
        </a-col>
        <a-col :span="12">
          <a-radio-group v-model:value="arrowDirection">
            <a-radio-button :value="0">上</a-radio-button>
            <a-radio-button :value="1">下</a-radio-button>
          </a-radio-group>
        </a-col>
      </a-row>
    </div>

  </a-modal>
</template>

<script setup>
import { getLocationPdf} from '/src/utils/pdf-util.js';
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import {  ref} from "vue";
import _ from 'lodash'

// 字体大小
const fontSize = ref(81)
// 纸张尺寸
const pageSize = ref([200,50])
// 切换方向
const arrowDirection = ref(0)
// 文件名
const fileName = ref('货位码')

// 是否显示
const visibleFlag = ref(false);

const qrInfoObject = ref() //二维码信息 key:货位码 value:二维码内容
const qrKeys = ref() //货位码

function handleExport() {
  getLocationPdf(fileName.value,"#pdf-content",pageSize.value,qrKeys.value.length)
  onClose()
}

// ------------------------ 显示与隐藏 ------------------------
// // 是否显示
// const visibleFlag = ref(false);

function show(data) {
  visibleFlag.value = true;
  // 数据赋值
  qrInfoObject.value = data
  qrKeys.value = Object.keys(data)
}

function onClose() {
  visibleFlag.value = false;
}


defineExpose({
  show,
});
</script>
<style scoped lang="less">
.background{
  display: flex;
  .QRCode{
    .qrInfo{
      margin-right: 10px;
    }
  }
  .locationInfo{
    background: white;
    color: #037dc3;
    display: flex;
    align-items: center;
    .shelf-number {
      font-weight:bold;
      display: inline-block;
      text-align: center;
      font-size: 81px; /* 数字字体大小 */
      margin: 0 10px; /* 数字两边的外边距 */
    }
    .up-arrow {
      font-size: 20px; /* 箭头字体大小 */
    }
  }
}
.tools{
  margin-top: 20px;
  text-align: center;
  .tools-title{
    line-height: 34px;
    font-size: 16px;
    text-align: center;
  }
}
</style>

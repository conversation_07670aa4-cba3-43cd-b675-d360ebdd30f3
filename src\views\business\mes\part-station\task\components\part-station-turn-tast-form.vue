<template>
    <a-modal
        title="驿站任务详情"
        :width="1000"
        :open="visible"
        @cancel="handleCancel"
    >
        <a-form :model="form" :label-col="{ span: 8 }" :wrapper-col="{ span: 14 }" disabled>
            <a-row>
                <a-col :span="8">
                    <a-form-item label="创建时间" name="createTime">
                        <a-input v-model:value="form.createTime" />
                    </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="删除标识" name="deletedFlag">
                            <a-input v-model:value="form.deletedFlag" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="任务编号" name="number">
                            <a-input v-model:value="form.number" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="任务名称" name="name">
                            <a-input v-model:value="form.name" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="周转箱ID" name="turnoverBoxId">
                            <a-input v-model:value="form.turnoverBoxId" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="提交人名称" name="submitterName">
                            <a-input v-model:value="form.submitterName" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="更新时间" name="updateTime">
                            <a-input v-model:value="form.updateTime" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-item label="更新人" name="updateBy">
                            <a-input v-model:value="form.updateBy" />
                        </a-form-item>
                    </a-col>
                        <a-col :span="8">
                        <a-form-item label="备注" name="remark">
                            <a-textarea v-model:value="form.remark" />
                        </a-form-item>
                    </a-col>
            </a-row>
        </a-form>

    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </template>
    </a-modal>
</template>
<script setup>
import { ref, reactive } from 'vue';

const formState = reactive({
    createTime: undefined,//创建时间
    createBy: undefined,//创建人
    updateTime: undefined,//更新时间
    updateBy: undefined,//更新人
    deletedFlag: undefined,//删除标识
    remark: undefined,//备注
    taskScope: undefined,//任务作用范围
    number: undefined,//任务编号
    name: undefined,//任务名称
    priority: undefined,//任务优先级
    type: undefined,//任务类型
    status: undefined,//任务状态
    autoDispatchFlag: undefined,//是否自动派发
    startLocationId: undefined,//起点货位ID
    endLocationId: undefined,//终点货位ID
    ticketIds: undefined,//菲票ID
    turnoverBoxId: undefined,//周转箱ID
    submitTime: undefined,//提交时间
    getTime: undefined,//领取时间
    beginTime: undefined,//开始时间
    endTime: undefined,//结束时间
    submitterType: undefined,//提交人类型
    submitterId: undefined,//提交人ID
    submitterName: undefined,//提交人名称
    executorType: undefined,//执行人类型
    executorId: undefined,//执行人ID
    executorName: undefined,//执行人名称
    bizType: undefined,//关联单据类型
    bizId: undefined,//关联单据ID
    bizDetailSeq: undefined,//关联单据详情序号
    bizDetailId: undefined,//关联单据详情ID
});

const form = reactive({ ...formState });
const visible = ref(false);

function showForm(record) {
    visible.value = true;
    Object.assign(form, record);
}

function handleCancel() {
    visible.value = false;
}

defineExpose({
    showForm,
});
</script>
/**
 * 生产退料单 api 封装
 *
 * @Author:    pxz
 * @Date:      2025-02-16
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkProduceInApi = {
    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/stkProdIn/queryPage', param);
    },
    /**
     * 添加  <AUTHOR>
     */
    add: (param) => {
        return postRequest('/stkProdIn/add', param);
    },
    /**
     * 修改  <AUTHOR>
     */
    update: (param) => {
        return postRequest('/stkProdIn/update', param);
    },
    /**
     * 根据id查询  <AUTHOR>
     */
    getById: (id) => {
        return getRequest(`/stkProdIn/byId?id=${id}`);
    },
    /**
     * 修改单据状态  <AUTHOR>
     */
    updateStatus: (id) => {
        return getRequest(`/stkProdIn/status?id=${id}`);
    },
    /**
     * 删除单据  <AUTHOR>
     */
    deleteById: (id) => {
        return getRequest(`/stkProdIn/delete?id=${id}`);
    }
}
<template>
  <a-modal
      :title="'编辑'"
      :width="480"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
      <a-form-item label="密钥" name="key">
          <a-input style="width: 85%" v-model:value="form.key" placeholder="请输入密钥" />
      </a-form-item>
      <a-form-item label="iv" name="iv">
          <a-input-password style="width: 85%" v-model:value="form.iv" placeholder="请输入iv" />
      </a-form-item>
      <a-form-item label="域名" name="host">
          <a-input style="width: 85%" v-model:value="form.host" placeholder="请输入域名" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/src/components/framework/smart-loading';
import { smartSentry } from '/src/lib/smart-sentry';
import { yuanyiApi } from '/src/api/system/yuanyi-api';

const visibleFlag = ref(false);
const formRef = ref();

const form = reactive({
    key: undefined,
    iv: undefined,
    host: undefined,
});

const rules = {
  key: [{ required: true, message: '请输入密钥' }],
  iv: [{ required: true, message: '请输入iv' }],
  host: [{ required: true, message: '请输入域名' }],
};

const onClose = () => {
  Object.assign(form, {
    key: undefined,
    iv: undefined,
    host: undefined,
  });
  visibleFlag.value = false;
};

const onSubmit = async () => {
  SmartLoading.show();
  try {
    await formRef.value.validate();
    await yuanyiApi.updateYuanyiConfig({...form});
    message.success('保存成功');
    onClose();
  } catch (error) {
    smartSentry.captureError(error);
    message.error(error.message);
  } finally {
    SmartLoading.hide();
  }
};

const open = async () => {
  const res = await yuanyiApi.getYuanyiConfig();
  Object.assign(form, {
    key: res.data.key,
    iv: res.data.iv,
    host: res.data.host,
  });
  visibleFlag.value = true;
};

defineExpose({
  open,
});
</script>
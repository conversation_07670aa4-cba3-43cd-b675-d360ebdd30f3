<template>
  <board-title :title="'驿站仪表盘'" style="line-height: 70px; height: 70px"/>

  <a-card :body-style="{ padding: 0 }" style="padding: 12px; margin-bottom: 8px">
    <a-row>
      <a-col span="8">
        <span style="margin-right: 10px">仓库:</span>
        <a-select placeholder="请选择" style="width: 50%" v-model:value="warehouseId" :options="warehouseOptions"
                  @change="warehouseChange"/>
      </a-col>
      <a-col span="16" style="display: flex; justify-content: flex-end">
        <a-radio-group button-style="solid" @change="onClickDate" v-model:value="dayChoice">
          <a-radio-button value="yesterday">昨天</a-radio-button>
          <a-radio-button value="today">今天</a-radio-button>
          <a-radio-button value="7days">近7天</a-radio-button>
          <a-radio-button value="30days">近30天</a-radio-button>
          <a-radio-button value="60days">近60天</a-radio-button>
        </a-radio-group>
        <a-range-picker :presets="defaultTimeRanges" style="width: 240px; margin-left: 6px" @change="onChangeDate"/>
        <a-button type="primary" style="margin-left: 6px; width: 120px" @click="search">查询</a-button>
      </a-col>
    </a-row>
  </a-card>

  <a-card :body-style="{ padding: 0 }" style="padding: 12px; margin-bottom: 8px">
    <div style="width: 100%; display: flex; justify-content: space-between">
      <div class="num-card">
        <div class="num">{{ inBoundCount }}<span class="unit">次</span></div>
        <div>入库次数</div>
      </div>
      <div class="num-card">
        <div class="num">{{ inBoundNum }}<span class="unit">件</span></div>
        <div>入库件数</div>
      </div>
      <div class="num-card">
        <div class="num">{{ outBoundCount }}<span class="unit">次</span></div>
        <div>出库次数</div>
      </div>
      <div class="num-card">
        <div class="num">{{ outBoundNum }}<span class="unit">件</span></div>
        <div>出库件数</div>
      </div>
      <div class="num-card">
        <div class="num">{{ inOutRatio }}<span class="unit">%</span></div>
        <div>
          出入库比率
          <div style="font-size: 12px; line-height: 12px; color: #909399">入库数/出库数</div>
        </div>
      </div>
    </div>
  </a-card>

  <a-card :body-style="{ padding: 0 }" style="padding: 12px">
    <a-spin style="width: 100%" :spinning="tableLoading"/>
    <a-row>
      <a-col span="19">
        <stock-in-out-count-line ref="stockInOutCountLine" style="width: 100%"/>
        <stock-in-out-num-line ref="stockInOutNumLine" style="width: 100%"/>
      </a-col>
      <a-col span="5">
        <div class="warehouse-card" style="display: flex; justify-content: space-between">
          <div>
            <div>货架数</div>
            <div class="num">{{ rackNum }}个</div>
          </div>
          <div>
            <div>货位数</div>
            <div class="num">{{ binNum }}个</div>
          </div>
        </div>
        <div class="warehouse-card" style="display: flex; justify-content: space-between">
          <div>
            <div>总扎数</div>
            <div class="desc">仓库内存放扎数</div>
            <div class="num">{{ totalTieNum }}扎</div>
          </div>
          <div style="text-align: right">
            <div>总库存数</div>
            <div class="desc">仓库内存放裁片数</div>
            <div class="num">{{ totalInventoryNum }}件</div>
          </div>
        </div>

        <div class="warehouse-card">
          <div>平均存放天数</div>
          <div class="desc">∑(当前时间-入库时间)/扎数</div>
          <div class="num">{{ averageStoredDays }}天</div>
        </div>
        <div class="warehouse-card">
          <div>库位利用率</div>
          <div class="desc">总库位数量/使用库存数量×100%</div>
          <div class="num">{{ binUsageRate }}%</div>
        </div>
      </a-col>
    </a-row>
  </a-card>
</template>
<script setup>
import {defaultTimeRanges} from '/@/lib/default-time-ranges.js';
import StockInOutCountLine from '/@/views/business/mes/part-station/stats-board/components/stock-in-out-count-line.vue';
import StockInOutNumLine from '/src/views/business/mes/part-station/stats-board/components/stock-in-out-num-bar.vue';
import {onMounted, ref, watch} from 'vue';
import {partStationWarehouseApi} from '/@/api/business/mes/part-station/warehouse/part-station-warehouse-api.js';
import {message} from 'ant-design-vue';
import {smartSentry} from '/@/lib/smart-sentry.js';
import BoardTitle from '/@/components/business/mes/board/board-title.vue';
import dayjs from 'dayjs';
import {partStationOptLogApi} from "/@/api/business/mes/part-station/opt-log/part-station-opt-log-api.js";

const tableLoading = ref(false);

const warehouseOptions = ref([]); // 仓库下拉框数据
const warehouseId = ref(null); // 仓库id
const inOutRatio = ref(null); // 出入库比率
const stockInOutCountLine = ref(null); // 出入库次数趋组件
const stockInOutNumLine = ref(null); // 出入库数量趋势组件
const totalInventoryNum = ref(0); // 总库存数
const totalTieNum = ref(0); // 总扎数
const averageStoredDays = ref(0); // 平均存放天数
const binUsageRate = ref(0); // 库位利用率
const rackNum = ref(0); // 货架数
const binNum = ref(0); // 货位数
const inBoundCount = ref(0);// 入库次数
const inBoundNum = ref(0);// 入库件数
const outBoundCount = ref(0);// 出库次数
const outBoundNum = ref(0);// 出库件数

const dayChoice = ref('30days');
const dayRange = ref(getDateBeginAndEndTime(dayChoice.value));

onMounted(async () => {
  await warehouseFocus();
  // console.log('dayRange', dayRange.value);

  await initData();
});

/**
 * 初始化页面数据
 */
function initData() {
  // 初始化出入库比率
  queryInOutRatio()
  // 初始化次数趋势与数量趋势图标数据
  queryInOutNumTrend()
  queryInOutCountTrend()
  // 初始化总库存数
  queryTotalInventory()
  // 初始化总扎数
  queryTotalTieNum()
  // 平均存放天数
  queryAverageStoredDays()
  // 库位利用率
  queryBinUsageRate()
  // 获取所属仓库下货架数量
  queryRackNum()
  // 获取所属仓库下货位数量
  queryBinNum()
  // 获取出入库次数与件数
  queryInCountAndNum()
  queryOutNumAndCount()
}

async function queryInOutRatio() {
  // 初始化出入库比率
  const inOutRatioData = await partStationWarehouseApi.inOutRatio({warehouseId: warehouseId.value, ...dayRange.value});
  inOutRatio.value = inOutRatioData.data;
}

async function queryTotalInventory() {
  // 初始化总库存数
  const totalInventoryData = await partStationWarehouseApi.totalInventoryNum({warehouseId: warehouseId.value});
  totalInventoryNum.value = totalInventoryData.data;
}

async function queryTotalTieNum() {
  // 初始化总扎数
  const totalTieNumData = await partStationWarehouseApi.totalTieNum({warehouseId: warehouseId.value});
  totalTieNum.value = totalTieNumData.data;
}

async function queryAverageStoredDays() {
  // 平均存放天数
  const averageStoredDaysData = await partStationWarehouseApi.averageStoredDays({warehouseId: warehouseId.value});
  averageStoredDays.value = averageStoredDaysData.data;
}

async function queryBinUsageRate() {
  // 库位利用率
  const binUsageRateData = await partStationWarehouseApi.binUsageRate({warehouseId: warehouseId.value});
  binUsageRate.value = binUsageRateData.data;
}

async function queryRackNum() {
  // 获取所属仓库下货架数量
  const getRackNum = await partStationWarehouseApi.getRackNum({warehouseId: warehouseId.value});
  // console.log('binCountData', getRackNum.data);
  rackNum.value = getRackNum.data;
}

async function queryBinNum() {
  // 获取所属仓库下货位数量
  const getBinNum = await partStationWarehouseApi.getBinNum({warehouseId: warehouseId.value});
  binNum.value = getBinNum.data;
}

async function queryInOutCountTrend() {
  const inOutCountTrendData = await partStationWarehouseApi.inOutCountTrend({warehouseId: warehouseId.value, ...dayRange.value});
  stockInOutCountLine.value.data = inOutCountTrendData.data;
}

async function queryInOutNumTrend() {
  const inOutNumTrendData = await partStationWarehouseApi.inOutNumTrend({warehouseId: warehouseId.value, ...dayRange.value});
  stockInOutNumLine.value.data = inOutNumTrendData.data;
}

async function queryInCountAndNum() {
  let res = await partStationOptLogApi.queryInboundCountAndNum({warehouseId: warehouseId.value,...dayRange.value})
  inBoundCount.value = res.data.count;
  inBoundNum.value = res.data.num;
}

async function queryOutNumAndCount() {
  let res = await partStationOptLogApi.queryOutboundCountAndNum({warehouseId: warehouseId.value,...dayRange.value})
  outBoundCount.value = res.data.count;
  outBoundNum.value = res.data.num;
}

/**
 * 查询
 */
async function search() {
  tableLoading.value = true;
  // console.log('进行查询', warehouseId.value, dayRange.value);
  //TODO 查询数据
  // await queryInOutCountTrend()
  // await queryInOutNumTrend()
  // await queryInOutRatio()
  initData();
  tableLoading.value = false;
}

/**
 * 选择日期
 * @param dates
 * @param dateStrings
 */
function onChangeDate(dates, dateStrings) {
  dayRange.value.beginTime = dateStrings[0];
  dayRange.value.endTime = dateStrings[1];
}

/**
 * 仓库下拉框选择事件
 */
async function warehouseChange() {
  initData();
}

/**
 * 点击日期
 * @param e
 */
async function onClickDate(e) {
  dayRange.value = getDateBeginAndEndTime(e.target.value);
  const inOutCountTrendData = await partStationWarehouseApi.inOutCountTrend({warehouseId: warehouseId.value, ...dayRange.value});
  stockInOutCountLine.value.data = inOutCountTrendData.data;
  const inOutNumTrendData = await partStationWarehouseApi.inOutNumTrend({warehouseId: warehouseId.value, ...dayRange.value});
  stockInOutNumLine.value.data = inOutNumTrendData.data;
}

async function warehouseFocus() {
  tableLoading.value = true;
  try {
    let res = await partStationWarehouseApi.getAll();
    //构造options
    warehouseOptions.value = res.data.map((e) => {
      return {
        value: e.id,
        label: e.name + '-' + e.warehouseCode,
        name: e.name,
        warehouseCode: e.warehouseCode,
      };
    });
    if (warehouseOptions.value.length > 0) {
      warehouseId.value = warehouseOptions.value[0].value;
    }
  } catch (e) {
    message.error('获取仓库信息失败');
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

function getDateBeginAndEndTime(type) {
  if (type === 'yesterday') {
    return {
      beginTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      endTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    };
  } else if (type === 'today') {
    return {
      beginTime: dayjs().format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
  } else if (type === '7days') {
    return {
      beginTime: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
  } else if (type === '30days') {
    return {
      beginTime: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
  } else if (type === '60days') {
    return {
      beginTime: dayjs().subtract(60, 'day').format('YYYY-MM-DD'),
      endTime: dayjs().format('YYYY-MM-DD'),
    };
  }
}
</script>
<style scoped lang="less">
.num-card {
  background-color: #e0effb;
  border-radius: 4px;
  width: 20%;
  margin-right: 6px;
  padding: 16px 0;
  font-size: 20px;
  text-align: center;

  .num {
    font-size: 30px;
    line-height: 30px;
    font-weight: 500;
  }

  .unit {
    font-size: 14px;
    line-height: 14px;
    margin-left: 4px;
    color: #606266;
  }
}

.warehouse-card {
  background-color: #fafafa;
  font-size: 15px;
  padding: 16px;
  margin-bottom: 8px;

  .num {
    font-size: 24px;
    //font-weight: bold;
  }

  .desc {
    color: #909399;
    font-size: 12px;
    line-height: 12px;
  }
}
</style>

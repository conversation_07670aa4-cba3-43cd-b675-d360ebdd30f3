export class cell {
    /*
    * type: 0-路径，1-障碍，2-货架，3-出口，4-入口，5-出入口
    * */
    constructor(type, desc, color) {
        this.type = type;
        this.desc = desc;
        this.color = color;
        this.fontColor = '#fff';

    }
}

export const CELL_TYPE_ENUM = {

    PATH: {
        value: 0,
        desc: '路径',
        color: '#e6ecf2',
        fontColor: '#000'
    },
    OBSTACLE: {
        value: 1,
        desc: '障碍',
        color: '#b3bdc6',
        fontColor: '#fff'
    },
    RACK: {
        value: 2,
        desc: '货架',
        color: '#1677ff',
        fontColor: '#fff'
    },
    ENTRY: {
        value: 4,
        desc: '入口',
        color: '#1df61e',
        fontColor: '#fff'
    },
    EXIT: {
        value: 3,
        desc: '出口',
        color: '#1df61e',
        fontColor: '#fff'
    },
    ENTRY_EXIT: {
        value: 5,
        desc: '出入口',
        color: '#1df61e',
        fontColor: '#fff'
    },

    getEnum(value) {
        if (value === 0) {
            return CELL_TYPE_ENUM.PATH;
        } else if (value === 1) {
            return CELL_TYPE_ENUM.OBSTACLE;
        } else if (value === 2) {
            return CELL_TYPE_ENUM.RACK;
        } else if (value === 3) {
            return CELL_TYPE_ENUM.EXIT;
        } else if (value === 4) {
            return CELL_TYPE_ENUM.ENTRY;
        } else if (value === 5) {
            return CELL_TYPE_ENUM.ENTRY_EXIT;
        }
    }
}

/**
 * 是否在边界上
 * @param w
 * @param h
 * @param x
 * @param y
 * @returns {boolean}
 */
export function edgeCheck(w,h,x,y){
    return x===0 || y===0 || x===w-1 || y===h-1
}

/**
 * 入口和出口是否合法
 * @param map
 * @returns {boolean}
 */
export function entryAndExitCheck(map){
    let entryCount = 0;
    let exitCount = 0;
    for(let i=0;i<map.length;i++){
        for(let j=0;j<map[i].length;j++){
            if(map[i][j].type===CELL_TYPE_ENUM.ENTRY_EXIT.value){
                entryCount++;
                exitCount++;
            }else if(map[i][j].type===CELL_TYPE_ENUM.ENTRY.value){
                entryCount++;
            }else if(map[i][j].type===CELL_TYPE_ENUM.EXIT.value){
                exitCount++;
            }
        }
    }
    return entryCount>=1 && exitCount>=1
}

/**
 * 初始化地图
 * @param wCount
 * @param hCount
 * @returns {*[]}
 */
export function initMap(wCount, hCount) {
    const mazeArray = [];
    for (let i = 0; i < hCount; i++) {
        mazeArray[i] = [];
        for (let j = 0; j < wCount; j++) {
            mazeArray[i][j] = new cell(CELL_TYPE_ENUM.PATH.value, null, CELL_TYPE_ENUM.PATH.color);
        }
    }
    return mazeArray;
}

/**
 * 获取已使用的货架id
 * @param map
 * @returns {*[]}
 */
export function getUsedRackIds(map){
    let usedRackIds = [];
    for(let i=0;i<map.length;i++){
        for(let j=0;j<map[i].length;j++){
            if(map[i][j].type===CELL_TYPE_ENUM.RACK.value){
                usedRackIds.push(map[i][j].rackId)
            }
        }
    }
    return usedRackIds
}

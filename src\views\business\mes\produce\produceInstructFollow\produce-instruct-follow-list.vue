<!--
  * 生产跟单-指派跟单
  *
  * @Author:    lwj
  * @Date:      2024-07-26 21:36:36
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!--            <a-form-item class="smart-query-form-item">-->
      <!--              <a-input-search-->
      <!--                  v-model:value="searchValue"-->
      <!--                  placeholder="搜索客户名称"-->
      <!--                  style="width: 200px"-->

      <!--              />-->
      <!--            </a-form-item>-->
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->

    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑跟单</a-button>
          </div>
        </template>

        <template v-else-if="column.dataIndex === 'produceStatus'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'employeeName'">
          <a-tag v-for="tag in record.employeeName" :key="tag" color="#3b5999">
            <template #icon>
              <UserOutlined />
            </template>
            {{ tag }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'delegateName'">
          <a-tag color="#3b5999" v-if="record.delegateName">
            <template #icon>
              <UserOutlined />
            </template>
            {{ record.delegateName }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).label }}
          </a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-container">
            <file-preview v-if="record.imgUrl && record.imgUrl.length > 0" :file-list="[record.imgUrl[0]]" type="picture" :width="70" />
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <ProduceInstructFollowForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, watch } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { produceInstructFollowApi } from '/@/api/business/mes/produce/produce-instruct-follow-api.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import ProduceInstructFollowForm from './produce-instruct-follow-form.vue';
  import FilePreview from '/@/components/support/file-preview/index.vue';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      ellipsis: true,
      align: 'center',
      width: 90,
    },
    {
      title: '指令单编号',
      dataIndex: 'instructNumber',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '指令单名称',
      dataIndex: 'name',
      ellipsis: true,
      align: 'center',
    },

    {
      title: '客户名称',
      dataIndex: 'customerName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '联系方式',
      dataIndex: 'telephone',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '生产状态',
      dataIndex: 'produceStatus',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      title: '跟单员',
      dataIndex: 'employeeName',
      ellipsis: true,
      Width: 500,
    },

    {
      title: '委派人',
      dataIndex: 'delegateName',
      ellipsis: true,

      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
    },
  ]);
  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }
  //客户动态搜索

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await produceInstructFollowApi.queryPage(queryForm);
      verifyImgUrl(queryResult);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  //校验图片url
  function verifyImgUrl(queryResult) {
    queryResult.data.list.map((e) => {
      if (!e.imgUrl) {
        e.imgUrl = [];
      }
    });
  }

  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data) {
    console.log('data', data);
    formRef.value.show(data);
  }
  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行  复选框
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await produceInstructFollowApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      await queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

<style scoped>
  .image-container {
    width: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-container :deep(img) {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
</style>

/**
 * 物料分类表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-02 09:54:23
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';


export const itemTypeApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/itemType/queryPage', param);
  },

  // 树表-查询类目层级树
  queryCategoryTree: (param) => {
    return postRequest('/itemType/queryTree', param);
  },


  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/itemType/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/itemType/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/itemType/delete/${id}`);
  },

};

<template>
      <a-input v-if="showQty" v-model:value="data.qty" style="width:100%" disabled />
      <a-input v-if="showAvbQty" v-model:value="data.avbQty" style="width:100%" disabled />
      <a-input v-if="showLockQty" v-model:value="data.lockQty" style="width:100%" disabled />
      <a-input v-if="showPredictQty" v-model:value="data.predictQty" style="width:100%" disabled />
  </template>
<script setup>
import { reactive, watch } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';
import {stkInventoryApi} from '/@/api/business/mes/stock/inventory-api';
const props=defineProps({
    materialId: Number,
    warehouseId: Number,
    locationId: Number,
    lotId: Number,
    showQty: {type: Boolean, default: false},
    showAvbQty: {type: Boolean, default: true},
    showLockQty: {type: Boolean, default: false},
    showPredictQty: {type: Boolean, default: false},
})

//查询参数
const paramsDefault={
    materialId: undefined,
    warehouseId: undefined,
    locationId: undefined,
    lotId: undefined
}
const params=reactive({...paramsDefault})
//结果数据
const data = reactive({
  qty: 0,
  avbQty: 0,
  lockQty: 0,
  predictQty: 0
});
watch(
  () => [props.materialId, props.warehouseId, props.locationId, props.lotId],
  ([materialId, warehouseId, locationId, lotId]) => {
    if(materialId){
    params.materialId = materialId;
    params.warehouseId = warehouseId;
    params.locationId = locationId;
    params.lotId = lotId;
    queryData();
    }
  },
  { immediate: true }
);
async function queryData(){
    try {
        let queryResult = await stkInventoryApi.queryMaterialInventory(params);
        data.avbQty=queryResult.data.avbQty;
        data.lockQty=queryResult.data.lockQty;
        data.predictQty=queryResult.data.predictQty;
    } catch (e) {
        smartSentry.captureError(e);
    }
}
</script>
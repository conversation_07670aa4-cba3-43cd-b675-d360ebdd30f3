<!--
  * 裁片收发日志
  *
  * @Author:    cjm
  * @Date:      2024-11-06 15:25:13
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!--      指令单编号和物料编号联动选择器-->
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="颜色" class="smart-query-form-item">
        <a-select
          v-model:value="initColorVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择颜色"
          :options="colorOptions"
          @change="handleColorChange"
        />
      </a-form-item>
      <a-form-item label="尺码" class="smart-query-form-item">
        <a-select
          v-model:value="initSizeVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择尺码"
          :options="sizeOptions"
          @change="handleSizeChange"
        />
      </a-form-item>
      <a-form-item label="收发状态" class="smart-query-form-item">
        <SmartEnumSelect width="120px" v-model:value="queryForm.actionStatus" placeholder="请选择收发状态" enum-name="ACTION_STATUS_ENUM" />
      </a-form-item>
      <a-form-item label="收发范围" class="smart-query-form-item">
        <SmartEnumSelect width="120px" v-model:value="queryForm.dispatchRange" placeholder="请选择收发范围" enum-name="DISPATCH_RANGE_ENUM" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 1700 }"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'actionStatus'">
          {{ $smartEnumPlugin.getDescByValue('ACTION_STATUS_ENUM', text) }}
        </template>
        <template v-else-if="column.dataIndex === 'dispatchRange'">
          {{ $smartEnumPlugin.getDescByValue('DISPATCH_RANGE_ENUM', text) }}
        </template>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">
                <a-typography-text>{{ value }}</a-typography-text>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <PartDispatchLogForm ref="formRef" @reloadList="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, watch, computed } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/src/components/framework/smart-loading';
  import { partDispatchLogApi } from '/@/api/business/mes/part-dispatch/part-dispatch-log-api.js';
  import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import TableOperator from '/src/components/support/table-operator/index.vue';
  import PartDispatchLogForm from './part-dispatch-log-form.vue';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import { ACTION_STATUS_ENUM } from '/@/constants/business/mes/part-dispatch/part-dispatch-log-const.js';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      ellipsis: true,
      width: 135,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      width: 135,
    },
    {
      title: '扎号',
      dataIndex: 'tieNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '款式颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      width: 80,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      width: 80,
    },
    {
      title: '部位',
      dataIndex: 'positions',
      ellipsis: true,
      width: 80,
    },

    {
      title: '数量',
      dataIndex: 'num',
      ellipsis: true,
      width: 50,
      align: 'center',
    },

    {
      title: '收发范围',
      dataIndex: 'dispatchRange',
      ellipsis: true,
      width: 80,
    },
    {
      title: '收发状态',
      dataIndex: 'actionStatus',
      ellipsis: true,
      width: 80,
      align: 'center',
      customCell: (text, record, column) => {
        let backgroundColor = '';
        if (text.actionStatus === ACTION_STATUS_ENUM.SEND.value) {
          backgroundColor = '#E6A23C';
        } else {
          backgroundColor = '#67C23A';
        }
        return {
          style: {
            backgroundColor: backgroundColor,
            color: '#fff',
          },
        };
      },
    },
    {
      title: '下发人',
      dataIndex: 'senderName',
      ellipsis: true,
      width: 80,
    },

    {
      title: '下发时间',
      dataIndex: 'sendTime',
      ellipsis: true,
    },
    {
      title: '下发描述',
      dataIndex: 'sendDesc',
      ellipsis: true,
    },
    {
      title: '收回人',
      dataIndex: 'receiverName',
      ellipsis: true,
      width: 80,
    },
    {
      title: '收回时间',
      dataIndex: 'receiveTime',
      ellipsis: true,
    },
    {
      title: '收回描述',
      dataIndex: 'receiveDesc',
      ellipsis: true,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    actionStatus: undefined, //收或发
    dispatchRange: undefined, //收发范围
    instructNumber: undefined, //生产指令编号
    itemNumber: undefined, // 物料编号
    colors: [],
    sizes: [],
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    //两个编号情况
    insItemSelectRef.value.clearAllNumber();
    //颜色和尺码清空
    initColorVal.value = undefined;
    initSizeVal.value = undefined;
    colorOptions.value = [];
    sizeOptions.value = [];
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await partDispatchLogApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  //-------------------------------合计----------------------------------------
  const totals = computed(() => {
    const sums = {
      totalNum: 0, //总数量
    };
    tableData.value.forEach((item) => {
      sums.totalNum += item.num;
    });
    return sums;
  });
  //计算剩余的空单元格
  const summaryColSpans = 8; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => 1 + columns.value.length - summaryColSpans - Object.keys(totals.value).length); //加1是因为复选框列

  // ---------------------------- 处理两个编号 ----------------------------
  const insItemSelectRef = ref();
  function insItemChange(data) {
    // 回传数据
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
    queryForm.instructOrderId = data.instructOrderId;
  }

  // ---------------------------- 获取颜色和尺码 ----------------------------
  //下拉框最多可显示数量
  const maxTagCount = ref(1);
  //颜色和尺码下拉框初始值
  const initColorVal = ref(undefined);
  const initSizeVal = ref(undefined);

  watch(
    () => queryForm.instructNumber,
    (newVal) => {
      initColorVal.value = undefined;
      initSizeVal.value = undefined;
      if (newVal !== undefined) {
        queryByInsAndItemNumber();
      }
    }
  );
  //根据指令单id获取颜色和尺码信息
  async function queryByInsAndItemNumber() {
    let colorList = await produceInstructOrderClothesApi.queryClothesColorList(queryForm.instructOrderId);
    let sizeList = await produceInstructOrderClothesApi.queryClothesSizeList(queryForm.instructOrderId);
    outstandingOptions(colorList.data, colorOptions);
    outstandingOptions(sizeList.data, sizeOptions);
  }

  //拼装options
  const colorOptions = ref([]);
  const sizeOptions = ref([]);
  function outstandingOptions(sourceData, target) {
    target.value = [];
    sourceData.map((item, index) =>
      target.value.push({
        label: item,
        value: index,
      })
    );
  }

  // --------------------- 处理颜色和尺码 ------------------------
  function handleColorChange(item, option) {
    queryForm.colors = [];
    option.map((e) => {
      queryForm.colors.push(e.label);
    });
  }

  function handleSizeChange(item, option) {
    queryForm.sizes = [];
    option.map((e) => {
      queryForm.sizes.push(e.label);
    });
  }

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      let deleteForm = {
        goodsIdList: selectedRowKeyList.value,
      };
      await partDispatchLogApi.delete(data.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 批量删除 ----------------------------

  // 选择表格行
  const selectedRowKeyList = ref([]);

  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  // 批量删除
  function confirmBatchDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要批量删除这些数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestBatchDelete();
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求批量删除
  async function requestBatchDelete() {
    try {
      SmartLoading.show();
      await partDispatchLogApi.batchDelete(selectedRowKeyList.value);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

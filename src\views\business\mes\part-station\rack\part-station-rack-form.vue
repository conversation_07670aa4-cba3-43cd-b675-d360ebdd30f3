<!--
  * 裁片货架
  *
  * @Author:    cjm
  * @Date:      2024-10-06 16:49:27
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="400px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="所属仓库" name="warehouseId" :label-col="{ span: 5 }">
        <a-select ref="select" v-model:value="form.warehouseId" style="width: 100%" :options="warehouseOptions" />
      </a-form-item>
      <a-form-item label="货架" name="rackCode">
        <a-input style="width: 100%" v-model:value="form.rackCode" placeholder="货架编码" />
      </a-form-item>
      <a-form-item label="位置" name="location">
        <a-input style="width: 100%" v-model:value="form.location" placeholder="位置" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationRackApi } from '/@/api/business/mes/part-station/rack/part-station-rack-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { partStationWarehouseApi } from '/@/api/business/mes/part-station/warehouse/part-station-warehouse-api.js';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    //先查询所有仓库信息
    queryAllWarehouse();
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 构建下拉数据 ------------------------
  async function queryAllWarehouse() {
    try {
      outstandingOptions((await partStationWarehouseApi.getAll()).data);
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  let warehouseOptions = ref([]);
  function outstandingOptions(warehouseObjs) {
    warehouseOptions.value = [];
    warehouseObjs.map((e) =>
      warehouseOptions.value.push({
        value: e.id,
        label: e.name,
      })
    );
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined, //主键
    remark: undefined, //备注
    warehouseId: undefined, //所属仓库id
    rackCode: undefined, //货架编码
    location: undefined, //位置
  };

  let form = reactive({ ...formDefault });

  const rules = {
    warehouseId: [{ required: true, message: '所属仓库 必填' }],
    rackCode: [{ required: true, message: '货架编码 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await partStationRackApi.update(form);
      } else {
        await partStationRackApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

<template>
  <a-card>
    <div class="chart-header">
      <div class="chart-title">库龄分布</div>
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="pie-main-1"></div>
    </div>
  </a-card>
</template>

<script setup>
import { onMounted } from "vue";
import * as echarts from "echarts";

let myChart = null;

onMounted(() => {
  initChart();
});

function initChart() {
  const chartDom = document.getElementById('pie-main-1');
  if (!chartDom) return;

  myChart = echarts.init(chartDom);

  // 蓝色系配色方案
  const blueColors = [
    '#4080FF', // 浅蓝色
    '#1890FF', // 标准蓝色
    '#0066CC', // 中蓝色
    '#003A8C'  // 深蓝色
  ];

  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderColor: '#eee',
      borderWidth: 1,
      textStyle: { color: '#333' },
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      type: 'scroll',
      orient: 'horizontal',
      top: '5%',
      left: 'center',
      textStyle: {
        color: '#666'
      },
      pageIconColor: '#999',
      pageTextStyle: {
        color: '#666'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '55%'],
        avoidLabelOverlap: false,
        color: blueColors, // 设置蓝色系配色
        label: {
          show: true,
          formatter: '{b}: {d}%',
          position: 'outside',
          padding: [0, 10],
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          borderColor: '#eee',
          borderWidth: 1,
          borderRadius: 4,
          rich: {
            name: {
              fontSize: 12,
              color: '#666'
            },
            value: {
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333'
            }
          }
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 20,
          lineStyle: {
            color: '#eee'
          }
        },
        data: [
          { value: 1048, name: '1-10天' },
          { value: 735, name: '10-30天' },
          { value: 580, name: '30-60天' },
          { value: 484, name: '60天以上' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.2)'
          }
        }
      }
    ]
  };

  myChart.setOption(option);

  // 监听窗口大小变化
  window.addEventListener("resize", () => {
    myChart.resize();
  });
}
</script>

<style scoped lang="less">
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .chart-title {
    font-size: 16px;
    font-weight: bold;
  }
}

.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .chart-main {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

<template>
  <board-title :title="'铺布仪表盘'" />
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="时间范围" class="smart-query-form-item">
        <a-range-picker v-model:value="pickerValue" @change="onDateChange"></a-range-picker>
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card>
    <a-row :gutter="16">
      <a-col :span="6">
        <a-card style="border-left: 5px solid #1890ff" class="card" size="small">
          <div>今日铺布任务</div>
          <div class="card-value">{{ issueCount }}</div>
          <div>
            <span v-if="issuePercent > 0 || issuePercent == 0" style="color: #67c23a"> <CaretUpOutlined />{{ Math.abs(issuePercent) }}% </span>
            <span v-else-if="issuePercent < 0" style="color: #ff4d4f"> <CaretDownOutlined />{{ Math.abs(issuePercent) }}% </span>
            较昨日
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card style="border-left: 5px solid #67c23a" class="card" size="small">
          <div>已完成任务</div>
          <div class="card-value">{{ completeCount }}</div>
          <div>
            <span v-if="completePercent > 0 || completePercent == 0" style="color: #67c23a">
              <CaretUpOutlined />{{ Math.abs(completePercent) }}%
            </span>
            <span v-else-if="completePercent < 0" style="color: #ff4d4f"> <CaretDownOutlined />{{ Math.abs(completePercent) }}% </span>
            较昨日
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card style="border-left: 5px solid #e6a23c" class="card" size="small">
          <div>进行中任务</div>
          <div class="card-value">{{ doingCount }}</div>
          <div>
            <span v-if="16 > 0" style="color: #ff4d4f"> </span>
            <span v-else style="color: #67c23a"> <CaretDownOutlined />{{ -5 }}% </span>
            <div style="color: rgba(255, 255, 255, 0)">较昨日</div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card style="border-left: 5px solid #f56c6c" class="card" size="small">
          <div>逾期任务</div>
          <div class="card-value">{{ overdueCount }}</div>
          <div style="color: rgba(255, 255, 255, 0)">逾期任务</div>
        </a-card>
      </a-col>
    </a-row>
  </a-card>

  <a-row :gutter="16" style="margin-top: 10px">
    <a-col :span="20">
      <a-row :gutter="16">
        <a-col :span="8">
          <task-status-pie-chart :date-range="dateRange" style="width: 100%; height: 300px" />
        </a-col>
        <a-col :span="16">
          <task-count-bar-chart style="width: 100%; height: 300px" />
        </a-col>
      </a-row>

      <a-row :gutter="16" style="margin-top: 10px">
        <a-col :span="4">
          <task-workshop-pie-chart :date-range="dateRange" style="width: 100%; height: 300px" />
        </a-col>
        <a-col :span="4">
          <task-team-pie-chart :date-range="dateRange" style="width: 100%; height: 300px" />
        </a-col>
        <a-col :span="16">
          <task-trend-line-chart style="width: 100%; height: 300px" />
        </a-col>
      </a-row>
    </a-col>

    <a-col :span="4">
      <a-card style="height: 610px" title="今日计划任务" size="small" :extra="`${total}个任务`">
        <a-list item-layout="horizontal" :grid="{ gutter: 0, column: 1 }" size="small" style="overflow-y: auto; height: 540px">
          <a-list-item style="padding: 0" v-for="item in tableData">
            <a-card size="small" style="background-color: #f5f5f5">
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div style="">{{ item.number }}</div>
                <a-tag style="margin: 0" :color="SPREAD_TASK_STATUS_ENUM.getEnum(item.taskStatus).color">{{
                  SPREAD_TASK_STATUS_ENUM.getEnum(item.taskStatus).label
                }}</a-tag>
              </div>
              <div style="display: flex; justify-content: space-between">
                <div>
                  <div style="color: #909399; font-size: 12px">{{ item.materialName }}</div>
                  <div style="color: #909399; font-size: 12px">床次:{{ item.cutNum }}</div>
                </div>
                <div style="text-align: right">
                  <div style="color: #909399; font-size: 12px">{{ item.teamName }}</div>
                  <div style="color: #909399; font-size: 12px">{{ dayjs(item.planBeginTime).format('YYYY-MM-DD HH:mm') }}</div>
                </div>
              </div>
            </a-card>
          </a-list-item>
        </a-list>
      </a-card>
    </a-col>
  </a-row>
</template>
<script setup>
  import BoardTitle from '/@/components/business/mes/board/board-title.vue';
  import TaskStatusPieChart from '/@/views/business/mes/tailor/spread/spread-task-dashboard/components/task-status-pie-chart.vue';
  import TaskCountBarChart from '/src/views/business/mes/tailor/spread/spread-task-dashboard/components/task-count-bar-chart.vue';
  import TaskTeamPieChart from '/@/views/business/mes/tailor/spread/spread-task-dashboard/components/task-team-pie-chart.vue';
  import TaskWorkshopPieChart from '/@/views/business/mes/tailor/spread/spread-task-dashboard/components/task-workshop-pie-chart.vue';
  import TaskTrendLineChart from '/@/views/business/mes/tailor/spread/spread-task-dashboard/components/task-trend-line-chart.vue';
  import { onMounted, reactive, ref, computed } from 'vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { spreadTaskApi } from '/@/api/business/mes/tailor/spread-task-api.js';
  import { spreadTaskDashboardApi } from '/@/api/business/mes/tailor/spread-task-dashboard-api';
  import { SPREAD_TASK_STATUS_ENUM } from '/@/constants/business/mes/tailor/spread-task-const.js';
  import dayjs from 'dayjs';

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    produceInstructOrderId: undefined, //生产指令单
    materialId: undefined, //款号
    cutNum: undefined, //床次
    workshopId: undefined, //车间
    teamId: undefined, //小组
    equipmentId: undefined, //设备ID
    taskStatus: undefined, //任务状态
    planBeginTime: [], //计划铺布开始时间
    planBeginTimeBegin: undefined, //计划铺布开始时间 开始
    planBeginTimeEnd: undefined, //计划铺布开始时间 结束
    realBeginTime: [], //开始时间
    realBeginTimeBegin: undefined, //开始时间 开始
    realBeginTimeEnd: undefined, //开始时间 结束
    realCompleterId: undefined, //完成人ID
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'plan_begin_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await spreadTaskApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  const taskObject = ref('all'); //默认选中全部

  //-------------------------------------------时间筛选事件----------------------------------
  const today = dayjs().format('YYYY-MM-DD');
  const yesterday = dayjs().subtract(1, 'day').format('YYYY-MM-DD');

  const dateRange = ref({
    startDate: '',
    endDate: '',
  });
  const pickerValue = ref([]);

  function onDateChange(dates) {
    if (dates && dates.length === 2) {
      dateRange.value.startDate = dates[0] ? dayjs(dates[0]).format('YYYY-MM-DD') : '';
      dateRange.value.endDate = dates[1] ? dayjs(dates[1]).format('YYYY-MM-DD') : '';
    } else {
      dateRange.value.startDate = '';
      dateRange.value.endDate = '';
    }
  }

  //逾期任务
  const overdueCount = ref(0);
  async function getOverdueCount() {
    const params = {
      beginDate: dateRange.value.startDate,
      endDate: dateRange.value.endDate,
    };
    const res = await spreadTaskDashboardApi.overdue(params);
    overdueCount.value = res.data;
  }

  //状态完成数量
  const issueCount = ref(0); //今日铺布任务
  const completeCount = ref(0); //已完成任务
  const doingCount = ref(0); //进行中任务
  async function queryStatusCounts(taskStatus, startDate, endDate) {
    const params = {
      taskStatus: taskStatus,
      planBeginTimeBegin: startDate,
      planBeginTimeEnd: endDate,
      taskObject: taskObject.value || '',
    };
    const res = await spreadTaskDashboardApi.statsCount(params);
    return res.data;
  }
  async function queryAllStatusCounts() {
    issueCount.value = await queryStatusCounts(SPREAD_TASK_STATUS_ENUM.ISSUE.value, today, today);
    doingCount.value = await queryStatusCounts(SPREAD_TASK_STATUS_ENUM.DOING.value, dateRange.value.startDate, dateRange.value.endDate);
    completeCount.value = await queryStatusCounts(SPREAD_TASK_STATUS_ENUM.COMPLETE.value, today, today);
  }

  //昨日数据  今日铺布&已完成任务
  const yesterdayIssueCount = ref(0); //昨日今日铺布任务
  const yesterdayCompleteCount = ref(0); //昨日已完成任务
  async function queryYesterdayStatusCounts() {
    yesterdayIssueCount.value = await queryStatusCounts(SPREAD_TASK_STATUS_ENUM.ISSUE.value, yesterday, yesterday);
    yesterdayCompleteCount.value = await queryStatusCounts(SPREAD_TASK_STATUS_ENUM.COMPLETE.value, yesterday, yesterday);
  }

  //今昨比较
  const issuePercent = computed(() => getPercentChange(issueCount.value, yesterdayIssueCount.value));
  const completePercent = computed(() => getPercentChange(completeCount.value, yesterdayCompleteCount.value));
  function getPercentChange(today, yesterday) {
    if (yesterday === 0) {
      return today === 0 ? 0 : 100; // 昨天为0，今天有数据，直接100%
    }
    return Math.round(((today - yesterday) / yesterday) * 100);
  }

  //查询
  function onSearch() {
    getOverdueCount();
    queryAllStatusCounts();
    queryYesterdayStatusCounts();
  }

  //重置
  function resetQuery() {
    pickerValue.value = [];
    dateRange.value.startDate = null;
    dateRange.value.endDate = null;
    taskObject.value = 'all';
    onSearch();
  }

  onMounted(() => {
    queryData();
    getOverdueCount();
    queryAllStatusCounts();
  });
</script>
<style scoped lang="less">
  .card {
    background-color: #f5f5f5;
    border-left: 5px solid #1890ff;
    font-size: 16px;
    border-radius: 2px;

    .card-value {
      font-size: 34px;
      font-weight: bold;
    }
  }
</style>

<template>
  <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules">
    <span style="color: #909399">手机:</span>
    <a-form-item name="loginName">
      <a-input v-model:value.trim="loginForm.loginPhone" placeholder="请输入手机号码" style="height: 42px"/>
    </a-form-item>

    <a-form-item name="captchaCode">
      <span style="color: #909399">验证码:</span>
      <div style="display: flex;justify-content: space-between;align-items: center;">
        <div style="width: 70%">

          <a-input class="captcha-input" v-model:value.trim="loginForm.captchaCode" placeholder="请输入验证码"
                   style="height: 42px;"/>

        </div>
        <a-button
            @click="getSmsCode"
            :disabled="smsDisable"
            style="cursor: pointer;margin-left: 10px;height: 38px"
            type="primary"

        >
          {{ smsButtonText }}
        </a-button>
      </div>
    </a-form-item>

    <a-form-item>
      <div class="btn" @click="onLogin">登录</div>
    </a-form-item>
  </a-form>
</template>
<script setup>
import {onMounted, onUnmounted, reactive, ref} from "vue";
import {LOGIN_DEVICE_ENUM} from "/@/constants/system/login-device-const.js";
import {useRouter} from "vue-router";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import {encryptData} from "/@/lib/encrypt.js";
import {loginApi} from "/@/api/system/login-api.js";
import {localSave} from "/@/utils/local-util.js";
import LocalStorageKeyConst from "/@/constants/local-storage-key-const.js";
import {message} from "ant-design-vue";
import {useUserStore} from "/@/store/modules/system/user.js";
import {buildRoutes} from "/@/router/index.js";
import {smartSentry} from "/@/lib/smart-sentry.js";
import {smsLoginApi} from "/@/api/business/mes/system/sms-login/sms-login-api.js";

const smsButtonText = ref('发送验证码');
const countDownNum = ref(60);
const smsDisable = ref(false);

let timer;

const loginForm = reactive({
  loginPhone: '',
  captchaCode: '',
  captchaUuid: '',
  loginDevice: LOGIN_DEVICE_ENUM.PC.value,
});
const rules = {
  loginPhone: [{required: true, message: '电话号码不能为空'}],
  captchaCode: [{required: true, message: '验证码不能为空'}],
};

const router = useRouter();
const formRef = ref();

onMounted(() => {
  document.onkeyup = (e) => {
    if (e.keyCode == 13) {
      onLogin();
    }
  };
})

onUnmounted(() => {
  document.onkeyup = null;
});


//登录
async function onLogin() {
  formRef.value.validate().then(async () => {
    try {
      SmartLoading.show();

      const res = await smsLoginApi.login(loginForm);

      localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
      message.success('登录成功');
      //更新用户信息到pinia
      useUserStore().setUserLoginInfo(res.data);
      //构建系统的路由
      buildRoutes();
      router.push('/home');
    } catch (e) {
      if (e.data && e.data.code !== 0) {
        // loginForm.captchaCode = '';

      }
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  });
}


async function getSmsCode() {
  try {
    if (!loginForm.loginPhone) {
      message.error('请输入手机号');
      return;
    }

    //正则验证手机号
    if (/^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])d{8}$/
        .test(loginForm.loginPhone)) {
      message.error('请输入正确的手机号');
      return;
    }

    startCountDown();
    let captchaResult = await smsLoginApi.getSmsCode(loginForm.loginPhone);

    loginForm.captchaUuid = captchaResult.data;

  } catch (e) {
    console.log(e);
    message.error(e.data.message)
  }
}


function startCountDown() {
  countDownNum.value = 60;
  timer = setInterval(() => {
    if (countDownNum.value > 0) {
      countDownNum.value -= 1;
      smsButtonText.value = `${countDownNum.value}秒后可重发`;
      smsDisable.value = true;
    } else {
      clearInterval(timer);
      smsButtonText.value = '发送验证码';
      smsDisable.value = false;
    }
  }, 1000);
}

// 在组件销毁时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped lang="less">
.img-drap {
  min-width: 260px;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(/@/assets/images/login/login-bg7.png);
}

.entry {
  position: relative;
  width: 480px;
  height: 100%;
  float: right;
  background: #fff;
  overflow-y: auto;

  .login-box {
    padding: 10px 72px;
  }

  .btn {
    width: 350px;
    height: 50px;
    background: #1748FD;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    color: #ffffff;
    line-height: 50px;
    cursor: pointer;
  }
}
</style>

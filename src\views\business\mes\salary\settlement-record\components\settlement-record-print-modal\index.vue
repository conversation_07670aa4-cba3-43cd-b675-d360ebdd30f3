<template>
  <a-modal
      :title="'打印结算记录'"
      :width="950"
      :open="visibleFlag"
      @cancel="onClose"
  >
     <div class="print-content">
      <div id="pdf-content" v-for="(record, index) in resArray" :key="index" :style="{fontSize: fontSize + 'px'}">
        <h2 style="text-align: center;margin-bottom: 0px;height: 33px;line-height: 33px;">计件工资明细</h2>
        
        <!-- 表头 -->
        <div id="header">
          <span style="padding-left: 10px;">员工姓名：{{ record.data[0].workerName }}</span>
          <span style="padding-right: 10px;">结算月份：{{ monthArray[index] }}</span>
        </div>
        
        <!-- 表格 -->
        <table style="width: 100%;">
          <tr>
            <th>产品名称</th>
            <th>裁剪编号</th>
            <th>款式颜色</th>
            <th>尺码</th>
            <th>扎号</th>
            <th>工序名称</th>
            <th>工作数量</th>
            <th>单价</th>
            <th>金额</th>
            <th>报工时间</th>
          </tr>
          <tr v-for="item in record.data" :key="item.id">
            <td>{{ item.itemName }}</td>
            <td>{{ item.cutNum }}</td>
            <td>{{ item.styleColor }}</td>
            <td>{{ item.size }}</td>
            <td>{{ item.tieNum }}</td>
            <td>{{ item.processName }}</td>
            <td>{{ item.workQuantity }}</td>
            <td>{{ item.price }}</td>
            <td>{{ (item.workQuantity * item.price).toFixed(2) }}</td>
            <td>{{ item.reportTime }}</td>
          </tr>
        </table>
        
        <!-- 页脚 -->
        <div id="footer">
          <span style="padding-left: 10px;">合计金额：{{ getTotal(record.data, 'price').toFixed(2) }}</span>
          <span >合计数量：{{ getTotal(record.data, 'quantity') }}</span>
          <span>审核：</span>
          <span></span>
          <span></span>
          <span>员工签字：</span>
          <span></span>
          <span></span>
          <span style="padding-right: 10px;">打印时间：{{ currentTime }}</span>
        </div>
        </div>
    </div>
    <a-row>

       <!-- <a-col :span="4">
          <span >字号:</span>
          <a-input-number v-model:value="fontSize" :min="12" :max="100" style="margin-left: 16px" />
           <a-slider v-model:value="fontSize" :min="12" :max="100"/>
        </a-col> -->
        
<a-col :span="6" style="display: flex;margin-top: 5px; ">
  <span style="white-space: nowrap;padding-top: 5px;">文件名:</span>
  <a-input 
    v-model:value="fileName" 
    style="margin-left: 8px; width: 200px;" 
    placeholder="请输入文件名"
  />
</a-col>
    </a-row>
    <template #footer>
      <a-button @click="pdfExport" type="primary">导出</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import { getSettlementRecordPdf } from '/@/utils/pdf-util';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs'

const currentTime = ref('')//当前时间
const fontSize = ref(18) // 默认字号18px
const monthArray = ref([])//结算月份
const fileName = ref('计件工资明细')
const resArray = ref([]) 
const visibleFlag = ref(false)
// 计算合计方法
function getTotal(data, type) {
  if (type === 'price') {
    return data.reduce((sum, item) => sum + (item.workQuantity * item.price), 0)
  }
  return data.reduce((sum, item) => sum + item.workQuantity, 0)
}

// 方法
function onClose() {
  visibleFlag.value = false
  fontSize.value = 18
  currentTime.value=''
 }

// 导出pdf
async function pdfExport() {
  SmartLoading.show();
  try {
    await getSettlementRecordPdf(fileName.value)
  } catch (error) {
    message.error("导出失败")
  }finally{
    SmartLoading.hide();
  }
}

function open(data, months) {
  resArray.value = data
  monthArray.value = months.map(month => month.slice(0, -3))
  visibleFlag.value = true
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

defineExpose({
  open,
})
</script>

<style scoped>

.print-content {
  max-height: 600px;
  overflow-y: auto;
}
#pdf-content { 
  width: 886px;
  margin-bottom: 20px;
}
td,th {
  border: 1px solid #000;
  text-align: center;
  height: 30px;
  font-size: inherit;  
}
#header,
#footer {
  display: flex;
  justify-content: space-between;
  border: 1px solid #000;
  height: 30px;
  line-height: 30px;
}

#header {
  border-bottom: none;
}

#footer {
  border-top: none;
}
</style>
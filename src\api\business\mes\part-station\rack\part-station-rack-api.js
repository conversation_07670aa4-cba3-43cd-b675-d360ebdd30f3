/**
 * 裁片货架 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-06 16:49:27
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partStationRackApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/partStationRack/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/partStationRack/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/partStationRack/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/partStationRack/delete/${id}`);
  },
  /**
   * 查询所有货架信息  <AUTHOR>
   */
  getAll:(param)=>{
    return postRequest(`/partStationRack/getAll`,param);
  },

  quickAdd:(param)=>{
    return postRequest(`/partStationRack/quickAdd`,param)
  }

};

<template>
  <a-row :gutter="8">
    <a-col :span="6">
      <a-card title="报工工作台" class="left-card">

        <div style="margin-bottom: 20px;background-color: #EBEEF5;color: #909399;padding: 6px;border-radius: 4px">
          1. 菲票输入框需处于输入状态
          <br/>
          2. 网页模式下,输入法需切换为英文输入法
        </div>

        <div style="margin-bottom: 30px">
          <div class="title-line"> 报工设置</div>

          <div class="sub-item">

            <a-radio-group v-model:value="reportWay">
              <a-radio :style="radioStyle" :value="1">手动确认</a-radio>
              <a-radio :style="radioStyle" :value="2">二次复核</a-radio>
              <a-radio :style="radioStyle" :value="3">直接报工</a-radio>

            </a-radio-group>
          </div>

        </div>

        <div style="margin-bottom: 30px">
          <div class="title-line"> 生产指令单号</div>
          <div class="sub-item">
            <a-input style="font-size: 25px" disabled :value="feiTicketInfo.instructOrderNumber"/>
          </div>

          <div class="sub-item">
            <div>工序：</div>
            <a-select style="width: 100%;font-size: 25px" placeholder="请选择工序" :allowClear="true"
                      @change="changeOrderProcess" :options="orderProcessOptions"
                      v-model:value="orderProcessId"
                      size="large">

            </a-select>
          </div>
        </div>

        <div>
          <div class="title-line"> 指令单信息</div>
          <div>
            <a-descriptions bordered :column="1" size="small">
              <a-descriptions-item label="优先级">
                <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(feiTicketInfo.producePriority).color"

                >{{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(feiTicketInfo.producePriority).label }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="下达时间">{{ feiTicketInfo.issuedTime }}</a-descriptions-item>
              <a-descriptions-item label="计划完工时间">{{ feiTicketInfo.planFinishTime }}</a-descriptions-item>
              <a-descriptions-item label="交货日期">{{ feiTicketInfo.deliverTime }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </div>

      </a-card>
    </a-col>
    <a-col :span="18">
      <a-card title="菲票信息">
        <a-input-group compact style="margin-bottom: 20px">
          <a-input style="font-size: 20px;width: calc(100% - 100px)" placeholder="请扫描菲票二维码" ref="feiTicketInput"
                   v-model:value="feiTicketCode"
                   @pressEnter="ticketPressEnter"/>
          <a-button style="height: 41px;font-size: 20px" type="primary" @click="resetData">重置</a-button>
        </a-input-group>
        <a-row>
          <a-col :span="6">
            <a-image width="80%"
                     v-if="feiTicketInfo.imgUrl && feiTicketInfo.imgUrl[0] && feiTicketInfo.imgUrl[0].fileUrl"
                     :src="feiTicketInfo.imgUrl[0].fileUrl"/>
            <div v-else class="no-image">
              无图片
            </div>
          </a-col>
          <a-col :span="13">
            <div style="font-size: 25px;margin-bottom: 10px">SPU编号(款号)：<span
                style="font-weight: bold">{{ feiTicketInfo.itemNumber }}</span></div>
            <a-descriptions :column="2" size="small">
              <a-descriptions-item label="床次" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.cutNum }}
              </a-descriptions-item>
              <a-descriptions-item label="扎号" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.tieNum }}
              </a-descriptions-item>
              <a-descriptions-item label="款式颜色" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.styleColor }}
              </a-descriptions-item>
              <a-descriptions-item label="尺寸" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.size }}
              </a-descriptions-item>
              <a-descriptions-item label="部位" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.positions }}
              </a-descriptions-item>
              <a-descriptions-item label="数量" :labelStyle="{fontSize: '18px', color: '#56585d'}"
                                   :contentStyle="{fontSize: '18px'}">{{ feiTicketInfo.num }}
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
          <a-col :span="5">
            <div
                style="padding: 20px;width: 100%;height:100%;background-color: #F2F6FC;display: flex;flex-direction: column;justify-content: space-around;align-items: center">
              <div style="font-size: 25px">数量</div>
              <div style="font-size: 60px;color: #005bf4;font-weight: bold;">{{ feiTicketInfo.num }}</div>
              <div style="font-size: 18px;color: #005bf4">
                请扫描二维码
                <br/>

              </div>
            </div>
          </a-col>
        </a-row>
      </a-card>

      <a-card title="当前报工信息" style="margin-top: 14px">
        <!--滚动        -->
        <a-table :columns="workReportColumns" :dataSource="nowWorkRecords" bordered size="small" :pagination="false"
                 :scroll="{y: '400px'}"/>

      </a-card>
    </a-col>
  </a-row>
</template>

<script setup>
import {useUserStore} from "/@/store/modules/system/user.js";
import {onMounted, ref, reactive} from "vue";
import {Modal, notification} from 'ant-design-vue';
import {QR_CODE_TYPE_ENUM} from "/@/constants/business/mes/common/qr-code-type-const.js";
import _ from "lodash";
import {feTicketApi} from "/@/api/business/mes/tailor/fe-ticket-api.js";
import {
  PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM
} from "/@/constants/business/mes/produce/produce-instruct-order-const.js";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import {produceInstructOrderProcessApi} from "/@/api/business/mes/produce/produce-instruct-order-process-api.js";
import {workRepoetApi} from "/@/api/business/mes/work/work-report-api.js";
import dayjs from "dayjs";

const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});

const workReportColumns = [
  {
    title: '生产指令单编号',
    dataIndex: 'instructOrderNumber',
    ellipsis: true,
    // width: 100,
  },
  {
    title: '工序名称',
    dataIndex: 'processName',
    ellipsis: true,
    // width: 80,
  },
  {
    title: 'SPU编号(款号)',
    dataIndex: 'itemNumber',
    ellipsis: true,
    // width: 100,
  },
  // {
  //   title: '物料名称',
  //   dataIndex: 'itemName',
  //   ellipsis: true,
  //   // width: 100,
  // },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    // width: 50,
  },

  {
    title: '款式颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    // width: 80,
  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    // width: 50,
  },
  {
    title: '部位',
    dataIndex: 'positions',
    ellipsis: true,
    // width: 50,
  },


  {
    title: '报工数量',
    dataIndex: 'num',
    ellipsis: true,
    // width: 80,
  },
  {
    title: '报工时间',
    dataIndex: 'reportTime',
    ellipsis: true,

  }
]

const feiTicketInput = ref(null);


const feiTicketInfoSate = {
  id: null,
  imgUrl: null,
  instructOrderId: null,
  instructOrderNumber: null,
  issuedTime: null,
  planFinishTime: null,
  deliverTime: null,
  itemNumber: null,
  itemName: null,
  tieNum: null,
  styleColor: null,
  size: null,
  positions: null,
  num: null,
  cutNum: null,
  producePriority: null,

}

const reportWay = ref(1);
const feiTicketCode = ref(null);
const feiTicketId = ref(null);
const feiTicketInfo = ref(feiTicketInfoSate);
const orderProcessId = ref(null);
const orderProcessOptions = ref([]);

const nowWorkRecords = ref([])

onMounted(() => {
  feiTicketInput.value.focus();
})

function openNotification(type, placement, message, description) {
  notification[type]({
    message: message,
    description: description,
    placement: placement,
  });
}

/**
 * 条码校验
 * @param code
 */
function codeCheck(code) {
  if (_.isEmpty(code)) {
    return false;
  }
  let codeType = QR_CODE_TYPE_ENUM.getTypeByCode(code);
  if (codeType !== QR_CODE_TYPE_ENUM.FE_TICKET) {
    openNotification("warn", 'top', '提示', '请扫描菲票二维码');
    return false;
  }
  return true
}

/**
 * 工序选择
 * @param e
 */
function changeOrderProcess(e) {
  orderProcessId.value = e;
  // console.log("工序id", e)
}

function resetData() {
  feiTicketId.value = null
  feiTicketInfo.value = Object.assign(feiTicketInfoSate)
  orderProcessId.value = null
  orderProcessOptions.value = []

  //清空输入框数据
  feiTicketCode.value = null
  feiTicketInput.value.value = null;
  feiTicketInput.value.focus();

}

/**
 * 条码扫描回车事件
 * @param e
 * @returns {Promise<void>}
 */
async function ticketPressEnter(e) {
  let code = e.target.value;
  if(!codeCheck(code)){
    return
  }

  let value = QR_CODE_TYPE_ENUM.getValueByCode(code);
  if (_.isEmpty(value)) {
    openNotification("warn", 'top', '提示', '请扫描菲票二维码');
    return
  }
  // console.log(feiTicketId.value)

  try {
    SmartLoading.show();

    if (reportWay.value === 2) {
      if (value === feiTicketId.value) {
        await reportWork()
        return
      } else {
        openNotification("warn", 'top', '提示', '请再次扫描当前菲票二维码');
      }

    }

    await queryData(value)

    await reportWorkSubmit()

  } finally {
    feiTicketInput.value.focus();
    feiTicketCode.value = null
    e.target.value = null
    SmartLoading.hide();
  }

}

async function reportWorkSubmit() {

  if (reportWay.value === 3) {
    await reportWork()
    return
  }
  if (reportWay.value === 1) {
    Modal.confirm({
      title: '是否确认报工',
      content: '是否确认报工',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        reportWork()
      },
    })
    return
  }


}

async function queryData(value) {
  feiTicketId.value = value;

  let ticketRes = await feTicketApi.queryById(feiTicketId.value)

  if (_.isEmpty(orderProcessOptions.value) || ticketRes.data.instructOrderId !== feiTicketInfo.value.instructOrderId) {
    orderProcessId.value = null

    let orderProcessRes = await produceInstructOrderProcessApi.getProcessList(ticketRes.data.instructOrderId)
    orderProcessOptions.value = orderProcessRes.data.map(item => (
        {
          label: item.name,
          value: item.id,
          position: item.position
        }
    ))
  }
  // feiTicketId.value = ticketRes.data.id
  feiTicketInfo.value = ticketRes.data
  // console.log(feiTicketInfo.value)
}

async function reportWork() {
  if (!feiTicketId.value) {
    openNotification("warn", 'top', '提示', '请扫描菲票二维码');
    return;
  }
  if (!orderProcessId.value) {
    console.log('请选择工序', orderProcessId.value)
    openNotification("warn", 'top', '提示', '请选择工序');
    return
  }
  let data = {
    feTicketId: feiTicketId.value,
    processIds: [orderProcessId.value],
  }
  try {
    let res = await workRepoetApi.feiTicketReport(data)
    openNotification("success", 'top', '提示', '报工成功')
    console.log(feiTicketInfo.value)
    nowWorkRecords.value.push({

      feTicketId: feiTicketId.value,
      processId: orderProcessId.value,

      processName: orderProcessOptions.value.find(item => item.value === orderProcessId.value).label,
      processPosition: orderProcessOptions.value.find(item => item.value === orderProcessId.value).position,
      instructOrderId: feiTicketInfo.value.instructOrderId,
      instructOrderNumber: feiTicketInfo.value.instructOrderNumber,
      itemNumber: feiTicketInfo.value.itemNumber,
      itemName: feiTicketInfo.value.itemName,
      tieNum: feiTicketInfo.value.tieNum,
      styleColor: feiTicketInfo.value.styleColor,
      size: feiTicketInfo.value.size,
      positions: feiTicketInfo.value.positions,
      num: feiTicketInfo.value.num,
      cutNum: feiTicketInfo.value.cutNum,
      producePriority: feiTicketInfo.value.producePriority,
      reportTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),

    })
  } catch (e) {
    console.log(e)
    openNotification("error", 'top', '提示', e.data.msg)
  }


}
</script>


<style scoped lang="less">
.left-card {
  //min-height: 80vh;
}

.title-line {
  padding-left: 6px;
  font-size: 15px;
  font-weight: bold;
  border-left: #00a0e9 3px solid;
  margin-bottom: 12px;
}

.sub-item {
  margin-bottom: 8px;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80%;
  height: 100%;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.04);
  color: #888;
}

</style>

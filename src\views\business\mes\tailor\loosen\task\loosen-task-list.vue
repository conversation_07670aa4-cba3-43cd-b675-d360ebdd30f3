<!--
  * 松布任务单
  * 
  * @Author:    wcc
  * @Date:      2025-06-24
  * @Copyright  zscbdic
-->
<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="松布任务单" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.taskNumber" placeholder="请输入松布任务单号" allowClear />
      </a-form-item>
      <a-form-item label="松布状态" class="smart-query-form-item smart-margin-left10">
        <a-select v-model:value="queryForm.loosenStatus" style="width: 120px" placeholder="请选择状态" allowClear>
          <a-select-option v-for="item in LOOSEN_TASK_STATUS_ENUM.getOptions()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="reset" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
        <a-button type="primary" @click="showFrame" class="smart-margin-left10">
          <template #icon>
            <SearchOutlined />
          </template>
          松布架
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRows: selectedRows, onChange: onSelectChange }"
      :scroll="{ x: 2200 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'loosenStatus'">
          <a-tag :bordered="false" :color="LOOSEN_TASK_STATUS_ENUM.getEnum(Number(record.loosenStatus)).color">
            {{ LOOSEN_TASK_STATUS_ENUM.getEnum(Number(record.loosenStatus)).label }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'loosenTime'">
          <a-tooltip :title="getTimeProgressTooltip(record)" color="blue">
            <a-progress :percent="calculateTimeProgress(record)" :status="getProgressStatus(record)" :format="() => ''" :strokeWidth="8" />
            <span class="loosen-progress-text">
              <span class="elapsed-hours">{{ getElapsedHours(record) }}</span
              >/<span class="total-hours">{{ record.loosenTime }}</span>
            </span>
          </a-tooltip>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
  <FrameList ref="frameRef" />
</template>

<script setup>
  import { ref, reactive, onMounted, onUnmounted } from 'vue';
  import { PAGE_SIZE_OPTIONS, PAGE_SIZE } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { loosenPlanDetailsApi } from '/@/api/business/mes/tailor/loosen-plan-details-api.js';
  import { tableMergeCell } from '/@/utils/table-merge-cell-util.js';
  import FrameList from '/@/views/business/mes/tailor/loosen/frame/frame-list.vue';
  import { LOOSEN_TASK_STATUS_ENUM } from '/@/constants/business/mes/tailor/loosen-task-status-const';
  import dayjs from 'dayjs';
  import duration from 'dayjs/plugin/duration';

  //------------------------------------------------表格--------------------------------------------------
  // 表格列定义
  const columns = [
    {
      title: '松布计划单',
      dataIndex: 'fabricLoosenNumber',
      ellipsis: true,
      customCell: (record, index) => tableMergeCell(tableData.value, record, index, 'fabricLoosenNumber'),
    },
    {
      title: '松布状态',
      dataIndex: 'loosenStatus',
      ellipsis: true,
      width: 100,
    },
    {
      title: '松布进度',
      dataIndex: 'loosenTime',
      ellipsis: true,
      width: 200,
    },
    // {
    //   title: '时长',
    //   dataIndex: 'loosenTime',
    //   ellipsis: true,
    //   width: 100,
    //   align: 'center',
    //   customRender: ({ text }) => (text ? `${text}小时` : '-'),
    // },
    {
      title: '卷数',
      dataIndex: 'rolls',
      ellipsis: true,
      width: 70,
      align: 'center',
    },
    {
      title: '松布任务单',
      dataIndex: 'taskNumber',
      ellipsis: true,
      width: 150,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      width: 150,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
    },
    {
      title: '颜色',
      dataIndex: 'itemColor',
      ellipsis: true,
    },
    {
      title: '缸号',
      dataIndex: 'lotNo',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'itemUnit',
      ellipsis: true,
      width: 70,
    },
    {
      title: '松布架',
      dataIndex: 'frame',
      ellipsis: true,
    },
    {
      title: '计划开始时间',
      dataIndex: 'planedStTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '计划结束时间',
      dataIndex: 'planedEndTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '实际开始时间',
      dataIndex: 'realStTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '实际结束时间',
      dataIndex: 'realEndTime',
      ellipsis: true,
      customRender: ({ text }) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : ''),
    },
  ];
  // 表格数据
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 行数据
  const selectedRows = ref([]);
  // 选择变化的处理函数
  const onSelectChange = (keys, rows) => {
    selectedRows.value = rows;
  };

  //------------------------------------------------表单---------------------------------------------------
  // 查询表单数据
  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    taskNumber: '',
    loosenStatus: undefined,
  };

  const queryForm = reactive({ ...queryFormState });

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      const res = await loosenPlanDetailsApi.queryPage(queryForm);
      tableData.value = res.data.list;
      columns.time = res.data.list.loosenTime;
      total.value = res.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 重置查询
  function reset() {
    Object.assign(queryForm, queryFormState);
    queryData();
  }

  // 查询
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  //---------------------------------------------松布架-----------------------------------------------
  const frameRef = ref();
  async function showFrame() {
    frameRef.value.show();
  }

  //--------------------------------------松布时长进度条处理-------------------------------------------
  dayjs.extend(duration);

  //定时器
  const refreshTrigger = ref(0);
  let timer = null;
  // 启动定时器函数
  function startProgressTimer() {
    // 每分钟更新一次进度
    timer = setInterval(() => {
      refreshTrigger.value += 1;
    }, 60000);
  }
  // 停止定时器函数
  function stopProgressTimer() {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  }

  //计算各种时长
  function calculateLoosenTimes(record) {
    const _ = refreshTrigger.value; // 刷新触发器

    // 未开始，返回默认值
    if (!record.realStTime) {
      return {
        isStarted: false,
        isCompleted: false,
        progress: 0,
        elapsedHours: 0,
        elapsedMinutes: 0,
        remainingHours: record.loosenTime,
        remainingMinutes: 0,
        totalHours: record.loosenTime,
      };
    }

    const startTime = dayjs(record.realStTime);
    const totalDuration = dayjs.duration(record.loosenTime, 'hour'); // 计划松布总时长
    const totalMilliseconds = totalDuration.asMilliseconds();

    // 判断状态
    const isCompleted = record.loosenStatus === 2; // 静置完成
    const isEnded = record.loosenStatus === 3; // 已结束

    // 计算已静置时间
    let endTime;
    if (isEnded && record.realEndTime) {
      // 只有在"已结束"状态且有实际结束时间时，才使用实际结束时间
      endTime = dayjs(record.realEndTime);
    } else {
      // 其他所有情况（包括进行中和静置完成），都使用当前时间
      endTime = dayjs();
    }

    const elapsedDuration = dayjs.duration(endTime.diff(startTime));
    const elapsedMilliseconds = elapsedDuration.asMilliseconds();

    // 计算进度百分比 - 对于非结束状态，进度可以超过100%
    let progress;
    if (isEnded) {
      // 已结束状态，进度不超过100%
      progress = Math.min(100, Math.floor((elapsedMilliseconds / totalMilliseconds) * 100));
    } else {
      // 其他状态，进度可以超过100%
      progress = Math.floor((elapsedMilliseconds / totalMilliseconds) * 100);
      // 限制最大显示进度为100%，但实际时间仍然是真实值
      progress = Math.min(progress, 100);
    }

    // 计算已静置的小时和分钟 - 显示实际值，不受计划时长限制
    const elapsedHours = Math.floor(elapsedDuration.asHours());
    const elapsedMinutes = Math.floor(elapsedDuration.minutes());

    // 计算剩余时间（如果已完成或超时，则为0）
    const remainingDuration = dayjs.duration(Math.max(0, totalMilliseconds - elapsedMilliseconds));
    const remainingHours = Math.floor(remainingDuration.asHours());
    const remainingMinutes = Math.floor(remainingDuration.minutes());

    return {
      isStarted: true, // 是否开始
      isCompleted: isCompleted || isEnded, // 是否完成
      isEnded, // 是否结束
      progress, // 进度百分比
      elapsedHours, // 已静置的小时
      elapsedMinutes, // 已静置的分钟
      remainingHours, // 剩余时间的小时
      remainingMinutes, // 剩余时间的分钟
      totalHours: record.loosenTime, // 计划总时长
      actualHours: elapsedHours, // 实际静置小时数
      actualMinutes: elapsedMinutes, // 实际静置分钟数
      hasRealEndTime: !!record.realEndTime, // 是否有实际结束时间
    };
  }

  // 计算松布时长进度
  function calculateTimeProgress(record) {
    return calculateLoosenTimes(record).progress;
  }

  // 获取进度条状态
  function getProgressStatus(record) {
    const times = calculateLoosenTimes(record);

    if (!times.isStarted) return 'normal';
    if (times.isCompleted) return 'success';
    return 'active';
  }

  function getElapsedHours(record) {
    const times = calculateLoosenTimes(record);
    return times.isStarted ? times.elapsedHours : 0;
  }

  // 获取进度条分数显示
  function getProgressFraction(record) {
    const times = calculateLoosenTimes(record);

    if (!times.isStarted) {
      return '0/' + record.loosenTime;
    }

    // 返回分数形式：已静置小时/计划总小时
    return times.elapsedHours + '/' + record.loosenTime;
  }

  // 获取进度条提示信息
  function getTimeProgressTooltip(record) {
    const times = calculateLoosenTimes(record);

    if (!times.isStarted) {
      return '未开始静置';
    }

    if (times.isEnded) {
      // 已结束
      return `已静置 ${times.actualHours} 小时 ${times.actualMinutes} 分钟（计划 ${times.totalHours} 小时）`;
    }

    if (times.isCompleted) {
      // 静置完成但未结束，显示实际静置时间
      return `已静置 ${times.actualHours} 小时 ${times.actualMinutes} 分钟（计划 ${times.totalHours} 小时）`;
    }

    // 进行中
    return `已静置 ${times.elapsedHours} 小时 ${times.elapsedMinutes} 分钟，剩余 ${times.remainingHours} 小时 ${times.remainingMinutes} 分钟`;
  }

  onMounted(() => {
    queryData();
    startProgressTimer();
  });

  // 清除定时器
  onUnmounted(() => {
    stopProgressTimer();
  });
</script>
<style scoped>
  .loosen-progress-wrapper {
    position: relative;
    width: 100%;
  }

  .loosen-progress-text {
    position: absolute;
    top: 50%;
    left: 88%;
    transform: translate(-50%, -50%);
    white-space: nowrap;
    font-size: 12px;
    text-shadow: 0px 0px 2px white;
  }

  .elapsed-hours {
    color: #ff8c00; /* 橙色 */
    font-weight: bold;
  }

  .total-hours {
    color: rgba(0, 0, 0, 0.88); /* 黑色 */
  }
</style>

<!--
  * 设备列表
  *
  * @Author:    cjm
  * @Date:      2025-02-09 20:12:13
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="设备查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="请输入编号或名称" />
            </a-form-item>
            <a-form-item label="设备类别" class="smart-query-form-item">
              <a-select
                  ref="select"
                  v-model:value="queryForm.typeId"
                  :allowClear="true"
                  :options="equipmentTypeOptions"
                  placeholder="设备类别"
                  style="width: 120px"
              />
            </a-form-item>
            <a-form-item label="车间" class="smart-query-form-item">
              <a-select
                  ref="select"
                  v-model:value="queryForm.workshopId"
                  :allowClear="true"
                  :options="workshopOptions"
                  placeholder="车间"
                  style="width: 120px"
              />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData" >
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>

        <EquipmentForm  ref="formRef" @reloadList="queryData"/>

    </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { equipmentApi } from '/src/api/business/mes/equipment/equipment-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import EquipmentForm from './equipment-form.vue';
import {equipmentTypeApi} from "/@/api/business/mes/equipment/equipment-type-api.js";
import {workshopApi} from "/@/api/business/mes/factory/workshop-api.js";
// ---------------------------- 表格列 ----------------------------

const columns = ref([
    {
        title: '设备编号',
        dataIndex: 'number',
        ellipsis: true,
        width: 120
    },
    {
        title: '设备名称',
        dataIndex: 'name',
        ellipsis: true,
        width: 150
    },
    {
        title: '规格',
        dataIndex: 'specification',
        ellipsis: true,
        width: 250
    },
    {
        title: '型号',
        dataIndex: 'model',
        ellipsis: true,
        width: 100
    },
    {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 90,
    },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
    queryKey: undefined, //关键字查询
    workshopId: undefined, //车间
    typeId: undefined, //设备类别
    pageNum: 1,
    pageSize: 10,
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
}

// 查询数据
async function queryData() {
    tableLoading.value = true;
    try {
        let queryResult = await equipmentApi.queryPage(queryForm);
        tableData.value = queryResult.data.list;
        total.value = queryResult.data.total;
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        tableLoading.value = false;
    }
}

// ------------------------ 输出车间和类型下拉数据 ---------------------
const equipmentTypeOptions = ref([]);
async function queryEquipmentTypeList() {
  await equipmentTypeApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    // 输出设备类别options
    equipmentTypeOptions.value = res.data.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    })
  });
}
const workshopOptions = ref([])
async function queryWorkshopList() {
  await workshopApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    // 输出设备类别options
    workshopOptions.value = res.data.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    })
  });
}

onMounted(()=>{
  queryData()
  queryEquipmentTypeList()
  queryWorkshopList()
});

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showForm(data) {
    formRef.value.show(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data){
    Modal.confirm({
        title: '提示',
        content: '确定要删除选吗?',
        okText: '删除',
        okType: 'danger',
        onOk() {
            requestDelete(data);
        },
        cancelText: '取消',
        onCancel() {},
    });
}

//请求删除
async function requestDelete(data){
    SmartLoading.show();
    try {
        await equipmentApi.delete(data.id);
        message.success('删除成功');
        queryData();
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        SmartLoading.hide();
    }
}
</script>



<template>
  <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules">
    <span style="color: #909399">用户名:</span>
    <a-form-item name="loginName">
      <a-input v-model:value.trim="loginForm.loginName" placeholder="请输入用户名" style="height: 42px"/>
    </a-form-item>
    <a-form-item name="password">
      <span style="color: #909399">密码:</span>
      <a-input-password v-model:value="loginForm.password" autocomplete="on"
                        :type="showPassword ? 'text' : 'password'"
                        placeholder="请输入密码：至少三种字符，最小 8 位" style="height: 42px"/>
    </a-form-item>
    <a-form-item name="captchaCode">
      <span style="color: #909399">验证码:</span>
      <div style="display: flex;justify-content: space-between;align-items: center;">
        <div style="width: 70%">

          <a-input class="captcha-input" v-model:value.trim="loginForm.captchaCode" placeholder="请输入验证码"
                   style="height: 42px;"/>
        </div>
        <img class="captcha-img" :src="captchaBase64Image" @click="getCaptcha"/>
      </div>
    </a-form-item>
    <a-form-item>
      <a-checkbox v-model:checked="rememberPwd">记住密码</a-checkbox>
      <!--          <span> ( 账号：admin, 密码：123456)</span>-->
    </a-form-item>
    <a-form-item>
      <div class="btn" @click="onLogin">登录</div>
    </a-form-item>
  </a-form>
</template>
<script setup>
import {onMounted, onUnmounted, reactive, ref} from "vue";
import {LOGIN_DEVICE_ENUM} from "/@/constants/system/login-device-const.js";
import {useRouter} from "vue-router";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import {encryptData} from "/@/lib/encrypt.js";
import {loginApi} from "/@/api/system/login-api.js";
import {localSave} from "/@/utils/local-util.js";
import LocalStorageKeyConst from "/@/constants/local-storage-key-const.js";
import {message} from "ant-design-vue";
import {useUserStore} from "/@/store/modules/system/user.js";
import {buildRoutes} from "/@/router/index.js";
import {smartSentry} from "/@/lib/smart-sentry.js";

const loginForm = reactive({
  loginName: 'admin',
  password: '',
  captchaCode: '',
  captchaUuid: '',
  loginDevice: LOGIN_DEVICE_ENUM.PC.value,
});
const rules = {
  loginName: [{required: true, message: '用户名不能为空'}],
  password: [{required: true, message: '密码不能为空'}],
  captchaCode: [{required: true, message: '验证码不能为空'}],
};

const showPassword = ref(false);
const router = useRouter();
const formRef = ref();
const rememberPwd = ref(false);

onMounted(() => {
  document.onkeyup = (e) => {
    if (e.keyCode == 13) {
      onLogin();
    }
  };
})

onUnmounted(() => {
  document.onkeyup = null;
});

//登录
async function onLogin() {
  formRef.value.validate().then(async () => {
    try {
      SmartLoading.show();
      // 密码加密
      let encryptPasswordForm = Object.assign({}, loginForm, {
        password: encryptData(loginForm.password),
      });
      const res = await loginApi.login(encryptPasswordForm);
      stopRefrestCaptchaInterval();
      localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
      message.success('登录成功');
      //更新用户信息到pinia
      useUserStore().setUserLoginInfo(res.data);
      //构建系统的路由
      buildRoutes();
      router.push('/home');
    } catch (e) {
      if (e.data && e.data.code !== 0) {
        loginForm.captchaCode = '';
        getCaptcha();
      }
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  });
}

//--------------------- 验证码 ---------------------------------

const captchaBase64Image = ref('');

async function getCaptcha() {
  try {
    let captchaResult = await loginApi.getCaptcha();
    captchaBase64Image.value = captchaResult.data.captchaBase64Image;
    loginForm.captchaUuid = captchaResult.data.captchaUuid;
    beginRefrestCaptchaInterval(captchaResult.data.expireSeconds);
  } catch (e) {
    console.log(e);
  }
}

let refrestCaptchaInterval = null;

function beginRefrestCaptchaInterval(expireSeconds) {
  if (refrestCaptchaInterval === null) {
    refrestCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
  }
}

function stopRefrestCaptchaInterval() {
  if (refrestCaptchaInterval != null) {
    clearInterval(refrestCaptchaInterval);
    refrestCaptchaInterval = null;
  }
}

onMounted(getCaptcha);
onUnmounted(stopRefrestCaptchaInterval);

</script>
<style scoped lang="less">
.img-drap {
  min-width: 260px;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(/@/assets/images/login/login-bg7.png);
}

.entry {
  position: relative;
  width: 480px;
  height: 100%;
  float: right;
  background: #fff;
  overflow-y: auto;

  .login-box {
    padding: 10px 72px;
  }

  .btn {
    width: 350px;
    height: 50px;
    background: #1748FD;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    color: #ffffff;
    line-height: 50px;
    cursor: pointer;
  }
}
</style>

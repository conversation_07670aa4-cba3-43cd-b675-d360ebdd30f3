

<template>
<a-card title="驿站设置">
  <template #extra>
  <a-button type="primary" @click="handleUpdate">保存</a-button>
  <a-button @click="queryConfig" class="smart-margin-left10"><template #icon><ReloadOutlined /></template>重置</a-button>
  </template>
  <a-card style="background-color: #F2F6FC;margin-bottom: 20px" title="存放时间巡检配置">
    <a-row class="smart-query-form-row" style="display: flex;justify-content: space-between; ">
        <a-form-item label="是否启用" class="smart-query-form-item">
        <a-switch v-model:checked="config.storageTimeConfig.enableFlag" ></a-switch>
        </a-form-item>
        <a-form-item label="最小存放时间"   class="smart-query-form-item">
          <a-input-number  style="width: 125px" v-model:value="config.storageTimeConfig.minDay" addon-after="天" />
        </a-form-item>
        <a-form-item label="最大存放时间"  class="smart-query-form-item">
            <a-input-number  style="width: 125px" v-model:value="config.storageTimeConfig.maxDay" addon-after="天" />
        </a-form-item>
           <a-form-item label="通知人"  class="smart-query-form-item">
              <EmployeeMoreSelect :style="{width: '350px'}" v-model:value="config.storageTimeConfig.employeeIds" />
        </a-form-item>
    </a-row>
  </a-card>
  <a-card style="background-color: #F2F6FC;margin-bottom: 20px" title="货位压力巡检配置">
    <a-row class="smart-query-form-row" style="display: flex;justify-content: space-between;">
        <a-form-item label="是否启用" class="smart-query-form-item">
        <a-switch v-model:checked="config.storagePressureConfig.enableFlag" ></a-switch>
        </a-form-item>
        <a-form-item label="货位最大使用率"  class="smart-query-form-item">
            <a-input-number  style="width: 125px" v-model:value="config.storagePressureConfig.maxUsageRate" addon-after="%" />  
        </a-form-item>
        <a-form-item label="通知人"  class="smart-query-form-item">
              <EmployeeMoreSelect :style="{width: '350px'}" v-model:value="config.storagePressureConfig.employeeIds" />
        </a-form-item>
    </a-row>
  </a-card>
  <a-card style="background-color: #F2F6FC;margin-bottom: 20px" title="管理模式配置">
    <a-row class="smart-query-form-row" style="display: flex;justify-content: space-between;">
        <a-form-item label="请选择管理模式" class="smart-query-form-item">
        <a-radio-group v-model:value="config.storageManageModeConfig.manageMode" @change="handleManageModeChange">
          <a-radio v-for="(mode,key) in MANAGE_MODE_ENUM" :key="mode.value" :value="mode.value">{{ mode.desc }}</a-radio>
        </a-radio-group>
        </a-form-item>
        <!-- <a-form-item label="通知人"  class="smart-query-form-item">
              <EmployeeMoreSelect :style="{width: '350px'}" v-model:value="config.storagePressureConfig.employeeIds" />
        </a-form-item> -->
    </a-row>
  </a-card>
</a-card>
</template>
<script setup>
import { partStationConfigApi } from '/@/api/business/mes/part-station/config/part-station-config';
import {  onMounted, reactive,ref } from 'vue';
import {Modal} from 'ant-design-vue'
import  EmployeeMoreSelect  from '/@/components/system/employee-more-select/index.vue';
import {MANAGE_MODE_ENUM} from '../../../../../constants/business/mes/part-station/setting/setting-const';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
const ConfigState = {
  storageTimeConfig: {
    enableFlag: false,
    minDay: 0,
    maxDay: 0,
    employeeIds: [],
  },
  storagePressureConfig: {
    enableFlag: false,
    maxUsageRate: 0,
    employeeIds: [],
  },
  storageManageModeConfig: {
    configId: undefined,
    manageMode: undefined,
  }
};

const config = reactive({...ConfigState});

async function handleUpdate(){
  try {
    await partStationConfigApi.update(config);
    message.success('更新成功');
  } catch (error) {
    message.error('更新失败');
  }finally{
   queryConfig();
  }
}
//保存之前的选项value值
const previousManageMode = ref({});
//管理模式选项改变确认
function handleManageModeChange(event) {
  const newMode = event.target.value;
  if (newMode !== previousManageMode.value) {
    Modal.confirm({
      title: '是否要切换管理模式',
      content: '切换管理模式后，库存相关数据将会清除，是否继续？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        config.storageManageModeConfig.manageMode = newMode;
        previousManageMode.value = newMode;
      },
      onCancel: () => {
        config.storageManageModeConfig.manageMode = previousManageMode.value;
      },
    });
  }
}

 async function queryConfig(){
  try {
    SmartLoading.show();
    const res = await partStationConfigApi.query();
    Object.assign(config, res.data);
    previousManageMode.value = config.storageManageModeConfig.manageMode;
  } catch (error) {
    message.error('查询失败');
  }finally{
    SmartLoading.hide();
  }
}
onMounted(queryConfig);
</script>
<style scoped lang="less">
        
</style>

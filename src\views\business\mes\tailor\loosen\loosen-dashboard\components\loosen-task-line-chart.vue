<template>
  <div style="display: flex; justify-content: space-between">
    <div style="font-size: 16px; font-weight: bold">松布产量趋势</div>
    <a-radio-group v-model:value="timeOption" @change="handleTimeChange" :options="timeOptions" option-type="button" />
  </div>
  <div class="echarts-box">
    <div class="chart-main" id="loosen-line-chart"></div>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted, ref } from 'vue';
  import * as echarts from 'echarts';

  let myChart = null;

  // 时间选择选项
  const timeOptions = [
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' },
    { label: '本季', value: 'quarter' },
    { label: '本年', value: 'year' },
  ];

  const timeOption = ref('week');

  // 静态数据
  const staticData = {
    week: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      planValues: [150, 230, 224, 218, 135, 147, 260],
      realValues: [120, 210, 190, 170, 110, 130, 200],
    },
    month: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      planValues: [150, 230, 224, 218, 135, 147, 260],
      realValues: [120, 210, 190, 170, 110, 130, 200],
    },
    quarter: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      planValues: [150, 230, 224, 218, 135, 147, 260],
      realValues: [120, 210, 190, 170, 110, 130, 200],
    },
    year: {
      dates: ['07/01', '07/02', '07/03', '07/04', '07/05', '07/06', '07/07'],
      planValues: [150, 230, 224, 218, 135, 147, 260],
      realValues: [120, 210, 190, 170, 110, 130, 200],
    },
  };

  onMounted(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (myChart) {
      myChart.dispose();
      myChart = null;
    }
  });

  // 窗口大小变化处理
  function handleResize() {
    if (myChart) {
      myChart.resize();
    }
  }

  // 处理时间选择变化
  function handleTimeChange() {
    updateChart();
  }

  // 初始化图表
  function initChart() {
    const chartDom = document.getElementById('loosen-line-chart');
    if (chartDom) {
      myChart = echarts.init(chartDom);
      updateChart();
    }
  }

  // 更新图表
  function updateChart() {
    if (!myChart) return;

    // 获取当前选择的时间范围对应的数据
    const currentData = staticData[timeOption.value];

    const option = {
      xAxis: {
        type: 'category',
        data: currentData.dates,
        axisLabel: {
          interval: 0,
          fontSize: 10,
        },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
        axisLabel: {
          fontSize: 10,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
        },
      },
      legend: {
        data: ['计划卷数', '实际卷数'],
        textStyle: {
          fontSize: 10,
        },
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      series: [
        {
          name: '计划卷数',
          data: currentData.planValues,
          type: 'line',
          symbolSize: 4,
          smooth: true,
          areaStyle: {
            color: '#004bf9',
            opacity: 0.3,
          },
          itemStyle: {
            color: '#004bf9',
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 9,
          },
        },
        {
          name: '实际卷数',
          data: currentData.realValues,
          type: 'line',
          symbolSize: 4,
          smooth: true,
          areaStyle: {
            color: '#00c2ff',
            opacity: 0.3,
          },
          itemStyle: {
            color: '#00c2ff',
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 9,
          },
        },
      ],
    };

    myChart.setOption(option);

    // 强制重新计算大小，确保图表充满容器
    setTimeout(() => {
      myChart.resize();
    }, 200);
  }
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: calc(100% - 40px);

    .chart-main {
      width: 100%;
      height: 100%;
      min-height: 230px;
      background: #fff;
    }
  }
</style>

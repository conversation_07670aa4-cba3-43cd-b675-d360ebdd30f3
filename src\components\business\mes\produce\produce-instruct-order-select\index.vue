<!--
  * 生产指令单下拉选择框 
  * 返回对应单据类型、id、单据编号、物料编号
  * @Author:    wxx
  * @Date:      2025-02-17

-->
<template>
  <a-select
    v-model:value="selectValue"
    :style="`width: ${width}`"
    :placeholder="props.placeholder"
    :showSearch="true"
    :allowClear="true"
    :filterOption="filterOption"
    :size="size"
    :disabled="props.disabled"
    @change="onChange"
  >
    <a-select-option v-for="item in instructList" :key="item.id" :value="item.id">
      <div>
        {{ item.instructNumber }}
        <br />
        <span style="font-size: 12px; color: #999">{{ item.itemNumber }}</span>
      </div>
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const';

  const props = defineProps({
    value: [Number, Array],
    placeholder: {
      type: String,
      default: '请选择',
    },
    width: {
      type: String,
      default: 'default',
    },
    size: {
      type: String,
      default: 'default',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  // 过滤选项
  function filterOption(inputValue, option) {
    const item = instructList.value.find((i) => i.id === option.value);
    if (!item || !inputValue) return true;

    // 转换为小写进行比较
    const searchText = inputValue.toLowerCase();
    const instructNumber = (item.instructNumber || '').toLowerCase();
    const itemNumber = (item.itemNumber || '').toLowerCase();

    // 只根据指令单编号和物料编号进行过滤
    return instructNumber.includes(searchText) || itemNumber.includes(searchText);
  }

  // =========== 查询指令单数据 =============
  const instructList = ref([]);
  async function query() {
    try {
      let resp = await produceInstructOrderApi.queryInsOrItem({});
      // 指令单列表数据
      instructList.value = resp.data.map((item) => ({
        id: item.id,
        instructNumber: item.instructNumber,
        itemNumber: item.itemNumber,
        orderType: BillTypeEnum.PRODUCE_INSTRUCT_ORDER.desc,
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  onMounted(query);

  // =========== 选择 监听、事件 =============
  const selectValue = ref(props.value);
  const isUserChange = ref(false); // 标识是否为用户手动选择

  watch(
    () => props.value,
    (newValue) => {
      selectValue.value = newValue;
    }
  );

  // 监听指令单列表和选中值
  watch([() => instructList.value, () => props.value], ([items, newValue]) => {
    if (newValue && items.length > 0 && !isUserChange.value) {
      const selectedItem = items.find((item) => item.id === newValue);
      if (selectedItem) {
        emit('change', selectedItem);
      }
    }
    // 重置标识
    isUserChange.value = false;
  });

  function onChange(value) {
    isUserChange.value = true; // 标记为用户手动选择
    const selectedItem = instructList.value.find((item) => item.id === value);
    emit('update:value', value);
    emit('change', selectedItem || null);
  }
</script>

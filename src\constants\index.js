/*
 * 所有常量入口
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-06 19:58:28
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import menu from './system/menu-const';
import goods from './business/erp/goods-const';
import category from './business/erp/category-const';
import { LOGIN_DEVICE_ENUM } from './system/login-device-const';
import { FLAG_NUMBER_ENUM, GENDER_ENUM, USER_TYPE_ENUM } from './common-const';
import { LAYOUT_ENUM } from './layout-const';
import file from './support/file-const';
import notice from './business/oa/notice-const';
import loginLog from './support/login-log-const';
import enterprise from './business/oa/enterprise-const';
import {Template} from './business/mes/tailor/cut-bed-sheet-const.js'
import message from './business/message/message-const';
import codeGeneratorConst from './support/code-generator-const';
import changeLogConst from './support/change-log-const';
import jobConst from './support/job-const';
import {IOT_PLATFORM_ENUM} from "/@/constants/business/mes/equipment/iot-platform-const.js";
import {IOT_EQUIPMENT_TYPE_ENUM} from "/@/constants/business/mes/equipment/iot-equipment-type-const.js";
import partDispatchLogConst from "/@/constants/business/mes/part-dispatch/part-dispatch-log-const.js";
import {WAGE_TYPE_ENUM} from "/@/constants/business/mes/salary/custom-pay-const.js";
import {SETTLEMENT_STATUS_ENUM} from "/@/constants/business/mes/salary/settlementStatus-const.js";
import {SETTLEMENT_WAY_ENUM} from "/@/constants/business/mes/salary/settlementWay-const.js";

export default {
  Template,
  FLAG_NUMBER_ENUM,
  LOGIN_DEVICE_ENUM,
  GENDER_ENUM,
  USER_TYPE_ENUM,
  LAYOUT_ENUM,
  ...loginLog,
  ...menu,
  ...goods,
  ...category,
  ...file,
  ...notice,
  ...enterprise,
  ...message,
  ...codeGeneratorConst,
  ...changeLogConst,
  ...jobConst,
  SETTLEMENT_WAY_ENUM,
  SETTLEMENT_STATUS_ENUM,
  WAGE_TYPE_ENUM,
  IOT_PLATFORM_ENUM,
  IOT_EQUIPMENT_TYPE_ENUM,
  ...partDispatchLogConst,
};

<template>
  <a-modal v-model:open="visible" width="100%" title="任务详情" @cancel="closeModal" >
    <a-spin :spinning="dataLoading">

    <a-descriptions
        v-if="task"
        bordered
        column="3"
        size="middle"
        style="font-size: 18px;margin-bottom: 14px"
    >
      <a-descriptions-item label="编号">{{ task.number }}</a-descriptions-item>
      <a-descriptions-item label="状态">{{
          SPREAD_TASK_STATUS_ENUM.getEnum(task.taskStatus).label
        }}
      </a-descriptions-item>
      <a-descriptions-item label="生产指令单编号">{{ task.produceInstructOrderNumber }}</a-descriptions-item>
      <a-descriptions-item label="款号名称">{{ task.materialName }}</a-descriptions-item>
      <a-descriptions-item label="款号">{{ task.materialSpuNumber }}</a-descriptions-item>
      <a-descriptions-item label="床次">{{ task.cutNum }}</a-descriptions-item>
      <a-descriptions-item label="车间名称">{{ task.workshopName }}</a-descriptions-item>
      <a-descriptions-item label="设备名称">{{ task.equipmentName }}</a-descriptions-item>
      <a-descriptions-item label="小组名称">{{ task.teamName }}</a-descriptions-item>
      <a-descriptions-item label="计划总层数">{{ task.planTotalLayer }}</a-descriptions-item>
      <a-descriptions-item label="计划铺布开始时间">{{ task.planBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="计划铺布结束时间">{{ task.planEndTime }}</a-descriptions-item>
      <a-descriptions-item label="实际开始时间" v-if="task.taskStatus===SPREAD_TASK_STATUS_ENUM.DOING.value" style="background-color: #67C23A;color: white">{{ task.realBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="实际铺布开始时间" v-else>{{ task.realBeginTime }}</a-descriptions-item>
      <a-descriptions-item label="实际完成时间">{{ task.realEndTime }}</a-descriptions-item>
      <a-descriptions-item label="完成人">{{ task.realCompleterName }}</a-descriptions-item>
      <a-descriptions-item label="生产要求" :span="3">{{ task.produceRequire }}</a-descriptions-item>
    </a-descriptions>

    <div>
      <span style="font-weight: bold">面料检查</span>
      <a-form class="smart-query-form" v-if="task.materialCheckFlag">
        <a-form-item class="smart-query-form-item" style="font-size: 20px">
          <a-input placeholder="扫描面料二维码" style="font-size: 20px" ref="codeScanInputRef" autofocus
                   @pressEnter="qrCodePressEnter" v-model:value="qrCode"/>
        </a-form-item>
      </a-form>

        <a-table
            v-if="task.materialCheckFlag"
            size="small"
            :columns="columns"
            :data-source="tableData"
            :pagination="false"
            bordered
            rowKey="id"
            :scroll="{ y: 300 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'lotCheckFlag'">
              <span v-if="record.lotCheckFlag" style="color: #0fd560">开启</span>
              <span v-else style="">关闭</span>
            </template>
            <template v-else-if="column.dataIndex === 'checkResultFlag'">
              <!--          <a-tag :color="record.checkResultFlag ? 'green' : 'red'" style="font-size: 18px">{{-->
              <!--              record.checkResultFlag ? '通过' : '未通过'-->
              <!--            }}-->
              <!--          </a-tag>-->
              {{ record.checkResultFlag ? '通过' : '未通过' }}
            </template>
            <template v-else-if="column.dataIndex === 'realLayer'">
              <a-input-number size="large" v-model:value="record.realLayer" min="0">
                <template #addonAfter>
                  <span @click="record.realLayer=record.planLayer">层</span>
                </template>
              </a-input-number>
            </template>
            <template v-else-if="column.dataIndex === 'realLength'">
              <a-input-number size="large" v-model:value="record.realLength" min="0">
                <template #addonAfter>
                  <span @click="record.realLength=record.planLength">米(m)</span>
                </template>
              </a-input-number>
            </template>
          </template>
        </a-table>
        <div v-else
             style="display: flex; justify-content: center; align-items: center; width: 100%; height: 40px; background-color: #F2F6FC; font-size: 20px; color: #909399;">
          暂无要求
        </div>
    </div>

    <div>
      <span style="font-weight: bold">铺布</span>
      <ColorSize
          :task-status="taskStatusInfo"
          :size-list="task.taskSizes"
          :color-list="task.taskColors"
          :part-list="task.partList"
          :produce-instruct-order-id="task.produceInstructOrderId"
          ref="colorSizeRef"/>
    </div>
    </a-spin>
    <template #footer>
      <a-button @click="closeModal" size="large">关闭</a-button>
      <a-button type="primary" style="background-color: #4abe4a" size="large" @click="startTask" v-if="task.taskStatus===SPREAD_TASK_STATUS_ENUM.ISSUE.value">开始任务</a-button>
      <a-button type="primary" style="background-color: #faad14" size="large" @click="finishTask" v-if="task.taskStatus===SPREAD_TASK_STATUS_ENUM.DOING.value">完成任务</a-button>
      <a-button type="primary" size="large" @click="cancelTask">取消任务</a-button>

    </template>
  </a-modal>
    <FeTicketDownload ref="feTicketDownloadRef" />
</template>

<script setup>
import {nextTick, onMounted, ref} from "vue";
import {spreadTaskApi} from "/@/api/business/mes/tailor/spread-task-api.js";
import {SPREAD_TASK_STATUS_ENUM} from "/@/constants/business/mes/tailor/spread-task-const.js";
import {message} from "ant-design-vue";
import { smartSentry } from '/@/lib/smart-sentry.js';
import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api.js';
import ColorSize from "/@/views/business/mes/tailor/spread/spread-workbench/components/color-size.vue";
import FeTicketDownload from '/@/views/business/mes/tailor/cutBedSheet/components/fetTicket-download/index.vue';
// ---------------------事件----------
const emits = defineEmits(['reloadList']);


const codeScanInputRef = ref();

onMounted(() => {
  setTimeout(() => {
    codeScanInputRef.value.focus()
  }, 1000)
})

//------------------------

defineExpose({
  showModal,
});

const visible = ref(false);

async function showModal(id) {
  visible.value = true;
  console.log("12")
  await queryTaskDetail(id);
}

function closeModal() {
  visible.value = false;
}

//---------------------------------

const dataLoading = ref(false);

const task = ref({
  materialCheckFlag: false,
});
const taskStatusInfo = ref(null);
async function queryTaskDetail(id) {
  dataLoading.value = true;
  try {
    const res = await spreadTaskApi.queryById(id);
    console.log(res.data)
    task.value = res.data;
    taskStatusInfo.value = task.value.taskStatus;
    tableData.value = res.data.taskMaterials || [];
  } finally {
    dataLoading.value = false;
  }
}

//---------------------
const tableData = ref([]);
const columns = [
  {
    title: '物料SPU编号',
    dataIndex: 'materialSpuNumber',
    align: 'center',
  },
  {
    title: '物料SKU编号',
    dataIndex: 'materialSkuNumber',
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'materialName',
    align: 'center',
  },
  {
    title: '物料型号',
    dataIndex: 'materialModel',
    align: 'center',
  },
  {
    title: '物料颜色',
    dataIndex: 'materialColor',
    align: 'center',
  },
  {
    title: '批次编号(缸号)',
    dataIndex: 'lotNumber',
    align: 'center',
  },
  {
    title: '批次校验',
    dataIndex: 'lotCheckFlag',
    align: 'center',
  },
  {
    title: '校验结果',
    dataIndex: 'checkResultFlag',
    align: 'center',
    customCell: (record) => {
      let backgroundColor = '';
      if (record.checkResultFlag) {
        backgroundColor = '#0fd560';
      } else {
        backgroundColor = '#faad14';
      }
      return {
        style: {
          backgroundColor: backgroundColor,
          color: '#fff',
        },
      };
    },
  },
]

//---------------------------------

async function startTask() {
  await spreadTaskApi.start(task.value.id)
  await queryTaskDetail(task.value.id)
  emits('reloadList');
  message.success('开始任务成功');
}

async function finishTask() {
  //加载
if(colorSizeRef.value.colorRow.map(e => e.realLayers).some(e => {
    return e == 0 || e == null;
})) {
    message.error('请填写实际层数');
    return;
}
try {
  dataLoading.value = true;
  let param = {
    id: task.value.id,
    taskColors:colorSizeRef.value.colorRow.map(e=>{
      return{
        color:e.color,
        realLayer:e.realLayers,
        realLength:e.realLength
      }
    }),
    taskSizes:colorSizeRef.value.sizeRow.map(e=>{
      return{
        id:e.id,
        size:e.size,
        realRatio:e.realRatio
      }
    }),
    partList:colorSizeRef.value.selectParts
  }
    const paramTicket = {
    instructOrderId: task.value.produceInstructOrderId,
    sizeList: colorSizeRef.value.sizeRow.map(e =>{
      return{
        size:e.size,
        ratio:e.realRatio
      }
    }),
    colorList: colorSizeRef.value.colorRow.map(e =>{
      return{
        color:e.color,
        num:e.realLayers
      }
    }),
    partList: colorSizeRef.value.selectParts.map(e=>{
      return{
        name:e
      }
    })
  }
  console.log(paramTicket)
  await spreadTaskApi.complete(param)

  message.success('完成任务成功');
  emits('reloadList');
  closeModal()
  saveBodyAndProportion(paramTicket);
}finally {
  dataLoading.value = false;
}

}

async function cancelTask() {
  await spreadTaskApi.cancel(task.value.id)
  await queryTaskDetail(task.value.id)
  message.success('取消任务成功');
  emits('reloadList');
  closeModal()

}

//-----------------------------------------
const qrCode = ref();

async function qrCodePressEnter(e) {
  try {
    let code = e.target.value;
    if (!code) {
      message.error('请输入正确的二维码');
      return
    }
    console.log(task.value.id);
    await spreadTaskApi.checkProcess({
      taskId: task.value.id,
      qrCode: code
    })
    await queryTaskDetail(task.value.id)
    message.success('校验成功');

  } finally {
    qrCode.value = null;
    nextTick(() => {
      codeScanInputRef.value.focus()
    })
  }
}
const feTicketDownloadRef = ref();
async function saveBodyAndProportion(data) {
  try {
    //获取提交生成的指令单id
    const result = await feTicketApi.addByRatio(data);
    if (result) {
      const cutBedSheetId = result.data?.id;
      //查询生成的菲票
      const response = await feTicketApi.queryList({ cutBedSheetId });
      console.log(response.data)
      if (response.data && response.data.length > 0) {
        // 提取ID到ids数组
        const ids = response.data.map((item) => item.id);
        const parts = data.partList.map(e=>e.name) || [];
       const tickForm = {
          ids: ids,
          parts: parts,
          processes: [],
          template: undefined,
        };
        // 显示菲票下载对话框
        feTicketDownloadRef.value.getData(tickForm);
      } else {
        message.info('未查询到相关菲票数据');
      }
    }
  } catch (error) {
    smartSentry.captureError(error);
  }
}

//-------------------------------------
const colorSizeRef = ref();

</script>


<style scoped lang="less">

</style>

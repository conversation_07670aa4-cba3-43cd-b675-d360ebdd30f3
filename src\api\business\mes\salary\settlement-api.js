/**
 * 薪酬结算记录 api 封装
 *
 * @Author:    linwj
 * @Date:      2024-11-20 20:48:59
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const settlementApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/settlement/queryPage', param);
  },

  /**
   * 结算明细  <AUTHOR>
   */
  settlementDetails: (employeeId,param) => {
      return postRequest(`/settlement/querySettlementDetails/${employeeId}`, param);
  },

  /**
   * 结算  <AUTHOR>
   */
  settlement: (param) => {
    return postRequest(`/settlement/settlement`, param);
  },

};

<!--
  * 工厂信息表
  *
  * @Author:    cyz
  * @Date:      2024-07-04 11:11:50
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
            </a-form-item>
            <a-form-item label="地址" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.address" placeholder="地址" />
            </a-form-item>
            <a-form-item label="邮政编码" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.postalCode" placeholder="邮政编码" />
            </a-form-item>
            <a-form-item label="联系人" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.contact" placeholder="联系人" />
            </a-form-item>
            <a-form-item label="电话" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.telephone" placeholder="电话" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                    </div>
                </template>
              <template v-else-if="column.dataIndex === 'name'">
                <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
              </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>

        <FactoryForm  ref="formRef" @reloadList="queryData"/>

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { factoryApi } from '/@/api/business/mes/factory/factory-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import FactoryForm from './factory-form.vue';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
      {
        title: '工厂名称',
        dataIndex: 'name',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '邮政编码',
        dataIndex: 'postalCode',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '联系人',
        dataIndex: 'contact',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '电话',
        dataIndex: 'telephone',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '地址',
        dataIndex: 'address',
        ellipsis: true,
        align: 'center',
      },
      {
        title: '备注',
        dataIndex: 'remark',
        ellipsis: true,
        align: 'center',
      },

      {
        title: '操作',
        dataIndex: 'action',
        fixed: 'right',
        width: 90,
        align: 'center',
      },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        queryKey: undefined, //关键字查询
        address: undefined, //地址
        postalCode: undefined, //邮政编码
        contact: undefined, //联系人
        telephone: undefined, //电话
        pageNum: 1,
        pageSize: 10,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await factoryApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }


    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }


    // ---------------------------- 批量删除 ----------------------------
    function onDelete(data) {
      Modal.confirm({
        title: '提示',
        content: '确定要删除选吗?',
        okText: '删除',
        okType: 'danger',
        onOk() {
          requestDelete(data);
        },
        cancelText: '取消',
        onCancel() {},
      });
    }
    async function requestDelete(data){
      SmartLoading.show();
      try {
        await factoryApi.delete(data.id);
        message.success('删除成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    }
</script>

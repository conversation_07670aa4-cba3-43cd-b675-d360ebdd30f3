<template>
  <a-card>
    <div style="display: flex;justify-content: space-between;">
      <div style="font-size: 16px;font-weight: bold">出入库情况</div>
      <a-radio-group v-model:value="timeOption" @change="timeChange" :options="timeOptions" option-type="button"/>
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="line-main-1"></div>
    </div>
    <div>
    </div>
  </a-card>
</template>
<script setup>
import {onMounted, reactive, ref} from "vue";
import * as echarts from "echarts";

let myChart = null;
onMounted(() => {
  init();
});
let option = reactive({});

function init() {
  option = {
    xAxis: {
      type: 'category',
      data: ["2025/6/24", "2025/6/25", "2025/6/26", "2025/6/27", "2025/6/28", "2025/6/29", "2025/6/30"],
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    legend: {
      data: ['出库数量', '入库数量'],
    },
    grid: {
      x: 50,
      y: 40,
      x2: 30,
      y2: 30,
    },
    // title: {
    //   text: '出入库数量趋势',
    // },
    series: [
      {
        name: '出库数量',
        data: [32, 20, 28, 37, 42, 60, 21],
        type: 'line',
        symbolSize: 8, //设定实心点的大小
        smooth: false,
        itemStyle: {
          color: '#004bf9',
        },
        label: {
          show: true, // 是否显示标签
          position: 'top', // 标签的位置
        },
      },
      {
        name: '入库数量',
        data: [22, 21, 25, 57, 32, 22, 45],
        type: 'line',
        symbolSize: 8, //设定实心点的大小
        smooth: false,
        itemStyle: {
          color: '#4ECDC4',
        },
        label: {
          show: true, // 是否显示标签
          position: 'top', // 标签的位置
        },
      },
    ],
  };
  let chartDom = document.getElementById('line-main-1');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    option && myChart.setOption(option);
  }
  window.addEventListener("resize", () => {
    myChart.resize();
  });
}

//----------------------------------------
const timeOptions = [
  {
    label: "本周",
    value: "week",
  },
  {
    label: "本月",
    value: "month",
  },
  {
    label: "本季",
    value: "quarter",
  },
  {
    label: "本年",
    value: "year",
  },
];

const timeOption = ref("week");

function timeChange(){
  console.log(timeOption.value)
}



//-------------------------------------
</script>

<style scoped lang="less">
.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .chart-main {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

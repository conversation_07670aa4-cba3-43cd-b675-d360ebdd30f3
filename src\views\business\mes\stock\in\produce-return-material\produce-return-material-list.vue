<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="关键字查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
            </a-form-item>
            <a-form-item label="单据状态" class="smart-query-form-item">
                <a-select v-model:value="queryForm.status" style="width: 150px" placeholder="单据状态">
                    <a-select-option :value="BILLS_STATUS.AUDIT.value">{{ BILLS_STATUS.AUDIT.desc }}</a-select-option>
                    <a-select-option :value="BILLS_STATUS.UN_AUDIT.value">{{ BILLS_STATUS.UN_AUDIT.desc }}</a-select-option>
                </a-select>
            </a-form-item>
            <a-form-item label="入库时间" class="smart-query-form-item">    
                <a-range-picker v-model:value="queryForm.inStockTime" :presets="defaultTimeRanges" style="width: 150px" @change="onChangeInStockTime" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon><SearchOutlined /></template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon><ReloadOutlined /></template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>

    <a-card size="small" :bordered="false" :hoverable="true">
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm()" type="primary" size="small">
                    <template #icon><PlusOutlined /></template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div> 
        </a-row>

        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="id"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :scroll="{ x: 2000 }"
            @change="handleTableChange"
        >
            <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex === 'status'">
                    <a-tag v-if="record.status === BILLS_STATUS.AUDIT.value" color="blue">{{ BILLS_STATUS.AUDIT.desc }}</a-tag>
                    <a-tag v-else color="orange">{{ BILLS_STATUS.UN_AUDIT.desc }}</a-tag>
                </template>
                <template v-if="column.dataIndex === 'number'">
                    <a type="link" @click="showForm(record.id)">{{ text }}</a>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record.id)" type="link">编辑</a-button>
                        <a-button @click="audit(record.id)" type="link">审核</a-button>
                        <a-button @click="deleteById(record.id)" danger type="link">删除</a-button>
                    </div>
                </template>
            </template>
        </a-table>

        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="queryForm.pageSize"
                v-model:current="queryForm.pageNum"
                v-model:pageSize="queryForm.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
    <ProduceReturnMaterialForm ref="formRef" @reloadList="queryData"/>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { defaultTimeRanges } from '/@/lib/default-time-ranges';
import { stkProduceReturnMaterialApi } from '/@/api/business/mes/stock/stk-produce-return-material-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { BILLS_STATUS } from '/@/constants/business/mes/stock/stk-status-const.js';
import ProduceReturnMaterialForm from './components/produce-return-material-form.vue';

const columns = ref([
  {
    title: '单据编号',
    dataIndex: 'number',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '单据状态',
    dataIndex: 'status',
    align: 'center',
  },
  {
    title: '入库时间',
    dataIndex: 'inStockTime',
    ellipsis: true,
    align: 'center',
    sorter: true,//启用排序
    sortDirections: ['ascend', 'descend'], // 允许升序和降序
  },
  {
    title: '申请人',
    dataIndex: 'applicantName',
    align: 'center',
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    ellipsis: true,
    align: 'center',
    sorter: true,//启用排序
    sortDirections: ['ascend', 'descend'], // 允许升序和降序
  },
  {
    title: '审核人',
    dataIndex: 'auditorName',
    align: 'center',
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    ellipsis: true,
    align: 'center',
    sorter: true,//启用排序
    sortDirections: ['ascend', 'descend'], // 允许升序和降序
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
    align: 'center',
    sorter: true,//启用排序
    sortDirections: ['ascend', 'descend'], // 允许升序和降序
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 125,
    fixed: 'right',
    align: 'center',
  }
]);

const queryFormDefault = {
    queryKey: undefined,
    status: undefined,
    inStockTime: [],
    inStockTimeBegin: undefined,
    inStockTimeEnd: undefined,
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
};

const queryForm = reactive({ ...queryFormDefault });
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const formRef = ref();

function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormDefault);
    queryForm.pageSize = pageSize;
    queryData();
}
//处理表格时间顺序变化事件
async function handleTableChange(pagination,filters,sorter) {
      if(sorter&&sorter.field==='inStockTime'){
      queryForm.sortItemList = 
      [{
        column:'in_stock_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='applyTime'){
        queryForm.sortItemList = 
      [{
        column:'apply_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='auditTime'){
      queryForm.sortItemList = 
      [{
        column:'audit_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='createTime'){
        queryForm.sortItemList = 
      [{
        column:'create_time',
        isAsc:sorter.order==='ascend'
      }];
    }
    if(!sorter.order){
      queryForm.sortItemList=
      [{
        column:'create_time',
        isAsc:false
      }]
    }
    await queryData();
    }
async function queryData() {
    tableLoading.value = true;
    try {
        let queryResult = await stkProduceReturnMaterialApi.queryPage(queryForm);
        tableData.value = queryResult.data.list;
        total.value = queryResult.data.total;
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        tableLoading.value = false;
    }
}

async function audit(id) {
    try {
        let result = await stkProduceReturnMaterialApi.updateStatus(id);
        if (result.code === 0) {
            message.success('审核成功');
            queryData();
        }
    } catch (e) {
        smartSentry.captureError(e);
    }
}

function deleteById(id) {
    Modal.confirm({
        title: '提示',
        content: '确定要删除选中记录吗?',
        okText: '删除',
        okType: 'danger',
        async onOk() {
            try {
                const result = await stkProduceReturnMaterialApi.deleteById(id);
                if (result.msg === '操作成功') {
                    message.success('删除成功');
                    queryData();
                }
            } catch (e) {
                smartSentry.captureError(e);
            }
        },
        cancelText: '取消',
        onCancel() {},
    });
}

function onChangeInStockTime(dates, dateStrings) {
    queryForm.inStockTimeBegin = dateStrings[0];
    queryForm.inStockTimeEnd = dateStrings[1];
}

function showForm(record) {
    formRef.value.showForm(record);
}

onMounted(queryData);
</script>
<!--
  * 布料下拉选择框 
  * 
  * @Author:    fkf
-->
<template>
    <a-select
      v-model:value="selectValue"
      :style="`width: ${width}`"
      :placeholder="props.placeholder"
      :showSearch="true"
      :allowClear="true"
      :disabled="props.disabled"
      :filterOption="filterOption"
      :size="size"
      @change="onChange"
    >
      <a-select-option v-for="item in clothList" :key="item.id" :value="item.id" >
        <div>{{ item.name }} <span style="font-size: 12px; color: #999;">{{ item.model }}</span>
          <br/>
          <span style="font-size: 12px; color: #999;">{{ item.skuNumber }}</span>
          <span v-if="item.colorName" style="font-size: 12px; color: #666; margin-left: 8px;">{{ item.colorName }}</span>
        </div>
      </a-select-option>
    </a-select>
  </template>
  
  <script setup>
    import { onMounted, ref, watch } from 'vue';
    import { itemClothApi } from '/@/api/business/mes/item/item-cloth-api';
    import { smartSentry } from '/@/lib/smart-sentry';
    
    // =========== 属性定义 和 事件方法暴露 =============
  
    const props = defineProps({
      value: [Number, Array],
      placeholder: {
        type: String,
        default: '请选择布料',
      },
      width: {
        type: String,
        default: '100%',
      },
      size: {
        type: String,
        default: 'default',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    });

    const emit = defineEmits(['update:value', 'change']);
    
    // 过滤选项
    function filterOption(inputValue, option) {
      const item = clothList.value.find((i) => i.id === option.value);
      if (!item || !inputValue) return true;

      // 转换为小写进行比较
      const searchText = inputValue.toLowerCase();
      const name = (item.name || '').toLowerCase();
      const skuNumber = (item.skuNumber || '').toLowerCase();
      const model = (item.model || '').toLowerCase();
      const colorName = (item.colorName || '').toLowerCase();

      // 返回名称、SKU编号、型号或颜色包含搜索文本的选项
      return name.includes(searchText) || 
             skuNumber.includes(searchText) || 
             model.includes(searchText) || 
             colorName.includes(searchText);
    }
  
    // =========== 查询数据 =============
  
    //布料列表数据
    const clothList = ref([]);
    async function query() {
      try {
        let resp = await itemClothApi.queryPage({
          pageNum: 1,
          pageSize: 500, // 获取足够多的数据
        });
        // 布料列表数据对象
        clothList.value = resp.data.list || [];
      } catch (e) {
        smartSentry.captureError(e);
      }
    }
    onMounted(query);
  
    // =========== 选择 监听、事件 =============
    
    const selectValue = ref(props.value);
    watch(
      () => props.value,
      (newValue) => {
        selectValue.value = newValue;
      }
    );
    
    // 监听布料列表和选中值 
    function onChange(value) {
      //查找布料id对应的布料对象
      const selectedItem = clothList.value.find(item => item.id === value);
      emit('update:value', value);
      // 触发 change 事件，传入选中的布料对象
      emit('change', selectedItem);
    }

  </script>

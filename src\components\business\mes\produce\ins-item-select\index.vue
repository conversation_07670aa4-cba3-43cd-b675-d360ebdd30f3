<!--
  * 指令单编号-物料编号联动显示
  * 无须传入任何值
  * 下拉框数值发生改变之后返回一个包含指令单编号、物料编号、指令单id的对象
  * 回传指令单id是方便根据id去联动获取其他属性，比如颜色，尺寸等
  * @Author:    linwj
  * @Date:      2024-10-15
-->
<template>
    <a-form-item label="指令单编号" class="smart-query-form-item">
      <a-select
          v-model:value="initInsOrItem"
          show-search
          placeholder="指令单编号"
          style="width: 170px"
          :options="insNoOptions"
          :filter-option="filterOption"
          @change="handleInsChange"
      />
    </a-form-item>
    <a-form-item label="物料编号" class="smart-query-form-item">
      <a-select
          v-model:value="initInsOrItem"
          show-search
          placeholder="物料编号"
          style="width: 170px"
          :options="itemNoOptions"
          :filter-option="filterOption"
          @change="handleItemChange"
      />
    </a-form-item>
</template>

<script setup>
import {onMounted, reactive, ref} from "vue";
import {produceInstructOrderApi} from "/src/api/business/mes/produce/produce-instruct-order-api.js";
import {smartSentry} from "/src/lib/smart-sentry.js";
const emits = defineEmits(['change'])

// ---------------------------- 查询数据表单和方法 ----------------------------
const queryFormState = {
  itemNumber: undefined, //物料编号
  instructOrderNumber: undefined, //指令单编号
  instructOrderId: undefined //指令单id
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });

// ---------------------------- 获取指令单编号和物料编号 ----------------------------
const tableLoading = ref(false);
//指令单编号和物料编号共同的value属性
const initInsOrItem = ref(undefined)
const insAndItemNoData = ref([])
//指令单编号options
const insNoOptions = ref([])
//物料编号options
const itemNoOptions = ref([])
async function queryInsOrItem(){
  tableLoading.value = true;
  try {
    let res = await produceInstructOrderApi.queryInsOrItem();
    insAndItemNoData.value = res.data
    outstandingInsNumberOptions(res.data)
    outstandingItemNumberOptions(res.data)
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false
  }
}
// ---------------------------- 输出指令单编号和物料编号options ----------------------------
function outstandingInsNumberOptions(insAndItemNumber){
  insNoOptions.value = insAndItemNumber.map((e)=>{
    return {
      label:e.instructNumber,
      value:e.id
    }
  })
}

function outstandingItemNumberOptions(insAndItemNumber){
  itemNoOptions.value = insAndItemNumber.map((e)=>{
    return {
      label:e.itemNumber,
      value:e.id
    }
  })
}


// ---------------------------- 处理指令单编号和物料编号Change ----------------------------

//两个编号动态筛选
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

function handleInsChange(value,options){
  queryForm.instructOrderNumber = options.label
  queryForm.instructOrderId = initInsOrItem.value
  // 物料编号赋值
  queryForm.itemNumber = itemNoOptions.value.find((e)=>{
    return e.value === value
  }).label
  //回传的值
  emits('change',queryForm)
}

function handleItemChange(value,options){
  queryForm.itemNumber = options.label
  queryForm.instructOrderId = initInsOrItem.value
  // 指令单编号赋值
  queryForm.instructOrderNumber = insNoOptions.value.find((e)=>{
    return e.value === value
  }).label
  //回传的值
  emits('change',queryForm)
}

// ---------------------------- 负责清空两个编号显示 ----------------------------
function clearAllNumber(){
  initInsOrItem.value = undefined
}

defineExpose({
  clearAllNumber,
});

onMounted(
  queryInsOrItem
)
</script>

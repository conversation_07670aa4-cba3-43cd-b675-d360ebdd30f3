/**
 * 设备列表 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-09 20:12:13
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/src/lib/axios';

export const equipmentApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/equipment/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/equipment/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/equipment/update', param);
  },


  /**
   * 查看设备详情  <AUTHOR>
   */
  getById: (id) => {
      return getRequest(`/equipment/byId/${id}`);
  },

    /**
     * 删除  <AUTHOR>
     */
    delete: (id) => {
        return getRequest(`/equipment/delete/${id}`);
    },
    queryList: (param) => {
        return postRequest('/equipment/queryList', param);
    },
};

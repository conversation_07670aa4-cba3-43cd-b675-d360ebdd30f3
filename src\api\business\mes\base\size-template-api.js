/**
 * 尺寸模板表 api 封装
 *
 * @Author:    hzx
 * @Date:      2024-07-05 15:45:35
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const sizeTemplateApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/sizeTemplate/queryPage', param);
  },
  // 下拉查询
  queryAll:(param) => {
  return postRequest('/sizeTemplate/sizeInfo', param);
},
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/sizeTemplate/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/sizeTemplate/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/sizeTemplate/delete/${id}`);
  },


  /**
   * 删除  <AUTHOR>
   */
  querySizeAndTemplateInfo: (param) => {
    return postRequest(`/sizeTemplate/sizeInfoWithTemplate`,param);
  },

};

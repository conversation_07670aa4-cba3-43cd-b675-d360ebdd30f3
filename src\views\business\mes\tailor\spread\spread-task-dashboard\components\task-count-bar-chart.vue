<template>
  <a-card>
    <div style="display: flex; justify-content: space-between">
      <div style="font-size: 16px">每日铺布任务数量</div>
      <a-radio-group v-model:value="timeOption" @change="timeChange" :options="timeOptions" option-type="button" />
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="bar-main-1"></div>
    </div>
  </a-card>
</template>
<script setup>
  import { onMounted, ref } from 'vue';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs';
  import { spreadTaskDashboardApi } from '/@/api/business/mes/tailor/spread-task-dashboard-api';

  let myChart = null;
  onMounted(() => {
    initChart();
    queryTrendData();
  });

  // 时间选择选项
  const timeOptions = [
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' },
    { label: '本季', value: 'quarter' },
    { label: '本年', value: 'year' },
  ];

  const timeOption = ref('week');
  const loading = ref(false);
  const chartData = ref({
    dates: [],
    counts: [],
  });

  // 根据选项获取日期范围
  function getDateRangeByOption(optionValue) {
    const today = dayjs();
    switch (optionValue) {
      case 'week': // 本周
        return {
          begin: today.startOf('week').format('YYYY-MM-DD'),
          end: today.endOf('week').format('YYYY-MM-DD'),
        };
      case 'month': // 本月
        return {
          begin: today.startOf('month').format('YYYY-MM-DD'),
          end: today.endOf('month').format('YYYY-MM-DD'),
        };
      case 'quarter': // 本季
        return {
          begin: today.startOf('quarter').format('YYYY-MM-DD'),
          end: today.endOf('quarter').format('YYYY-MM-DD'),
        };
      case 'year': // 本年
        return {
          begin: today.startOf('year').format('YYYY-MM-DD'),
          end: today.endOf('year').format('YYYY-MM-DD'),
        };
      default:
        return {
          begin: today.startOf('week').format('YYYY-MM-DD'),
          end: today.endOf('week').format('YYYY-MM-DD'),
        };
    }
  }

  // 获取趋势数据
  async function queryTrendData() {
    loading.value = true;
    try {
      const dateRange = getDateRangeByOption(timeOption.value);
      const params = {
        planBeginTimeBegin: dateRange.begin,
        planBeginTimeEnd: dateRange.end,
      };

      const res = await spreadTaskDashboardApi.countTrend(params);
      if (res && res.data) {
        processChartData(res.data);
      }
    } catch (e) {
      console.error('获取铺布任务数量趋势数据失败:', e);
      chartData.value = { dates: [], counts: [] };
    } finally {
      loading.value = false;
      updateChart();
    }
  }

  // 处理图表数据
  function processChartData(data) {
    const dates = [];
    const counts = [];

    if (Array.isArray(data)) {
      data.forEach((item) => {
        dates.push(dayjs(item.x).format('MM/DD'));
        counts.push(item.y);
      });
    }

    chartData.value = { dates, counts };
  }

  // 初始化图表
  function initChart() {
    const chartDom = document.getElementById('bar-main-1');
    if (chartDom) {
      myChart = echarts.init(chartDom);

      window.addEventListener('resize', () => {
        myChart && myChart.resize();
      });
    }
  }

  // 更新图表数据
  function updateChart() {
    if (!myChart) return;

    const option = {
      xAxis: {
        type: 'category',
        data: chartData.value.dates,
        axisLabel: {
          interval: chartData.value.dates.length > 30 ? 'auto' : 0,
          rotate: chartData.value.dates.length > 7 ? 45 : 0,
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          const dataIndex = params[0].dataIndex;
          const date = chartData.value.dates[dataIndex];
          const count = chartData.value.counts[dataIndex];
          return `日期: ${date}<br/>任务数量: ${count}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
      },
      series: [
        {
          name: '铺布任务数量',
          data: chartData.value.counts,
          type: 'bar',
          barWidth: chartData.value.dates.length > 60 ? '80%' : '60%',
          itemStyle: {
            color: '#004bf9',
          },
          label: {
            show: chartData.value.dates.length < 30,
            position: 'top',
          },
        },
      ],
    };

    myChart.setOption(option);
  }

  function timeChange() {
    queryTrendData();
  }
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .chart-main {
      width: 100%;
      height: 200px;
      background: #fff;
    }
  }
</style>

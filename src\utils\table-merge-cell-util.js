/**
 * 将表格中字段值相同的相邻单元格合并
 * @param {Array} dataSource 数据源数组
 * @param {Object} record 当前记录
 * @param {number} index 当前行索引
 * @param {string} fieldName 需要合并的字段名
 * @returns {Object} 包含 rowSpan 的对象
 */
export const tableMergeCell = (dataSource, record, index, fieldName) => {
    // 如果是第一行
    if (index === 0) {
        let rowSpan = 1;
        for (let i = 1; i < dataSource.length; i++) {
            if (dataSource[i][fieldName] === record[fieldName]) {
                rowSpan++;
            } else {
                break;
            }
        }
        return { rowSpan };
    }

    // 非第一行,与前一行比较
    const prevRecord = dataSource[index - 1];
    if (prevRecord[fieldName] === record[fieldName]) {
        return { rowSpan: 0 };
    }

    // 计算当前值向下连续相同的行数
    let rowSpan = 1;
    for (let i = index + 1; i < dataSource.length; i++) {
        if (dataSource[i][fieldName] === record[fieldName]) {
            rowSpan++;
        } else {
            break;
        }
    }
    return { rowSpan };
};
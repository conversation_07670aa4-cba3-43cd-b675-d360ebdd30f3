<!--
  * 其他工资项明细
  *
  * @Author:    linwj
  * @Date:      2024-12-10 11:12:45
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      title="其他项明细"
      width="400px"
      :open="visibleFlag"
      @cancel="onClose"
      @ok="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
      <!---------- 表格 begin ----------->
      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          :pagination="false"
          bordered
      >
          <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'fieldName'">
                  {{ record.fieldName+`&nbsp;&nbsp;(${record.type === '0'?'加项':'减项'})`}}
              </template>
          </template>
      </a-table>

  </a-modal>
</template>
<script setup>
import {reactive, ref, onMounted} from 'vue';
import {payoffApi} from "/@/api/business/mes/salary/payoff-api.js";
import {PAGE_SIZE_OPTIONS} from '/@/constants/common-const';
import {smartSentry} from '/@/lib/smart-sentry';
import _ from "lodash";
import {payoffRecordApi} from "/@/api/business/mes/salary/payoff-record-api.js";

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);
function show(rowData) {
  tableData.value = rowData.otherAmounts
  visibleFlag.value = true;
}

function onClose() {
  visibleFlag.value = false;
}
// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '工资项',
    dataIndex: 'fieldName',
    ellipsis: true,
    width: 80

  },
  {
    title: '金额',
    dataIndex: 'fieldValue',
    ellipsis: true,
    width: 80

  },
]);
// ---------------------------- 查询数据表单和方法 ----------------------------
// 表格数据
const tableData = ref([]);

defineExpose({
  show,
})
</script>

/**
 * ��Ƭ��ת�� api ��װ
 *
 * @Author:    pxz
 * @Date:      2024-10-06 16:49
 * @Copyright  zscbdic
 */

import { postRequest, getRequest } from '/@/lib/axios';

export const partStationTurnBoxApi = {
    /**
     * ��ҳ��ѯ  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/partStationTurnBox/queryPage', param);
    },
    /**
     * ���  <AUTHOR>
     */
    add: (param) => {
        return postRequest('/partStationTurnBox/add', param);
    },
    /**
     * �������  <AUTHOR>
     */
    batchAdd: (param) => {
        return postRequest('/partStationTurnBox/batchAdd', param);
    },
    /**
     * �޸�  <AUTHOR>
     */
    update: (param) => {
        return postRequest('/partStationTurnBox/update', param);
    },
    /**
     * ����ɾ��  <AUTHOR>
     */
    delete: (id) => {
        return getRequest(`/partStationTurnBox/delete/${id}`);
    },
    /**
     * ����ɾ��  <AUTHOR>
     */
    batchDelete: (ids) => {
        return postRequest(`/partStationTurnBox/batchDelete`, ids);
    },
    /**
     * ��ȡ��ת���ά��base64  <AUTHOR>
     */
    getQRURL: (id) => {
        return getRequest(`/partStationTurnBox/getQrCode/${id}`);
    },


    queryById(id) {
        return getRequest(`/partStationTurnBox/getById/${id}`);
    }
}

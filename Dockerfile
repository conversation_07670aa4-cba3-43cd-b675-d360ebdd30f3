# 设置基础镜像
#FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nginx:1.27.0
## 定义作者
#MAINTAINER cjm
#
##COPY /nginx/nginx.conf /etc/nginx/nginx.conf
#
## 将dist文件中的内容复制到 /usr/share/nginx/html/ 这个目录下面
#COPY dist/  /usr/share/nginx/html/
#----------------------------------------------

#声明变量
#ARG NODE_ENV=docker
#打印
#RUN echo "ARG ENV = ${NODE_ENV}"

#
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/node:18-alpine as builder

WORKDIR /app

# 确保路径正确
COPY package-lock.json .
COPY package.json .

RUN npm install --registry=https://mirrors.cloud.tencent.com/npm --legacy-peer-deps

# 复制项目文件
COPY . .

#选择构建环境
RUN npm run build:localhost


# 打包项目
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/nginx:1.27.0

# Nginx config
COPY nginx/nginx.conf /etc/nginx/nginx.conf

COPY --from=builder /app/dist /usr/share/nginx/html/

# 复制企业微信认证文件
#COPY nginx/WW_verify_z2Zffeoe0j3bVAY6.txt /usr/share/nginx/html/

EXPOSE 80


#CMD ["nginx", "-g", "daemon off;"]

<!--
  * 生产指令单
  *
  * @Author:    wxx
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!-- 指令单编号和物料编号组件-->
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="生产类型" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.produceType"
          placeholder="生产类型"
          style="width: 100px"
          :options="Object.values(PRODUCE_TYPE_ENUM).slice(0, -1)"
          :allowClear="true"
        />
      </a-form-item>
      <a-form-item label="生产业务状态" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.produceStatus"
          placeholder="生产业务状态"
          style="width: 150px"
          :options="Object.values(PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM).slice(0, -1)"
          :allowClear="true"
        />
      </a-form-item>
      <a-form-item label="优先级" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.priority"
          placeholder="优先级"
          style="width: 100px"
          :options="Object.values(PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM).slice(0, -1)"
          :allowClear="true"
        />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 列表/卡片视图切换 -->
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="tableList" tab="列表视图">
        <TableList
          :query-form="queryForm"
          v-model:table-loading="tableLoading"
          :query-data="queryData"
          :table-data="tableData"
          :total="total"
          @refresh="queryData"
        />
      </a-tab-pane>
      <a-tab-pane key="cardList" tab="卡片视图">
        <CardList
          :query-form="queryForm"
          v-model:table-loading="tableLoading"
          :query-data="queryData"
          :table-data="tableData"
          :total="total"
          @refresh="queryData"
        />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableList from './components/table-list.vue';
  import CardList from './components/card-list.vue';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
    PRODUCE_TYPE_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
  import { itemApi } from '/@/api/business/mes/item/item-api.js';

  // tab栏切换
  const activeKey = ref('tableList');

  // ---------------------------- 处理搜索栏 ----------------------------
  //两个编号下拉组件引用
  const insItemSelectRef = ref();
  //两个编号回传
  function insItemChange(data) {
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
  }

  // ---------------------------- 查询数据表单和方法 ----------------------------
  const queryFormState = {
    instructNumber: undefined, //指令单编号
    itemNumber: undefined, //物料编号
    produceType: undefined, //生产类型;0自产,1自裁委外，2整件委外
    produceStatus: undefined, //生产业务状态;0计划，1下达，2开工，3完工
    priority: undefined, //优先级;0一般,1紧急,2非常紧急
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }], //排序字段及顺序
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    insItemSelectRef.value.clearAllNumber();
    queryForm.pageSize = pageSize;
    queryData();
  }

  //请求分页查询
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await produceInstructOrderApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(() => {
    queryData();
  });
</script>

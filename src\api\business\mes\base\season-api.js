/**
 * 季度表 api 封装
 *
 * @Author:    lwt
 * @Date:      2024-07-05 15:45:34
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const seasonApi = {

  /**
   * 分页查询  <AUTHOR>
   */
 queryPage : (param) => {
    return postRequest('/season/queryTree', param);
  },

    // 添加树表查询
  queryTree:(param) => {
    return postRequest('/season/queryTree', param);
  },
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/season/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/season/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/season/delete/${id}`);
  },

};

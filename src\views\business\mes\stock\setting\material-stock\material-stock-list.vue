<!--
  * 物料库存属性
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:18:19
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="物料名称查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materielName" placeholder="关键字查询" />
            </a-form-item>
            <a-form-item label="物料spu编号查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materielSpuNumber" placeholder="物料spu编号查询" />
            </a-form-item>
            <a-form-item label="物料sku编号查询" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materielSkuNumber" placeholder="物料sku编号查询" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格操作行 begin ----------->
        <a-row class="smart-table-btn-block">
            <div class="smart-table-operate-block">
                <a-button @click="showForm" type="primary" size="small">
                    <template #icon>
                        <PlusOutlined />
                    </template>
                    新建
                </a-button>
            </div>
            <div class="smart-table-setting-block">
                <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
            </div>
        </a-row>
        <!---------- 表格操作行 end ----------->

        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"

        >
            <template #bodyCell="{ text, record, column }">
                <template v-if="column.dataIndex === 'lotManageFlag'">
                    <a-tag v-if="record.lotManageFlag === true" color="green">启用</a-tag>
                    <a-tag v-else color="red">停用</a-tag>
                </template>
                <template v-if="column.dataIndex === 'action'">
                    <div class="smart-table-operate">
                        <a-button @click="showForm(record)" type="link">编辑</a-button>
                        <!-- <a-button @click="onDelete(record)" danger type="link">删除</a-button> -->
                    </div>
                </template>
            </template>
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>

        <StkMaterialStockForm  ref="formRef" @reloadList="queryData"/>

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { stkMaterialStockApi } from '/@/api/business/mes/stock/stk-material-stock-api';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import StkMaterialStockForm from './material-stock-form.vue';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
        // {
        //     title: '物料id',
        //     dataIndex: 'materialId',
        //     ellipsis: true,
        // },
        {
            title: '物料名称',
            dataIndex: 'materielName',
            ellipsis: true,
        },
        {
            title: '物料型号',
            dataIndex: 'materielModel',
            ellipsis: true,
        },
        {
            title: '物料spu编号',
            dataIndex: 'materielSpuNumber',
            ellipsis: true,
        },
        {
            title: '物料sku编号',
            dataIndex: 'materielSkuNumber',
            ellipsis: true,
        },
        // {
        //     title: '作用范围',
        //     dataIndex: 'scope',
        //     ellipsis: true,
        // },
        {
            title: '最小库存',
            dataIndex: 'minStock',
            ellipsis: true,
        },
        {
            title: '最大库存',
            dataIndex: 'maxStock',
            ellipsis: true,
        },
        {
            title: '安全库存',
            dataIndex: 'safeStock',
            ellipsis: true,
        },
        {
            title: '批号管理',
            dataIndex: 'lotManageFlag',
            ellipsis: true,
        },
        {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            width: 90,
        },
    ]);

    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        pageNum: 1,
        pageSize: 10,
        materielName: '', //物料名称
        materielSpuNumber: '', //物料spu编号
        materielSkuNumber: '', //物料sku编号

    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await stkMaterialStockApi.queryPage(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }


    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }
</script>

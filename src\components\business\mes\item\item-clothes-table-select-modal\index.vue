<template>
  <a-modal v-model:open="visible" :width="900" title="选择成衣" @cancel="closeModal" @ok="onSelectItem">
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字查询" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="params.queryKey" placeholder="关键字查询" />
        </a-form-item>
        <a-form-item label="停用标识" class="smart-query-form-item">
          <a-select v-model:value="params.enableFlag" placeholder="请选择">
            <a-select-option value="false">启用</a-select-option>
            <a-select-option value="true">停用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <a-table
      :row-selection="{ selectedRowKeys: [selectedRowKey], onChange: onSelectChange, type: 'radio' }"
      :loading="tableLoading"
      size="small"
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      bordered
      rowKey="id"
      :scroll="{ y: 300, x: 1300 }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showEditForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-preview-container">
            <file-preview :file-list="record.imgUrl || []" type="picture" :width="35" />
          </div>
        </template>
        <!--  停用标识      -->
        <template v-else-if="column.dataIndex === 'enableFlag'">
          <a-tag :color="text === true ? 'red' : 'green'">{{ text === true ? '停用' : '启用' }}</a-tag>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="params.pageSize"
        v-model:current="params.pageNum"
        v-model:pageSize="params.pageSize"
        :total="total"
        @change="queryItem"
        @showSizeChange="queryItem"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
  import { message } from 'ant-design-vue';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { itemClothesApi } from '/@/api/business/mes/item/item-clothes.js';
  import FilePreview from '/@/components/support/file-preview/index.vue';

  const emits = defineEmits(['selectData']);
  defineExpose({
    showModal,
  });

  const props = defineProps({
    attribute: String,
    attributeList: Array,
  });
  const visible = ref(false);
  const selectedRowKey = ref(null);

  const tableLoading = ref(false);
  const total = ref();

  let defaultParams = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    attribute: ITEM_ATTRIBUTE_ENUM.CLOTHES.value,
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  const params = reactive({ ...defaultParams });

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      align: 'center',
      width: 50,
    },
    {
      title: '物料编号',
      dataIndex: 'number',
      ellipsis: true,
      width: 140,
      align: 'center',
    },
    {
      title: '物料SKU编号',
      dataIndex: 'skuNumber',
      ellipsis: true,
      width: 150,
      align: 'center',
    },
    {
      title: '物料名称',
      dataIndex: 'name',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      ellipsis: true,
      width: 200,
      align: 'center',
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      ellipsis: true,
      // width:50,
      align: 'center',
    },
    {
      title: '价格',
      dataIndex: 'price',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '停用标识',
      dataIndex: 'enableFlag',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      align: 'center',
    },
  ]);

  let tableData = ref([]);

  async function showModal() {
    selectedRowKey.value = null;
    visible.value = true;
    onSearch();
  }

  function closeModal() {
    Object.assign(params, defaultParams);
    selectedRowKey.value = null;
    visible.value = false;
  }

  function onSearch() {
    params.pageNum = 1;
    queryItem();
  }

  function reset() {
    Object.assign(params, defaultParams);
    queryItem();
  }

  //----------------------------

  async function queryItem() {
    tableLoading.value = true;
    try {
      let res = await itemClothesApi.queryPage(params);
      tableData.value = res.data.list;
      // console.log("组件1311：", tableData.value)
      total.value = res.data.total;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      tableLoading.value = false;
    }
  }

  function onSelectChange(selectedRowKeys) {
    if (selectedRowKeys.length > 0) {
      selectedRowKey.value = selectedRowKeys[0];
    } else {
      selectedRowKey.value = null;
    }
  }

  function onSelectItem() {
    if (!selectedRowKey.value) {
      message.warning('请选择物料');
      return;
    }
    const selectedItem = tableData.value.find((item) => item.id === selectedRowKey.value);
    emits('selectData', selectedItem);
    closeModal();
  }
</script>
<style scoped>
  .image-preview-container {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }

  .image-preview-container :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
</style>

<!--
  * 款式尺码多选组件
  * 返回所选尺码名称
  * @Author:    fkf
  * @Date:      2024-1-17
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="'选择尺寸'"
      width="600px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="尺寸查询" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.sizeMessage" placeholder="尺寸查询"/>
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="queryData">
            <template #icon>
              <SearchOutlined/>
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined/>
            </template>
            重置
          </a-button>
          <a-button @click="showForm" class="smart-margin-left10">
            <PlusOutlined />
            添加
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

      <div style="margin-bottom: 10px">
        <span style="margin-left: 8px">
        <template v-if="hasSelected">
          {{ `已选 ${selectedRowKeyList.length} 项` }}
        </template>
      </span>
      </div>

      <a-table
          :columns="columns"
          :data-source="tableData"
          row-key="id"
          bordered
          :row-selection="{ selectedRowKeys: selectedRowKeyList,
          onChange: onSelectChange }"
          :pagination="{
            defaultPageSize:pageForm.pageSize, //默认条数
            defaultCurrent:pageForm.pageNum, //默认页码
            pageSizeOptions:PAGE_SIZE_OPTIONS, //每页可展示条数
            showSizeChanger:true, //配合上面的属性，页数切换器
            showQuickJumper:true, //页码跳转
            showLessItems:true,
            total:total,
            showTotal:()=> `共 ${total} 条`,
            onChange:handlePageChange,

          }"
      />
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onConfirm">确定</a-button>
      </a-space>
    </template>
  </a-modal>

  <StyleSizerForm ref="formRef" @reloadList="queryData"/>

</template>
<script setup>
import {reactive, ref, onMounted, computed} from 'vue';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {sizeApi} from '/@/api/business/mes/base/size-api.js';
import {smartSentry} from '/@/lib/smart-sentry';
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
import StyleSizerForm from "/@/views/business/mes/base/size/size-form.vue";

const formRef = ref();

function showForm(data) {
  formRef.value.show(data);
}
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);
function show() {
  visibleFlag.value = true;
}

function onClose() {
  visibleFlag.value = false;
}

// ------------------------ 分页默认信息 ------------------------
const pageInfo = {
  pageSize: 5,
  pageNum: 1,
};
let pageForm = reactive({...pageInfo})

function handlePageChange(pageNum, pageSize){
  pageForm.pageNum = pageNum
  pageForm.pageSize = pageSize
}


// 点击确定
async function onConfirm() {
  SmartLoading.show();
  try {
    // 传送数据
    emits('reloadList',selectedSizeList);
    message.success("选择成功")
    // 清空多选列表数据
    selectedSizeList.value = []
    selectedRowKeyList.value = []
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

const columns = ref([
  {
    title: '款式尺寸',
    dataIndex: 'sizeMessage',
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    ellipsis: true,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  sizeMessage: undefined, //关键字查询
};
// 查询表单form
const queryForm = reactive({...queryFormState});

// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}
// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await sizeApi.queryAll(queryForm);
    tableData.value = queryResult.data;
    total.value = tableData.value.length;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}
// ---------------------------------------------------------------------------
//当所选条数大于0时方可展示
const hasSelected = computed(() => selectedRowKeyList.value.length > 0);

// 多选表格行
const selectedRowKeyList = ref([]);
const selectedSizeList = ref([]);

function onSelectChange(selectedRowKeys,selectedRows) {
  selectedRowKeyList.value = selectedRowKeys;
  selectedSizeList.value = selectedRows.map((e)=>{
    return e.sizeMessage
  })
}


onMounted(queryData);
defineExpose({
  show,
});
</script>
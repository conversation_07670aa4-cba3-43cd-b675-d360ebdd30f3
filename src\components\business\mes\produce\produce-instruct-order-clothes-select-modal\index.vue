<!--
  * 生产成衣列表选择弹窗
  * 
  * @Author:      pxz
  * @Date:      2025-02-14
  * @Copyright  zscbdic
-->
<template>
  <a-modal
    v-model:open="visible"
    title="生产成衣列表"
    width="1300px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字"/>
        </a-form-item>
        <a-form-item label="订单编号" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.orderNumber" placeholder="订单编号"/>
        </a-form-item>
        <a-form-item label="物料名称" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.itemName" placeholder="物料名称"/>
        </a-form-item>
        <a-form-item label="物料SKU编号" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.itemSkuNumber" placeholder="物料SKU编号"/>
        </a-form-item>
        <a-form-item label="物料SPU编号" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.itemSpuNumber" placeholder="物料SPU编号"/>
        </a-form-item>
        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined/>
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined/>
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, preserveSelectedRowKeys: true }"
      :scroll="{ x: 1500 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'orderProduceStatus'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(record.orderProduceStatus).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(record.orderProduceStatus).desc }}
          </a-tag>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { PAGE_SIZE_OPTIONS, PAGE_SIZE } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api';
import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const';
import { PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const';
import { tableMergeCell } from '/@/utils/table-merge-cell-util';
import _ from 'lodash';

// 表格列定义
const columns = [
  {
    title: '生产指令单编号',
    dataIndex: 'orderNumber',
    ellipsis: true,
    align: 'center',
    customCell: (record, index) => tableMergeCell(tableData.value, record, index, 'orderNumber')
  },
  {
    title: '生产状态',
    dataIndex: 'orderProduceStatus',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料名称',
    dataIndex: 'itemName',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料SPU编号',
    dataIndex: 'itemNumber',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '物料SKU编号',
    dataIndex: 'itemSkuNumber',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '数量',
    dataIndex: 'num',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    ellipsis: true,
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    ellipsis: true,
    align: 'center',
  }
];

// 查询表单数据
const queryFormState = {
  pageNum: 1,
  pageSize: PAGE_SIZE,
  queryKey: undefined,
  orderNumber: undefined,
  itemName: undefined,
  itemSkuNumber: undefined,
  itemSpuNumber: undefined,
  sortItemList: [{ column: 'create_time', isAsc: false }],
};

const visible = ref(false);
const queryForm = reactive({ ...queryFormState });
const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    const res = await produceInstructOrderClothesApi.queryPage(queryForm);
    tableData.value = res.data.list;

    total.value = res.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

// 选择变化的处理函数
const onSelectChange = (keys, rows) => {
  selectedRows.value = rows;
  selectedRowKeys.value = keys;
  console.log(selectedRowKeys.value,selectedRows.value)
};

const emit = defineEmits(['selectValue']);

function handleOk() {
  visible.value = false;
  if(_.size(selectedRows.value)){
    emit('selectValue', {
      selectedRows: selectedRows.value,
      billType: BillTypeEnum.PRODUCE_INSTRUCT_ORDER_CLOTHES.value,
      orderBillType: BillTypeEnum.PRODUCE_INSTRUCT_ORDER.value,
    });
  }
}

function handleCancel() {
  visible.value = false;
  reset();
}

// 重置查询     
function reset(){
  Object.assign(queryForm, queryFormState);
  selectedRows.value = [];
  selectedRowKeys.value = [];
  console.log(selectedRowKeys.value,selectedRows.value)
  queryData();
}

// 查询
function onSearch(){
  queryForm.pageNum = 1;
  queryData();
}

// 打开弹窗
function open() {
  visible.value = true;
  queryData();
}

defineExpose({
  open,
});
</script>
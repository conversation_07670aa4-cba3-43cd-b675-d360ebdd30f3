<template>
  <a-card  hoverable="true" >
    <template #title>
      <a-row :gutter="[8,16]">
        <a-col>
          <div style="font-size: 17px;">编菲详情</div>
        </a-col>
        <a-col style="display: flex;justify-content: center;align-content: center">
          <ScissorOutlined :style="{ fontSize: '20px' }"/>
        </a-col>
      </a-row>
    </template>
    <!--  --------------------------提交保存--------------------------->
    <template #avatar>
      <setting-outlined key="setting" />

    </template>
    <template #extra>
      <a-button style="margin-right: 20px" @click="addFeTicket" :disabled="isDisable">下载菲票</a-button>
      <a-button type="primary" @click="onSubmit">生成菲票</a-button>
    </template>
    <!--  --------------------------头部基本信息---------------------------->
    <!--  第一行-->
    <a-form  :model="cutBedSheetForm" :rules="rules" :label-col="{ span: 5 }" >
      <a-row :gutter="[16,16]">
        <a-col :span="6">
          <a-form-item
              label="裁床单编号"
              :label-col="labelCol"
          >
            <a-input v-model:value="cutBedSheetForm.cutNumber" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="裁床单名称"
              :label-col="labelCol"
          >
            <a-input v-model:value="cutBedSheetForm.cutSheetName" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="生产指令单编号"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.instructOrderNumber" disabled/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="生产指令单名称"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.instructOrderName" disabled/>
          </a-form-item>
        </a-col>
      </a-row>
      <!--  第二行-->
      <a-row :gutter="[16,16]">
        <a-col :span="6">
          <a-form-item
              label="物料编号"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.itemNumber" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="物料名称"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.itemName" disabled/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="规格型号"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.model" disabled/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="物料单位名称"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.unitName" disabled />
          </a-form-item>
        </a-col>
      </a-row>
      <!--  第三行-->
      <a-row :gutter="[16,16]">
        <a-col :span="6">
          <a-form-item
              label="属性"
          >
            <!--          <a-input v-model:value="cutBedSheetForm.attribute" disabled/>-->
            <a-radio-group v-model:value="cutBedSheetForm.attribute" button-style="solid" disabled>
              <a-radio :value="'0'">布料</a-radio>
              <a-radio  :value="'2'">成衣</a-radio>
              <a-radio :value="'1'">其他</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="物料类型"
          >
            <!--          <a-input v-model:value="cutBedSheetForm.category" disabled/>-->
            <a-radio-group v-model:value="cutBedSheetForm.category"  style="width: 90%" disabled>
              <a-radio :value="'0'" checked>半成品</a-radio>
              <a-radio :value="'1'">成品</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="床次"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.cutNum" disabled/>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
              label="裁剪人"
              :label-col="labelCol"

          >
            <a-input v-model:value="cutBedSheetForm.cutter" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-button @click="showForm" type="primary">
      <template #icon>
        <SearchOutlined/>
      </template>
      选择裁床单
    </a-button>


    <!--  --------------------------中间信息---------------------------->
    <br/>
    <br/>

    <a-row style="height:50vh;" :key="changeKey">
      <a-col :span="3">
        <a-row>
          <a-col  :span="24">
            <a-list size="large" bordered :data-source="colorData" style="height: 50vh;overflow-x: hidden;overflow-y: auto;" >
              <template #renderItem="{ item,index }">
                <a-list-item style="display: flex;justify-content: center;align-content: center;">
                  <a-badge :count="item.clicknum" :offset="[-10,0]" showZero >
                    <a-tag
                        :color="chooseColor[index%chooseColor.length]"
                        style=" border-radius: 10px;font-size: 17px; width: 7vw; height:25px;display: flex;justify-content: center;align-content: center;"
                        @click="chooseCurrentTag(0,item,index)"
                    >
                      {{item.value}}
                    </a-tag>
                  </a-badge>
                </a-list-item>
              </template>
              <template #header>
                <div
                    style="
                  font-size: 15px;
                  display: flex;
                  justify-content: center;
                  align-content: center;">
                  <strong>颜色</strong>
                </div>
              </template>
            </a-list>
          </a-col>
        </a-row>

      </a-col>
      <a-col style="" :span="17">
        <a-row>
          <a-col :span="24">
            <a-table

                size="small"
                :dataSource="form"
                :columns="columns"
                rowKey="id"
                :scroll="{y: 500,x:1400}"
                bordered
                style="height: 90%"
                :pagination="false"
            >
              <template #headerCell="{ column }">
                <!--              这里修改-->
                <template v-if="dataIndexList.some(item=>item.includes(column.dataIndex))">
                  <CloseCircleTwoTone two-tone-color="#eb2f96" @click="deleteColumn(column)"/>
                  {{column.title}}
                </template>
              </template>
              <template #bodyCell="{ record, column ,index}">
                <!--              这里修改-->
                <template v-if="dataIndexList.some(item=>item.includes(column.dataIndex))">
                  <a-input
                      placeholder="请输入数量"
                      v-model:value="form[index][column.dataIndex]"
                      style="width: 40%"
                  />
                  <span style="margin-left: 5px">件</span>
                </template>
                <template v-if="column.dataIndex === 'option'">
                  <div class="smart-table-operate">
                    <a-button @click="onDelete(record)" danger type="link">删除</a-button>
                  </div>
                </template>
              </template>
            </a-table>

          </a-col>
        </a-row>

      </a-col>
      <a-col :span="4">
        <a-row>
          <a-col  :span="24">
            <a-list size="large" bordered :data-source="sizeData" style="height: 50vh;overflow-x: hidden;overflow-y: auto;" >
              <template #renderItem="{ item,index }">
                <a-list-item style="display: flex;justify-content: center;align-content: center;">
                  <a-tag
                      :color="chooseColor[(index+3)%chooseColor.length]"
                      style="border-radius: 10px;font-size: 15px; width: 20vw;display: flex;justify-content: center;align-content: center"
                      @click="chooseCurrentTag(1,item,index)"
                  >
                    {{item}}
                  </a-tag>
                </a-list-item>
              </template>
              <template #header>
                <div
                    style="
                  font-size: 15px;
                  display: flex;
                  justify-content: center;
                  align-content: center;">
                  <strong>尺寸</strong>
                </div>
              </template>
            </a-list>
          </a-col>
        </a-row>
      </a-col>
    </a-row>
  </a-card>

  <!--  抽屉-->
  <a-drawer
      title="下载菲票内容"
      :width="500"
      :open="open"
      :body-style="{ paddingBottom: '80px' }"
      :footer-style="{ textAlign: 'right' }"
      @close="onClose"
  >
    <a-form :model="tickForm" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="模板" >
            <a-select
                ref="select"
                placeholder="请输入模板"
                v-model:value="tickForm.template"
                :options="$smartEnumPlugin.getValueDescList('Template')"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="部位" name="name">
            <a-input ref="inputRef" v-model:value="inputValue" placeholder="回车输入部位" @pressEnter="pressEnter"/>
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 添加部位标签 -->
      <a-row :gutter="16">
        <a-col :span="24" >
          <a-tag  v-for="item in partsArr" :key="item" :bordered="false" closable @close="closeTag(item)">{{item}}</a-tag>
        </a-col>
      </a-row>

    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="downloadTicket">下载菲票</a-button>
      </a-space>
    </template>
  </a-drawer>
  <cut-bead-sheet-table-select-model ref="formRef" @select-data="selectData" />
</template>
<script setup>
import {onMounted, ref, watch, h} from 'vue'
import ProduceInstructOrderTableSelectModal
  from "/@/components/business/mes/produce/produce-instruct-order-table-select-modal/index.vue"
import {smartSentry} from "/@/lib/smart-sentry.js";
import {message, Modal} from 'ant-design-vue';
import {produceInstructOrderClothesApi} from "/@/api/business/mes/produce/produce-instruct-order-clothes-api.js"
import {produceInstructOrderApi} from "/@/api/business/mes/produce/produce-instruct-order-api.js";
import {cutBedSheetApi} from "/@/api/business/mes/tailor/cut-bed-sheet-api.js"
import {Input} from "ant-design-vue";
import {feTicketApi} from "/@/api/business/mes/tailor/fe-ticket-api.js";
import CutBeadSheetTableSelectModel from "/@/components/business/mes/tailor/cut-bead-sheet-table-select-modal/index.vue"
import {Template} from "/@/constants/business/mes/tailor/cut-bed-sheet-const.js";

//--------------------------------头部基本信息----------------------------
const cutBedSheetForm = ref({
  id: undefined,//
  cutNumber: undefined, // 裁床单编号 可编辑 可选
  cutSheetName: undefined, // 裁床单名称 可编辑 可选
  instructOrderNumber: undefined, // 生产指令单编号 不能编辑 可选
  instructOrderName: undefined, // 生产指令单名称 不能编辑 可选
  instructOrderId: undefined, // 生产指令单id 不能编辑 可选
  itemId: undefined, // 物料id 不能编辑 可选
  itemNumber: undefined, // 物料编号 不能编辑 可选
  model: undefined, // 规格型号 不能编辑 可选
  itemName: undefined, // 物料名称 不能编辑 可选
  unitId: undefined, // 物料单位id 不能编辑 可选
  unitName: undefined, // 物料单位名称 不能编辑 可选
  attribute: undefined, // 物料属性;0面料，1其他，2成衣 不能编辑 可选
  category: undefined, // 物料类型;0半成品 1成品 不能编辑 可选
  cutNum: undefined, // 床次 请求接口 不能编辑 可选
  cutterId: undefined, // 裁剪人id 可选
  cutter: undefined, // 裁剪人 可选
  details: [], // 裁床单详情 可选
});
// 表单标签布局
const labelCol=ref({
  span:9
});
// 定义校验规则
const rules = {
  cutNumber: [{required: true, message: '裁床单编号 必填'}], // 裁床单编号 可编辑 可选
  // 类型，可为字符串或 undefined，可选，0 半成品 1 成品
  category: [{required: true, message: '类型 必填'}],
  // 属性，可为字符串或 undefined，可选，0 面料，1 辅料
  attribute: [{required: true, message: '属性 必填'}],
};
// 表单列名
const columns = ref([
  {
    title: '颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    align: 'center',
    width: 50
  },
  {
    title: '操作',
    dataIndex: 'option',
    fixed: 'right',
    align: 'center',
    width: 90,
  },
]);

//
const formRef = ref()

// 触发模态框
function showForm(data) {
  formRef.value.showModal(data);
}

// 组件key
const changeKey = ref(false)
// 接收旧oldData
const oldData = ref({})
// 接受模态框数据
const selectData = (data) => {
  console.log("收取data", data)
  // 清空头部信息
  Object.keys(cutBedSheetForm.value).forEach(key => {
    cutBedSheetForm.value[key] = undefined;
  });
//   遍历cutBedSheetForm的属性
  Object.keys(cutBedSheetForm.value).forEach((item) => {
    if (item in data) {
      console.log("item",item)
      cutBedSheetForm.value[item] = data[item]
    }
  })

  // 补充赋值instructOrderId,instructOrderName,instructOrderNumber
  cutBedSheetForm.value.instructOrderId = data.instructOrderId
  cutBedSheetForm.value.instructOrderName = data.instructOrderName
  cutBedSheetForm.value.instructOrderNumber = data.instructOrderNumber
  // 清空颜色，尺寸数组
  // #bug这里有些问题
  if (oldData.value !== data) {
    console.log("old1113123131313", oldData)
    oldData.value = data
    colorData.value = []
    sizeData.value = []
    form.value = []
    cutBedSheetForm.value.details = []
    columns.value = [
      {
        title: '颜色',
        dataIndex: 'styleColor',
        ellipsis: true,
        align: 'center',
        width:50
      },
      {
        title: '操作',
        dataIndex: 'option',
        fixed: 'right',
        align: 'center',
        width:20
      },
    ]
  }

  // 组件识别id
  // changeKey.value=!changeKey.value
  console.log(cutBedSheetForm)
}

// 请求床次
async function queryProduceOrder() {
  try {
    let newBedNumData = await cutBedSheetApi.queryNewBedNum(cutBedSheetForm.value.instructOrderId)
    cutBedSheetForm.value.cutNum = newBedNumData.data
  } catch (error) {
    message.error("请求床次失败")
    smartSentry.captureError(error)
  }
}

//--------------------------------中间信息----------------------------
// 接收颜色数组
const colorData = ref([])
// 接受尺寸数组
const sizeData = ref([])

// 标签颜色数组
const chooseColor = ['pink', 'green', 'blue', 'purple', 'cyan', 'magenta', 'red', 'orange']
// 关联表单
const form = ref([])

async function queryColor() {
  try {
    let color = await produceInstructOrderClothesApi.queryClothesColorList(cutBedSheetForm.value.id)
    // 测试-
    // let id=9
    // let color=await produceInstructOrderClothesApi.queryClothesColorList(id)
    // colorData.value=color.data
    console.log(color.data)
    // 添加每个对象的点击次数
    //   colorData.value
    color.data.forEach(item => {
      colorData.value.push({
        clicknum: 0,
        value: item
      })
    })
  } catch (error) {
    message.error("请求颜色失败")
    smartSentry.captureError(error)
  }
}

async function querySize() {
  try {
    console.log(",cutBedSheetForm.value.id", cutBedSheetForm.value.id)

    let size = await produceInstructOrderClothesApi.queryClothesSizeList(cutBedSheetForm.value.id)
    sizeData.value = size.data
    // //   转换数据
    //   size.data.forEach(item=>{
    //     sizeData.value.push({
    //       isClick:false,
    //       value:item
    //     })
    //   })
  } catch (error) {
    message.error("请求尺寸失败")
    smartSentry.captureError(error)
  }
}


// 测试-
const indexKey = ref(1)
//
const dataIndexList = ref([])
// 点击颜色标签回调
const chooseCurrentTag = (sign, item, index) => {
  console.log(item)
  // sign为0,选择颜色状态，否则选择尺寸状态
  if (sign === 0) {
    // 被点击次数+1
    item.clicknum += 1
    // 计算行数
    let rowNumber = colorData.value.reduce((acc, curritem) => acc + curritem.clicknum, 0)
    // 放入表单中
    if (item.clicknum > 0) {
      form.value.push({
        styleColor: item.value,
        rowNo: rowNumber,
        columnNo: 0,
        size: 0,
        num: 0
      })
    }
  } else {
    // 选择尺码
    console.log("sssssss", item)
    //   触发添加尺码列
    columns.value.splice(-1, 0, {
      key: indexKey.value,
      title: item,
      dataIndex: item + indexKey.value,
      ellipsis: true,
      align: 'center',
      width: 50,
      customRender: ({text, record, index, column}) => {
        //   存储对应的dataIndex数组
        console.log(column.dataIndex)
        console.log("record", record)
        console.log("index", index)

      }
      //   console.log("---------当前新增列-----------")
      //   // console.log("text",text)
      //   console.log("record",record)
      //   // console.log("index",index)
      //   console.log("column",column)
      //   // console.log("列索引",column.key)
      //   // detailsItem.value=
      //   // let dItem=
      //   // 添加对应的行列
      //   // cutBedSheetForm.value.details=[]
      //   console.log("此时的列名:",column.title)
      //   cutBedSheetForm.value.details.push({
      //     rowNo: record.rowNo,
      //     columnNo: column.key-1,
      //     styleColor: record.styleColor,
      //     size: column.title,
      //     num: 0,
      //     lotNo: ""
      //   })
      //   console.log("添加之后",cutBedSheetForm.value.details)
      //   return h(Input,{
      //     placeholder:'请输入数量',
      //     onInput:(event)=>{
      //       console.log("输入为：",event.target.value)
      //       // dItem.num=event.target.value
      //       console.log(cutBedSheetForm.value.details)
      //     }
      //   });
      // }
    })
    indexKey.value += 1
    console.log(columns.value, "clums.value")
    // 遍历对应的colums即可
    let arr = []
    columns.value.forEach((item) => {
      console.log("item", item)
      if (sizeData.value.some(str => str.includes(item.title))) {
        arr.push(item.dataIndex)
      }
    })
    dataIndexList.value = arr
    console.log(dataIndexList, "dataIndexList")

  }
};
// 删除列
const deleteColumn = (column) => {
  console.log("删除列", column)
  columns.value = columns.value.filter(item => item.dataIndex !== column.dataIndex)
  // 同步更新对象属性列
  dataIndexList.value = dataIndexList.value.filter(item => item != column.dataIndex)
  let deleteColomnIndex = column.dataIndex
//   更新form对象
  form.value.forEach(item => {
    delete item[deleteColomnIndex]
    return item
  })
}

function onDelete(record) {
  console.log("onDete", record)
  console.log("recordkey", record.index)
  Modal.confirm({
    title: '提示',
    content: '确定要删除该数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      message.success('删除成功');
      form.value = form.value.filter((item) => item !== record)
      // 对应颜色列表数量递减
      let color=record.styleColor
      console.log(colorData.value)
      colorData.value.forEach(item=>{
        if(item.value===color)item.clicknum-=1
      })
    },
    cancelText: '取消',
    onCancel() {
    },
  });
}

watch(() => cutBedSheetForm.value.id, (value) => {
  queryColor()
  queryProduceOrder()
  querySize()
})

// -------------------------------提交保存-------------------------------
const cutBedSheetId = ref(0)
const isDisable = ref(true)

// 生成菲票
async function onSubmit() {
  console.log(cutBedSheetForm.value)
  isDisable.value = false

  let arr = []
  form.value.forEach(item => {
    // 判断属性是否存在,如果对象中的属性等于数组中的元素，那么遍历赋值对象
    dataIndexList.value.forEach(dataIndex => {
      if (dataIndex in item) {
        // 转换字符串dataindex
        let sizeString = ""
        let found = false;
        sizeData.value.forEach(size => {
          if (dataIndex.includes(size)) {
            sizeString = size;
            found = true;
            return;
          }
        })
        let obj = {
          styleColor: item.styleColor,
          size: sizeString,
          num: item[dataIndex]
        }
        arr.push(obj)
      }
    })
  })

  cutBedSheetForm.value.cutBedSheetId = cutBedSheetId.value
  cutBedSheetForm.value.cutBedSheetNumber = cutBedSheetForm.value.cutNumber
  cutBedSheetForm.value.cutBedSheetName = cutBedSheetForm.value.cutSheetName
  cutBedSheetForm.value.details = arr
  cutBedSheetForm.value.feTicketAttributes = arr
  // 发送添加异步请求
  try {
    let data = await feTicketApi.add(cutBedSheetForm.value)
    cutBedSheetId.value = data.data
    console.log("cutBEdID", cutBedSheetId.value)
    message.success("保存成功")
  } catch (error) {
    smartSentry.captureError(error)
    message.error("保存裁床单失败")
  }
}

// -------------------------------生成菲票-------------------------------
//
const inputValue = ref("")
const partsArr = ref([])
const open = ref(false)
const tickForm = ref({
  ids: [],
  parts: [],
  processes: [],
  template: undefined
})

const onClose = () => {
  open.value = false

}
// 打印菲票
const addFeTicket = async () => {
  // 抽屉
  open.value = true
  // 发送添加异步请求
  try {
    console.log(cutBedSheetForm.value)
    // 添加裁床单id
    cutBedSheetForm.value.cutBedSheetId = cutBedSheetId.value
    cutBedSheetForm.value.cutBedSheetNumber = cutBedSheetForm.value.cutNumber
    cutBedSheetForm.value.cutBedSheetName = cutBedSheetForm.value.cutSheetName

    let data = await feTicketApi.add(cutBedSheetForm.value)
    tickForm.value.ids = data.data
    console.log("菲票ids", data.data)

  } catch (error) {
    smartSentry.captureError(error)
    message.error("生成菲票失败")
  }
}
// 回车添加数组
const pressEnter = (e) => {
  console.log("press", e)
  partsArr.value.push(e.target.value)
//   清空输入框
  inputValue.value = ""
}
// 关闭标签
const closeTag = (e) => {
  console.log("guanbi1231313", e)
  partsArr.value = partsArr.value.filter(item => item !== e)
  console.log("cishiClostat1231", partsArr.value)
}
// 下载菲票
const downloadTicket = async () => {
  // 关闭抽屉
  onClose()
  console.log("feiticket", tickForm.value)

  tickForm.value.parts = partsArr.value
  try {
    await feTicketApi.downLoad(tickForm.value)
    // console.log("菲票ids",data.data)
    console.log(tickForm.value)

  } catch (error) {
    smartSentry.captureError(error)
    message.error("下载菲票失败")
  }

}

</script>
<style>
.scrollable-container {
  max-height: 200px; /* 可自定义的最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
  border: 1px solid #ccc; /* 边框，仅为了演示 */
}
</style>
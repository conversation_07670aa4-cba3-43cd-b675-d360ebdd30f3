/**
 * 裁片仓库表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-06 16:48:11
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partStationWarehouseMapApi = {

  /**
   * 根据仓库id获取仓库地图
   * @param id
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  queryByWarehouseId: (warehouseId) => {
    return getRequest(`/partStationWarehouseMap/getMapByWarehouseId/${warehouseId}`);
  },

  /**
   * 更新仓库地图
   * @param param
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  updateMap: (param) => {
    return postRequest(`/partStationWarehouseMap/update`, param);
  }
};

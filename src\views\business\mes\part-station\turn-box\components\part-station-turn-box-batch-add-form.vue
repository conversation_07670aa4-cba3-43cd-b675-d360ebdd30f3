<template>
  <a-modal
      :open="visible"
      :title= "'批量添加周转箱'"
      :width="400"
      @cancel="handleCancel"
  >
 <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="编号前缀" name="numberPrefix">
        <a-input style="width: 100%" v-model:value="form.numberPrefix" placeholder="周转箱编号" />
      </a-form-item>
      <a-form-item label="名称前缀" name="namePrefix">
        <a-input style="width: 100%" v-model:value="form.namePrefix" placeholder="名称" />
      </a-form-item>
      <a-form-item label="容量" name="capacity">
        <a-input style="width: 100%" v-model:value="form.capacity" placeholder="容量" />
      </a-form-item>
      <a-form-item label="数量" name="num">
        <a-input style="width: 100%" v-model:value="form.num" placeholder="数量" />
      </a-form-item>
    </a-form>
  <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import { ref, reactive } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { partStationTurnBoxApi } from '/@/api/business/mes/part-station/turn-box/part-station-turn-box-api';

const visible = ref(false);
const emit = defineEmits(['reloadList']);

const formState = {
  numberPrefix: undefined,
  namePrefix: undefined,
  capacity: undefined,
  num: undefined,
};
const form = reactive({ ...formState });

const formRef = ref();
const rules = {
//   name: [{ required: true, message: '请输入名称' }],
//   capacity: [{ required: true, message: '请输入容量' }],
};

async function onSubmit() {
    try{
        await partStationTurnBoxApi.batchAdd(form);
        visible.value = false;
        emit('reloadList');
    }catch(error){
        smartSentry.captureError(error);
    }
}
const handleCancel = () => {
  visible.value = false;
  Object.assign(form, formState);
};

function showForm() {
  visible.value = true;
}
defineExpose({showForm}
);
</script>

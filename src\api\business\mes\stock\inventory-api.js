/**
 * 即时库存 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-01-14 15:20:23
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkInventoryApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/stkInventory/queryPage', param);
  },
  // 即时库存分页查询
  queryPageByStkInventory: (param) => {
    return postRequest('/stkInventory/queryInventoryPage', param);
  },
  // 多维库存分页查询
  queryPageByStkInventoryMulti: (param) => {
    return postRequest('/stkInventory/multiBalanceInventoryPage', param);
  },
  //即时库存更新日志分页查询
  queryPageByStkInventoryLog: (param) => {
    return postRequest('/stkInventoryLog/queryPage', param);
  },
  //单物料库存查询
  queryMaterialInventory: (param) => {
    return postRequest('/stkInventory/queryMaterialInventory', param);
  },
  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/stkInventory/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/stkInventory/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/stkInventory/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/stkInventory/batchDelete', idList);
  },

};

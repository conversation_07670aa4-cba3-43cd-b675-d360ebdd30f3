<!--
  * 生产用料清单
  * 
  * @Author:    pxz
  * @Date:      2025-02-14
  * @Copyright  zscbdic
-->
<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字" />
      </a-form-item>
      <a-form-item label="生产指令单编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.orderNumber" placeholder="生产指令单编号" />
      </a-form-item>
      <a-form-item label="物料名称" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.itemName" placeholder="物料名称" />
      </a-form-item>
      <a-form-item label="物料SKU编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.itemSkuNumber" placeholder="物料SKU编号" />
      </a-form-item>
      <a-form-item label="物料SPU编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.itemNumber" placeholder="物料SPU编号" />
      </a-form-item>
      <a-form-item class="smart-query-form-item smart-margin-left10">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="reset" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRows: selectedRows, onChange: onSelectChange }"
      :scroll="{ x: 1900 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'orderProduceStatus'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(record.orderProduceStatus).color">{{
            PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(record.orderProduceStatus).desc
          }}</a-tag>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { PAGE_SIZE_OPTIONS, PAGE_SIZE } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { produceInstructOrderItemApi } from '/@/api/business/mes/produce/produce-instruct-order-item-api';
  import { PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const';
  import { tableMergeCell } from '/@/utils/table-merge-cell-util';
  import _ from 'lodash';
  // 表格列定义

  const columns = [
    {
      title: '生产指令单编号',
      dataIndex: 'orderNumber',
      ellipsis: true,
      align: 'center',
      customCell: (record, index) => tableMergeCell(tableData.value, record, index, 'orderNumber'),
      width: 180,
    },
    {
      title: '生产状态',
      dataIndex: 'orderProduceStatus',
      ellipsis: true,
      align: 'center',
      width: 120,
    },

    {
      title: '指令单物料SPU编号(产品)',
      dataIndex: 'orderItemNumber',
      ellipsis: true,
      width:180,
      align: 'center',
    },
    {
      title: '指令单物料名称(产品)',
      dataIndex: 'orderItemName',
      ellipsis: true,
      width:160,
      align: 'center',
    },
    {
      title: '物料SPU编号(用料)',
      dataIndex: 'itemNumber',
      ellipsis: true,
      width:150,
      align: 'center',
    },
    {
      title: '物料SKU编号(用料)',
      dataIndex: 'itemSkuNumber',
      ellipsis: true,
      width:150,
      align: 'center',
    },
    {
      title: '物料名称(用料)',
      dataIndex: 'itemName',
      ellipsis: true,
      width:150,
      align: 'center',
    },
    {
      title: '物料规格型号(用料)',
      dataIndex: 'itemModel',
      ellipsis: true,
      width:150,
      align: 'center',
    },
    {
      title: '物料单位(用料)',
      dataIndex: 'itemUnitName',
      ellipsis: true,
      width:150,
      align: 'center',
    },
    {
      title: '单位用量(用料)',
      dataIndex: 'dosage',
      ellipsis: true,
      width:150,
      align: 'center',
      customRender: ({ text }) => text?.toFixed(2),
    },
    {
      title: '单位损耗率(用料)',
      dataIndex: 'loss',
      ellipsis: true,
      width:150,
      align: 'center',
      customRender: ({ text }) => text?.toFixed(2),
    },
    {
      title: '总用量(用料)',
      dataIndex: 'totalDosage',
      ellipsis: true,
      width:150,
      align: 'center',
      customRender: ({ text }) => text?.toFixed(2),
    },
    {
      title: '发料数量(用料)',
      dataIndex: 'giveQty',
      ellipsis: true,
      width:150,
      align: 'center',
      customRender: ({ text }) => text?.toFixed(2),
    },
    {
      title: '退料数量(用料)',
      dataIndex: 'backQty',
      ellipsis: true,
      width:150,
      align: 'center',
      customRender: ({ text }) => text?.toFixed(2),
    },
  ];

  // 查询表单数据
  const queryFormState = {
    pageNum: 1,
    pageSize: PAGE_SIZE,
    queryKey: undefined,
    orderNumber: undefined, //生产指令单编号
    itemName: undefined, //物料名称
    itemSkuNumber: undefined, //物料SKU编号
    itemNumber: undefined, //物料SPU编号
    sortItemList: [{ column: 't_mes_produce_instruct_order_item.create_time', isAsc: false }],
  };

  const queryForm = reactive({ ...queryFormState });
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      const res = await produceInstructOrderItemApi.queryPage(queryForm);
      tableData.value = res.data.list;
      total.value = res.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }
  // 添加选择相关的响应式变量
  //id
  // const selectedRowKeys = ref([]);
  // 行数据
  const selectedRows = ref([]);

  // 选择变化的处理函数
  const onSelectChange = (keys, rows) => {
    selectedRows.value = rows;
  };

  // 重置查询
  function reset() {
    Object.assign(queryForm, queryFormState);
    queryData();
  }

  // 查询
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  onMounted(() => {
    queryData();
  });
</script>

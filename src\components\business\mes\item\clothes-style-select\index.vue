<!--
  *  目录 树形选择组件
  *
  * @Author:   <PERSON><PERSON><PERSON><PERSON><PERSON>
  * @Date:      2024-07-10 8:01:52
  * @Wechat:
  * @Email:
  * @Copyright  电子科技大学中山学院大数据实验室
  *
-->
<!--采用级联选择组件-->
<template>
<!--  <a-tree-select-->
<!--      v-model:value="selectValue"-->
<!--      :style="`width:${width}`"-->
<!--      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"-->
<!--      :tree-data="TreeData"-->
<!--      :placeholder="placeholder"-->
<!--      tree-default-expand-all-->
<!--      @change="onChange"-->
<!--  />-->
  <a-cascader
      v-model:value="styleName1"
      :options="TreeData"
      :placeholder="props.placeholder"
      change-on-select
      @change="onChange"
  />
</template>

<script setup>
import { ref, watch, onMounted,toRaw } from 'vue';
import { styleApi } from '/src/api/business/mes/base/style-api';
import { smartSentry } from '/src/lib/smart-sentry';



const props = defineProps({
  value: Number,
  placeholder: {
    type: String,
    default: '请选择',
  },
  categoryType: Number,
  parentId: {
    type:Number,
    default:0
  },
  width: {
    type: String,
    default: '100%',
  },

});

const emit = defineEmits(['update:value', 'change','changelabel','getarray']);

// -----------------  查询 目录 数据 -----------------
// 定义联机选择
const styleName1=ref([])
// const TreeData=ref([])
const TreeData = ref([{
  // value:,
  label:"顶级",
  children:[]
}]);
// 定义children转换函数
function transformChildren(data) {
  return data.map((item) => {
    const { children, ...rest } = item;
    if (children.length > 0) {
      rest.children = transformChildren(children);
    }
    return rest;
  });
}


async function queryCategoryTree() {

  if (!props.categoryType) {
    TreeData.value = [];
    // 这里props为空
    return;
  }
  try {
    let param = {
      // queryKey: props.categoryType,
      queryKey: "",
      parentId: 0
    };
    // // 将其复原
    // param.categoryType=""
    // 查询数据
    let resp = await styleApi.queryTree(param);
    let respdata=transformChildren(resp.data)
    emit('getarray',respdata)
    //  遍历添加
    respdata.forEach((item)=>{
      TreeData.value.push(item)
    })
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// -----------------  选中相关监听、事件 -----------------
const selectValue = ref(props.value);
// 箭头value变化
watch(
    () => props.value,
    (newValue) => {
      selectValue.value = newValue;
    }
);

// 监听类型变化
watch(
    () => props.categoryType,
    () => {
      queryCategoryTree();
    }
);

function onChange(value,selectOptions) {
  emit('update:value', value);
  emit('change', value);

  // let stringValue=toRaw(selectOptions)
  let stringValue= JSON.parse(JSON.stringify(selectOptions))
  emit('changelabel',stringValue[0].label)

}

onMounted(queryCategoryTree);
</script>

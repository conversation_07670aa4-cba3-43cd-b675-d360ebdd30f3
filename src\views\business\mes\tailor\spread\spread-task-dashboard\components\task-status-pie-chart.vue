<template>
  <a-card>
    <div style="display: flex; justify-content: space-between">
      <div style="font-size: 16px">任务状态分布</div>
      <a-select v-model:value="timeOption" :options="timeOptions" @change="timeChange" />
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="pie-main-1"></div>
    </div>
  </a-card>
</template>
<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import * as echarts from 'echarts';
  import { spreadTaskDashboardApi } from '/@/api/business/mes/tailor/spread-task-dashboard-api';
  import dayjs from 'dayjs';
  import { SPREAD_TASK_STATUS_ENUM } from '/@/constants/business/mes/tailor/spread-task-const.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';

  let myChart = null;

  onMounted(() => {
    init();
    queryTaskCountList();
  });

  let option = reactive({});
  const chartData = ref([]);

  function init() {
    option = {
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255,255,255,0.9)',
        borderColor: '#ccc',
        textStyle: {
          color: '#333',
        },
      },
      legend: {
        show: false,
        top: '5%',
        left: 'center',
        textStyle: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '80%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c} ({d}%)',
            color: '#333',
            fontSize: 14,
          },
          labelLine: {
            show: true,
            lineStyle: {
              color: '#999',
            },
          },
          itemStyle: {
            borderRadius: 1,
            borderColor: '#fff',
            borderWidth: 2,
          },
          data: chartData.value,
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          shadowBlur: 10,
          shadowColor: 'rgba(0, 150, 255, 0.3)',
        },
      ],
      color: ['#2ec7c9', '#b6a2f5', '#5ab1ef', '#ffb980', '#d87a80'],
    };
    let chartDom = document.getElementById('pie-main-1');
    if (chartDom) {
      myChart = echarts.init(chartDom);
      option && myChart.setOption(option);
    }
    window.addEventListener('resize', () => {
      myChart && myChart.resize();
    });
  }

  // 时间选择
  const timeOptions = [
    {
      value: '1',
      label: '今天',
    },
    {
      value: '2',
      label: '本周',
    },
    {
      value: '3',
      label: '本月',
    },
    {
      value: '4',
      label: '本季',
    },
  ];

  let timeOption = ref('1');

  // 根据选择的时间选项计算日期范围
  function calculateDateRange(option) {
    const today = dayjs();
    let startDate, endDate;

    switch (option) {
      case '1': // 今天
        startDate = today.format('YYYY-MM-DD');
        endDate = today.format('YYYY-MM-DD');
        break;
      case '2': // 本周
        startDate = today.startOf('week').format('YYYY-MM-DD');
        endDate = today.endOf('week').format('YYYY-MM-DD');
        break;
      case '3': // 本月
        startDate = today.startOf('month').format('YYYY-MM-DD');
        endDate = today.endOf('month').format('YYYY-MM-DD');
        break;
      case '4': // 本季
        const quarter = Math.floor(today.month() / 3);
        startDate = today
          .month(quarter * 3)
          .startOf('month')
          .format('YYYY-MM-DD');
        endDate = today
          .month(quarter * 3 + 2)
          .endOf('month')
          .format('YYYY-MM-DD');
        break;
      default:
        startDate = today.format('YYYY-MM-DD');
        endDate = today.format('YYYY-MM-DD');
    }

    return { startDate, endDate };
  }

  // 获取饼图数据
  async function queryTaskCountList() {
    try {
      // 根据选择的时间选项计算日期范围
      const dateRange = calculateDateRange(timeOption.value);

      const params = {
        planBeginTimeBegin: dateRange.startDate,
        planBeginTimeEnd: dateRange.endDate,
      };

      const res = await spreadTaskDashboardApi.statsCountList(params);
      if (res && res.data) {
        // 更新图表数据
        chartData.value = res.data.map((item) => ({
          name: item.x,
          value: item.y,
        }));

        // 更新图表
        if (myChart) {
          option.series[0].data = chartData.value;
          myChart.setOption(option);
        }
      }
    } catch (e) {
      smartSentry.captureError(e);
      console.error('获取任务状态分布数据失败', e);
    }
  }

  // 时间选择变化时重新获取数据
  function timeChange() {
    queryTaskCountList();
  }
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .chart-main {
      width: 100%;
      height: 200px;
    }
  }
</style>

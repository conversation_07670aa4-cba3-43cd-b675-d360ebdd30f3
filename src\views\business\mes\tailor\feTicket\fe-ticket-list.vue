<!--
  * 菲票
  *
  * @Author:    cjm
  * @Date:      2024-07-14 16:55:01
  * @Copyright  cjm
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!--      指令单编号和物料编号联动选择器-->
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="颜色" class="smart-query-form-item">
        <a-select
          v-model:value="initColorVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择颜色"
          :options="colorOptions"
          @change="handleColorChange"
        />
      </a-form-item>
      <a-form-item label="尺码" class="smart-query-form-item">
        <a-select
          v-model:value="initSizeVal"
          :max-tag-count="maxTagCount"
          mode="multiple"
          style="width: 145px"
          placeholder="请选择尺码"
          :options="sizeOptions"
          @change="handleSizeChange"
        />
      </a-form-item>
      <a-form-item label="床次" class="smart-query-form-item"> 
        <a-input
          v-model:value="queryForm.cutNum"
          style="width: 145px"
          placeholder="请输入床次"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div style="display: flex; gap: 10px">
        <a-button @click="addFeTicket" size="small"> 下载菲票 </a-button>
        <div style="color: rgba(0, 0, 0, 0.5)">
          {{ `已选择 ${state.selectedRowKeys.length} 条菲票` }}
        </div>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      :scroll="{ x: 1000 }"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record, '追踪')" type="link">追踪</a-button>
            <a-button @click="showForm(record, '查看')" type="link">查看</a-button>
            <!-- 操作下拉菜单 -->
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                操作
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu @click="({ key }) => handleOperation(record, key)">
                  <a-menu-item key="modifyNum">改数量</a-menu-item>
                  <a-menu-item key="split">拆分</a-menu-item>
                  <!-- 可以添加更多操作选项 -->
                </a-menu>
              </template>
            </a-dropdown>
            <a-popover v-model:open="record.QRvisiable" :title="record.tieNum + `扎`" placement="left" trigger="click">
              <template #content>
                <a-image :width="100" :src="qrCode" />
              </template>
              <a-button @click="showQRById(record.id)" type="link">二维码</a-button>
            </a-popover>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'attribute'">
          <a-tag :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">{{ ITEM_ATTRIBUTE_ENUM.getEnum(text).label }}</a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <FeTicketForm ref="formRef" @reloadList="queryData" />
    <FeTicketModifyNumform ref="modifyNumFormRef" @reload-list="queryData" />
    <FeTicketFollowDrawer ref="FllowDrawerRef" />
    <FeTicketSplitForm ref="splitFormRef" @reload-list="queryData" />
  </a-card>
  <!--  抽屉-->
  <a-drawer
    title="下载菲票内容"
    :width="500"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="tickForm" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="模板">
            <a-select
              ref="select"
              placeholder="请输入模板"
              v-model:value="tickForm.template"
              :options="$smartEnumPlugin.getValueDescList('Template')"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <!--      <a-row :gutter="16">-->
      <!--        <a-col :span="24">-->
      <!--          <a-form-item label="部位" name="name">-->
      <!--            <a-input ref="inputRef" v-model:value="inputValue" placeholder="回车输入部位" @pressEnter="pressEnter"/>-->
      <!--          </a-form-item>-->
      <!--        </a-col>-->
      <!--      </a-row>-->
      <!-- 添加部位标签 -->
      <!--      <a-row :gutter="16">-->
      <!--        <a-col :span="24" >-->
      <!--          <a-tag  v-for="item in partsArr" :key="item" :bordered="false" closable @close="closeTag(item)">{{item}}</a-tag>-->
      <!--        </a-col>-->
      <!--      </a-row>-->
    </a-form>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="downloadTicket">下载菲票</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
  import { reactive, ref, onMounted, computed, watch } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import FeTicketForm from './fe-ticket-form.vue';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { Template } from '/@/constants/business/mes/tailor/cut-bed-sheet-const.js';
  import FeTicketModifyNumform from '/@/views/business/mes/tailor/feTicket/fe-ticket-modifyNumform.vue';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import FeTicketFollowDrawer from './Component/fe-ticket-follow-drawer.vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import FeTicketSplitForm from './Component/fe-ticket-split-form.vue';

  const splitFormRef = ref();

  // 处理操作下拉菜单选择
  function handleOperation(record, key) {
    switch (key) {
      case 'modifyNum':
        showForm(record, '改数量');
        break;
      case 'split':
        splitFormRef.value.show(record);
        break;
      default:
        break;
    }
  }
  // ---------------------------- 二维码 ----------------------------

  const qrCode = ref();

  async function showQRById(id) {
    try {
      let res = await feTicketApi.getQRURL(id);
      qrCode.value = res.data;
    } catch (e) {
      message.error('二维码不存在');
      smartSentry.captureError(e);
    }
  }
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '生产指令单编号',
      dataIndex: 'instructOrderNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '裁床单编号',
      dataIndex: 'cutBedSheetNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      ellipsis: true,
      align: 'center',
      width: 60,
    },
    {
      title: '扎号',
      dataIndex: 'tieNum',
      ellipsis: true,
      align: 'center',
      width: 50,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      align: 'center',
      width: 130,
    },
    {
      title: '款式颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      align: 'center',
      width: 80,
    },
    {
      title: '部位',
      dataIndex: 'positions',
      ellipsis: true,
      align: 'center',
      width: 60,
    },

    {
      title: '数量',
      dataIndex: 'num',
      ellipsis: true,
      align: 'center',
      width: 60,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 160,
      align: 'center',
    },
  ]);
  // 菲票ids
  const ticketIds = ref([]);
  // 菲票部位
  const ticketParts = ref([]);
  // 菲票工序
  const ticketProcesses = ref([]);
  // 菲票模板
  const ticketTemplate = ref('');

  // 多选
  const hasSelected = computed(() => state.selectedRowKeys.length > 0);
  const state = reactive({
    selectedRowKeys: [],
    // Check here to configure the default column
    loading: false,
  });
  const onSelectChange = (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys;
    // 遍历数据
    let ids = [];
    let parts = [];
    selectedRows.forEach((item) => {
      ids.push(item.id);
      parts.push(item.positions);
    });
    ticketIds.value = ids;
    ticketParts.value = parts;
  };

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    cutNum:undefined,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    //两个编号清空
    insItemSelectRef.value.clearAllNumber();
    // 重置 queryForm 中的相关字段
    queryForm.instructNumber = undefined;
    queryForm.itemNumber = undefined;
    queryForm.instructOrderId = undefined;
    //颜色和尺码清空
    initColorVal.value = undefined;
    initSizeVal.value = undefined;
    colorOptions.value = [];
    sizeOptions.value = [];
    Object.assign(queryForm, queryFormState);
    console.log(queryForm);
    queryForm.pageSize = pageSize;
    queryForm.colors = [];
    queryForm.sizes = [];
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
     // 创建一个用于查询的表单副本
    const queryParams = { ...queryForm };
    
    // 确保 cutNum 是数字类型
    if (queryParams.cutNum !== undefined && queryParams.cutNum !== '') {
      queryParams.cutNum = Number(queryParams.cutNum);
      // 如果转换后不是有效数字，则设为 undefined
      if (isNaN(queryParams.cutNum)) {
        queryParams.cutNum = undefined;
      }
    } else {
      queryParams.cutNum = undefined;
    }
      let queryResult = await feTicketApi.queryPage(queryParams);
      tableData.value = queryResult.data.list;
      //初始化二维码显示状态
      tableData.value.map((e) => {
        e['QRvisiable'] = false;
      });
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }
  onMounted(queryData);

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();
  const modifyNumFormRef = ref();
  const FllowDrawerRef = ref();
  function showForm(data, text) {
    if (text === '查看') {
      formRef.value.show(data);
    } else if (text === '改数量') {
      modifyNumFormRef.value.show(data);
    } else {
      FllowDrawerRef.value.show(data);
    }
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      let deleteForm = {
        goodsIdList: selectedRowKeyList.value,
      };
      await feTicketApi.delete(data.id);
      message.success('删除成功');
      await queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
  // -----------------------------生成菲票-----------------------------
  // 下载菲票
  // -------------------------------生成菲票-------------------------------
  //
  const inputValue = ref('');
  const partsArr = ref([]);
  const open = ref(false);
  const tickForm = ref({
    ids: [],
    parts: [],
    processes: [],
    template: Template.TICKET1.value,
  });

  const onClose = () => {
    open.value = false;
  };
  // 打印菲票
  const addFeTicket = async () => {
    // 抽屉
    if (state.selectedRowKeys.length === 0) {
      message.warning('请选择菲票');
      return;
    }
    open.value = true;
  };
  // 回车添加数组
  const pressEnter = (e) => {
    partsArr.value.push(e.target.value);
    //   清空输入框
    inputValue.value = '';
  };
  // 关闭标签
  const closeTag = (e) => {
    partsArr.value = partsArr.value.filter((item) => item !== e);
  };
  // 下载菲票
  const downloadTicket = async () => {
    // 关闭抽屉
    onClose();
    tickForm.value.parts = partsArr.value;
    // 更新ids
    tickForm.value.ids = ticketIds.value;
    try {
      await feTicketApi.downLoad(tickForm.value);
      console.log(tickForm.value);
    } catch (error) {
      smartSentry.captureError(error);
      message.error('下载菲票失败');
    }
  };

  // ---------------------------- 处理两个编号 ----------------------------
  const insItemSelectRef = ref();
  function insItemChange(data) {
    // 回传数据
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
    queryForm.instructOrderId = data.instructOrderId;
  }

  // ---------------------------- 获取颜色和尺码 ----------------------------
  //下拉框最多可显示数量
  const maxTagCount = ref(1);
  //颜色和尺码下拉框初始值
  const initColorVal = ref(undefined);
  const initSizeVal = ref(undefined);

  watch(
    () => queryForm.instructNumber,
    (newVal) => {
      initColorVal.value = undefined;
      initSizeVal.value = undefined;
      if (newVal !== undefined) {
        queryByInsAndItemNumber();
      }
    }
  );
  //根据指令单id获取颜色和尺码信息
  async function queryByInsAndItemNumber() {
    let colorList = await produceInstructOrderClothesApi.queryClothesColorList(queryForm.instructOrderId);
    let sizeList = await produceInstructOrderClothesApi.queryClothesSizeList(queryForm.instructOrderId);
    outstandingOptions(colorList.data, colorOptions);
    outstandingOptions(sizeList.data, sizeOptions);
  }

  //拼装options
  const colorOptions = ref([]);
  const sizeOptions = ref([]);
  function outstandingOptions(sourceData, target) {
    target.value = [];
    sourceData.map((item, index) =>
      target.value.push({
        label: item,
        value: index,
      })
    );
  }

  // --------------------- 处理颜色和尺码 ------------------------
  function handleColorChange(item, option) {
    queryForm.colors = [];
    option.map((e) => {
      queryForm.colors.push(e.label);
    });
  }

  function handleSizeChange(item, option) {
    queryForm.sizes = [];
    option.map((e) => {
      queryForm.sizes.push(e.label);
    });
  }
</script>
<style scoped></style>

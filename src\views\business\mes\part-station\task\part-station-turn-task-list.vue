<!--
  * 驿站任务表
  *
  * @Author:    cjm
  * @Date:      2025-03-14 20:13:46
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="任务优先级" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.priority" placeholder="任务优先级" :options="TURN_TASK_PRIORITY_ENUM.getOptions()" />
      </a-form-item>
      <a-form-item label="任务类型" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.type" placeholder="任务类型" :options="TURN_TASK_TYPE_ENUM.getOptions()" />
      </a-form-item>
      <a-form-item label="任务状态" class="smart-query-form-item">
        <a-select style="width:150px" v-model:value="queryForm.status" placeholder="任务状态" :options="TURN_TASK_STATUS_ENUM.getOptions()" />
      </a-form-item>
       <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
        <a-button class="smart-margin-left20" @click="moreQueryConditionFlag = !moreQueryConditionFlag">
          <template #icon>
            <MoreOutlined />
          </template>
          {{ moreQueryConditionFlag ? '收起' : '展开' }}
        </a-button>
      </a-form-item>
      <a-row class="smart-query-form-row" v-show="moreQueryConditionFlag">
        <a-form-item label="提交人" class="smart-query-form-item">
          <EmployeeSelect v-model:value="queryForm.submitterId" placeholder="提交人"  :width="'200px'"/>
        </a-form-item>
      <a-form-item label="执行人" class="smart-query-form-item">
        <EmployeeSelect v-model:value="queryForm.executorId" placeholder="执行人"  :width="'200px'"/>
      </a-form-item>
      <a-form-item label="提交时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.submitTime" :presets="defaultTimeRanges" style="width: 200px" @change="onChangeSubmitTime" />
      </a-form-item>
      <a-form-item label="领取时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.getTime" :presets="defaultTimeRanges" style="width: 200px" @change="onChangeGetTime" />
      </a-form-item>
      <a-form-item label="开始时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.beginTime" :presets="defaultTimeRanges" style="width: 200px" @change="onChangeBeginTime" />
      </a-form-item>
      <a-form-item label="结束时间" class="smart-query-form-item">
        <a-range-picker v-model:value="queryForm.endTime" :presets="defaultTimeRanges" style="width: 200px" @change="onChangeEndTime" />
      </a-form-item>
      <a-form-item label="起点货位" class="smart-query-form-item">
        <LocationSelect v-model:value="queryForm.startLocationId" placeholder="起点货位"  :width="'200px'"/>
      </a-form-item>
      <a-form-item label="终点货位" class="smart-query-form-item">
        <LocationSelect v-model:value="queryForm.endLocationId" placeholder="终点货位"  :width="'200px'"/>
      </a-form-item>
      </a-row>
    </a-row>
    </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
<!--        <a-button @click="showForm" type="primary" size="small">-->
<!--          <template #icon>-->
<!--            <PlusOutlined />-->
<!--          </template>-->
<!--          新建-->
<!--        </a-button>-->
        <a-button @click="confirmBatchDelete" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
        <a-button @click="cancelTask" type="primary" danger size="small" :disabled="selectedRowKeyList.length == 0">
          取消任务
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
        size="small"
        :dataSource="tableData"
        :columns="columns"
        rowKey="id"
        bordered
        :loading="tableLoading"
        :pagination="false"
        :scroll="{ x: 2000 }"
        :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'status'" >
          {{ TURN_TASK_STATUS_ENUM.getOption(text).desc }}
        </template>
        <template v-if="column.dataIndex === 'priority'">
          {{ TURN_TASK_PRIORITY_ENUM.getOption(text).desc }}
        </template>
        <template v-if="column.dataIndex === 'type'">
          {{ TURN_TASK_TYPE_ENUM.getOption(text).desc }}
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <!-- <a-button @click="showForm(record)" type="link">编辑</a-button> -->
            <a-button @click="showDetailForm(record)" type="link">详情</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
      />

    </div>

    <PartStationTurnTaskForm  ref="formRef" />

  </a-card>
</template>
<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { partStationTurnTaskApi } from '/src/api/business/mes/part-station/turn-task/part-station-turn-task-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { defaultTimeRanges } from '/@/lib/default-time-ranges';
import PartStationTurnTaskForm from './components/part-station-turn-tast-form.vue';
import EmployeeSelect from '/@/components/system/employee-select/index.vue';
import LocationSelect from '/@/components/business/mes/part-station/location-select/index.vue';
import { TURN_TASK_STATUS_ENUM, TURN_TASK_PRIORITY_ENUM, TURN_TASK_TYPE_ENUM } from '/@/constants/business/mes/part-station/turn-task/turn-task-const.js';
//import FilePreview from '/@/components/support/file-preview/index.vue'; // 图片预览组件

// ---------------------------- 表格列 ----------------------------
const moreQueryConditionFlag = ref(false);

const columns = ref([
  // {
  //   title: '主键',
  //   dataIndex: 'id',
  //   ellipsis: true,
  // },
  // {
  //   title: '创建人',
  //   dataIndex: 'createBy',
  //   ellipsis: true,
  // },
  // {
  //   title: '更新时间',
  //   dataIndex: 'updateTime',
  //   ellipsis: true,
  // },
  // {
  //   title: '更新人',
  //   dataIndex: 'updateBy',
  //   ellipsis: true,
  // },
  // {
  //   title: '删除标识',
  //   dataIndex: 'deletedFlag',
  //   ellipsis: true,
  // },
  // {
  //   title: '备注',
  //   dataIndex: 'remark',
  //   ellipsis: true,
  // },
  // {
  //   title: '任务作用范围',
  //   dataIndex: 'taskScope',
  //   ellipsis: true,
  // },
  {
    title: '任务编号',
    dataIndex: 'number',
    ellipsis: true,
  },
  {
    title: '任务名称',
    dataIndex: 'name',
    width: 200,
    ellipsis: true,
  },
  {
    title: '任务优先级',
    dataIndex: 'priority',
    ellipsis: true,
  },
  {
    title: '任务类型',
    dataIndex: 'type',
    ellipsis: true,
  },
  {
    title: '任务状态',
    dataIndex: 'status',
    ellipsis: true,
    width: 100,
    align: 'center',
    customCell: (record) => {
      let backgroundColor = '';
        let option = TURN_TASK_STATUS_ENUM.getOption(record.status);
        if (option) {
          backgroundColor = option.color;
        }else {
          backgroundColor = '#909399';
        }
        return {
          style: {
            backgroundColor: backgroundColor,
            color: '#fff',
          },
        };
      },
  },
  // {
  //   title: '是否自动派发',
  //   dataIndex: 'autoDispatchFlag',
  //   ellipsis: true,
  // },
  {
    title: '起点货位编号',
    dataIndex: 'startLocationNumber',
    ellipsis: true,
  },
  {
    title: '终点货位编号',
    dataIndex: 'endLocationNumber',
    ellipsis: true,
  },
  // {
  //   title: '菲票数组',
  //   dataIndex: 'ticketIds',
  //   ellipsis: true,
  // },
  // {
  //   title: '周转箱ID',
  //   dataIndex: 'turnoverBoxId',
  //   ellipsis: true,
  // },
  {
    title: '提交时间',
    dataIndex: 'submitTime',
    ellipsis: true,
  },
  {
    title: '领取时间',
    dataIndex: 'getTime',
    ellipsis: true,
  },
  {
    title: '开始时间',
    dataIndex: 'beginTime',
    ellipsis: true,
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    ellipsis: true,
  },
  // {
  //   title: '提交人类型',
  //   dataIndex: 'submitterType',
  //   ellipsis: true,
  // },
  // {
  //   title: '提交人ID',
  //   dataIndex: 'submitterId',
  //   ellipsis: true,
  // },
  {
    title: '提交人名称',
    dataIndex: 'submitterName',
    ellipsis: true,
  },
  // {
  //   title: '执行人类型',
  //   dataIndex: 'executorType',
  //   ellipsis: true,
  // },
  // {
  //   title: '执行人ID',
  //   dataIndex: 'executorId',
  //   ellipsis: true,
  // },
  {
    title: '执行人名称',
    dataIndex: 'executorName',
    ellipsis: true,
  },
  // {
  //   title: '关联单据类型',
  //   dataIndex: 'bizType',
  //   ellipsis: true,
  // },
  // {
  //   title: '关联单据ID',
  //   dataIndex: 'bizId',
  //   ellipsis: true,
  // },
  // {
  //   title: '关联单据详情序号',
  //   dataIndex: 'bizDetailSeq',
  //   ellipsis: true,
  // },
  // {
  //   title: '关联单据详情ID',
  //   dataIndex: 'bizDetailId',
  //   ellipsis: true,
  // },
  //   {
  //   title: '创建时间',
  //   dataIndex: 'createTime',
  //   ellipsis: true,
  // },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 90,
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  queryKey: undefined, //关键字查询
  priority: undefined, //任务优先级
  type: undefined, //任务类型
  status: undefined, //任务状态
  submitterId: undefined, //提交人ID
  executorId: undefined, //执行人ID
  submitTime: [], //提交时间
  submitTimeBegin: undefined, //提交时间 开始
  submitTimeEnd: undefined, //提交时间 结束
  getTime: [], //领取时间
  getTimeBegin: undefined, //领取时间 开始
  getTimeEnd: undefined, //领取时间 结束
  beginTime: [], //开始时间
  beginTimeBegin: undefined, //开始时间 开始
  beginTimeEnd: undefined, //开始时间 结束
  endTime: [], //结束时间
  endTimeBegin: undefined, //结束时间 开始
  endTimeEnd: undefined, //结束时间 结束
  startLocationId: undefined, //起点货位ID
  endLocationId: undefined, //终点货位ID
  sortItemList: [{
    column: "create_time",
    isAsc: false
  }],
  pageNum: 1,
  pageSize: 10,
};
// 查询表单form
const queryForm = reactive({ ...queryFormState });
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch(){
  queryForm.pageNum = 1;
  queryData();
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    let queryResult = await partStationTurnTaskApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

function onChangeSubmitTime(dates, dateStrings){
  queryForm.submitTimeBegin = dateStrings[0];
  queryForm.submitTimeEnd = dateStrings[1];
}

function onChangeGetTime(dates, dateStrings){
  queryForm.getTimeBegin = dateStrings[0];
  queryForm.getTimeEnd = dateStrings[1];
}

function onChangeBeginTime(dates, dateStrings){
  queryForm.beginTimeBegin = dateStrings[0];
  queryForm.beginTimeEnd = dateStrings[1];
}

function onChangeEndTime(dates, dateStrings){
  queryForm.endTimeBegin = dateStrings[0];
  queryForm.endTimeEnd = dateStrings[1];
}


onMounted(queryData);

// ---------------------------- 添加/修改 ----------------------------
const formRef = ref();

function showDetailForm(data) {
  formRef.value.showForm(data);
}

// ---------------------------- 单个删除 ----------------------------
//确认删除
function onDelete(data){
  Modal.confirm({
    title: '提示',
    content: '确定要删除选吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestDelete(data);
    },
    cancelText: '取消',
    onCancel() {},
  });
}

//请求删除
async function requestDelete(data){
  SmartLoading.show();
  try {
    let deleteForm = {
      goodsIdList: selectedRowKeyList.value,
    };
    await partStationTurnTaskApi.delete(data.id);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

// ---------------------------- 批量删除 ----------------------------

// 选择表格行
const selectedRowKeyList = ref([]);

function onSelectChange(selectedRowKeys) {
  selectedRowKeyList.value = selectedRowKeys;
}

// 批量删除
function confirmBatchDelete() {
  Modal.confirm({
    title: '提示',
    content: '确定要批量删除这些数据吗?',
    okText: '删除',
    okType: 'danger',
    onOk() {
      requestBatchDelete();
    },
    cancelText: '取消',
    onCancel() {},
  });
}

// 取消任务
function cancelTask() {
  Modal.confirm({
    title: '提示',
    content: '确定要取消这些任务吗?',
    okText: '确定',
    okType: 'danger',
    async onOk() {
      try {
        await partStationTurnTaskApi.cancelTask(selectedRowKeyList.value);
        message.success('取消成功');
        queryData();
      } catch (e) {
        smartSentry.captureError(e);
      }
    },
    cancelText: '取消',
    onCancel() {},
  });
}

//请求批量删除
async function requestBatchDelete() {
  try {
    SmartLoading.show();
    await partStationTurnTaskApi.batchDelete(selectedRowKeyList.value);
    message.success('删除成功');
    queryData();
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}
</script>

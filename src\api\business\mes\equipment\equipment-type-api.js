/**
 * 设备类别 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-02-09 20:12:33
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/src/lib/axios';

export const equipmentTypeApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/equipmentType/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/equipmentType/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/equipmentType/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/equipmentType/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/equipmentType/batchDelete', idList);
  },

};

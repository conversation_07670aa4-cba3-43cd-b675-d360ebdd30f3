/**
 * 员工申请表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-09-19 22:50:32
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const employeeApplyApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/employeeApply/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/employeeApply/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/employeeApply/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/employeeApply/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/employeeApply/batchDelete', idList);
  },

  /**
   * 入职申请  <AUTHOR>
   */
  apply:(param)=>{
    return postRequest('/employeeApply/apply',param)
  },

   /**
   * 通过申请  <AUTHOR>
   */
  pass:(param )=>{
    return postRequest('/employeeApply/pass',param)
  },

  /**
   * 拒绝申请  <AUTHOR>
   */
  refuse:(id)=>{
    return getRequest(`/employeeApply/refuse/${id}`)
  }
};

<!--
  * 安排信息
  *
  * @Author:    fkf
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
    <div><strong>安排信息</strong></div>
  </a-row>
  <a-row>
    <a-col :span="2">
      <a-button type="primary" style="margin-right: 10px" @click="addItem(0)">添加</a-button>
    </a-col>
    <a-col :span="7">
      <a-form>
        <a-form-item label="选择安排信息" name="username">
          <a-select v-model:value="selectArrange" :options="options" placeholder="请选择安排信息" style="width: 100%" @select="changeArrange" />
        </a-form-item>
      </a-form>
    </a-col>
  </a-row>
  <br />
  <a-form ref="formPackage" :model="arrangeList" :rules="rules">
    <a-table size="small" :dataSource="arrangeList" :columns="columns" :pagination="false" bordered :scroll="{ x: 120 }">
      <template #bodyCell="{ text, record, column, index }">
        <template v-if="column.dataIndex === 'nodeName'">
          <a-form-item class="tableItem" :name="[index, 'nodeName']" :rules="rules.nodeName">
            <a-input style="width: 90%" placeholder="请输入节点名称" v-model:value="record[column.dataIndex]" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'headId'">
          <a-form-item class="tableItem" :name="[index, 'headId']" :rules="rules.headId">
            <employee-select v-model:value="record[column.dataIndex]" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'planBeginTime'">
          <a-form-item class="tableItem" :name="[index, 'planBeginTime']" :rules="rules.planBeginTime">
            <a-date-picker v-model:value="record[column.dataIndex]" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'planEndTime'">
          <a-form-item class="tableItem" :name="[index, 'planEndTime']" :rules="rules.planEndTime">
            <a-date-picker v-model:value="record[column.dataIndex]" format="YYYY-MM-DD" valueFormat="YYYY-MM-DD" style="width: 100%" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'remark'">
          <a-form-item class="tableItem" :name="[index, 'remark']" :rules="rules.remark">
            <a-input placeholder="请输入备注" v-model:value="record[column.dataIndex]" />
          </a-form-item>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate">
            <a-button @click="addItem(record.key)" type="link">添加</a-button>
            <a-button @click="deleteItem(record.key)" danger type="link">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>
  </a-form>
</template>
<script setup>
  import { onMounted, ref, watch, defineExpose } from 'vue';
  import EmployeeSelect from '/@/components/system/employee-select/index.vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { produceArrangeApi } from '/@/api/business/mes/produce/produce-arrange-api';

  const props = defineProps({
    value: [Array],
  });
  const emit = defineEmits(['update:value', 'change']);
  // 表单标识
  const formPackage = ref();
  //------------------------------表格-----------------------------
  const columns = ref([
    {
      title: '序号',
      dataIndex: 'serialNumber',
      width: 50,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '节点名称(必填)',
      dataIndex: 'nodeName',
      width: 100,
      align: 'center',
    },
    {
      title: '负责人',
      dataIndex: 'headId',
      width: 100,
      align: 'center',
    },
    {
      title: '计划开始时间',
      dataIndex: 'planBeginTime',
      width: 100,
      align: 'center',
    },
    {
      title: '计划结束时间(必填)',
      dataIndex: 'planEndTime',
      width: 100,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 100,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 90,
      align: 'center',
    },
  ]);
  // 通用非空验证器
  const validateRequired = (fieldName) => async (_rule, value) => {
    if (value === '' || value === undefined) {
      return Promise.reject(`请输入${fieldName}`);
    }
    return Promise.resolve();
  };
  // 表单校验规则
  const rules = ref({
    // 节点名称
    nodeName: [{ required: true, message: '请输入节点名称', trigger: 'blur', validator: validateRequired('节点名称') }],
    // 负责人
    headId: [{ required: true, message: '请输入负责人', trigger: 'blur', validator: validateRequired('负责人') }],
    // 计划结束时间
    planEndTime: [{ required: true, message: '请输入计划结束时间', trigger: 'blur', validator: validateRequired('计划结束时间') }],
    //   remark
    remark: [{}],
  });
  // 校验方法
  const handleValidate = async () => {
    let checkRules = await formPackage.value.validate();
  };
  //------------------------------请求-----------------------------
  // 安排信息下拉框
  const selectArrange = ref(null);
  const options = ref([]);
  const arrangeList = ref([]);
  const changeArrange = (value, option) => {
    //   选中发送请求
    queryId(option.id);
  };
  async function queryData() {
    try {
      let data = await produceArrangeApi.queryList();
      data.data.forEach((item) => {
        options.value.push({
          id: item.id,
          value: item.id,
          label: item.name,
        });
      });
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  async function queryId(id) {
    try {
      let data = await produceArrangeApi.queryDetailArrangeInfo(id);
      // 清空数组
      arrangeList.value = [];
      if (data.data.produceArrangeDetailVOS !== null) {
        data.data.produceArrangeDetailVOS.forEach((item) => {
          arrangeList.value.push({
            key: arrangeList.value.length,
            ...item,
          });
        });
      }
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  //------------------------------添加删除-----------------------------
  function addItem(key) {
    const newData = {
      key: arrangeList.value.length,
      nodeName: undefined,
      headId: undefined,
      planBeginTime: undefined,
      planEndTime: undefined,
      remark: undefined,
    };
    arrangeList.value.push(newData);
    arrangeList.value.forEach((item, index) => {
      item.key = index;
      item.serialNumber = index + 1;
    });
  }
  function deleteItem(key) {
    // 查找真正的索引位置
    const index = arrangeList.value.findIndex((item) => item.key === key);
    if (index !== -1) {
      arrangeList.value.splice(index, 1);
      // 重新排序 key 和 serialNumber
      arrangeList.value.forEach((item, idx) => {
        item.key = idx;
        item.serialNumber = idx + 1;
      });
    }
  }
  // 重置方法
  function reset() {
    arrangeList.value = [];
    selectArrange.value = null;
    if (formPackage.value) {
      formPackage.value.resetFields();
    }
  }
  //------------------------------挂载监听事件-----------------------------
  onMounted(() => {
    queryData();
    // 监听props.value的变化，确保新数据也有key
    watch(
      () => props.value,
      (value) => {
        if (value) {
          arrangeList.value = value.map((item, index) => {
            if (item.key === undefined) {
              return { ...item, key: index };
            }
            return item;
          });
        } else {
          arrangeList.value = [];
        }
      },
      { deep: true, immediate: true }
    );
  });
  // 监听arrangeList.value的变化
  watch(
    () => arrangeList.value.length,
    (newValue, oldValue) => {
      const array = arrangeList.value;
      emit('change', array);
      emit('update:value', array);
    },
    {
      immediate: true,
    }
  );
  //------------------------------暴露方法-----------------------------
  defineExpose({
    handleValidate,
    reset,
  });
</script>
<style lang="less" scoped>
  // 表单元素样式
  .tableItem {
    margin-top: 20px;
  }
  .toRequired {
    align-items: center;
    color: #ff4949;
    padding-right: 10px;
  }
</style>

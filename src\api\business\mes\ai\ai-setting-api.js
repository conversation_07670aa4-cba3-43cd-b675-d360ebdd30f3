import { postRequest, getRequest } from '/@/lib/axios';

export const aiSettingApi={

    /**
     * 获取当前助手的配置
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    getAssistantSetting:()=>{
        return getRequest('/ai/llm/assistantConfig/get')
    },

    /**
     * 修改当前助手的配置
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    updateAssistantSetting:(param)=>{
        return postRequest('/ai/llm/assistantConfig/update',param)
    },

    /**
     * 获取当前助手的模型路由配置
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    getModelRouteSetting:()=>{
        return getRequest('/ai/llm/chatRouteConfig/get')
    },

    /**
     * 更新当前助手的模型路由配置
     * @param param
     * @returns {Promise<axios.AxiosResponse<any>>}
     */
    updateModelRouteSetting:(param)=>{
        return postRequest('/ai/llm/chatRouteConfig/update',param)
    },

}

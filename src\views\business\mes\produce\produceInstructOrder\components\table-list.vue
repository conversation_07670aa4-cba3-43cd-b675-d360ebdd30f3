<!--
  * 列表视图
  *
  * @Author:    wxx
  * @Date:      2025-03-21
  * @Copyright  zscbdic
-->
<template>
  <div>
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm" type="primary" size="small" style="height: 30px">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-dropdown>
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item v-for="item in Object.values(PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM).slice(0, -1)" :key="item.value">
                {{ item.label }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            <span>优先级修改</span>
            <DownOutlined />
          </a-button>
        </a-dropdown>
        <a-button @click="confirmForceComplete" type="primary" danger size="small" style="height: 30px">
          <template #icon>
            <AlertOutlined />
          </template>
          强制完工
        </a-button>
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2000 }"
      style="width: 100%"
      @change="handleTableChange"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showForm(record)" type="link">编辑</a-button>
            <a-dropdown>
              <a class="ant-dropdown-link" style="color: orange" @click.prevent>
                下达
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="issuedOrder(record,0)">
                    <a href="javascript:;">下达</a>
                  </a-menu-item>
                  <a-menu-item @click="issuedOrder(record,1)">
                    <a href="javascript:;">反下达</a>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'checked'">
          <a-checkbox v-model:checked="record.checked" @click="onCheck(record)" />
        </template>

        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-preview-container">
            <file-preview :file-list="!_.isEmpty(record.imgUrl) ? [record.imgUrl[0]] : []" type="picture" :width="35" />
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'produceStatus'">
          <span :style="{ color: PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color }" style="font-weight: bold">
            {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
          </span>
        </template>
        <template v-else-if="column.dataIndex === 'produceType'">
          <span>{{ PRODUCE_TYPE_ENUM.getEnum(text).label }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'produceNum'">
          <div :style="{ color: text ? 'black' : '#909399', fontStyle: text ? 'normal' : 'italic' }">
            {{ text ? text : '生产未下达' }}
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'instructNumber'">
          <a-button type="link" @click="showForm(record)">{{ text }}</a-button>
        </template>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">{{ value }}</a-table-summary-cell>
            </template>
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="handlePageChange"
        @showSizeChange="handlePageChange"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <!-- 表单模态框弹窗 -->
    <ProduceInstructOrderForm ref="formRef" @reloadList="queryData" />
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import _ from 'lodash';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { produceInstructOrderApi } from '/@/api/business/mes/produce/produce-instruct-order-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import FilePreview from '/@/components/support/file-preview/index.vue';
  import ProduceInstructOrderForm from '../produce-instruct-order-form.vue';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
    PRODUCE_TYPE_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';

  // 定义props 父传子
  const props = defineProps({
    queryForm: {
      type: Object,
      required: true,
    },
    queryData: {
      type: Function,
      required: true,
    },
    tableData: {
      type: Array,
      required: true,
    },
    tableLoading: {
      type: Boolean,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  });

  // 定义emit 子传父
  const emit = defineEmits(['update:query-form', 'update:table-loading']);
  const newQueryForm = JSON.parse(JSON.stringify(props.queryForm));

  // 表格列定义
  const columns = ref([
    {
      title: '',
      dataIndex: 'checked',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      ellipsis: true,
      width: 80,
      align: 'center',
    },
    {
      title: '单据编号',
      dataIndex: 'instructNumber',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      align: 'center',
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      align: 'center',
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '生产数量',
      dataIndex: 'produceNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '生产状态',
      dataIndex: 'produceStatus',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '生产类型',
      dataIndex: 'produceType',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '交货日期',
      dataIndex: 'deliverTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '下达日期',
      dataIndex: 'issuedTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      align: 'center',
    },
  ]);

  // 分页变化
  const handlePageChange = async (page, pageSize) => {
    emit('update:query-form', newQueryForm);
    await props.queryData();
  };

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();
  function showForm(data) {
    formRef.value.show(data);
  }

  //---------------------------- 交货日期和下达日期的排序  ----------------------------
  // 排序字段映射
  const sortFieldMap = computed(() => ({
    deliverTime: 'deliver_time',
    issuedTime: 'issued_time',
    default: 'create_time',
  }));

  async function handleTableChange(pagination, filters, sorter) {
    // 设置排序字段和顺序
    if (sorter && sorter.field && sortFieldMap.value[sorter.field] && sorter.order) {
      newQueryForm.sortItemList = [
        {
          column: sortFieldMap.value[sorter.field],
          isAsc: sorter.order === 'ascend', // 升序
        },
      ];
    } else {
      // 默认排序
      newQueryForm.sortItemList = [
        {
          column: sortFieldMap.value.default,
          isAsc: false,
        },
      ];
    }

    emit('update:query-form', newQueryForm);
    await props.queryData();
  }

  //----------------------------下达和反下达生产-----------------------------
  async function issuedOrder(record,option) {
    try {
      SmartLoading.show();
      if(option == 0){
        await produceInstructOrderApi.singleOrder(record.id);
        message.success('下达成功');
      }else{
        await produceInstructOrderApi.cancelOrder(record.id);
        message.success('反下达成功');
      }
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 单个删除 ----------------------------
  // 删除确认
  const onDelete = (record) => {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  };
  //请求删除
  async function requestDelete(id) {
    try {
      SmartLoading.show();
      await produceInstructOrderApi.delete(id);
      message.success('删除成功');
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  // ---------------------------- 修改下拉框优先级 ----------------------------
  //优先级
  const priorityStr = ref();
  //选中数据
  const selectedOptions = ref([]);
  //选中ID
  const selectedRowKeys = ref([]);

  //清除选中的数据
  function clearSelected() {
    selectedOptions.value = [];
    selectedRowKeys.value = [];
  }

  //请求修改订单优先级
  async function updateOrder() {
    emit('update:table-loading', true);
    try {
      const param = {
        id: selectedRowKeys.value,
        value: priorityStr.value,
      };
      await produceInstructOrderApi.updateOrder(param);
      message.success('修改成功');
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      emit('update:table-loading', false);
    }
  }

  //获得点击的优先级数据
  function handleMenuClick(key) {
    const { label, value } = PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(key.key);
    //判断是否有选中数据
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择修改的数据');
      return;
    }
    priorityStr.value = value;
    //对选中数据的优先级进行修改
    selectedOptions.value.forEach((item) => {
      item.priority = value;
    });
    updateOrder();
    clearSelected();
  }

  //选中和取消选中数据
  function onCheck(record) {
    const index = selectedRowKeys.value.indexOf(record.id);
    if (index === -1) {
      // 如果ID不存在，添加记录
      selectedRowKeys.value.push(record.id);
      selectedOptions.value.push(record);
    } else {
      // 如果ID存在，移除记录
      selectedRowKeys.value.splice(index, 1);
      selectedOptions.value.splice(index, 1);
    }
  }

  // ---------------------------- 强制完工 ----------------------------

  //确认强制完工提示框
  function confirmForceComplete() {
    let param = selectedRowKeys.value;
    if (param.length === 0) {
      return message.warn('请选择数据');
    }

    Modal.confirm({
      title: '提示',
      content: '确定要强制完工这些数据吗?',
      okText: '确认',
      okType: 'danger',
      onOk() {
        forceComplete(param);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求强制完工
  async function forceComplete(param) {
    emit('update:table-loading', true);
    try {
      const response = await produceInstructOrderApi.forceComplete(param);
      await props.queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      clearSelected();
      emit('update:table-loading', false);
    }
  }

  //------------------------------ 计算总计 ------------------
  const totals = computed(() => ({
    totalProduceNum: props.tableData.reduce((sum, item) => sum + (item.produceNum || 0), 0),
  }));

  //计算空列数
  const summaryColSpans = 7;
  const emptyColumnNum = computed(() => columns.value.length - summaryColSpans - Object.keys(totals.value).length);
</script>

<style scoped>
  .smart-table-btn-block {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .smart-table-operate-block {
    display: flex;
    gap: 8px;
  }

  .smart-table-operate {
    display: flex;
    gap: 8px;
  }

  .smart-query-table-page {
    margin-top: 16px;
    text-align: right;
  }

  .image-preview-container {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }

  .image-preview-container :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
</style>

/**
 * 裁片收发日志 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-11-06 15:25:13
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partDispatchLogApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/partDispatchLog/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/partDispatchLog/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/partDispatchLog/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/partDispatchLog/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/partDispatchLog/batchDelete', idList);
  },

};

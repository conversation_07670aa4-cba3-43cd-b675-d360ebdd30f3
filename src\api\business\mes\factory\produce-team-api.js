/**
 * 生产小组 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-09 10:32:46
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const produceTeamApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/produceTeam/queryPage', param);
  },

  queryAll : () => {
    return getRequest('/produceTeam/all');
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/produceTeam/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/produceTeam/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/produceTeam/delete/${id}`);
  },

  //单个查询
  queryById:(id) =>{
    return getRequest(`/produceTeam/${id}`)
  },
  queryList: (param) => {
    return postRequest('/produceTeam/queryList', param);
  },
};

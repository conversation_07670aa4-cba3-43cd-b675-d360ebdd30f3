<!--
  * 款式品类表
  *
  * @Author:    hzx
  * @Date:      2024-07-05 15:45:34
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加品类'"

      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
      <!--    上级分类需要修改成下拉选项 xx/xx       -->
      <a-form-item label="上级分类"  name="parentId">
        <ClothesStyle v-if="isShow"  v-model:value="form.parentId" placeholder="请选择" :categoryType="Number(emptystring)" @change="getChangeValue" @changelabel="getLabel" @getarray="getArray"/>
        <ClothesStyle v-else v-model:value="form.parentId" :placeholder="placeholder1" 	disabled/>
      </a-form-item>
      <a-form-item label="品类名称"  name="styleName">
        <a-input style="width: 100%" v-model:value="form.styleName" placeholder="品类名称" />
      </a-form-item>

      <a-form-item label="品类代码"  name="styleCode"   >

        <a-input style="width: 100%" v-model:value="form.styleCode" placeholder="品类代码" />
      </a-form-item>
      <a-form-item label="备注"  name="remark">
        <a-textarea style="width: 100%" v-model:value="form.remark" placeholder="可选择填写备注"  />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">提交</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick, computed} from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { styleApi } from '/@/api/business/mes/base/style-api';
import { smartSentry } from '/@/lib/smart-sentry';
// 导入下拉组件
import ClothesStyle from "/@/components/business/mes/item/clothes-style-select/index.vue"
import { sizeTemplateApi } from '/@/api/business/mes/base/size-template-api';


// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);
// ---------------------------下拉组件----------------------
const defaultSizeIdOptions=ref([])

// 拼装函数
function mergeString(data,value){
// 得到对应的拼装出来的字符串 name+info
  return data.map((item)=>({
    value:item[value],
    label:item.templateName+" "+item.sizeInfo
  }))
}

async function defaultSizeIdList(){
  try {
    // 请求树表参数
    let queryForm = {
      queryKey:""
    };
    let responseModel = await sizeTemplateApi.queryAll(queryForm);
    let res=mergeString(responseModel.data,'id')
    defaultSizeIdOptions.value = res;
  } catch (e) {
    message.error('默认尺码D请求失败！');
    smartSentry.captureError("this is the:",e);
  }
}
defaultSizeIdList()


// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);
// 是否是选择当前行
const rowFlag=ref(false)
// 接收parentid
const idvalue=ref(0)
// 传入子组件为空
const emptystring=ref("1")
const placeholder1=ref("")
// 接收子组件传过来的参数
const getValue=ref([])
const isShow=computed(()=>{
  return rowFlag.value===false
})
let listArray=[];
//   接收标记
const signvalue=ref(0)
// 获取得到的数组
const getArray=(value)=>{
  listArray=value
}



const getChangeValue=(value)=>{
  getValue.value=value
  // 获取当前的节点的parentId
  const lastid=getValue.value[getValue.value.length-1]
  // 迭代数组遍历获取对应的id
  form.parentId=lastid;
}
const getLabel=(value)=> {
  //   进行判断，如果label为顶级，修改form.parentId为0
  if (value === '顶级') {
    form.parentId = 0
  }
}

function showModal(categoryType,parentId,rowData,sign) {
  // 进行判断sign为0则说明添加子分类,为1说明编辑
  Object.assign(form, formDefault);
  form.categoryType = categoryType;
  form.parentId=parentId;
  signvalue.value=sign

  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
    //   选择当前行
    rowFlag.value=true
    placeholder1.value=JSON.parse(JSON.stringify(form)).fullName
    if(signvalue.value==0){
      //初始化id值
      form.id=undefined
      idvalue.value=JSON.parse(JSON.stringify(rowData)).id
      // 清空内容
      form.styleName=""
      form.styleCode=""
      form.remark=""
    }else if(signvalue.value==1){
      placeholder1.value = categoryType;
    }

  }

  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });

}

function onClose() {
  Object.assign(form, formDefault);
  // 复原
  rowFlag.value=false
  placeholder1.value="请选择"
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  categoryType :undefined,
  parentId: undefined, //父id
  styleName: undefined, //品类名称
  styleCode: undefined, //品类代码
  defaultSizeId: undefined, //默认尺码表
  templateSizeMessage:undefined,
  remark:"" //备注
};

let form = reactive({ ...formDefault });

const rules = {
  styleName: [{ required: true, message: '品类名称 必填' }],
  styleCode:[{ required: true, message: '品类代码 必填' }]
};

// 提交表单
function onSubmit() {
  formRef.value
      .validate()
      .then(async () => {
        SmartLoading.show();
        try {
          // 编辑功能就是这里出了问题
          if (signvalue.value==0) {
            form.parentId=idvalue.value
            await styleApi.add(form);

          }else if(signvalue.value==1){
            await styleApi.update(form);
          }else{
            // 这里导致新建和添加子类冲突
            await styleApi.add(form);
          }
          message.success(`${form.categoryId ? '修改' : '添加'}成功`);
          emits('reloadList', form.parentId);
          onClose();
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      })
      .catch(() => {
        message.error('参数验证错误，请仔细填写表单数据!');
      }).finally(()=>{

  });
}

defineExpose({
  showModal,
});
</script>

/**
 * 部位表 api 封装
 *
 * @Author:    cjm
 * @Date:      2025-06-23 19:56:55
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/part/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/part/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/part/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/part/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/part/batchDelete', idList);
  },

};

<!--
  * 裁片货位
  *
  * @Author:    cjm
  * @Date:      2024-10-06 20:17:14
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="400px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
        <a-form-item label="所属货架"  name="rackId">
          <a-select
              ref="select"
              v-model:value="form.rackId"
              style="width: 100%"
              :options="rackOptions"
          />
        </a-form-item>
        <a-form-item label="库位编码"  name="binCode">
          <a-input style="width: 100%" v-model:value="form.binCode" placeholder="库位编码" />
        </a-form-item>
        <a-form-item label="容量"  name="capacity">
          <a-input-number style="width: 100%" v-model:value="form.capacity" placeholder="容量" />
        </a-form-item>
        <a-form-item label="货位描述"  name="binDesc">
          <a-input style="width: 100%" v-model:value="form.binDesc" placeholder="货位描述" />
        </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { partStationBinApi } from '/@/api/business/mes/part-station/bin/part-station-bin-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import {partStationRackApi} from "/@/api/business/mes/part-station/rack/part-station-rack-api.js";

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    //先查询所有货架
    queryAllRack()
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 构建下拉数据 ------------------------
  async function queryAllRack(){
    try {
      outstandingOptions((await partStationRackApi.getAll({})).data)
    }catch(e) {
      smartSentry.captureError(e)
    }
  }

  let rackOptions = ref([])
  function outstandingOptions(rackObjs){
    rackOptions.value = []
    rackObjs.map((e)=>(
        rackOptions.value.push({
          value:e.id,
          label:e.rackCode
        })
    ))
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {

              id: undefined, //主键
              rackId: undefined, //所属货架id
              binCode: undefined, //库位编码
              capacity: undefined, //容量
              binDesc:undefined,//货位描述
  };

  let form = reactive({ ...formDefault });

  const rules = {

                  rackId: [{ required: true, message: '所属货架 必填' }],
                  binCode: [{ required: true, message: '库位编码 必填' }],
                  capacity: [{ required: true, message: '容量 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await partStationBinApi.update(form);
      } else {
        await partStationBinApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

<template>
  <a-card hoverable>
    <template #title>
      <a-row :gutter="[8, 16]">
        <a-col>
          <div style="font-size: 17px">裁床单详情</div>
        </a-col>
        <a-col style="display: flex; justify-content: center; align-content: center">
          <ScissorOutlined :style="{ fontSize: '20px' }" />
        </a-col>
      </a-row>
    </template>
    <template #extra>
      <a-button type="primary" @click="onSubmit">保存裁床单</a-button>
    </template>
    <a-button @click="showForm" type="primary" style="margin-bottom: 20px">
      <template #icon>
        <SearchOutlined />
      </template>
      选择指令单
    </a-button>
    <!--  --------------------------头部基本信息---------------------------->
    <a-form :model="cutBedSheetForm" :rules="rules" :label-col="{ span: 5 }" ref="cutBedSheetFormRef">
      <a-row :gutter="[16, 16]">
        <a-col :span="6">
          <a-form-item name="cutNumber" label="裁床单编号" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.cutNumber" placeholder="请输入裁床单编号" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="instructOrderNumber" label="生产指令单编号" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.instructOrderNumber" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="instructOrderName" label="生产指令单名称" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.instructOrderName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="itemNumber" label="物料编号" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.itemNumber" disabled />
          </a-form-item>
        </a-col>
      </a-row>
      <!--  第二行-->
      <a-row :gutter="[16, 16]">
        <a-col :span="6">
          <a-form-item name="itemName" label="物料名称" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.itemName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="model" label="规格型号" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.model" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="unitName" label="物料单位名称" :label-col="labelCol">
            <a-input v-model:value="cutBedSheetForm.unitName" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item name="cutNum" label="床次" :label-col="labelCol">
            <cut-bed-detail v-model:value="cutBedSheetForm.cutNum" :produceInstructOrderId="cutBedSheetForm.instructOrderId" disabled />
          </a-form-item>
        </a-col>
      </a-row>
      <!--  第三行-->
      <a-row :gutter="[16, 16]">
        <a-col :span="6">
          <a-form-item name="cutterId" label="裁剪人" :label-col="labelCol">
            <EmployeeSelect v-model:value="cutBedSheetForm.cutterId" @change="onCutterChange" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <!--  --------------------------中间信息---------------------------->
    <a-tabs v-model:activeKey="tabValue">
      <a-tab-pane tab="自定义编菲" key="customize">
        <cutBedCustomize ref="cutBedCustomizeRef" v-model:colorSizeForm="colorSizeForm" :colorData="colorData" :sizeData="sizeData" />
      </a-tab-pane>
      <a-tab-pane tab="按比例编菲" key="proportion">
        <addBedProportion
          ref="cutBedProportionRef"
          :sizeList="sizeData"
          :colorList="colorData"
          :produceInstructOrderId="cutBedSheetForm.instructOrderId"
        />
      </a-tab-pane>
    </a-tabs>
  </a-card>
  <produce-instruct-order-table-select-modal ref="formRef" @select-data="selectData" />
  <FeTicketBodyPart ref="addPartModal" @reloadList="saveBodyAndCutBedSheet" />
  <FeTicketDownload ref="feTicketDownloadRef" />
  <ProportionViewModal ref="proportionViewModalRef" @submit="saveBodyAndProportion" />
</template>
<script setup>
  import { ref, watch } from 'vue';
  import ProduceInstructOrderTableSelectModal from '/@/components/business/mes/produce/produce-instruct-order-table-select-modal/index.vue';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { message } from 'ant-design-vue';
  import { produceInstructOrderClothesApi } from '/@/api/business/mes/produce/produce-instruct-order-clothes-api.js';
  import { cutBedSheetApi } from '/@/api/business/mes/tailor/cut-bed-sheet-api.js';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api.js';
  import EmployeeSelect from '/@/components/system/employee-obj-select/index.vue';
  import FeTicketBodyPart from '/@/views/business/mes/tailor/cutBedSheet/components/feTicket-bodypart-modal/index.vue';
  import cutBedCustomize from '/@/views/business/mes/tailor/cutBedSheet/components/addBedCustomize.vue';
  import addBedProportion from '/@/views/business/mes/tailor/cutBedSheet/components/addBedProportion.vue';
  import FeTicketDownload from '/@/views/business/mes/tailor/cutBedSheet/components/fetTicket-download/index.vue';
  import ProportionViewModal from '/@/views/business/mes/tailor/cutBedSheet/components/proportion-view-modal/index.vue';
  import cutBedDetail from '/@/components/business/mes/tailor/cut-bed-detail/index.vue';
  //--------------------------------头部基本信息----------------------------
  const cutBedSheetFormRef = ref(); //裁床单表单ref 用于验证规则
  //自定义编非数据
  const cutBedSheetForm = ref({
    id: undefined, //
    cutNumber: undefined, // 裁床单编号 可编辑 可选
    cutSheetName: undefined, // 裁床单名称 可编辑 可选
    instructOrderNumber: undefined, // 生产指令单编号 不能编辑 可选
    instructOrderName: undefined, // 生产指令单名称 不能编辑 可选
    instructOrderId: undefined, // 生产指令单id 不能编辑 可选
    itemId: undefined, // 物料id 不能编辑 可选
    itemNumber: undefined, // 物料编号 不能编辑 可选
    model: undefined, // 规格型号 不能编辑 可选
    positions: undefined, //部位 不能为空
    itemName: undefined, // 物料名称 不能编辑 可选
    unitId: undefined, // 物料单位id 不能编辑 可选
    unitName: undefined, // 物料单位名称 不能编辑 可选
    attribute: undefined, // 物料属性;0面料，1其他，2成衣 不能编辑 可选
    category: undefined, // 物料类型;0半成品 1成品 不能编辑 可选
    cutNum: undefined, // 床次 请求接口 不能编辑 可选
    cutterId: undefined, // 裁剪人id 可选
    cutter: undefined, // 裁剪人 可选
    details: [], // 裁床单详情 可选
  });
  //按比例编菲数据
  const proportionForm = ref({});
  //添加部位modal的ref对象
  const addPartModal = ref();
  function showModal() {
    addPartModal.value.show(cutBedSheetForm.value.instructOrderId);
  }
  // 定义校验规则
  const rules = {
    cutterId: [{ required: true, message: '裁剪人不能为空' }],
  };
  // 表单标签布局
  const labelCol = ref({
    span: 9,
  });

  //-------------------------------标签监听-------------------------------
  const tabValue = ref('customize');
  const cutBedCustomizeRef = ref();
  const cutBedProportionRef = ref();
  watch(
    () => tabValue.value,
    (newVal) => {
      if (newVal == 'proportion') {
        if (cutBedProportionRef.value) {
          cutBedProportionRef.value.initData();
        }
      } else if (newVal == 'customize') {
        if (cutBedCustomizeRef.value) {
          cutBedCustomizeRef.value.resetTable();
        }
      }
    }
  );
  //-------------------------------选择指令单模态框-------------------------------
  const formRef = ref();
  // 触发模态框
  function showForm(data) {
    formRef.value.showModal(data);
  }
  // 接收旧oldData
  const oldData = ref({});
  // 接受模态框数据
  const selectData = (data) => {
    Object.keys(cutBedSheetForm.value).forEach((item) => {
      if (item in data) {
        cutBedSheetForm.value[item] = data[item] ?? undefined;
      }
    });

    // 补充赋值instructOrderId,instructOrderName,instructOrderNumber
    cutBedSheetForm.value.instructOrderId = data.id;
    cutBedSheetForm.value.instructOrderName = data.name;
    cutBedSheetForm.value.instructOrderNumber = data.instructNumber;
    // 清空颜色，尺寸数组
    if (oldData.value !== data) {
      oldData.value = data;
      colorData.value = [];
      sizeData.value = [];
      colorSizeForm.value = [];
      cutBedSheetForm.value.details = [];
      // 重置子组件中的列索引和动态列数组
      if (cutBedCustomizeRef.value) {
        cutBedCustomizeRef.value.resetTable();
      }
      if (cutBedProportionRef.value) {
        cutBedProportionRef.value.resetDate();
      }
    }
    //获取信息
    queryColor();
    queryProduceOrder();
    querySize();
  };
  // 请求床次
  async function queryProduceOrder() {
    try {
      let newBedNumData = await cutBedSheetApi.queryNewBedNum(cutBedSheetForm.value.instructOrderId);
      cutBedSheetForm.value.cutNum = newBedNumData.data;
    } catch (error) {
      message.error('请求床次失败');
      smartSentry.captureError(error);
    }
  }
  //--------------------------------中间信息----------------------------

  const colorData = ref([]);
  const sizeData = ref([]);
  const colorSizeForm = ref([]); //颜色数组
  const dataIndexList = ref([]); //动态列数组
  // 请求颜色
  async function queryColor() {
    let color = await produceInstructOrderClothesApi.queryClothesColorList(cutBedSheetForm.value.id);
    color.data.forEach((item) => {
      colorData.value.push({
        clicknum: 0,
        value: item,
      });
    });
  }
  // 请求尺寸
  async function querySize() {
    let size = await produceInstructOrderClothesApi.queryClothesSizeList(cutBedSheetForm.value.id);
    sizeData.value = size.data;
  }

  function onCutterChange(selectedEmployee) {
    cutBedSheetForm.value.cutter = selectedEmployee.actualName;
  }
  // -------------------------------提交保存-------------------------------
  const proportionViewModalRef = ref(); //比例预览模态框
  // 提交和显示生成菲票的模态框
  async function onSubmit() {
    let arr = [];
    // 验证表单
    await cutBedSheetFormRef.value.validateFields();
    if (tabValue.value == 'customize') {
      dataIndexList.value = cutBedCustomizeRef.value.dataIndexList;
      colorSizeForm.value.forEach((item) => {
        // 判断属性是否存在,如果对象中的属性等于数组中的元素，那么遍历赋值对象
        dataIndexList.value.forEach((dataIndex) => {
          if (dataIndex in item) {
            // 转换字符串dataindex
            let sizeString = '';
            sizeData.value.forEach((size) => {
              if (dataIndex.split('-')[0] === size) {
                sizeString = size;
              }
            });
            let obj = {
              styleColor: item.styleColor,
              size: sizeString,
              num: item[dataIndex],
            };
            arr.push(obj);
          }
        });
      });
      cutBedSheetForm.value.details = arr;
      cutBedSheetForm.value.feTicketAttributes = arr;
      // 表单验证
      if (!cutBedSheetForm.value.id) {
        message.error('裁床单信息为空');
        return;
      }
      if (arr.length === 0 || !arr.every((item) => item.num && !isNaN(Number(item.num)))) {
        message.error('衣物信息错误');
        return;
      }
      // 验证通过，显示模态框
      showModal();
    } else if (tabValue.value == 'proportion') {
      const sizeList = cutBedProportionRef.value.sizeRow.map(({ size, proportion }) => ({
        size,
        ratio: proportion,
      }));
      // 获取颜色层数数据
      const colorList = cutBedProportionRef.value.colorRow.map(({ color, layers }) => ({
        color: color,
        num: layers,
      }));
      const partList = cutBedProportionRef.value.selectParts.map((part) => ({
        name: part,
      }));
      proportionForm.value = {
        colorList,
        sizeList,
        partList,
        instructOrderId: cutBedSheetForm.value.instructOrderId,
        cutBedSheetNumber: cutBedSheetForm.value.cutNumber,
        cutBedSheetName: cutBedSheetForm.value.cutSheetName,
        remark: cutBedSheetForm.value.remark,
      };
      if (!cutBedSheetForm.value.id) {
        message.error('裁床单信息为空');
        return;
      }
      //检测是否有尺码比例数据
      const hasValidProportion = sizeList.some((size) => size.ratio > 0);
      if (!hasValidProportion) {
        message.error('至少需要一个尺码的比例大于0');
        return;
      }
      //检测是否有颜色和颜色层数数据
      if (colorList.length == 0) {
        message.error('颜色信息为空');
        return;
      }
      const hasValidLayers = colorList.some((color) => color.num > 0);
      if (!hasValidLayers) {
        message.error('至少需要一个颜色的层数大于0');
        return;
      }
      //获取数据传入按比例预览模态框
      const viewData = (await feTicketApi.preview(proportionForm.value)).data;
      proportionViewModalRef.value.show(viewData);
    }
  }
  // -------------------------------生成菲票-------------------------------
  //应用下载菲票组建
  const feTicketDownloadRef = ref();
  const open = ref(false);
  const tickForm = ref({
    ids: [],
    parts: [],
    processes: [],
    template: undefined,
  });
  //裁床单id
  const cutBedSheetId = ref(0);
  //--------------------------------自定义生成菲票-------------------------------
  //输入身体部位信息
  async function saveBodyAndCutBedSheet(data) {
    try {
      // 数据验证
      if (data.length == 0 || data.length == undefined) {
        message.error('部位信息为空');
        return;
      }
      
      // 业务逻辑
      cutBedSheetForm.value.positions = data;
      //抽屉
      open.value = true;
      //添加裁床单
      let respCutBedSheet = await cutBedSheetApi.add(cutBedSheetForm.value);
      cutBedSheetId.value = respCutBedSheet.data;
      //添加裁床单id
      cutBedSheetForm.value.cutBedSheetId = cutBedSheetId.value;
      cutBedSheetForm.value.cutBedSheetNumber = cutBedSheetForm.value.cutNumber;
      cutBedSheetForm.value.cutBedSheetName = cutBedSheetForm.value.cutSheetName;
      //添加菲票
      let respFeTicket = await feTicketApi.add(cutBedSheetForm.value);
      tickForm.value.ids = respFeTicket.data;
      feTicketDownloadRef.value.show(tickForm.value);
      //添加裁床单
      message.success('保存成功');
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
  // -------------------------------比例提交生成菲票-------------------------------
  async function saveBodyAndProportion() {
    try {
      //获取提交生成的指令单id
      const result = await feTicketApi.addByRatio(proportionForm.value);
      if (result) {
        message.success('保存成功');
        const cutBedSheetId = result.data?.id;
        //查询生成的菲票
        const response = await feTicketApi.queryList({ cutBedSheetId });
        if (response.data && response.data.length > 0) {
          // 提取ID到ids数组
          const ids = response.data.map((item) => item.id);
          const parts = cutBedProportionRef.value.selectParts || [];
          tickForm.value = {
            ids: ids,
            parts: parts,
            processes: [],
            template: undefined,
          };
          // 显示菲票下载对话框
          feTicketDownloadRef.value.show(tickForm.value);
        } else {
          message.info('未查询到相关菲票数据');
        }
      }
    } catch (error) {
      smartSentry.captureError(error);
    }
  }
</script>

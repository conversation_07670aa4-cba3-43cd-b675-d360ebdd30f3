<template>
  <a-modal v-model:open="visible" :width="800" title="导入" @cancel="closeModal" @ok="onSelectItem">
    <div class="import-modal-content">
      <div class="import-modal-buttons">
        <p class="ant-upload-hint" style="font-size: 16px;" download>
          1、下载导入模版
        </p>
        <p style="color:grey; font-size:16px">· 下载模版后，请按照导入说明填写完善表格中的内容</p>
        <p style="color:grey; font-size:16px">· 当上传的表格有多个Sheet时，默认只导入第一个Sheet</p>
        <a-button @click="downloadFile" size="small">
          <LayoutOutlined />下载模版表格
        </a-button>
      </div>
      <div class="ant-upload">
        <p class="ant-upload-hint" style="padding: 20px;font-size: 16px;">
          2、上传完善好的表格
        </p>
        <a-upload-dragger
          v-model:fileList="fileList"
          name="file"
          :multiple="false" 
          :before-upload="beforeUpload"
          :custom-request="customRequest"
          @change="handleChange"
          class="import-upload-dragger"
          style="background-color: white;width:90%;margin:30px auto;margin-top:-20px;font-size: 16px;"
        >
          <p class="ant-upload-drag-icon">
            <inbox-outlined/>
          </p>
          <p class="ant-upload-text">把文件拖到此处或点击此处上传文件</p>
          <p class="ant-upload-hint">
            支持的文件类型（.xlsx），大小不超过10MB
          </p>
        </a-upload-dragger>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { InboxOutlined, LayoutOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { ref, nextTick } from 'vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
// ------------------------ 事件 ------------------------
const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visible = ref(false);
// 定义 props
const props = defineProps({
  downloadTemplateApi: {
    type: Function,
  },
  importfileApi: {
    type: Function,
  }
});

function show() {
  visible.value = true;
  // 重置文件列表和 formData 数组
  fileList.value = [];
  formDataArray.value = [];
}
function closeModal() {
  visible.value = false;
}

const fileList = ref([]);
const formDataArray = ref([]);

// 下载模版文件
async function downloadFile() {
  try {
    await props.downloadTemplateApi();
  } catch (err) {
    message.error('下载模板文件失败，请重试!');
  }
}

// 上传前校验
function beforeUpload(file) {
  const isXlsx = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  if (!isXlsx) {
    message.error('仅支持 .xlsx 文件格式.');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!');
    return false;
  }
  return true;
}

// 自定义上传请求
const customRequest = async (options) => {
  // 开始加载
  SmartLoading.show();
  try {
    // 在这里仅进行校验，不实际调用上传接口
    const file = options.file;
    const isValid = beforeUpload(file);  // 使用 await 确保校验完成

    if (isValid) {
      // 设置文件状态为 done
      options.onSuccess('文件上传成功'); // 模拟成功
      await nextTick();  // 使用 await 确保 DOM 更新完成
      const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
      if (fileIndex !== -1) {
        fileList.value[fileIndex].status = 'done';
      }

      // 创建 FormData 并添加到数组
      const formData = new FormData();
      formData.append('file', file); // 确保文件字段名为 'file'
      formDataArray.value.push({ file, formData });
    } else {
      // 设置文件状态为 error
      options.onError(new Error('文件不符合要求')); // 模拟失败
      await nextTick();  // 使用 await 确保 DOM 更新完成
      const fileIndex = fileList.value.findIndex(f => f.uid === file.uid);
      if (fileIndex !== -1) {
        fileList.value[fileIndex].status = 'error';
      }
    }
  } catch (error) {
    // 处理 beforeUpload 中的异常
    console.error('文件校验失败:', error);
    options.onError(new Error('文件校验失败'));
  }finally{
    // 结束加载
    SmartLoading.hide();
  }
};

// 处理文件上传变化
const handleChange = (info) => {
  const status = info.file.status;
  if (status === 'done') {
    message.success(`${info.file.name} 文件校验通过.`);
  } else if (status === 'error') {
    message.error(`${info.file.name} 文件校验失败.`);
  } else if (status === 'removed') {
    // 处理文件取消
    const fileIndex = formDataArray.value.findIndex(item => item.file.uid === info.file.uid);
    if (fileIndex !== -1) {
      formDataArray.value.splice(fileIndex, 1);
    }
  }
};

// 提交表单
async function onSelectItem() {
  SmartLoading.show();
  // 检查 formDataArray 是否为空
  if (formDataArray.value.length === 0) {
    message.error('请先上传文件!');
    return; // 阻止模态框关闭
  }

  let allFilesUploaded = true;

  for (const { file, formData } of formDataArray.value) {
    // 前端校验文件格式和大小
    const isValid = beforeUpload(file);
    if (!isValid) {
      message.error(`${file.name} 文件不符合要求，请检查文件格式和大小!`);
      allFilesUploaded = false;
      continue;
    }

    try {
      // 调用 importfileApi
      const importResponse = await props.importfileApi(formData);

      if (importResponse.ok) {
        message.success(`${file.name} 文件上传成功`);
      } else {
        message.error(`${file.name} 文件上传失败: ${importResponse.msg || '未知错误'}`);
        allFilesUploaded = false;
      }
        if (allFilesUploaded) {
      message.success('所有文件上传成功');
      emits('reloadList');
      closeModal();
  }
    } catch (err) {
      console.error('文件上传失败:', err); // 添加日志记录
      message.error(`${file.name} 文件上传失败，请检查文件数据!`);
      allFilesUploaded = false;
    }finally{
      SmartLoading.hide();
    }
  }

  
}

// 暴露 show 方法
defineExpose({
  show,
});
</script>

<style scoped>
.import-modal-buttons {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 10px;
}
.ant-upload {
  background-color: #f5f5f5;
  border-radius: 10px;
}
</style>
<template>
  <div class="top-card">
    <div class="avatar-wrapper">
      <a-avatar :size="54" :src="defaultAvatarImg"/>
      <div class="avatar-glow"></div>
    </div>
    <div class="content">
      <h2 class="title">你好，我是小裁</h2>
      <p class="description">我是一位专业的服装生产管理助手,可以帮您解答生产计划、工艺流程、质量管理等各类问题。让我们一起提升生产效率,打造卓越品质!</p>
    </div>
  </div>
  
 
</template>

<script setup>
import { ref, h } from 'vue';
import defaultAvatarImg from '/@/assets/images/ai/avatar.png';
import { 
  BulbOutlined,
  InfoCircleOutlined,
  ToolOutlined,
  SafetyOutlined,
} from '@ant-design/icons-vue';


</script>

<style scoped lang="less">
.top-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: linear-gradient(to right, #f2f9fe, #f6f3ff);
  border-radius: 12px;
  margin-bottom: 20px;

  .avatar-wrapper {
    position: relative;
    margin-right: 20px;

    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .avatar-glow {
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, #7546c9, #2b5adc);
      border-radius: 50%;
      z-index: -1;
      opacity: 0.5;
      filter: blur(4px);
    }
  }

  .content {
    flex: 1;

    .title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1f1f1f;
      line-height: 1.4;
    }

    .description {
      margin: 4px 0 0;
      font-size: 14px;
      color: #666;
      line-height: 1.6;
    }
  }
}


</style>

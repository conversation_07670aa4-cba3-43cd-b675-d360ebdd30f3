<template>
    <a-modal
      title="新增物料"
      width="1000px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
    >
      <div>
        <a-tabs
          v-model:activeKey="activeKey"
          tab-position="left"
          @tabScroll="callback"
        >
          <a-tab-pane tab="基本信息">
            <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
              <strong>基本信息</strong>
            </a-row>
            <a-form ref="formRef" :model="form" :rules="rules" v-bind="formItemLayout">
                <a-row>
                  <a-col :span="11">
                    <a-form-item label="物料编号" name="number">
                      <a-input style="width: 100%" v-model:value="form.number" placeholder="请输入,忽略将自动生成"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="11">
                    <a-form-item label="物料名称" name="name">
                      <a-input style="width: 100%" v-model:value="form.name" placeholder="物料名称"/>
                    </a-form-item>
                  </a-col>
                  <a-col :span="11">
                    <a-form-item label="物料分类" name="typeId">
                      <a-cascader
                          style="width: 100%;"
                          :options="itemTypeTree"
                          v-model:value="form.typeId"
                          placeholder="请选择物料分类"
                      />
                    </a-form-item>
                  </a-col>
                  <a-col :span="11">
                    <a-form-item label="单位" name="unitId">
                      <a-select
                          v-model:value="form.unitId"
                          style="width: 100%"
                          :options="unitList"
                          placeholder="请选择单位"
                      />
                    </a-form-item>
                  </a-col>

                  <a-col :span="11">
                    <a-form-item label="规格型号" name="model">
                      <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号"/>
                    </a-form-item>
                  </a-col>

                  <a-col :span="11">
                    <a-form-item label="供应商" name="supplierId">
                      <a-select
                          v-model:value="form.supplierId"
                          style="width: 100%"
                          :options="supplierList"
                          placeholder="请选择供应商"
                      />
                    </a-form-item>
                  </a-col>

                  <a-col :span="11">
                    <a-form-item label="价格" name="price">
                      <a-input-number style="width: 100%" v-model:value="form.price" placeholder="价格" :precision="2" :min="0">
                        <template #addonAfter>
                          ¥
                        </template>
                      </a-input-number>
                    </a-form-item>
                  </a-col>

                  <a-col :span="11">
                    <a-form-item label="停用标识" name="enableFlag">
                      <a-radio-group v-model:value="form.enableFlag" button-style="solid" style="width: 90%">
                        <a-radio-button :value="false">启用</a-radio-button>
                        <a-radio-button :value="true">停用</a-radio-button>
                      </a-radio-group>
                    </a-form-item>
                  </a-col>
                </a-row>
                <a-row>
                <a-col style="width: 150%; margin-left: -16%;">
                    <a-form-item label="图片" name="imgUrl">
                      <div>
                      <file-upload
                      style="padding:10px 10px 0 10px;"
                      @change="form.imgUrl = $event"
                      :default-file-list="form.imgUrl"
                      :maxUploadSize=10
                      :accept="'.jpg,.jfif,.bmp,.png,.gif'"
                      :maxSize=5
                    />
                    <span style="margin-left: 10px; color:rgba(0, 0, 0, 0.3);">说明：一次最多可上传多张照片，最多支持上传10张，每张图片大小不超过5M，格式为：jpg、jfif、gif、png、bmp</span>
                  </div>
                    </a-form-item>
                </a-col>
            </a-row>
              </a-form>
          </a-tab-pane>
        </a-tabs>
      </div>
      <template #footer>
        <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
        </a-space>
      </template>
    </a-modal>
  </template>

<script setup>
import {nextTick, onMounted, reactive, ref} from "vue";
import FileUpload from "/@/components/support/file-upload/index.vue";
import {itemTypeApi} from '/@/api/business/mes/item/item-type-api.js';
import {smartSentry} from "/@/lib/smart-sentry.js";
import {unitApi} from "/@/api/business/mes/base/unit-api.js";
import {supplierApi} from "/@/api/business/mes/base/supplier-api.js";
import {message} from "ant-design-vue";
import {SmartLoading} from "/@/components/framework/smart-loading/index.js";
import {itemApi} from "/@/api/business/mes/item/item-api.js";

// ------------------------ 显示与隐藏 ------------------------
const emits = defineEmits(['reloadList']);

// 是否显示
const visibleFlag = ref(false);

function show() {
  Object.assign(form, formDefault);
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

const formItemLayout = {
  labelCol: {
    xs: {span: 24},
    sm: {span: 7},
  },
  wrapperCol: {
    xs: {span: 24},
    sm: {span: 17},
  },
};
// ------------------------ 表单 ------------------------
// 组件ref
const formRef = ref();

// 初始化表单
const formDefault = {
  id: undefined,
  typeId: [],
  name: undefined,
  supplierId: undefined,
  model: undefined,
  skuNumber: undefined,
  number: undefined,
  unitId: undefined,
  enableFlag: false,
  price: undefined,
  category: "0",
  attribute: "1",
  imgUrl: [],
};

let form = reactive({...formDefault});

const rules = {
  name: [{required: true, message: '物料名称 必填'}],
  unitId: [{required: true, message: '单位 必填'}],
  enableFlag: [{required: true, message: '停用标识 必填'}],
};
onMounted(() => {
  queryItemType();
  queryUnitList();
  querySupplierList();
});

const itemTypeTree = ref([]); // 物料分类
const unitList = ref([]); // 单位
const supplierList = ref([]); // 供应商

async function queryItemType() {
  try {
    let queryResult = await itemTypeApi.queryCategoryTree({});
    // 确保每个节点有 label 和 value 字段
    const formattedData = formatItemTypeTree(queryResult.data);
    itemTypeTree.value = formattedData;
    
  } catch (e) {
    smartSentry.captureError(e);
  }
}
function formatItemTypeTree(data) {
  return data.map(item => ({
    value: item.id,
    label: item.name,
    children: item.children ? formatItemTypeTree(item.children) : undefined
  }));
}

async function queryUnitList() {
  try {
    let queryResult = await unitApi.querySelect({});
    unitList.value = queryResult.data.map(item => ({
      value: item["id"],
      label: item["name"]
    }));
  } catch (e) {
    smartSentry.captureError(e);
  }
}

async function querySupplierList() {
  try {
    let queryResult = await supplierApi.queryAll({});
    supplierList.value = queryResult.data.map(item => ({
      value: item["id"],
      label: item["name"]
    }));
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建API
async function save() {
  SmartLoading.show();
  try {
    // 只传递接口要求的字段
    const dataToSend = {
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
      name: form.name,
      supplierId: form.supplierId,
      model: form.model,
      skuNumber: form.skuNumber,
      number: form.number,
      price: form.price,
      unitId: form.unitId,
      enableFlag: form.enableFlag,
      category: form.category,
      attribute: form.attribute,
      imgUrl: form.imgUrl,
    };
    await itemApi.add(dataToSend);
    
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>
<style scoped>
</style>

/**\
 * 获取三方登陆配置api
 */
import { getRequest, postRequest } from '/src/lib/axios';

export const thirdAuthApi = {


    // 绑定并登陆
    bindAndLogin: (param) => {
        return postRequest(`/userThirdAuth/bindAndLogin`, param);
    },
    // 查询个人绑定信息
    queryBindInfo: () => {
        return getRequest(`/userThirdAuth/queryBindInfo/`);
    },
    //取消绑定
    cancelBind: (param) => {
        return getRequest(`/userThirdAuth/cancelBind/${param}`);
    }
}

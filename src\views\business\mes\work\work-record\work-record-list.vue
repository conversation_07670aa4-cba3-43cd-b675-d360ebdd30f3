<!--
  * 报工记录
  *
  * @Author:    cjm
  * @Date:      2024-07-22 20:33:41
  * @Copyright  cjm
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="工人名称" class="smart-query-form-item">
        <a-select
          v-model:value="queryForm.workerId"
          show-search
          placeholder="工人名称"
          style="width: 100px"
          :options="workerOptions"
          :filter-option="filterOption"
        />
      </a-form-item>
      <a-form-item label="报工日期" class="smart-query-form-item">
        <a-range-picker :presets="defaultTimeRanges" style="width: 240px" @change="onChangeRecordTime" v-model:value="reportTime" />
      </a-form-item>
      <a-form-item label="审核状态" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.auditFlag" placeholder="审核状态">
          <a-select-option
            v-for="item in Object.values(Work_Record_AuditFlag_ENUM).filter((item) => typeof item !== 'function' && item.desc !== '未知')"
            :key="item.value"
            :value="item.value"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="记录状态" class="smart-query-form-item">
        <a-select style="width: 150px" v-model:value="queryForm.recordStatus" placeholder="记录状态">
          <a-select-option
            v-for="item in Object.values(Work_Record_Status_ENUM).filter((item) => typeof item !== 'function' && item.desc !== '未知')"
            :key="item.value"
            :value="item.value"
            >{{ item.desc }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 start ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-dropdown>
          <a-button type="primary" @click="showAssistWorkReportForm">
            <span>协助报工</span>
            <PlusOutlined />
          </a-button>
        </a-dropdown>
        <a-dropdown>
          <template #overlay>
            <a-menu @click="handleWorkRecord">
              <a-menu-item :key="Work_Record_AuditFlag_ENUM.getEnum(`1`).value">
                {{ Work_Record_AuditFlag_ENUM.getEnum(`1`).label }}
              </a-menu-item>
              <a-menu-item :key="Work_Record_AuditFlag_ENUM.getEnum(`2`).value">
                {{ Work_Record_AuditFlag_ENUM.getEnum(`2`).label }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            <span>修改审核状态</span>
            <DownOutlined />
          </a-button>
        </a-dropdown>

        <a-dropdown>
          <template #overlay>
            <a-menu @click="handleStatusClick">
              <a-menu-item v-for="item in Object.values(Work_Record_Status_ENUM).filter((item) => typeof item !== 'function' && item.desc !== '未知')" :key="item.value">
                {{ item.label }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            <span>修改记录状态</span>
            <DownOutlined />
          </a-button>
        </a-dropdown>
      </div>
    </a-row>
    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2000 }"
      @change="handleTableChange"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <template v-if="record.auditFlag === '0'">
            <div class="smart-table-operate">
              <a-button @click="showForm(record, { disnum: false })" type="link">改数量</a-button>
              <a-button @click="showForm(record, { disprocess: false })" type="link">改工序</a-button>
              <a-button @click="showForm(record)" type="link">修改</a-button>
            </div>
          </template>

          <template v-else-if="record.auditFlag !== '0'">
            <div class="smart-table-operate">
              <a-button @click="showForm(record)" type="link">查看</a-button>
            </div>
          </template>
        </template>

        <template v-else-if="column.dataIndex === 'checked'">
          <a-checkbox v-model:checked="record.checked" @click="onCheck(record)" />
        </template>

        <template v-else-if="column.dataIndex === 'recordStatus'">
          <a-tag :color="Work_Record_Status_ENUM.getEnum(text).color">{{ Work_Record_Status_ENUM.getEnum(text).label }}</a-tag>
        </template>

        <template v-else-if="column.dataIndex === 'auditFlag'">
          <a-tag :color="Work_Record_AuditFlag_ENUM.getEnum(String(record.auditFlag)).color">{{
            Work_Record_AuditFlag_ENUM.getEnum(String(record.auditFlag)).label
          }}</a-tag>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <WorkRecordForm ref="formRef" @reloadList="queryData" />
  </a-card>
  <AssistWorkReportForm ref="assistWorkReportFormRef" @reloadList="queryData" />
</template>
<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/src/components/framework/smart-loading';
  import { workRecordApi } from '/src/api/business/mes/work/work-record-api.js';
  import { workAuditApi } from '/src/api/business/mes/work/work-audit-api.js';
  import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import WorkRecordForm from '/src/views/business/mes/work/work-record/component/work-record-form.vue';
  import { employeeApi } from '/src/api/system/employee-api';
  import { defaultTimeRanges } from '/src/lib/default-time-ranges.js';
  import AssistWorkReportForm from '/src/views/business/mes/work/work-record/component/assist-work-report-form.vue';
  import { Work_Record_AuditFlag_ENUM, Work_Record_Status_ENUM } from '/src/constants/business/mes/work/work-record-const.js';
  // import {ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM} from "/@/constants/business/mes/item/item-const.js";
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '',
      dataIndex: 'checked',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '生产指令单编号',
      dataIndex: 'produceInstructOrderNumber',
      ellipsis: true,
      width: 100,
    },

    {
      title: '裁床单编号',
      dataIndex: 'cutBedSheetNumber',
      ellipsis: true,
      width: 100,
    },
    {
      title: '床次',
      dataIndex: 'cutNum',
      ellipsis: true,
      width: 50,
    },

    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      width: 150,
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      width: 100,
    },
    {
      title: '款式颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      width: 120,
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      width: 50,
    },

    {
      title: '工人姓名',
      dataIndex: 'workerName',
      ellipsis: true,
      width: 80,
    },

    {
      title: '扎号',
      dataIndex: 'tieNum',
      ellipsis: true,
      width: 50,
    },

    {
      title: '工序名称',
      dataIndex: 'processName',
      ellipsis: true,
      width: 80,
    },
    {
      title: '报工数量',
      dataIndex: 'workQuantity',
      ellipsis: true,
      width: 80,
    },
    {
      title: '单价价格',
      dataIndex: 'price',
      ellipsis: true,
      width: 80,
    },
    {
      title: '报工时间',
      dataIndex: 'reportTime',
      ellipsis: true,
      defaultSortOrder: 'descend',
      width: 150,
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '审核状态',
      dataIndex: 'auditFlag',
      ellipsis: true,
      align: 'center',
      width: 70,
    },
    {
      title: '记录状态',
      dataIndex: 'recordStatus',
      ellipsis: true,
      width: 65,
    },

    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
      align: 'center',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    workerId: undefined, //工人ID号
    recordStatus: undefined, //记录状态;0正常 1作废
    auditFlag: undefined, //审核状态;0未审核 1已审核recordStatus
    startTime: undefined, //开始时间
    endTime: undefined, //结束时间
    sortItemList: [{ column: 'create_time', isAsc: false }],
    pageNum: 1,
    pageSize: 10,
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  // 报工日期范围
  const reportTime = ref([]);
  // -----------------添加报工单-----------------
  const assistWorkReportFormRef = ref();
  function showAssistWorkReportForm() {
    assistWorkReportFormRef.value.show();
  }
  //-------------- 工人名称下拉框 -------------------
  const workerOptions = ref([]);
  async function queryWorker() {
    try {
      let res = await employeeApi.queryAll({});
      workerOptions.value = res.data.map((item) => {
        return {
          value: item.employeeId,
          label: item.actualName,
        };
      });
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  //工人名称下拉框搜索逻辑
  const filterOption = (input, option) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  //-------------- 查询时间 ------------------
  function onChangeRecordTime(dates, dateStrings) {
    queryForm.startTime = dateStrings[0];
    queryForm.endTime = dateStrings[1];
  }
  //-------------- 记录状态 -------------------
  async function handleStatusClick(item) {
    tableLoading.value = true;
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择修改的数据');
      tableLoading.value = false;
      return;
    }
    let param = {
      ids: selectedRowKeys.value,
      status: item.key,
    };
    try {
      await workRecordApi.changeStatus(param);
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      selectedRowKeys.value = [];
      selectedOptions.value = [];
      await queryData();
      tableLoading.value = false;
    }
  }
  //处理表格时间顺序变化事件
  async function handleTableChange(pagination,filters,sorter){
      if(sorter&&sorter.field==='reportTime'){
        queryForm.sortItemList = 
        [{
          column:'report_time',
          isAsc:sorter.order==='ascend'
        }];
      }
      if(!sorter.order){
        queryForm.sortItemList=
        [{
          column:'create_time',
          isAsc:false
        }]
      }
      await queryData();
    }
  // 查询表格数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await workRecordApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }
  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    reportTime.value = []; // 手动清空报工日期
    queryData();
  }
  onMounted(() => {
    queryData();
    queryWorker();
  });
  // ---------------------------- 复选框操作 ----------------------------
  const selectedRowKeys = ref([]);
  const selectedOptions = ref([]);
  function onCheck(record) {
    //不存在则添加
    if (!selectedRowKeys.value.includes(record.id)) {
      selectedRowKeys.value.push(record.id);
      selectedOptions.value.push(record);
    } else {
      //存在则移除
      selectedRowKeys.value.splice(selectedRowKeys.value.indexOf(record.id), 1);
      selectedOptions.value.splice(selectedRowKeys.value.indexOf(record), 1);
    }
  }
  async function handleWorkRecord(item) {
    tableLoading.value = true;
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择修改的数据');
      tableLoading.value = false;
      return;
    }
    let param = {
      ids: selectedRowKeys.value,
    };
    console.log(selectedRowKeys.value);
    try {
      if (Work_Record_AuditFlag_ENUM.getEnum(item.key).value === '1') {
        await workAuditApi.passRecord(param);
      } else if (Work_Record_AuditFlag_ENUM.getEnum(item.key).value === '2') {
        await workAuditApi.rebackRecord(param);
      }
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      selectedRowKeys.value = [];
      selectedOptions.value = [];
      await queryData();
      tableLoading.value = false;
    }
  }

  // ---------------------------- 添加/修改 ----------------------------
  const formRef = ref();

  function showForm(data, ...isVisible) {
    if (isVisible.length) {
      let key = Object.keys(isVisible[0])[0];
      data[key] = isVisible[0][key];
    }

    formRef.value.show(data);
    delete data.disnum;
    delete data.disprocess;
  }

  // ---------------------------- 单个删除 ----------------------------
  //确认删除
  function onDelete(data) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除选吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(data);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }

  //请求删除
  async function requestDelete(data) {
    SmartLoading.show();
    try {
      await workRecordApi.delete(data.id);
      message.success('删除成功');
      await queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

<template>
  <a-select
    v-model:value="modelId"
    :style="style"
    :placeholder="placeholder"
    :loading="loading"
    :disabled="disabled"
    @change="handleChange">
    <a-select-option v-for="item in modelList" :key="item.id" :value="item.id">
      {{ item.modelName }}
      <div style="font-size: 12px;color: #999;">
        {{item.modelNickname}}
      </div>
    </a-select-option>
  </a-select>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, defineEmits } from 'vue';
import { aiModelApi } from '/@/api/business/mes/ai/ai-model-api.js';

const props = defineProps({
  value: {
    type: [String, Number],
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请选择模型'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  style: {
    type: Object,
    default: () => ({
      width: '100%'
    })
  }
});

const emit = defineEmits(['update:value', 'change']);

const modelId = ref(props.value);
const modelList = ref([]);
const loading = ref(false);

// 获取模型列表
const getModelList = async () => {
  loading.value = true;
  try {
    const res = await aiModelApi.list();
    if (res && res.data) {
      modelList.value = res.data;
    }
  } catch (error) {
    console.error('获取模型列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理值变化
const handleChange = (val) => {
  emit('update:value', val);
  const selectedModel = modelList.value.find(item => item.id === val);
  emit('change', val, selectedModel);
};

// 监听外部值变化
watch(() => props.value, (newVal) => {
  modelId.value = newVal;
});

// 组件挂载时获取模型列表
onMounted(() => {
  getModelList();
});
</script>

<style scoped lang="less">

</style>

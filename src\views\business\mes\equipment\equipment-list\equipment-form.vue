<!--
  * 设备列表
  *
  * @Author:    linwj
  * @Date:      2025-02-09 20:12:13
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="800px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-tabs v-model:activeKey="activeKey" tab-position="left" >
      <a-tab-pane key="1" tab="基本信息">
        <a-form ref="formRef" :rules="rules"  :label-col="{ span: 6 }"  :model="form">
          <a-row :gutter="6">
            <a-col :span="12">
              <a-form-item label="设备编号" name="number" ref="number">
                <a-input style="width:90%;"  v-model:value="form.number"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备名称" name="name" ref="name">
                <a-input style="width:90%;"  v-model:value="form.name"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备类别">
                <a-select
                    ref="select"
                    v-model:value="form.typeId"
                    :allowClear="true"
                    :options="equipmentTypeOptions"
                    style="width: 120px"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="规格">
                <a-input style="width:90%;"  v-model:value="form.specification"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="型号">
                <a-input style="width:90%;"  v-model:value="form.model"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="车间">
                <a-select
                    ref="select"
                    v-model:value="form.workshopId"
                    :allowClear="true"
                    :options="workshopOptions"
                    style="width: 120px"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>
      <a-tab-pane key="2" tab="SCADA信息">
        <a-form ref="formRef" :rules="rules"  :label-col="{ span: 6 }"  :model="form">
          <a-row :gutter="6">
            <a-col :span="12">
              <a-form-item label="产品编码">
                <a-input style="width:90%;"  v-model:value="form.scadaProductCode"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备编码">
                <a-input style="width:90%;"  v-model:value="form.scadaEquipmentCode"/>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设备类型">
                <a-select
                    v-model:value="form.iotEquipmentType"
                    :style="`width: 90%`"
                    placeholder="请选择"
                    :showSearch="true"
                    :allowClear="true"
                    :options="iotEquipmentTypeOptions"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物联平台">
                <a-select
                    v-model:value="form.iotNetworkPlatform"
                    :style="`width: 90%`"
                    placeholder="请选择"
                    :showSearch="true"
                    :allowClear="true"
                    :options="iotPlatformOptions"
                    @change="handlePlatformChange"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否联网" name="iotNetworkFlag" ref="iotNetworkFlag">
                <a-radio-group v-model:value="form.iotNetworkFlag">
                  <a-radio :value="true">是</a-radio>
                  <a-radio :value="false">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>
    </a-tabs>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick, onMounted} from 'vue';
import _ from 'lodash';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { equipmentApi } from '/src/api/business/mes/equipment/equipment-api';
import { equipmentTypeApi } from '/src/api/business/mes/equipment/equipment-type-api';
import { workshopApi } from '/@/api/business/mes/factory/workshop-api';
import { smartSentry } from '/@/lib/smart-sentry';
import {IOT_EQUIPMENT_TYPE_ENUM} from '/@/constants/business/mes/equipment/iot-equipment-type-const.js'
import {IOT_PLATFORM_ENUM} from "/@/constants/business/mes/equipment/iot-platform-const.js";

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);
const activeKey = ref('1');

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  activeKey.value = '1';
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  if(!_.isNil(rowData.id)){
    getScadaData(rowData.id)
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 输出车间和类型下拉数据 ---------------------
const equipmentTypeOptions = ref([]);
async function queryEquipmentTypeList() {
  await equipmentTypeApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    // 输出设备类别options
    equipmentTypeOptions.value = res.data.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    })
  });
}
const workshopOptions = ref([])
async function queryWorkshopList() {
  await workshopApi.queryPage({ pageSize: 100, pageNum: 1 }).then((res) => {
    // 输出设备类别options
    workshopOptions.value = res.data.list.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    })
  });
}

//设备类型下拉数据
const iotEquipmentTypeOptions = ref([])
function outstandingEquipmentType(){
  const entries = Object.entries(IOT_EQUIPMENT_TYPE_ENUM)
  iotEquipmentTypeOptions.value = entries.map((item)=>{
    return {
      label:item[1].label,
      value:item[1].value
    }
  })
}

//设备物联平台下拉数据
const iotPlatformOptions = ref([])
function outstandingPlatform(){
  const entries = Object.entries(IOT_PLATFORM_ENUM)
  iotPlatformOptions.value = entries.map((item)=>{
    return {
      label:item[1].label,
      value:item[1].value
    }
  })
}

/**
 * 输出所属物联平台下的设备类型
 * 切换物联平台,清空设备类型；取消物联平台，重置设备类型
 * @param platformName 平台名称
 */
function handlePlatformChange(platformName){
  form.iotEquipmentType = undefined
  //取消物联平台
  if(platformName === undefined){
    outstandingEquipmentType()
    return;
  }
  //输出物联平台下的设备类型
  const entries = Object.entries(IOT_EQUIPMENT_TYPE_ENUM)
  const result = entries.filter((item)=>{
    return item[0].indexOf(platformName) !== -1
  })
  iotEquipmentTypeOptions.value = result.map((item)=>{
    return {
      label:item[1].label,
      value:item[1].value
    }
  })
}

function getScadaData(equipId){
  equipmentApi.getById(equipId).then((res)=>{
        form.scadaProductCode = res.data.scadaProductCode
        form.scadaEquipmentCode = res.data.scadaEquipmentCode
        form.iotEquipmentType = res.data.iotEquipmentType
        form.iotNetworkPlatform = res.data.iotNetworkPlatform
        form.iotNetworkFlag = res.data.iotNetworkFlag
  })
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  //基本信息
    number: undefined, //设备编号
    name: undefined, //设备名称
    typeId: undefined,//设备类型id
    model: undefined,//型号
    status: undefined,//设备状态
    specification: undefined,//规格
    workshopId: undefined,//车间id
  //第三方设备信息
    iotNetworkFlag:undefined,// 是否连接物联网
    iotNetworkPlatform:undefined,//物联网平台
    iotEquipmentType:undefined,//设备类型
    scadaProductCode:undefined, // 产品编码
    scadaEquipmentCode:undefined,// 设备编码
};

let form = reactive({ ...formDefault });

const rules = {
    name: [{ required: true, message: '设备名称 必填' }],
    number: [{ required: true, message: '设备编号 必填' }],
    iotNetworkFlag:[{ required: true, message: '是否联网 必填' }]
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await equipmentApi.update(form);
    } else {
      await equipmentApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
onMounted(()=>{
  queryEquipmentTypeList()
  queryWorkshopList()
  outstandingEquipmentType()
  outstandingPlatform()
})
</script>

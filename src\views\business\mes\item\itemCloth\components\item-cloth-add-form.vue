<!--
  * 新增布料
  *
  * @Author:    wxx
  * @Date:      2025-01-09
  * @Copyright  zscbdic
-->

<template>
  <a-modal width="1000px" :open="visibleFlag" @cancel="onClose" @ok="onSubmit" :maskClosable="false" :destroyOnClose="true" title="新增布料">
    <hr style="border-color: rgba(5, 5, 5, 0.1)" />
    <a-tabs v-model:activeKey="activeKey" tab-position="left" style="margin-top: 20px">
      <a-tab-pane key="1" tab="基本信息">
        <a-row class="smart-table-btn-block" style="margin-bottom: 20px">
          <strong>物料基本信息</strong>
        </a-row>
        <!-- 基本信息表单 -->
        <a-form ref="formRef" :model="form" :rules="rules">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="物料编号" name="number">
                <a-input style="width: 100%" v-model:value="form.number" placeholder="请输入,忽略将自动生成" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物料分类" name="typeId">
                <a-cascader v-model:value="form.typeId" :options="typeOptions" placeholder="请选择物料分类" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="物料名称" name="name">
                <a-input style="width: 100%" v-model:value="form.name" placeholder="物料名称" />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="供应商" name="supplierId">
                <a-select ref="select" v-model:value="form.supplierId" placeholder="请选择" :options="supplierOptions" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="规格型号" name="model">
                <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号" />
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="单位" name="unitId">
                <a-select ref="select" placeholder="请选择" v-model:value="form.unitId" :options="unitOptions" style="width: 100%" />
              </a-form-item>
            </a-col>
<!--            <a-col :span="12">-->
<!--              <a-form-item label="物料类别" name="category">-->
<!--                <a-radio-group v-model:value="form.category" button-style="solid">-->
<!--                  <a-radio-button value="0">半成品</a-radio-button>-->
<!--                  <a-radio-button value="1">成品</a-radio-button>-->
<!--                </a-radio-group>-->
<!--              </a-form-item>-->
<!--            </a-col>-->

            <a-col :span="12">
              <a-form-item label="停用标识" name="enableFlag">
                <a-radio-group v-model:value="form.enableFlag" button-style="solid" style="width: 90%">
                  <a-radio-button :value="false">启用</a-radio-button>
                  <a-radio-button :value="true">停用</a-radio-button>
                </a-radio-group>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-tab-pane>
      <a-tab-pane key="2" tab="布料属性" force-render>
        <!-- 颜色管理 -->
        <a-row style="margin: 20px 0">
          <label>颜色：</label>
          <a-col>
            <a-button size="small" @click="showColorModal" style="margin-right: 15px">+</a-button>
          </a-col>

          <div v-for="item in colorList" :key="item">
            <a-tag closable @close="removeColor(item)">{{ item }}</a-tag>
          </div>
        </a-row>
        <!-- 属性信息 -->
        <a-form ref="formRef" :model="form">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="克重" name="gramWeight">
                <a-input style="width: 100%" v-model:value="form.gramWeight" placeholder="单位：g/m²" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="宽度" name="width">
                <a-input style="width: 100%" v-model:value="form.width" placeholder="单位：mm" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="重量" name="weight">
                <a-input style="width: 100%" v-model:value="form.weight" placeholder="单位：g" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="长度" name="length">
                <a-input style="width: 100%" v-model:value="form.length" placeholder="单位：mm" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="成分" name="ingredient">
                <a-input style="width: 100%" v-model:value="form.ingredient" placeholder="请输入成分" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="净价" name="netPrice">
                <a-input style="width: 100%" v-model:value="form.netPrice" placeholder="单位：元" />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="米净价" name="metreNetPrice">
                <a-input style="width: 100%" v-model:value="form.metreNetPrice" placeholder="单位：元/m" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="千克净价" name="kgNetPrice">
                <a-input style="width: 100%" v-model:value="form.kgNetPrice" placeholder="单位：元/kg" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <!-- 生成表格 -->
        <div>
          <a-space>
            <a-button type="primary" @click="fabricFrom" style="margin-bottom: 10px">属性生成</a-button>
            <a-button type="primary" style="background: #ff4d4f; color: white; margin-bottom: 10px" @click="deleteItems">批量删除</a-button>
          </a-space>
        </div>

        <a-table
          :columns="columns"
          :data-source="skuData"
          bordered
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
          }"
          :scroll="{ y: 250 }"
        >
          <template #bodyCell="{ column, record }">
            <!-- 上传图片 -->
            <template v-if="column.dataIndex === 'imgUrl'">
              <div class="scroll-container">
                <file-upload :default-file-list="pictureArr" @change="handleFileChange($event, record, column)" />
              </div>
            </template>
            <!-- 价格 -->
            <template v-if="column.dataIndex === 'price'">
              <a-input style="width: 80%" v-model:value="record.price" placeholder="单位：元" />
            </template>
            <!-- sku编号 -->
            <template v-if="column.dataIndex === 'skuNumber'">
              <a-input style="width: 80%" v-model:value="record.skuNumber" placeholder="请输入,忽略将自动生成"></a-input>
            </template>
            <!-- 颜色编码 -->
            <template v-if="column.dataIndex === 'colorNum'">
              <a-input style="width: 80%" v-model:value="record.colorNum" placeholder="颜色编码" />
            </template>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <!--款式尺寸组件-->
  <StyleColorSelect ref="colorVisible" @reloadList="onColorSubmit" />
</template>

<script setup>
  import { message, Modal } from 'ant-design-vue';
  import _ from 'lodash';
  import { reactive, ref, onMounted, watch } from 'vue';
  import { unitApi } from '/@/api/business/mes/base/unit-api';
  import { supplierApi } from '/@/api/business/mes/base/supplier-api';
  import { itemTypeApi } from '/@/api/business/mes/item/item-type-api';
  import { itemClothApi } from '/@/api/business/mes/item/item-cloth-api';
  import StyleColorSelect from '/@/components/business/mes/base/style-color-modal/index.vue';
  import FileUpload from '/@/components/support/file-upload/index.vue';

  onMounted(() => {
    queryTypeOptions();
    querySupplierOptions();
    queryUnitOptions();
  });

  let activeKey = ref('1'); //激活tab

  //-----------------------------事件---------------------
  const emits = defineEmits(['reloadList']);

  //显示与隐藏
  const visibleFlag = ref(false);
  function show() {
    visibleFlag.value = true;
  }
  function onClose() {
    activeKey = ref('1'); //重置标签页
    visibleFlag.value = false;
    resetForm();
  }

  //-----------------------------布料基本信息---------------------
  //------定义布料数据-------
  const formRef = ref();

  const formDefault = {
    typeId: undefined, //物料分类id;关联t_item_type
    number: undefined, //物料编号
    name: '', //物料名称
    supplierId: undefined, //供应商id;关联t_item_supplier
    model: '', //规格型号
    unitId: undefined, //单位id;关联t_unit
    price: undefined, //价格
    imgUrl: undefined, //图片url
    enableFlag: false, //停用标识;0启用，1停用
    category: undefined, //类型;0半成品 1成品
    attribute: undefined, //属性;0面料，1辅料
    gramWeight: undefined, //克重
    width: undefined, //宽度
    weight: undefined, //重量
    length: undefined, //长度
    ingredient: undefined, //成分
    netPrice: undefined, //净价
    metreNetPrice: undefined, //米净价
    kgNetPrice: undefined, //千克净价
    skuColors: undefined, //颜色
  };

  const form = reactive({ ...formDefault });

  //布料基本信息表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入物料名称' }],
    unitId: [{ required: true, message: '请选择单位' }],
    enableFlag: [{ required: true, message: '请选择启用状态' }],
  };

  //--------------------------------获取选项数据----------------------------------
  //获取物料分类
  const typeOptions = ref([]);

  async function queryTypeOptions() {
    let queryForm = {};
    const res = await itemTypeApi.queryCategoryTree(queryForm);
    typeOptions.value = res.data;
  }

  //获取供应商名称
  const supplierOptions = ref([]);

  async function querySupplierOptions() {
    let queryForm = {};
    const res = await supplierApi.queryAll(queryForm);
    supplierOptions.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  }

  //获取单位
  const unitOptions = ref([]);

  async function queryUnitOptions() {
    let queryForm = {};
    const res = await unitApi.querySelect(queryForm);
    unitOptions.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
  }

  //----------------------------------------颜色弹窗模块-----------------------------------------

  const colorVisible = ref(); // 颜色弹窗显示状态
  const colorList = ref([]); // 存储颜色列表

  //显示颜色选择弹窗
  const showColorModal = () => {
    colorVisible.value.show();
  };
  //保存颜色数据
  async function onColorSubmit(data) {
    data.value.map((item) => {
      if (!colorList.value.includes(item)) {
        colorList.value.push(item);
      } else {
        message.warning('颜色已存在');
      }
    });
  }
  //移除颜色
  function removeColor(color) {
    colorList.value = colorList.value.filter((e) => {
      return e !== color;
    });
  }

  //---------------------------------------------生成表格---------------------------------------
  //定义表格列
  const columns = [
    {
      title: '图片',
      dataIndex: 'imgUrl',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: 'sku编号',
      dataIndex: 'skuNumber',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '颜色名称',
      dataIndex: 'colorName',
      align: 'center',
      ellipsis: true,
      width: 70,
    },
    {
      title: '参考颜色',
      dataIndex: 'referColor',
      align: 'center',
      ellipsis: true,
      width: 70,
    },
    {
      title: '颜色编码',
      dataIndex: 'colorNum',
      align: 'center',
      ellipsis: true,
      width: 100,
    },
    {
      title: '价格',
      dataIndex: 'price',
      align: 'center',
      ellipsis: true,
      width: 80,
    },
  ];

  // 定义数据源
  const skuData = ref([]);

  // 生成表格数据
  const fabricFrom = () => {
    skuData.value = [];
    if (colorList.value.length === 0) {
      message.error('颜色为空');
      return;
    }
    colorList.value.forEach((color, index) => {
      skuData.value.push({
        key: index.toString(), // 主键
        skuNumber: undefined, // sku编码
        colorNum: undefined, // 颜色编号
        colorName: color, // 颜色名称
        referColor: color, // 参考颜色
        imgUrl: [], // 图片url
        price: 0, // 价格
      });
    });
  };

  // -------------------------上传图片---------------------------
  const pictureArr = ref([]);
  const handleFileChange = (event, record) => {
    const index = parseInt(record.key);
    if (index >= 0 && index < skuData.value.length) {
      skuData.value[record.key].imgUrl = event;
    }
  };

  //  ------------------------ 批量删除功能 ----------------------
  //定义选中行
  const selectedRowKeys = ref([]); // 选中的行的key数组

  // 选择行发生变化
  const onSelectChange = (keys) => {
    selectedRowKeys.value = keys;
  };

  // 批量删除方法
  const deleteItems = () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的项');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedRowKeys.value.length} 项吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        // 过滤掉选中的项
        skuData.value = skuData.value.filter((item) => !selectedRowKeys.value.includes(item.key));
        // 清空选中项
        selectedRowKeys.value = [];
        message.success('删除成功');
      },
    });
  };

  //---------------------------------------------表单提交功能----------------------------------
  //重置表单
  const resetForm = () => {
    Object.assign(form, formDefault);
    skuData.value = [];
    colorList.value = [];
    pictureArr.value = [];
    selectedRowKeys.value = [];
  };

  // 添加提交方法
  const onSubmit = async () => {
    await formRef.value.validate();

    const submitData = {
      ...form,
      typeId: form.typeId ? form.typeId[form.typeId.length - 1] : undefined,
      skuAttributes: skuData.value.map((item) => ({
        skuNumber: item.skuNumber,
        colorName: item.colorName,
        referColor: item.referColor,
        colorNum: item.colorNum,
        price: item.price,
        imgUrl: item.imgUrl,
      })),
    };
    await itemClothApi.add(submitData);
    resetForm();
    visibleFlag.value = false;
    emits('reloadList');
    message.success('保存成功');
  };

  defineExpose({
    show,
  });
</script>

<style scoped>
  .upload-wrapper {
    position: relative;
  }

  .edit-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.45);
    color: white;
    opacity: 0;
  }

  .upload-wrapper:hover .edit-overlay {
    opacity: 1;
  }
</style>

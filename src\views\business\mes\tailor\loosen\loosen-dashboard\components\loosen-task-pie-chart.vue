<template>
  <div style="display: flex; justify-content: space-between">
    <div style="font-size: 16px; font-weight: bold">今日松布卷数统计</div>
  </div>
  <div class="echarts-box">
    <div class="chart-main" id="loosen-task-pie-chart"></div>
  </div>
</template>

<script setup>
  import { onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';

  let myChart = null;

  // 静态数据
  const staticData = [
    { value: 180, name: '今日计划总卷数' },
    { value: 120, name: '今日完成总卷数' },
  ];

  onMounted(() => {
    initChart();
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    if (myChart) {
      myChart.dispose();
      myChart = null;
    }
  });

  // 窗口大小变化处理
  function handleResize() {
    if (myChart) {
      myChart.resize();
    }
  }

  // 初始化图表
  function initChart() {
    const chartDom = document.getElementById('loosen-task-pie-chart');
    if (chartDom) {
      myChart = echarts.init(chartDom);

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'center',
          textStyle: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: '卷数统计',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['65%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: staticData,
            color: ['#4687f0', '#37c6c0'],
          },
        ],
      };

      myChart.setOption(option);

      // 强制重新计算大小，确保图表充满容器
      setTimeout(() => {
        myChart.resize();
      }, 200);
    }
  }
</script>

<style scoped lang="less">
  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 100%;
    height: calc(100% - 40px);

    .chart-main {
      width: 100%;
      height: 100%;
      min-height: 200px;
      background: #fff;
    }
  }
</style>

<template>
  <div class="card">
    <div class="title">指令单优先级占比</div>
    <div class="time">{{props.time.startTime}}~{{props.time.endTime}}</div>


    <div style="display: flex;justify-content: space-between;font-size: 16px;margin: 16px 0 20px 0 ">
      <div style="border-left: #46c26f 3px solid;padding-left: 10px;">一般 {{newData[0].value}}</div>
      <div style="border-left: #f0a800 3px solid;padding-left: 10px;">紧急 {{newData[1].value}}</div>
      <div style="border-left: #eb5050 3px solid;padding-left: 10px;">非常紧急 {{newData[2].value}}</div>
    </div>

    <div class="echarts-box">
      <div class="pie-main" id="pie-main"></div>
    </div>


  </div>
</template>
<script setup>
import * as echarts from 'echarts';
import {onMounted, ref, defineProps, watch} from 'vue';
const props=defineProps({
  data:{
    type:Array,
    default: ()=> [],
  },
  time:{
    type:Object,
    default:()=>({
      startTime:"暂无时间",
      endTime:"暂无时间",
    })
  }
})
//
const option=ref({})
const newData=ref([
  {value: 0, name: '一般', itemStyle: {color: '#46c26f'}},
  {value: 0, name: '紧急', itemStyle: {color: '#f0a800'}},
  {value: 0, name: '非常紧急', itemStyle: {color: '#eb5050'}}
])
onMounted(() => {
  init()
});
// 监听父组件传过来的值
watch(()=>props.data,(newValue)=>{
  if(newValue){
    init(newValue)
  }
})

function init(data) {
  if(data){
    if(data instanceof Array){

      data.forEach(item=>{
        newData.value.forEach(item1=>{
          if(item.x===item1.name){
            item1.value=item.y
          }
        })
      })
      option.value = {
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '优先级占比',
            type: 'pie',
            radius: ['70%', '100%'],
            center: ['50%', '50%'],
            startAngle: 180,
            endAngle: 360,
            data:newData.value,
            stillShowZeroSum: false,
            label: {
              show: false
            },
          }
        ]
      };
    }
  }else{
    option.value = {
      tooltip: {
        trigger: 'item'
      },
      // legend: {
      //   top: '5%',
      //   left: 'center'
      // },
      series: [
        {
          name: '优先级占比',
          type: 'pie',
          radius: ['70%', '100%'],
          center: ['50%', '50%'],
          // adjust the start and end angle
          startAngle: 180,
          endAngle: 360,
          data: [
            {value: 0, name: '一般', itemStyle: {color: '#46c26f'}},
            {value: 0, name: '紧急', itemStyle: {color: '#f0a800'}},
            {value: 0, name: '非常紧急', itemStyle: {color: '#eb5050'}}
          ]
        }
      ]
    };
  }

  let chartDom = document.getElementById('pie-main');
  if (chartDom) {
    let myChart = echarts.init(chartDom);
    document.getElementById('pie-main').setAttribute('_echarts_instance_', '')
    option.value && myChart.setOption(option.value);
  }
}
</script>
<style scoped lang="less">
.card {
  background-color: #fff;
  padding: 20px;
  height: 350px;
  width: 100%;

  .title {
    font-size: 16px;
    //font-weight: bold;
    color: #303133;
    margin-bottom: 4px;
  }

  .time {
    font-size: 16px;
    color: #9b9b9b;
    margin-bottom: 4px;
  }

  .now {
    font-size: 16px;
    color: #5b5d60;
  }

  .echarts-box {

    display: flex;
    align-items: center;
    justify-content: center;
    .pie-main {
      //border: 1px solid;
      width: 100%;
      height: 200px;
      background: #fff;
    }
  }

}
</style>

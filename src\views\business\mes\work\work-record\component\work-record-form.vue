<!--
  * 报工记录
  *
  * @Author:    cjm
  * @Date:      2024-07-22 20:33:41
  * @Copyright  cjm
-->
<template>
  <a-modal
    :title="form.id ? '报工记录:指令单编号：' + form.produceInstructOrderNumber + '   |   时间:' + form.reportTime + '   |   ' + form.workerName : '添加'"
    width="900px"
    :open="visibleFlag"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 8 }" :wrapperCol="{ span: 12 }">
      <a-row>
        <a-col :span="9">
          <a-form-item label="裁床单编号" name="cutBedSheetNumber">
            <a-input style="width: 100%" v-model:value="form.cutBedSheetNumber" placeholder="裁床单编号" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="7">
          <a-form-item label="扎号" name="tieNum">
            <a-input-number style="width: 100%" v-model:value="form.tieNum" placeholder="扎号" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="床次" name="cutNum">
            <a-input-number style="width: 100%" v-model:value="form.cutNum" placeholder="床次" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="9">
          <a-form-item label="物料名称" name="itemName">
            <a-input style="width: 100%" v-model:value="form.itemName" placeholder="物料名称" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="7">
          <a-form-item label="款式颜色" name="styleColor">
            <a-input style="width: 100%" v-model:value="form.styleColor" placeholder="款式颜色" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="尺码" name="size">
            <a-input style="width: 100%" v-model:value="form.size" placeholder="尺码" disabled />
          </a-form-item>
        </a-col>

        <a-col :span="9">
          <a-form-item label="工序名称" name="processName">
            <a-select
              ref="memberInfos"
              v-model:value="form.processId"
              style="width: 100%"
              placeholder="请选择"
              :options="processes"
              @change="onChange"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>

        <a-col :span="7">
          <a-form-item label="单价价格" name="price">
            <a-input-number style="width: 100%" v-model:value="form.price" placeholder="单价价格" :disabled="disabled" />
          </a-form-item>
        </a-col>

        <a-col :span="8">
          <a-form-item label="报工数量" name="workQuantity">
            <a-input-number style="width: 100%" v-model:value="form.workQuantity" placeholder="报工数量" :disabled="disNum" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">关闭</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/src/components/framework/smart-loading';
  import { workRecordApi } from '/src/api/business/mes/work/work-record-api.js';
  import { smartSentry } from '/src/lib/smart-sentry';
  import { produceInstructOrderProcessApi } from '/src/api/business/mes/produce/produce-instruct-order-process-api.js';

  const disabled = ref(true);
  const disNum = ref(true);

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;

    if (form.disnum === false) {
      disNum.value = form.disnum;
    } else if (form.disprocess === false) {
      disabled.value = form.disprocess;
    }

    getProcessInfo(rowData.produceInstructOrderId);
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    delete form.disnum;
    delete form.disprocess;
    disNum.value = true;
    disabled.value = true;
    visibleFlag.value = false;
  }

  //工序信息发生改变，单价随之变更
  function onChange(value) {
    processes.value.map((item) => {
      if (item.value === value) {
        form.price = item.price;
      }
    });
  }
  // ------------------------ 工序信息 ------------------------
  const processes = ref([]);

  // 拼装工序信息函数
  function mergeString(data, value, label) {
    return data.map((e) => ({
      value: e[value],
      label: e[label],
      // TODO 目前单价与工价1写死
      price: e['unitPrice1'],
    }));
  }

  async function getProcessInfo(id) {
    try {
      let responseModel = await produceInstructOrderProcessApi.getProcessInfoById(id);
      processes.value = mergeString(responseModel.data, 'id', 'name');
    } catch (e) {
      message.error('请求工序信息失败！');
    }
  }
  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined, //主键
    // produceInstructOrderId: undefined, //生产指令单id
    // produceInstructOrderNumber: undefined, //生产指令单编号
    // cutBedSheetId: undefined, //裁床单id
    // cutBedSheetNumber: undefined, //裁床单编号
    // cutNum: undefined, //床次
    // itemId: undefined, //物料id
    // itemName: undefined, //物料名称
    // itemNumber: undefined, //物料编号
    // styleColor: undefined, //款式颜色
    // size: undefined, //尺码
    // workerId: undefined, //工人ID号
    // workerName: undefined, //工人姓名
    // feTicketId: undefined, //菲票ID
    // tieNum: undefined, //扎号
    // position: undefined, //部位
    processId: undefined, //工序ID
    processName: undefined, //工序名称
    // workQuantity: undefined, //报工数量
    price: undefined, //单价价格
    // reportTime: undefined, //报工时间
    //             recordStatus: undefined, //记录状态;0正常 1作废
    //             auditFlag: undefined, //审核状态;0未审核 1已审核
  };

  let form = reactive({ ...formDefault });

  const rules = {
    // id: [{ required: true, message: '主键 必填' }],
    produceInstructOrderId: [{ required: true, message: '生产指令单id 必填' }],
    produceInstructOrderNumber: [{ required: true, message: '生产指令单编号 必填' }],
    cutBedSheetId: [{ required: true, message: '裁床单id 必填' }],
    cutBedSheetNumber: [{ required: true, message: '裁床单编号 必填' }],
    cutNum: [{ required: true, message: '床次 必填' }],
    itemId: [{ required: true, message: '物料id 必填' }],
    itemName: [{ required: true, message: '物料名称 必填' }],
    itemNumber: [{ required: true, message: '物料编号 必填' }],
    styleColor: [{ required: true, message: '款式颜色 必填' }],
    size: [{ required: true, message: '尺码 必填' }],
    workerId: [{ required: true, message: '工人ID号 必填' }],
    workerName: [{ required: true, message: '工人姓名 必填' }],
    feTicketId: [{ required: true, message: '菲票ID 必填' }],
    tieNum: [{ required: true, message: '扎号 必填' }],
    position: [{ required: true, message: '部位 必填' }],
    processId: [{ required: true, message: '工序ID 必填' }],
    processName: [{ required: true, message: '工序名称 必填' }],
    workQuantity: [{ required: true, message: '报工数量 必填' }],
    price: [{ required: true, message: '单价价格 必填' }],
    reportTime: [{ required: true, message: '报工时间 必填' }],
    recordStatus: [{ required: true, message: '记录状态;0正常 1作废 必填' }],
    auditFlag: [{ required: true, message: '审核状态;0未审核 1已审核 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      await save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        if (form.disnum === false) {
          let param = {
            id: form.id,
            workQuantity: form.workQuantity,
          };
          await workRecordApi.changeNum(param);
        } else if (form.disprocess === false) {
          await workRecordApi.update(form);
        }
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      message.error('工序信息更改失败');
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

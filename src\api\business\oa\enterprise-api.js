/*
 * 企业信息
 *
 * @Author:    开云
 * @Date:      2022-09-03 21:47:28
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import {postRequest, getRequest, postDownload} from '/@/lib/axios';

export const enterpriseApi = {
    // 新建企业 <AUTHOR>
    create: (param) => {
        return postRequest('/oa/enterprise/create', param);
    },

    // 删除企业 <AUTHOR>
    delete: (enterpriseId) => {
        return getRequest(`/oa/enterprise/delete/${enterpriseId}`);
    },

    // 查询企业详情 <AUTHOR>
    detail: (enterpriseId) => {
        return getRequest(`/oa/enterprise/get/${enterpriseId}`);
    },

    // 分页查询企业模块 <AUTHOR>
    pageQuery: (param) => {
        return postRequest('/oa/enterprise/page/query', param);
    },

    // 导出企业数据excel <AUTHOR>
    exportExcel: (param) => {
        return postDownload('/oa/enterprise/exportExcel', param);
    },

    //企业列表查询 含数据范围 <AUTHOR>
    queryList: (type) => {
        let query = '';
        if (type) {
            query = `?type=${type}`;
        }
        return getRequest(`/oa/enterprise/query/list${query}`);
    },

    // 编辑企业 <AUTHOR>
    update: (param) => {
        return postRequest('/oa/enterprise/update', param);
    },
    // 企业全部员工List <AUTHOR>
    employeeList: (param) => {
        return postRequest('/oa/enterprise/employee/list', param);
    },
    // 分页查询企业员工List <AUTHOR>
    queryPageEmployeeList: (param) => {
        return postRequest('/oa/enterprise/employee/queryPage', param);
    },
    // 添加员工 <AUTHOR>
    addEmployee: (param) => {
        return postRequest('/oa/enterprise/employee/add', param);
    },

    // 删除员工 <AUTHOR>
    deleteEmployee: (param) => {
        return postRequest('/oa/enterprise/employee/delete', param);
    },

};

<!--
  * 角色 设置
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>
  <a-card class="role-container">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="角色-功能权限">
        <RoleTree />
      </a-tab-pane>
      <a-tab-pane key="4" tab="app角色-功能权限">
        <AppRoleTree />
      </a-tab-pane>
      <a-tab-pane key="5" tab="角色-大模型能力">
        <RoleLLMToolAbility />
      </a-tab-pane>
      <a-tab-pane key="2" tab="角色-数据范围">
        <RoleDataScope />
      </a-tab-pane>
      <a-tab-pane key="3" tab="角色-员工列表">
        <RoleEmployeeList />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script setup>
  import { ref } from 'vue';
  import RoleDataScope from '../role-data-scope/index.vue';
  import RoleEmployeeList from '../role-employee-list/index.vue';
  import RoleTree from '../role-tree/index.vue';
  import AppRoleTree from '/@/views/system/app-role/components/role-tree/index.vue';
  import RoleLLMToolAbility from '../role-llmtool-ability/index.vue';
  defineProps({
    value: Number,
  });

  defineEmits(['update:value']);

  let activeKey = ref();
</script>
<style scoped lang="less">
  .role-container {
    height: 100%;
  }
</style>

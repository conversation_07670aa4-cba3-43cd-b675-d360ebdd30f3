<!--
  * 物料库存属性
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:18:19
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }" >
                    <a-form-item label="物料"  name="materialId">
                      <MaterialSelect v-model:value="form.materialId" style="width: 90%" @change="onMaterialChange"/>
                    </a-form-item>
                    <a-form-item label="物料型号"  name="materielModel">
                      <a-input style="width: 90%" v-model:value="form.materielModel" placeholder="物料名称" disabled/>
                    </a-form-item>
                    <a-form-item label="物料spu编号"  name="materielSpuNumber">
                      <a-input style="width: 90%" v-model:value="form.materielSpuNumber" placeholder="物料spu编号" disabled/>
                    </a-form-item>
                    <a-form-item label="物料sku编号"  name="materielSkuNumber">
                      <a-input style="width: 90%" v-model:value="form.materielSkuNumber" placeholder="物料sku编号" disabled/>
                    </a-form-item>
                    <!-- <a-form-item label="作用范围"  name="scope">
                      <a-input style="width: 100%" v-model:value="form.scope" placeholder="ALL所有仓库 ONE 单一仓库" />
                    </a-form-item>
                    <a-form-item label="仓库限制"  name="warehouseLimit">
                      <a-input-number style="width: 100%" v-model:value="form.warehouseLimit" placeholder="仓库限制;0停用 1启用" />
                    </a-form-item>
                    <a-form-item label="仓库id"  name="warehouseId">
                      <a-input-number style="width: 100%" v-model:value="form.warehouseId" placeholder="仓库id;保留" />
                    </a-form-item>
                    <a-form-item label="仓位限制"  name="locationLimit">
                      <a-input-number style="width: 100%" v-model:value="form.locationLimit" placeholder="仓位限制;0停用 1启用" />
                    </a-form-item>
                    <a-form-item label="仓位id"  name="locationId">
                      <a-input-number style="width: 100%" v-model:value="form.locationId" placeholder="仓位id;保留" />
                    </a-form-item> -->
                    <a-form-item label="最小库存"  name="minStock">
                      <a-input-number style="width: 90%" v-model:value="form.minStock" placeholder="最小库存" />
                    </a-form-item>
                    <a-form-item label="最大库存"  name="maxStock">
                      <a-input-number style="width: 90%" v-model:value="form.maxStock" placeholder="最大库存" />
                    </a-form-item>
                    <a-form-item label="安全库存"  name="safeStock">
                      <a-input-number style="width: 90%" v-model:value="form.safeStock" placeholder="安全库存" />
                    </a-form-item>
                    <!-- <a-form-item label="再订货点"  name="reorderGood">
                      <a-input-number style="width: 100%" v-model:value="form.reorderGood" placeholder="再订货点" />
                    </a-form-item> -->
                    <a-form-item label="启用最小库存:"  name="minStockFlag">
                      <BooleanSelect v-model:value="form.minStockFlag" style="width: 90%" />
                    </a-form-item>
                    <a-form-item label="启用最大库存:"  name="maxStockFlag">
                      <BooleanSelect v-model:value="form.maxStockFlag" style="width: 90%" />
                    </a-form-item>
                    <a-form-item label="启用安全库存:"  name="safeStockFlag">
                      <BooleanSelect v-model:value="form.safeStockFlag" style="width: 90%" />
                    </a-form-item>
                    <!-- <a-form-item label="启用再订货点;0停用 1启用"  name="reorderGoodFlag">
                      <BooleanSelect v-model:value="form.reorderGoodFlag" style="width: 100%" />
                    </a-form-item> -->
                    <a-form-item label="批号管理:"  name="lotManageFlag">
                      <BooleanSelect v-model:value="form.lotManageFlag" style="width: 90%" />
                    </a-form-item>
                    <!-- <a-form-item label="SN管理;0停用 1启用"  name="snManageFlag">
                      <BooleanSelect v-model:value="form.snManageFlag" style="width: 100%" />
                    </a-form-item> -->
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { stkMaterialStockApi } from '/@/api/business/mes/stock/stk-material-stock-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import BooleanSelect from '/@/components/framework/boolean-select/index.vue';
  import MaterialSelect from '/@/components/business/mes/item/material-all-select/index.vue';
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
              id: undefined, //主键
              materialId: undefined, //物料id
              materielModel: undefined, //物料型号
              materielSpuNumber: undefined, //物料spu编号
              materielSkuNumber: undefined, //物料sku编号
              scope: "ALL", //作用范围;ALL所有仓库 ONE 单一仓库
              warehouseLimit: 0, //仓库限制;0停用 1启用
              warehouseId: undefined, //仓库id;保留
              locationLimit: 0, //仓位限制;0停用 1启用
              locationId: undefined, //仓位id;保留
              minStock: undefined, //最小库存
              maxStock: undefined, //最大库存
              safeStock: undefined, //安全库存
              reorderGood: undefined, //再订货点
              minStockFlag: 0, //启用最小库存;0停用 1启用
              maxStockFlag: 0, //启用最大库存;0停用 1启用
              safeStockFlag: 0, //启用安全库存;0停用 1启用
              reorderGoodFlag: 0, //启用再订货点;0停用 1启用
              lotManageFlag: 0, //批号管理;0停用 1启用
              snManageFlag: 0, //SN管理;0停用 1启用
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  materialId: [{ required: true, message: '物料 必填' }],
                  // scope: [{ required: true, message: '作用范围;ALL所有仓库 ONE 单一仓库 必填' }],
                  minStockFlag: [{ required: true, message: '启用最小库存 必填' }],
                  maxStockFlag: [{ required: true, message: '启用最大库存 必填' }],
                  safeStockFlag: [{ required: true, message: '启用安全库存 必填' }],
                  lotManageFlag: [{ required: true, message: '批号管理 必填' }],
  };
  // 物料选择改变事件
  function onMaterialChange(material) {
    form.materielModel = material?.model;
    form.materielSpuNumber = material?.number;
    form.materielSkuNumber = material?.skuNumber;
  }

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await stkMaterialStockApi.update(form);
      } else {
        await stkMaterialStockApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

<!--
  * 工序信息
  *
  * @Author:    xmt
  * @Date:      2024-07-13 15:19:57
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="600px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 ,offset:1}" @keyup.enter="onSubmit">
      <a-row>
        <a-col :span="12">
          <a-form-item label="工序编号" name="processNumber">
            <a-input style="width: 100%" v-model:value="form.processNumber"
                     placeholder="工序编号"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工序名称" name="name">
            <a-input style="width: 100%" v-model:value="form.name"
                     placeholder="工序名称"/>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="工序控制" name="processControl">
            <a-select v-model:value="form.processControl" style="width: 100%">
              <a-select-option value="0">自制</a-select-option>
              <a-select-option value="1">委外</a-select-option>
              <a-select-option value="2">不限</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="部位" name="position">
            <a-input style="width: 100%" v-model:value="form.position" placeholder="部位"/>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="工序类型" name="processType">
            <DictSelect v-model:value="form.processType" keyCode="PROCESS_TYPE" width="100%"/>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="标准工时" name="standardTime">
            <a-input-number style="width: 100%" v-model:value="form.standardTime"
                            placeholder="标准工时 单位：秒"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="工价一" name="unitPrice1">
            <a-input-number style="width: 100%" v-model:value="form.unitPrice1"
                            prefix="￥" placeholder="工价一" :precision="3" min="0"
                            defaultValue="0.00"/>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="备注" name="remark">
            <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注"/>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/src/components/framework/smart-loading';
import {processApi} from '/src/api/business/mes/process/process-api';
import {smartSentry} from '/src/lib/smart-sentry';
import DictSelect from "/@/components/support/dict-select/index.vue";

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}


function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined,
  remark: undefined, //备注
  name: undefined, //工序名称
  processNumber: undefined,//工序编号
  position: undefined, //部位
  processType: undefined, //工序类型
  standardTime: undefined, //标准工时;单位
  unitPrice1: 0.0, //工价一
  unitPrice2: 0.0, //工价二
  unitPrice3: 0.0, //工价三
  unitPrice4: 0.0, //工价四
  unitPrice5: 0.0, //工价五
  unitPrice6: 0.0, //工价六
  processControl: '0', //工序控制;0自制 1委外 2不限
  sopId: undefined, //sopId;保留
};

let form = reactive({...formDefault});

const rules = {
  name: [{required: true, message: '工序名称 必填'}],
  processNumber: [{required: true, message: '工序编号 必填'}],
  unitPrice1: [{required: true, message: '工价一 必填'}],
  processControl: [{required: true, message: '工序控制 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await processApi.update(form);
    } else {
      await processApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>
<style>

.ant-modal-body label {
  padding-right: 140px;
}
</style>

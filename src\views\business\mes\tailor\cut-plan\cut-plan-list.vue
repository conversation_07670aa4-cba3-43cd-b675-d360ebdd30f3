<!--
  * 裁剪计划单
  *
  * @Author:    fkf
  * @Date:      2025-01-18
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="裁剪计划单编号" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.number" placeholder="裁剪计划单编号" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showAddForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false" @change="handleTableChange">
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                审核
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu @click="({ key }) => onAudit(record, key)">
                  <a-menu-item v-for="item in CUT_PLAN_AUDIT_ENUM" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button @click="showEditForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="CUT_PLAN_STATUS_ENUM.getEnum(text).color">{{ CUT_PLAN_STATUS_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'number'">
          <a-button type="link" @click="showEditForm(record)">{{ text }}</a-button>
        </template>
        <!-- <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
        </template> -->
        <!-- <template v-else-if="column.dataIndex === 'imgUrl'">
          <file-preview
            :file-list="record.imgUrl || []"
            type="picture"
            :width="30"
            :height="30"
          />
        </template> -->
        <!--  停用标识      -->
        <!-- <template v-if="column.dataIndex === 'enableFlag'">
          <a-tag :color="text === true ? 'red' : 'green'">{{ text === true ? '停用' : '启用' }}</a-tag>
        </template> -->
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
    <cutPlanFormAdd ref="addFormRef" @success="queryData" />
    <cutPlanFormEdit ref="editFormRef" @success="queryData" />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { cutPlanApi } from '/@/api/business/mes/tailor/cut-plan.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { ITEM_ATTRIBUTE_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { CUT_PLAN_STATUS_ENUM, CUT_PLAN_AUDIT_ENUM } from '/@/constants/business/mes/tailor/cut-plan-const.js';
  import cutPlanFormAdd from '/@/views/business/mes/tailor/cut-plan/components/cut-plan-form-add.vue';
  import cutPlanFormEdit from '/@/views/business/mes/tailor/cut-plan/components/cut-plan-form-edit.vue';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '裁剪计划单编号',
      dataIndex: 'number',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '生产指令单编号',
      dataIndex: 'produceInstructOrderNumber',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '计划单状态',
      dataIndex: 'status',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '计划开始时间',
      dataIndex: 'planStartTime',
      ellipsis: true,
      align: 'center',
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '计划完成时间',
      dataIndex: 'planFinishTime',
      ellipsis: true,
      align: 'center',
      sorter: true,//启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    // {
    //   title: '实际开始时间',
    //   dataIndex: 'realStartTime',
    //   ellipsis: true,
    //   align: 'center',
    // },
    // {
    //   title: '实际完成时间',
    //   dataIndex: 'realFinishTime',
    //   ellipsis: true,
    //   align: 'center',
    // },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 180,
      align: 'center',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    attribute: ITEM_ATTRIBUTE_ENUM.CLOTHES.value,
    pageNum: 1,
    pageSize: 10,
    number: undefined, //裁剪计划单编号
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  //... 是 JavaScript 的展开运算符，它用于将 queryFormState 对象的所有属性复制到新的对象中。
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }
  //处理表格时间顺序变化事件
  async function handleTableChange(pagination,filters,sorter){
    if(sorter&&sorter.field==='planStartTime'){
      queryForm.sortItemList = 
      [{
        column:'plan_start_time',
        isAsc:sorter.order==='ascend'
      }];
    }else if(sorter&&sorter.field==='planFinishTime'){
      queryForm.sortItemList = 
      [{
        column:'plan_finish_time',
        isAsc:sorter.order==='ascend'
      }];
    }
    if(!sorter.order){
      queryForm.sortItemList=
      [{
        column:'create_time',
        isAsc:false
      }]
    }
    await queryData();
  }
  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await cutPlanApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      // console.log(tableData.value)
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 审核 ----------------------------
  async function onAudit(record, key) {
    let param = {
      id: record.id,
      event: key,
    };
    try {
      await cutPlanApi.status(param);
      message.success('审核成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    }
  }
  // ---------------------------- 添加/修改 ----------------------------
  const addFormRef = ref();
  const editFormRef = ref();
  function showAddForm() {
    addFormRef.value.show();
  }
  function showEditForm(record) {
    editFormRef.value.show(record.id);
  }
  // ---------------------------- 删除 ----------------------------
  function onDelete(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }
  async function requestDelete(id) {
    try {
      SmartLoading.show();
      await cutPlanApi.delete(id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>

/**
 * 裁片收发汇总 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-11-06 15:25:13
 * @Copyright  zscbdic
 */
import {postRequest, getRequest} from '/@/lib/axios';

export const partDispatchSummaryApi = {

    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/partDispatchSummary/summaryPage', param);
    },

    queryDetail: (id) => {
        return getRequest(`/partDispatchSummary/summaryDetail/${id}`);
    },


};

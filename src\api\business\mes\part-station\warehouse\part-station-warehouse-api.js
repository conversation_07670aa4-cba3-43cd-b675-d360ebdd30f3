/**
 * 裁片仓库表 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-06 16:48:11
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partStationWarehouseApi = {

  /**
   * 根据 id 查询  <AUTHOR>
   * @param id
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  queryById: (id) => {
    return getRequest(`/partStationWarehouse/getById/${id}`);
  },

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/partStationWarehouse/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/partStationWarehouse/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/partStationWarehouse/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/partStationWarehouse/delete/${id}`);
  },
  /**
   * 查询所有仓库  <AUTHOR>
   */
  getAll: () => {
    return postRequest(`/partStationWarehouse/getAll`);
  },

  /**
   * 出入库比率 <AUTHOR>
  inOutRatio: (param) => {
    return getRequest(`/partStationOptLog/stats/inOutRatio`, param);
  },

  /**
   * 出入库次数趋势  <AUTHOR>
   */
  inOutCountTrend: (param) => {
    return getRequest(`/partStationOptLog/stats/inOutCountTrend`, param);
  },
  /**
   * 出入库数量趋势  <AUTHOR>
   */
  inOutNumTrend: (param) => {
    return getRequest(`/partStationOptLog/stats/inOutNumTrend`, param);
  },
  /**
   * 获取所属仓库下货架数量 <AUTHOR>
  getRackNum: (param) => {
    return getRequest(`/partStationRack/getRackNum`, param);
  },
  /**
   * 获取所属仓库下货位数量 <AUTHOR>
  getBinNum: (param) => {
    return getRequest(`/partStationBin/getBinNum`, param);
  },

  /**
   * 总库存数 <AUTHOR>
   */
  totalInventoryNum: (param) => {
    return getRequest(`/partStationInventoryStats/totalInventoryNum`, param);
  },
  /**
   * 总扎数 <AUTHOR>
   */
  totalTieNum: (param) => {
    return getRequest(`/partStationInventoryStats/totalTieNum`, param);
  },
  /**
   * 平均存放天数 <AUTHOR>
   */
  averageStoredDays: (param) => {
    return getRequest(`/partStationInventoryStats/averageStoredDays`, param);
  },
  /**
   * 库位利用率 <AUTHOR>
   */
  binUsageRate: (param) => {
    return getRequest(`/partStationInventoryStats/binUsageRate`, param);
  },
};

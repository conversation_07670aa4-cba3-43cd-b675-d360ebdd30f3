<template>
  <a-select
    v-model:value="selectValue"
    :style="`width: ${width}`"
    :placeholder="props.placeholder"
    show-search
    :showSearch="true"
    :allowClear="true"
    :filter-option="filterOption"
    @change="onChange"
    :options="locationList"
  />
</template>

<script setup>
import { partStationBinApi } from '/@/api/business/mes/part-station/bin/part-station-bin-api';
import { onMounted, ref, watch, computed } from 'vue';
import { smartSentry } from '/@/lib/smart-sentry';

const props = defineProps({
  value: {
    type: [String, Number],  
    default: undefined,      
  },  
  width: {
    type: String,
    default: '100%',
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
}); 

const emit = defineEmits(['update:value', 'change']);
const locationList = ref([]);
const selectValue = ref(props.value);

const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};



async function queryLocationList() {
  try {
    const res = await partStationBinApi.queryPage({
      pageNum: 1, 
      pageSize: 500,
    });
    locationList.value = res.data.list.map((item) => ({
      value: item.id,
      label: item.binCode,
    }));
  } catch (error) {
    smartSentry.captureError(error);
  }
}

// 监听 value 变化
watch(
  () => props.value,
  (newValue) => {
    selectValue.value = newValue;  
  },
);

function onChange(value) {
  emit('update:value', value);
  emit('change', value);  
}

onMounted(() => {
  queryLocationList();
});
</script>
<!--
  * 员工申请表
  *
  * @Author:    cjm
  * @Date:      2024-09-19 22:50:32
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-row>
        <a-form-item label="主键" name="id">
          <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键"/>
        </a-form-item>
        <a-form-item label="删除标识;0未删除，1删除" name="deletedFlag">
          <BooleanSelect v-model:value="form.deletedFlag" style="width: 100%"/>
        </a-form-item>
        <a-form-item label="登录账号" name="loginName">
          <a-input style="width: 100%" v-model:value="form.loginName" placeholder="登录账号"/>
        </a-form-item>
        <a-form-item label="登录密码" name="loginPwd">
          <a-input style="width: 100%" v-model:value="form.loginPwd" placeholder="登录密码"/>
        </a-form-item>
        <a-form-item label="员工名称" name="actualName">
          <a-input style="width: 100%" v-model:value="form.actualName" placeholder="员工名称"/>
        </a-form-item>
        <a-form-item label="性别" name="gender">
          <BooleanSelect v-model:value="form.gender" style="width: 100%"/>
        </a-form-item>
        <a-form-item label="手机号码" name="phone">
          <a-input style="width: 100%" v-model:value="form.phone" placeholder="手机号码"/>
        </a-form-item>
        <a-form-item label="申请状态;0待审核 1已同意 2已拒绝" name="applyStatus">
          <a-input style="width: 100%" v-model:value="form.applyStatus" placeholder="申请状态;0待审核 1已同意 2已拒绝"/>
        </a-form-item>
      </a-row>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {employeeApplyApi} from '/@/api/business/mes/employee/employee-apply-api.js';
import {smartSentry} from '/@/lib/smart-sentry';
import BooleanSelect from '/@/components/framework/boolean-select/index.vue';

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined, //主键
  deletedFlag: undefined, //删除标识;0未删除，1删除
  loginName: undefined, //登录账号
  loginPwd: undefined, //登录密码
  actualName: undefined, //员工名称
  gender: undefined, //性别
  phone: undefined, //手机号码
  applyStatus: undefined, //申请状态;0待审核 1已同意 2已拒绝
};

let form = reactive({...formDefault});

const rules = {
  id: [{required: true, message: '主键 必填'}],
  deletedFlag: [{required: true, message: '删除标识 必填'}],
  loginName: [{required: true, message: '登录账号 必填'}],
  loginPwd: [{required: true, message: '登录密码 必填'}],
  actualName: [{required: true, message: '员工名称 必填'}],
  gender: [{required: true, message: '性别 必填'}],
  phone: [{required: true, message: '手机号码 必填'}],
  applyStatus: [{required: true, message: '申请状态 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await employeeApplyApi.update(form);
    } else {
      await employeeApplyApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

<template>
  <a-modal :title="'添加部位'" width="500px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-row>
      <a-col :span="24">
        <part-select v-model:value="selectedParts" :produceInstructOrderId="produceInstructOrderId" width="100%" />
      </a-col>
    </a-row>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { ref } from 'vue';
  import partSelect from '/@/components/business/mes/base/part-select/index.vue';
  import { message } from 'ant-design-vue';

  //----------------------- 事件 --------------------------
  const emits = defineEmits(['reloadList']);

  // 选中的部位
  const selectedParts = ref([]);
  // 生产指令单ID
  const produceInstructOrderId = ref();

  //----------------------------------------------
  const visibleFlag = ref(false);

  function show(instructOrderId) {
    // 设置生产指令单ID
    produceInstructOrderId.value = instructOrderId;
    visibleFlag.value = true;
    // 重置选择的部位
    selectedParts.value = [];
  }

  function onSubmit() {
    // 直接使用选中的部位数组
    if (selectedParts.value && selectedParts.value.length > 0) {
      emits('reloadList', selectedParts.value);
      onClose();
    } else {
      message.warning('请至少选择一个部位');
    }
  }

  function onClose() {
    selectedParts.value = [];
    visibleFlag.value = false;
  }

  defineExpose({
    show,
  });
</script>

<template>
  <a-modal v-model:open="visible" :width="900" title="选择物料" @cancel="closeModal" @ok="onSelectItem">
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="params.queryKey" placeholder="关键字" />
        </a-form-item>

        <a-form-item label="状态" class="smart-query-form-item">
          <a-select style="width: 120px" v-model:value="params.enableFlag" placeholder="请选择状态" allowClear>
            <a-select-option :key="true"> 禁用</a-select-option>
            <a-select-option :key="false"> 启用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <a-table
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: props.selectionType }"
      :loading="tableLoading"
      size="small"
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      bordered
      rowKey="id"
      :scroll="{ y: 300 }"
    >
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'enableFlag'">
          <a-tag :color="text === false ? 'processing' : 'error'">{{ text === false ? '启用' : '停用' }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ text === '0' ? '半成品' : '成品' }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'attribute'">
          <a-tag :color="ITEM_ATTRIBUTE_ENUM.getEnum(text).color">{{ ITEM_ATTRIBUTE_ENUM.getEnum(text).desc }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-preview-container">
            <file-preview :fileList="record.imgUrl && record.imgUrl.length > 0 ? [record.imgUrl[0]] : []" type="picture" :width="35" />
          </div>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="params.pageSize"
        v-model:current="params.pageNum"
        v-model:pageSize="params.pageSize"
        :total="total"
        @change="queryItem"
        @showSizeChange="queryItem"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
  import { message } from 'ant-design-vue';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { itemApi } from '/@/api/business/mes/item/item-api.js';
  import FilePreview from '/@/components/support/file-preview/index.vue';

  const emits = defineEmits(['selectData']);
  defineExpose({
    showModal,
  });

  const props = defineProps({
    attribute: String,
    attributeList: Array,
    //单选或多选
    selectionType: {
      type: String,
      default: 'checkbox',
      validator: (value) => ['checkbox', 'radio'].includes(value),
    },
  });
  const visible = ref(false);
  const selectedRowKeys = ref([]);

  const tableLoading = ref(false);
  const total = ref();

  let defaultParams = {
    queryKey: undefined,
    enableFlag: undefined,
    searchCount: undefined,
    attribute: props.attribute,
    attributeList: props.attributeList,
    category: undefined,
    pageNum: 1,
    pageSize: PAGE_SIZE,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  const params = reactive({ ...defaultParams });

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      align: 'center',
      width: 50,
    },
    {
      title: '物料编号',
      dataIndex: 'number',
      ellipsis: true,
    },
    {
      title: '物料名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      ellipsis: true,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      ellipsis: true,
    },
    {
      title: '价格',
      dataIndex: 'price',
      ellipsis: true,
    },
    {
      title: '停用标识',
      dataIndex: 'enableFlag',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'category',
      ellipsis: true,
    },
    {
      title: '属性',
      dataIndex: 'attribute',
      ellipsis: true,
    },
  ]);

  let tableData = ref([]);

  async function showModal() {
    selectedRowKeys.value = [];
    visible.value = true;
    onSearch();
  }

  function closeModal() {
    Object.assign(params, defaultParams);
    selectedRowKeys.value = [];
    visible.value = false;
  }

  function onSearch() {
    params.pageNum = 1;
    queryItem();
  }

  function reset() {
    Object.assign(params, defaultParams);
    queryItem();
  }

  //----------------------------

  async function queryItem() {
    tableLoading.value = true;
    try {
      let res = await itemApi.queryPage(params);
      tableData.value = res.data.list;
      total.value = res.data.total;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      tableLoading.value = false;
    }
  }

  function onSelectChange(keys) {
    selectedRowKeys.value = keys;
  }

  function onSelectItem() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择物料');
      return;
    }
    const selectedItems = tableData.value.filter((item) => selectedRowKeys.value.includes(item.id));
    emits('selectData', selectedItems); // 发送选中的物料数组
    closeModal();
  }
</script>
<style scoped>
  .image-preview-container {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }

  .image-preview-container :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
</style>

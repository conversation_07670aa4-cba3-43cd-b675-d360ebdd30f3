<!--
  * 生产安排信息
  *
  * @Author:    xmt
  * @Date:      2024-07-22 18:44:38
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="700px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
      <a-row>
        <a-form-item label="名称"  name="name">
          <a-input style="width: 100%" v-model:value="form.name" placeholder="名称"/>
        </a-form-item>
        <a-form-item label="备注"  name="remark">
          <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注"/>
        </a-form-item>

      </a-row>

    </a-form>

<!--编辑界面-->
<!--    <a-card size="small" :bordered="false" :hoverable="true" v-if="form.id">-->
      <a-button type="primary" @click="addItem(tableData)"   v-if="form.id" style="margin-bottom: 20px;">添加</a-button>
      <a-table
          size="small"
          :dataSource="tableData"
          :columns="columns"
          rowKey="id"
          bordered
          :loading="tableLoading"
          :pagination="false"
          v-if="form.id"
      >
        <template #bodyCell="{ index, record, column }">
          <template v-if="column.dataIndex==='nodeName'">
            <a-input v-model:value="record.nodeName"/>
          </template>

          <template v-if="column.dataIndex==='headId'">
            <employee-select  v-model:value="record.headId" style="width: 200px;"/>
          </template>

          <template v-else-if="column.dataIndex==='remark'">
            <a-input v-model:value="record.remark"/>
          </template>

          <template v-else-if="column.dataIndex==='action'">
            <div class="smart-table-operate">
              <a-button @click="deleteItem(index,tableData)" danger type="link">删除</a-button>
            </div>
          </template>

        </template>
      </a-table>
<!--    </a-card>-->

<!--新增界面-->
<!--    <a-card size="small" :bordered="false" :hoverable="true">-->
      <a-button type="primary" @click="addItem(insertData)"   v-if="!form.id" style="margin-bottom: 20px;">添加</a-button>
      <a-table
          size="small"
          :dataSource="insertData"
          :columns="insertColumns"
          rowKey="id"
          bordered
          :loading="tableLoading"
          :pagination="false"
          v-if="!form.id"
      >
        <template #bodyCell="{ index, record, column }">
          <template v-if="column.dataIndex==='nodeName'">
            <a-input v-model:value="record.nodeName"/>
          </template>

          <template v-else-if="column.dataIndex==='headId'">
            <employee-select  v-model:value="record.headId" style="width: 200px;"/>
          </template>

          <template v-else-if="column.dataIndex==='remark'">
            <a-input v-model:value="record.remark"/>
          </template>

          <template v-else-if="column.dataIndex==='action'">
            <div class="smart-table-operate">
              <a-button @click="deleteItem(index,insertData)" danger type="link">删除</a-button>
            </div>
          </template>
        </template>
      </a-table>
<!--    </a-card>-->

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {nextTick, reactive, ref} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/@/components/framework/smart-loading';
import {produceArrangeApi} from '/@/api/business/mes/produce/produce-arrange-api';
import {smartSentry} from '/@/lib/smart-sentry';
import EmployeeSelect from "/@/components/system/employee-select/index.vue";
import {lowerFirst} from "lodash/string.js";
// 表格加载loading
// ------------------------ 编辑 ------------------------
const tableLoading = ref(false);

const tableData = ref([])

const columns = ref([
  {
    title:"序号",
    dataIndex:"serialNumber",

  },
  {
    title: '节点名称',
    dataIndex: 'nodeName',

  },
  {
    title: '负责人',
    dataIndex: 'headId',

  },
  {
    title:"备注",
    dataIndex:"remark",

  },
  {
    title: '操作',
    dataIndex: 'action',

  },
])

async function queryData(id){
  console.log("id:",id)
  try {
    let res = await produceArrangeApi.queryDetailArrangeInfo(id)
    console.log(res.data.produceArrangeDetailVOS)
    if (res.data.produceArrangeDetailVOS != null) {
      tableData.value = res.data.produceArrangeDetailVOS
    }else{
      tableData.value = []
    }
    console.log(tableData.value,"111")
  } catch (e) {
    smartSentry.captureError(e);
  }
}

// ------------------------ 添加 ------------------------
const insertData = ref([])

const insertColumns = ref([
  {
    title:"序号",
    dataIndex:"serialNumber",

  },
  {
    title:"节点名称",
    dataIndex:"nodeName",

  },
  {
    title:"负责人",
    dataIndex:"headId",

  },
  {
    title:"备注",
    dataIndex:"remark",

  },
  {
    title:"操作",
    dataIndex:"action",

  },
])
//添加
function addItem(data){
  console.log(data)
  const newData = {
    "remark": "",
    "serialNumber": 0,
    "nodeName": "",
    "headId": null,
    "headName": "",
    "endFlag": false
  }
  data.push(newData)
  data.forEach((item, index) => {
    item.key = index;
    item.serialNumber = index + 1;
  });
}
//删除
function deleteItem(key,data){
  console.log(data,'222')
  data.splice(key,1)
  data.forEach((item,index)=>{
    item.key = index;
    item.serialNumber = index+1
  })
}

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  if (rowData.id) {
    console.log("编辑进入")
    queryData(rowData.id)
  }
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
  insertData.value = [] //重置table数据
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined,
  remark: undefined, //备注
  name: undefined, //名称
  produceArrangeDetailAddForms:undefined
};

let form = reactive({ ...formDefault });

const rules = {
  name: [{ required: true, message: '名称 必填' }],
};

const subData = reactive({
  "id": "",
  "remark": "",
  "name": "",
  "produceArrangeDetailAddForms" :[]
})

// 点击确定，验证表单
async function onSubmit() {
  subData.id = form.id
  subData.remark = form.remark
  subData.name = form.name
  console.log(subData)
  subData.produceArrangeDetailAddForms = []
  tableData.value.map((item,index)=>{
    let flag = false
    if (index ===tableData.value.length-1){
      flag = true
    }
    subData.produceArrangeDetailAddForms.push({
      "remark": item.remark,
      "serialNumber": item.serialNumber,
      "nodeName": item.nodeName,
      "headId": item.headId,
      "headName": item.headName,
      "endFlag": flag
    })
  })
  console.log("after:",subData)
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await produceArrangeApi.update(subData);
    } else {

      form.produceArrangeDetailAddForms = insertData.value
      let len = form.produceArrangeDetailAddForms.length
      if (form.produceArrangeDetailAddForms.length > 0) {
        form.produceArrangeDetailAddForms[len-1].endFlag = true
      }
      await produceArrangeApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}
defineExpose({
  show,
});
</script>

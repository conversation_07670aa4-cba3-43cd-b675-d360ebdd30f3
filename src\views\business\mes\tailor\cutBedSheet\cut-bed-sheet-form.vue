<!--
  * 裁床单
  *
  * @Author:    cjm
  * @Date:      2024-07-13 10:53:07
  * @Copyright  cjm
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="1000px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 10 }" :wrapperCol="{ span: 14 }">
      <a-row>
        <a-col :span="8">
          <a-form-item label="单据编号" name="cutNumber">
            <a-input style="width: 100%" v-model:value="form.cutNumber" placeholder="单据编号" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="裁床单名称" name="cutSheetName">
            <a-input style="width: 100%" v-model:value="form.cutSheetName" placeholder="裁床单名称" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="生产指令单编号" name="instructOrderNumber">
            <a-input style="width: 100%" v-model:value="form.instructOrderNumber" placeholder="生产指令单编号" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="生产指令单名称" name="instructOrderName">
            <a-input style="width: 100%" v-model:value="form.instructOrderName" placeholder="生产指令单名称" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物料编号" name="itemNumber">
            <a-input style="width: 100%" v-model:value="form.itemNumber" placeholder="物料编号" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物料名称" name="itemName">
            <a-input style="width: 100%" v-model:value="form.itemName" placeholder="物料名称" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="规格型号" name="model">
            <a-input style="width: 100%" v-model:value="form.model" placeholder="规格型号" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物料单位名称" name="unitName">
            <a-input style="width: 100%" v-model:value="form.unitName" placeholder="物料单位名称" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物料属性" name="attribute">
            <a-input
              style="width: 100%"
              v-model:value="ITEM_ATTRIBUTE_ENUM.getEnum(form.attribute).label"
              placeholder="物料属性;0面料，1其他，2成衣"
              :disabled="isFormReadOnly"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物料类型" name="category">
            <a-input
              style="width: 100%"
              v-model:value="ITEM_CATEGORY_ENUM.getEnum(form.category).label"
              placeholder="物料类型;0半成品 1成品"
              :disabled="isFormReadOnly"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="床次" name="cutNum">
            <a-input-number style="width: 100%" v-model:value="form.cutNum" placeholder="床次" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="总裁数" name="quantity">
            <a-input-number style="width: 100%" v-model:value="form.quantity" placeholder="总裁数" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="裁剪人" name="cutterId">
            <EmployeeSelect v-model:value="form.cutterId" :disabled="isFormReadOnly" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <a-table :dataSource="groupedDetails" :columns="columns" :pagination="false" bordered>
      <!-- 自定义单元格插槽,用于显示数量-->
      <template #customRenderCell="{ record }">
        {{ record.data.details.num }}
      </template>
    </a-table>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick, watch, onMounted } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { cutBedSheetApi } from '/@/api/business/mes/tailor/cut-bed-sheet-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { employeeApi } from '/src/api/system/employee-api';
  import EmployeeSelect from '/@/components/system/employee-select/index.vue';
  const sizes = ref([]);
  const colors = ref([]);
  const groupedDetails = ref([]);
  const columns = ref([]);
  const Id = ref(null);
  const isFormReadOnly = ref(true);

  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
      Id.value = form.id;
    });
  }
  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    // id: undefined,
    id: undefined, //主键
    cutNumber: undefined, //单据编号
    cutSheetName: undefined, //裁床单名称
    instructOrderNumber: undefined, //生产指令单编号
    instructOrderName: undefined, //生产指令单名称
    instructOrderId: undefined, //生产指令单id
    itemId: undefined, //物料id
    itemNumber: undefined, //物料编号
    model: undefined, //规格型号
    itemName: undefined, //物料名称
    unitId: undefined, //物料单位id
    unitName: undefined, //物料单位名称
    attribute: undefined, //物料属性;0面料，1其他，2成衣
    category: undefined, //物料类型;0半成品 1成品
    cutNum: undefined, //床次
    quantity: undefined, //总裁数
    cutterId: undefined, //裁剪人id
    cutter: undefined, //裁剪人
  };

  let form = reactive({ ...formDefault });

  const rules = {
    // id: [{ required: true, message: '主键 必填' }],
    cutNumber: [{ required: true, message: '单据编号 必填' }],
    instructOrderNumber: [{ required: true, message: '生产指令单编号 必填' }],
    // instructOrderName: [{ required: true, message: '生产指令单名称 必填' }],
    instructOrderId: [{ required: true, message: '生产指令单id 必填' }],
    itemId: [{ required: true, message: '物料id 必填' }],
    itemNumber: [{ required: true, message: '物料编号 必填' }],
    model: [{ required: true, message: '规格型号 必填' }],
    itemName: [{ required: true, message: '物料名称 必填' }],
    unitId: [{ required: true, message: '物料单位id 必填' }],
    unitName: [{ required: true, message: '物料单位名称 必填' }],
    attribute: [{ required: true, message: '物料属性 必填' }],
    category: [{ required: true, message: '物料类型 必填' }],
    cutNum: [{ required: true, message: '床次 必填' }],
    quantity: [{ required: true, message: '总裁数 必填' }],
  };

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await cutBedSheetApi.update(form);
      } else {
        await cutBedSheetApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }
  // 定义获取详情数据的函数
  async function fetchDetailsData() {
    // 假设 Id.value 已经包含了需要获取数据的 ID
    const response = await cutBedSheetApi.viewDetails(Id.value);
    if (response && response.data && response.data.details) {
      groupedDetails.value = response.data.details;
      sizes.value = Array.from(new Set(groupedDetails.value.map((item) => item.size)));
      colors.value = Array.from(new Set(groupedDetails.value.map((item) => item.styleColor)));
      initTable();
    } else {
      message.error('获取数据失败：' + (response.msg || ''));
    }
  }

  // 使用 watch 来观察 Id 的变化
  watch(
    Id,
    (newVal) => {
      // 当 Id 发生变化时执行
      if (newVal !== null) {
        // 如果新的 Id 值不是 null，获取详情数据
        fetchDetailsData();
      }
    },
    { immediate: false }
  ); // immediate 设置为 false，防止在初始时触发
  defineExpose({
    show,
  });
  // 初始化表格列和数据源的方法
  function initTable() {
    columns.value = [];
    columns.value.push({
      title: '颜色',
      dataIndex: 'styleColor',
      key: 'styleColor',
      width: 100,
    });
    sizes.value.forEach((size) => {
      columns.value.push({
        title: size,
        dataIndex: size,
        key: size,
        scopedSlots: { customRender: 'customRenderCell' },
      });
    });
    // 创建分组的表格数据
    groupedDetails.value = colors.value.map((color) => {
      const groupedBySize = groupedDetails.value
        .filter((item) => item.styleColor === color)
        .reduce((acc, item) => {
          acc[item.size] = item.num; // 假设每个item都有一个num属性
          return acc;
        }, {});
      return {
        styleColor: color,
        ...groupedBySize,
      };
    });
  }
</script>

/**
 * 裁片货位 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-10-06 20:17:14
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const partStationBinApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/partStationBin/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/partStationBin/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/partStationBin/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/partStationBin/delete/${id}`);
  },

  getQRURL:(id)=>{
    return getRequest(`/partStationBin/getQrCode/${id}`)
  },

  /**
   * 查询货位下菲票
   * @param id
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  queryBinFeTicket: (id) => {
    return getRequest(`/partStationBin/getFeTicketList/${id}`);
  },

  //获取货位码内容
  getQrCodeContent: (ids) => {
    return postRequest(`/partStationBin/getQrCodeContent`,ids);
  },

  /**
   * 根据id查询货位
   * @param id
   * @returns {Promise<axios.AxiosResponse<any>>}
   */
  queryBinById: (id) => {
    return getRequest(`/partStationBin/getById/${id}`);
  },
};

/**
 * 裁片任务 api 封装
 *
 * @Author:    pxz
 * @Date:      2024-10-06 16:49
 * @Copyright  zscbdic
 */

import { postRequest, getRequest } from '/@/lib/axios';

export const partStationTurnTaskApi = {
    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/partStationTurnTask/queryPage', param);
    },
    /**
     * 查询我的任务  <AUTHOR>
     */
    queryMyTaskPage: (param) => {
        return postRequest('/partStationTurnTask/queryMyTaskPage', param);
    },

    /**
     * 批量删除  <AUTHOR>
     */
    batchDelete: (ids) => {
        return postRequest('/partStationTurnTask/batchDelete', ids);
    },
    /**
     * 删除  <AUTHOR>
     */
    delete: (id) => {
        return getRequest(`/partStationTurnTask/delete/${id}`);
    },
    /**
     * 取消任务  <AUTHOR>
     */
    cancelTask: (ids) => {
        return postRequest(`/partStationTurnTask/cancel`, ids);
    }
}

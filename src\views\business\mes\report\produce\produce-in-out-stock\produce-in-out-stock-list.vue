<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="生产指令单号" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="form.produceInstructOrderNumber" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="生产状态" class="smart-query-form-item">
                <a-select v-model:value="form.produceStatus" style="width: 150px" placeholder="请选择" :options="Object.values(PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM).slice(0,-1)"></a-select>
            </a-form-item>
            <a-form-item label="优先级" class="smart-query-form-item">
                <a-select v-model:value="form.producePriority" style="width: 150px" placeholder="请选择" :options="OPTIONS"></a-select>
            </a-form-item>
            <a-form-item label="生产交付时间" class="smart-query-form-item">    
                <a-range-picker 
                    v-model:value="form.produceDeliverTime"
                    :presets="defaultTimeRanges" 
                    style="width: 220px" 
                    @change="onChangeDeliverTime" 
                />
            </a-form-item>
            <a-form-item label="物料SPU编码" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="form.materialSpuNumber" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="物料SKU编码" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="form.materialSkuNumber" placeholder="请输入" />
            </a-form-item>
            <a-form-item label="物料名称" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="form.materialName" placeholder="请输入" />
            </a-form-item>
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon><SearchOutlined /></template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon><ReloadOutlined /></template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block"></div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>

        <a-table
            size="small"
            :dataSource="tableData"
            :columns="columns"
            rowKey="produceClothesDataId"
            bordered
            :loading="tableLoading"
            :pagination="false"
            :scroll="{ x: 2200 }"
        >
        <template #bodyCell="{ text, record, column }">
            <template v-if="column.dataIndex === 'producePriority'">
             <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).label }}
            </a-tag>
            </template>
            <template v-else-if="column.dataIndex === 'produceStatus'">
                <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).color">
                    {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}
                </a-tag>
            </template>
        </template>
        </a-table>

        <div class="smart-query-table-page">
            <a-pagination
                showSizeChanger
                showQuickJumper
                show-less-items
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                :defaultPageSize="form.pageSize"
                v-model:current="form.pageNum"
                v-model:pageSize="form.pageSize"
                :total="total"
                @change="queryData"
                @showSizeChange="queryData"
                :show-total="(total) => `共${total}条`"
            />
        </div>
    </a-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
import { produceReportApi } from '/@/api/business/mes/produce/produce-report-api.js';
import { defaultTimeRanges } from '/@/lib/default-time-ranges';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,PRODUCE_TYPE_ENUM } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';


const formDefault = {
    pageNum: 1,
    pageSize: 10,
    produceDeliverTime: [],
    produceDeliverTimeBegin: undefined, // 生产交付时间开始
    produceDeliverTimeEnd: undefined, // 生产交付时间结束
    produceInstructOrderNumber: undefined, // 生产指令单号
    produceStatus: undefined, // 生产业务状态
    producePriority: undefined, // 优先级
    materialSpuNumber: undefined, // 物料spu编码
    materialSkuNumber: undefined, // 物料sku编码 
    materialName: undefined, // 物料名称
    sortItemList: [{ column: 't_mes_produce_instruct_order.create_time', isAsc: false }],
}
const form = reactive({...formDefault});

// 表格列定义
const columns = ref([
    {
        title: '生产指令单号',
        dataIndex: 'produceInstructOrderNumber',
        width: 150,
        align: 'center',    
        ellipsis: true,
    },
    {
        title: '生产状态',
        dataIndex: 'produceStatus',
        align: 'center',
    },
    {
        title: '优先级',
        dataIndex: 'producePriority',
        align: 'center',
    },
    {
        title: '发布时间',
        dataIndex: 'produceIssuedTime',
        align: 'center',
        width:150,
        ellipsis: true,
    },
    {
        title: '交货时间',
        dataIndex: 'produceDeliverTime',
        ellipsis: true,
        width:100,
        align: 'center',
    },
    {
        title: '物料名称',
        dataIndex: 'materialName',
        align: 'center',
        width:150,
        ellipsis: true,
    },
    {   
        title: '物料型号',
        dataIndex: 'materialModel',
        align: 'center',
        width:250,
        ellipsis: true,
    },
    {
        title: '物料SPU编号',
        dataIndex: 'materialSpuNumber',
        width: 150,
        align: 'center',
        ellipsis: true,
    },
    {
        title: '物料SKU编号',
        dataIndex: 'materialSkuNumber',
        width: 150,
        align: 'center',
        ellipsis: true,
    },
    {
        title: '款式颜色',
        dataIndex: 'style',
        align: 'center',
    },
    {
        title: '尺码',
        dataIndex: 'size',
        align: 'center',
    },
    {
        title: '单位',
        dataIndex: 'unitName',
        align: 'center',
    },
    {
    title: '产品入库',
    children: [  
        {
            title: '生产数量',
            dataIndex: 'produceNum',
            align: 'center',
        },
        {
            title: '合格品数量',
            dataIndex: 'qualityInQty',
            align: 'center',
            },
        {
            title: '不合格品数量',
            dataIndex: 'nonQualityInQty',
            align: 'center',
        },
        {
            title: '报废品数量',
            dataIndex: 'scrapInQty',
            align: 'center',
        },
        {
            title: '返工品数量',
            dataIndex: 'reworkInQty',
            align: 'center',
        },
        {
            title: '退库数量',
            dataIndex: 'returnOutQty',
            align: 'center',
        },
        {
            title: '已入库数量',
            dataIndex: 'totalInQty',
            align: 'center',
            customRender: ({ record }) => {
                // 已入库 = 合格品 + 不合格 + 报废 + 返工 - 退库
                const total = (record.qualityInQty || 0) + (record.nonQualityInQty || 0) + (record.scrapInQty || 0) + (record.reworkInQty || 0) - (record.returnOutQty || 0);
                record._totalInQty = total;
                return total;
            }
        },
        {
            title: '未入库数量',
            dataIndex: 'remainingQty',
            align: 'center',
            customRender: ({ record }) => {
                // 未入库 = 生产数量 - 已入库数量
                const remaining = (record.produceNum || 0) - record._totalInQty;
                return remaining;
            }
        },
        {
            title: '达成率(%)',
            dataIndex: 'completionRate',
            align: 'center',
            customRender: ({ record }) => {
                // 达成率 = 已入库数量 / 生产数量
                const rate = record.produceNum ? ((record._totalInQty / record.produceNum) * 100).toFixed(2) : 0;
                if(rate<0){
                    return `0%`;
                }
                return `${rate}%`;
            }
        }
    ]
}
]);

// 表格数据和加载状态
const tableData = ref([]);
const tableLoading = ref(false);
const total = ref(0);

// 查询条件重置
function resetQuery() {
    let pageSize = form.pageSize;
    Object.assign(form, formDefault);
    form.pageSize = pageSize;
    queryData();
}

// 交付时间改变处理
function onChangeDeliverTime(dates, dateStrings) {
    form.produceDeliverTimeBegin = dateStrings[0];
    form.produceDeliverTimeEnd = dateStrings[1];
}

// 查询数据
async function queryData() {
    tableLoading.value = true;
    try {
        let queryResult = await produceReportApi.queryProduceInOutStock(form);
        tableData.value = queryResult.data.list;
        total.value = queryResult.data.total;
    } catch (e) {
        smartSentry.captureError(e);
    } finally {
        tableLoading.value = false;
    }
}
// 指令单优先级
const OPTIONS =  [
  {
    value: '0',
    label: '一般',
    color: '#46c26f',
  },
  {
    value: '1',
    label: '紧急',
    color: '#f0a800',
  },
  {
    value: '2',
    label: '非常紧急',
    color: '#eb5050',
  }
]
onMounted(queryData);
</script>
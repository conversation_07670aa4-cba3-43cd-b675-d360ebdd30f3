<!--
  * 第三方登陆与系统用户绑定
  *
  * @Author:    linwj
  * @Date:      2025-01-12 14:34:00
  * @Copyright  zscbdic
  *
-->
<template>
  <a-modal v-model:open="open" title="第三方信息绑定" @ok="handleOk" style="width: 400px" @cancel="handleCancel">
    <a-form ref="formRef" :model="loginForm" :rules="rules">
      <span style="color: #909399">用户名:</span>
      <a-form-item>
        <a-input v-model:value.trim="loginForm.loginName" placeholder="请输入系统用户名" style="height: 42px"/>
      </a-form-item>
      <a-form-item>
        <span style="color: #909399">密码:</span>
        <a-input-password v-model:value="loginForm.password" autocomplete="on"
                          :type="showPassword ? 'text' : 'password'"
                          placeholder="请输入密码" style="height: 42px"/>
      </a-form-item>
      <a-form-item name="captchaCode">
        <span style="color: #909399">验证码:</span>
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <div style="width: 70%">
            <a-input class="captcha-input" v-model:value.trim="loginForm.captchaCode" placeholder="请输入验证码"
                     style="height: 42px;"/>
          </div>
          <img class="captcha-img" :src="captchaBase64Image" @click="getCaptcha"/>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import {onMounted, onUnmounted, reactive, ref} from 'vue';
import {LOGIN_DEVICE_ENUM} from "/src/constants/system/login-device-const.js";
import {message} from "ant-design-vue";
import {useRouter} from "vue-router";
import {SmartLoading} from "/src/components/framework/smart-loading/index.js";
import {encryptData} from "/src/lib/encrypt.js";
import {loginApi} from "/src/api/system/login-api.js";
import {localSave} from "/src/utils/local-util.js";
import LocalStorageKeyConst from "/src/constants/local-storage-key-const.js";
import {useUserStore} from "/src/store/modules/system/user.js";
import {buildRoutes} from "/src/router/index.js";
import {smartSentry} from "/src/lib/smart-sentry.js";
import {thirdAuthApi} from "/src/api/system/third-auth-api.js";
import {AUTH_TYPE_ENUM} from "/src/constants/system/third-auth-const.js";
import feishuIcon from "/src/assets/images/login/feishu-icon.png";

const router = useRouter();
const loginForm = reactive({
  loginName: '',
  password: '',
  captchaCode: '',
  captchaUuid: '',
  loginDevice: LOGIN_DEVICE_ENUM.PC.value,
});
const rules = {
  loginName: [{required: true, message: '用户名不能为空'}],
  password: [{required: true, message: '密码不能为空'}],
  captchaCode: [{required: true, message: '验证码不能为空'}],
};
const showPassword = ref(false);
const open = ref(false);

const formRef = ref();
function show(data) {
  if (data.sceneType === AUTH_TYPE_ENUM.UN_AUTH_UN_LOGIN.value) {
    // 未绑定则先绑定
    handleUnBind(data)
  }else if (data.sceneType === AUTH_TYPE_ENUM.AUTH_UN_LOGIN.value) {
    // 绑定则直接登陆
    handleLogin(data.loginResultVO)
  }
}

function handleLogin(userLoginInfo) {
  try {
    SmartLoading.show();
    localSave(LocalStorageKeyConst.USER_TOKEN, userLoginInfo.token ? userLoginInfo.token : '');
    message.success('登录成功');
    //更新用户信息到pinia
    useUserStore().setUserLoginInfo(userLoginInfo);
    //构建系统的路由
    buildRoutes();
    window.location.href = window.location.protocol + '//' + window.location.host
  } catch (e) {
    if (e.data && e.data.code !== 0) {
      loginForm.captchaCode = '';
      getCaptcha();
    }
    smartSentry.captureError(e);
  } finally {
    SmartLoading.hide();
  }
}

function handleUnBind(data) {
  message.warning("账号信息未绑定，请先绑定")
  loginForm.openId = data.openId
  open.value = true;
}

function handleCancel() {
  message.warning("取消绑定")
  // 跳转回登录页；协议+//+ 域名
  window.location.href = window.location.protocol + '//' + window.location.host;
}


function handleOk() {
  onLogin()
}

// ------------------- 登陆与验证码 ---------------------
async function onLogin() {
  formRef.value.validate().then(async () => {
    try {
      SmartLoading.show();
      // 密码加密
      let encryptPasswordForm = Object.assign({}, loginForm, {
        password: encryptData(loginForm.password),
      });
      const res = await thirdAuthApi.bindAndLogin(encryptPasswordForm);
      localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
      message.success('登录成功');
      //更新用户信息到pinia
      useUserStore().setUserLoginInfo(res.data);
      //构建系统的路由
      buildRoutes();
      window.location.href = window.location.protocol + '//' + window.location.host
    } catch (e) {
      if (e.data && e.data.code !== 0) {
        loginForm.captchaCode = '';
        getCaptcha();
      }
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  });
}

const captchaBase64Image = ref('');

async function getCaptcha() {
  try {
    let captchaResult = await loginApi.getCaptcha();
    captchaBase64Image.value = captchaResult.data.captchaBase64Image;
    loginForm.captchaUuid = captchaResult.data.captchaUuid;
    beginRefrestCaptchaInterval(captchaResult.data.expireSeconds);
  } catch (e) {
    console.log(e);
  }
}

let refrestCaptchaInterval = null;

function beginRefrestCaptchaInterval(expireSeconds) {
  if (refrestCaptchaInterval === null) {
    refrestCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
  }
}

function stopRefrestCaptchaInterval() {
  if (refrestCaptchaInterval != null) {
    clearInterval(refrestCaptchaInterval);
    refrestCaptchaInterval = null;
  }
}
onMounted(getCaptcha);
onUnmounted(stopRefrestCaptchaInterval);

defineExpose({
  show
})
</script>

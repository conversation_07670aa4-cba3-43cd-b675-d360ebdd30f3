<template>
  <div class="card">
    <!--    <div class="title">出入库次数趋势</div>-->
    <!--    <div class="time">2024-10-20 ~ 2024-11-20</div>-->
    <div class="echarts-box">
      <div class="line-main" id="line-main-1"></div>
    </div>
  </div>
</template>
<script setup>
  import * as echarts from 'echarts';
  import { onMounted, reactive, ref, watch } from 'vue';
  import DefaultHomeCard from '/@/views/system/home/<USER>/default-home-card.vue';

  let myChart = null;

  const data = ref(); // 父组件传递的数据
  const XTime = ref([]); // x轴时间数据
  const inData = ref([]); // 入库数据
  const outData = ref([]); // 出库数据
  onMounted(() => {
    init();
  });
  let option = reactive({});

  // 监听父组件传递的数据变化，重新渲染图表
  watch(data, (newValue) => {
    console.log(newValue);

    if (newValue) {
      XTime.value = [];
      inData.value = [];
      outData.value = [];
      newValue.in.forEach((item) => {
        XTime.value.push(item.x);
        inData.value.push(item.y);
      });
      newValue.out.forEach((item) => {
        outData.value.push(item.y);
      });
    }
    init();
  });
  function init() {
    option = {
      xAxis: {
        type: 'category',
        data: XTime.value,
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      legend: {
        data: ['入库', '出库'],
      },
      grid: {
        x: 50,
        y: 40,
        x2: 30,
        y2: 30,
      },
      title: {
        text: '出入库数量趋势',
      },
      series: [
        {
          name: '入库',
          data: inData.value,
          type: 'line',
          symbolSize: 8, //设定实心点的大小
          smooth: true,
          itemStyle: {
            color: '#a483ff',
          },
          lineStyle: {
            width: 3,
            color: '#a483ff',
          },
          label: {
            show: true, // 是否显示标签
            position: 'top', // 标签的位置
          },
        },
        {
          name: '出库',
          data: outData.value,
          type: 'line',
          symbolSize: 8, //设定实心点的大小
          smooth: true,
          itemStyle: {
            color: '#00c2ff',
          },
          lineStyle: {
            width: 3,
            color: '#00c2ff',
          },
          label: {
            show: true, // 是否显示标签
            position: 'top', // 标签的位置
          },
        },
      ],
    };
    let chartDom = document.getElementById('line-main-1');
    if (chartDom) {
      myChart = echarts.init(chartDom);
      option && myChart.setOption(option);
    }
    window.addEventListener("resize", () => {
      myChart.resize();
    });
  }
  defineExpose({
    data,
  });
</script>
<style scoped lang="less">
  .card {
    //background-color: #fff;
    padding: 8px;
    width: 100%;

    .title {
      font-size: 16px;
      line-height: 16px;
      color: #303133;
      //margin-bottom: 4px;
    }

    .time {
      font-size: 16px;
      color: #9b9b9b;
      margin-bottom: 4px;
    }
  }

  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .line-main {
      width: 100%;
      height: 200px;
      background: #fff;
    }
  }
</style>

export const QR_CODE_TYPE_ENUM = {

    FE_TICKET: {
        value: 'feTicket',
        desc: '菲票',
    },

    PART_STATION_TURN_BOX: {
        value: 'partStationTurnBox',
        desc: '裁片周转箱',
    },

    PART_STATION_BIN: {
        value: 'partStationBin',
        desc: '裁片库位',
    },

    UNKNOWN: {
        value: 'unknown',
        desc: '未知'
    },


    getTypeByCode(value) {
        let type = value.split("-")[0]
        if (!type) {
            return QR_CODE_TYPE_ENUM.UNKNOWN
        }
        if (type === QR_CODE_TYPE_ENUM.FE_TICKET.value) {
            return QR_CODE_TYPE_ENUM.FE_TICKET
        }else if (type === QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX.value) {
            return QR_CODE_TYPE_ENUM.PART_STATION_TURN_BOX
        }else if (type === QR_CODE_TYPE_ENUM.PART_STATION_BIN.value) {
            return QR_CODE_TYPE_ENUM.PART_STATION_BIN
        }
        return QR_CODE_TYPE_ENUM.UNKNOWN
    },

    getValueByCode(code) {
        return code.split("-")[1];
    }

}
export default {
    QR_CODE_TYPE_ENUM
}

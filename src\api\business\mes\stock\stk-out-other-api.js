/**
 * 其他出库单 api 封装
 *
 * @Author:    fkf
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkOutOtherApi = {

  /**
   * 分页查询  
   */
  queryPage : (param) => {
    return postRequest('/stkOtherOutStock/queryPage', param);
  },
  /**
   * 增加  
   */
  add: (param) => {
      return postRequest('/stkOtherOutStock/add', param);
  },

  /**
   * 修改  
   */
  update: (param) => {
      return postRequest('/stkOtherOutStock/update', param);
  },

  /**
   * 根据id查询其他出库单  
   */
  byId: (id) => {
      return getRequest(`/stkOtherOutStock/byId?id=${id}`);
  },

  /**
   * 修改单据状态
   */
  status: (id) => {
      return getRequest(`/stkOtherOutStock/status?id=${id}`);
  },

  /**
   * 删除
   */
  delete: (id) => {
      return getRequest(`/stkOtherOutStock/delete?id=${id}`);
  },
};

<!--
  * 批号输入和搜索下拉选择组件
  * 返回输入批号或者所选批号对象
  * @Author:    lyq
  * @Date:      2025-02-12
  * @Copyright  zscbdic
-->
<template>
  <a-select
    v-model:value="selectedValue"
    :style="{ width: width }"
    :placeholder="placeholder"
    show-search
    :filter-option="false"
    :allowClear="true"
    @search="handleSearch"
    @change="handleChange"
  >
    <!-- 自定义选项渲染 -->
    <a-select-option v-for="item in filteredOptions" :key="item.id || 'custom'" :value="item.id ? item.id : 'custom'">
      {{ item.number }}
      <span v-if="!item.id" style="color: #999; margin-left: 10px">(自定义)</span>
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { stkLotMasterApi } from '/@/api/business/mes/stock/lot-master-api';

  const props = defineProps({
    // value:[String,Object]
    value: Number,
    materielId: Number, // 新增物料ID prop
    placeholder: {
      type: String,
      default: '请选择或输入批号',
    },
    width: {
      type: String,
      default: '100%',
    },
  });

  const emit = defineEmits(['update:value', 'change']);

  // 数据相关
  const lotOptions = ref([]);
  const searchText = ref('');
  const selectedValue = ref();

  // 过滤后的选项（包含自定义选项）
  const filteredOptions = computed(() => {
    const options = lotOptions.value.filter((item) => item.number.includes(searchText.value));

    //自定义选项（当输入内容不在现有选项中时）
    if (searchText.value && !options.some((item) => item.number === searchText.value)) {
      options.unshift({
        id: null,
        number: searchText.value,
      });
    }
    return options;
  });

  // 获取批次列表（根据物料ID）
  async function queryLotList() {
    if (!props.materielId) return;
    try {
      const res = await stkLotMasterApi.queryLotList({
        materielId: props.materielId,
      });
      lotOptions.value = res.data;
    } catch (e) {
      console.error('获取批次失败', e);
    }
  }

  // 处理搜索
  function handleSearch(value) {
    searchText.value = value;
  }

  // 处理选择变化
  function handleChange(value) {
    const selected = filteredOptions.value.find((item) => item.id === value || (value === 'custom' && !item.id));

    const result = selected
      ? {
          id: selected.id,
          number: selected.number,
        }
      : null;

    emit('update:value', result?.id);
    emit('change', result);
  }

  // 监听物料ID变化
  watch(
    () => props.materielId,
    (newVal) => {
      if (newVal) {
        queryLotList();
      } else {
        lotOptions.value = [];
      }
    },
    { immediate: true },
    { deep: true }
  );

  // 监听外部value变化
  // watch(
  //   () => props.value,
  //   (newVal) => {
  //     if (newVal?.lotId) {
  //       selectedValue.value = newVal.lotId;
  //     } else if (newVal?.number) {
  //       selectedValue.value = 'custom';
  //       searchText.value = newVal.number;
  //     }
  //   },
  //   { immediate: true },
  //   { deep: true }
  // );
   watch(
    () => props.value,
    (newVal) => {
      if (newVal) {
        selectedValue.value = newVal;
      } else if (newVal?.number) {
        selectedValue.value = 'custom';
        searchText.value = newVal.number;
      }
    },
    { immediate: true },
    { deep: true }
  );
</script>

/**
 * 松布架 api 封装
 *
 * @Author:    liwj
 * @Date:      2025-06-25 15:29:48
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const loosenFrameApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/loosenFrame/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/loosenFrame/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/loosenFrame/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/loosenFrame/delete/${id}`);
  },

  /**
   * 松布架二维码  <AUTHOR>
   */
  getFrameCode: (frameId) => {
    return getRequest(`/loosenFrame/QrCodeContent?frameId=${frameId}`);
  },

};

/**
 * 报工记录 枚举
 *
 * @Author:    cjm
 * @Date:      2024-07-22 20:33:41
 * @Copyright  cjm
 */


export const Work_Record_AuditFlag_ENUM = {
    UNAUDITED : {
        value: '0',
        desc: '未审核',
        label: '未审核',
        color: '#b0acac',
    },
    AUDITED: {
        value: '1',
        desc: '已审核',
        label: '已审核',
        color: '#46c26f',
    },
    AUDIT_FAIL: {
        value: '2',
        desc: '不通过',
        label: '不通过',
        color: '#eb5050',

    },
    UNKNOWN: {
        value: '3',
        desc: '未知',
        label: '未知',
        color: '#b0acac',
    },
    getEnum(value) {
        if (value === '0') {
            return  Work_Record_AuditFlag_ENUM.UNAUDITED;
        } else if (value === '1') {
            return Work_Record_AuditFlag_ENUM.AUDITED;
        } else if (value === '2') {
            return  Work_Record_AuditFlag_ENUM.AUDIT_FAIL;
        }
        return Work_Record_AuditFlag_ENUM.UNKNOWN;
    }
};
export const Work_Record_Status_ENUM = {
     NORMAL: {
        value: '0',
        desc: '正常',
        label: '正常',
        color: '#108ee9',
    },
      CANCEL:{
         value: '1',
        desc: '作废',
        label: '作废',
        color: '#969393',
    },
    UNKNOWN: {
        value: '2',
        desc: '未知',
        label: '未知',
        color: '#b0acac',
    },
    getEnum(value) {
        if (value === '0') {
            return Work_Record_Status_ENUM.NORMAL;
        } else if (value === '1') {
            return Work_Record_Status_ENUM. CANCEL;
        }
        return Work_Record_Status_ENUM.UNKNOWN;

    }
};

export default {
    Work_Record_AuditFlag_ENUM,
    Work_Record_Status_ENUM
};



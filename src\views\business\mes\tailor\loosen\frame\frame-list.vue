<!--
  * 松布架 list
  * @Author:    linwj
  * @Date:      2024-11-05 20:49:04
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="'选择松布架'" width="600px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="松布架" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.frameName" placeholder="请输入松布架名称" />
        </a-form-item>
        <a-form-item class="smart-query-form-item">
          <a-button type="primary" @click="queryData">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
          <!--          <a-button @click="showForm" class="smart-margin-left10">-->
          <!--            <PlusOutlined />-->
          <!--            添加-->
          <!--          </a-button>-->
        </a-form-item>
      </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->
    <a-table size="small" :dataSource="tableData" :columns="columns" rowKey="id" bordered :loading="tableLoading" :pagination="false">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'frameName'">
          <a-popover v-model:open="record.QrVisiable" placement="left" trigger="click">
            <template #content>
              <a-qrcode :value="frameQrCode" />
            </template>
            <a-button @click="showQRById(record.id)" type="link">{{ record.frameName }}</a-button>
          </a-popover>
        </template>
      </template>
    </a-table>
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onClose">确定</a-button>
      </a-space>
    </template>
  </a-modal>

  <!--  <StyleColorForm ref="formRef" @reloadList="queryData"/>-->
</template>
<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const.js';
  import { loosenFrameApi } from '/@/api/business/mes/tailor/loosen-frame-api.js';

  const formRef = ref();

  function showForm(data) {
    formRef.value.show(data);
  }
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);
  function show() {
    visibleFlag.value = true;
  }

  function onClose() {
    visibleFlag.value = false;
  }

  const columns = ref([
    {
      title: '松布架',
      dataIndex: 'frameName',
      ellipsis: true,
      width: 50,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      width: 100,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------
  const queryFormState = {
    pageNum: 1,
    pageSize: 10,
    frameName: '', //松布架名称
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }
  // 查询数据
  const frameQrCode = ref('');
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await loosenFrameApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = tableData.value.length;
      tableData.value.map((e) => {
        e['QrVisiable'] = false;
      });
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  async function showQRById(id) {
    console.log(id);
    let res = await loosenFrameApi.getFrameCode(id);
    frameQrCode.value = res.data;
  }

  onMounted(queryData);
  defineExpose({
    show,
  });
</script>

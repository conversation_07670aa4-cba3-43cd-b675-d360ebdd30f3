/**
 * 单位表 api 封装
 *
 * @Author:    xmt
 * @Date:      2024-07-04 11:12:45
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const unitApi = {

  /**
   * 分页查询  <AUTHOR>
   */
  queryPage : (param) => {
    return postRequest('/unit/queryPage', param);
  },
  // 单位下拉查询ID
    querySelect:(param)=>{
        return postRequest('/unit/queryAll', param);
    },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
      return postRequest('/unit/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
      return postRequest('/unit/update', param);
  },


  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
      return getRequest(`/unit/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
      return postRequest('/unit/batchDelete', idList);
  },

  /**
   * 获取单位编号  <AUTHOR>
   */
  getUnitNo: () => {
      return getRequest('/unit/getUnitNo');
  },
  /**
   * 通过id查询 <AUTHOR>
   */
  getId: (id) => {
    return getRequest(`/unit/get/${id}`);
  },
};

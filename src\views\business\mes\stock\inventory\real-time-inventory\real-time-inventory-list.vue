<!--
  * 即时库存
  *
  * @Author:    lyq
  * @Date:      2025-02-14
  * @Copyright  zscbdic
-->
<template>
    <!---------- 查询表单form begin ----------->
    <a-form class="smart-query-form">
        <a-row class="smart-query-form-row">
            <a-form-item label="物料名称" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materialName" placeholder="物料名称" />
            </a-form-item>
            <a-form-item label="物料spu编码" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materialSpuNumber" placeholder="物料spu编码" />
            </a-form-item>
            <a-form-item label="物料sku编码" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.materialSkuNumber" placeholder="物料sku编码" />
            </a-form-item>
            <!-- <a-form-item label="批次号" class="smart-query-form-item">
                <a-input style="width: 150px" v-model:value="queryForm.lotNumber" placeholder="批次号" />
            </a-form-item>  -->
            
            <a-form-item class="smart-query-form-item">
                <a-button type="primary" @click="queryData">
                    <template #icon>
                        <SearchOutlined />
                    </template>
                    查询
                </a-button>
                <a-button @click="resetQuery" class="smart-margin-left10">
                    <template #icon>
                        <ReloadOutlined />
                    </template>
                    重置
                </a-button>
            </a-form-item>
        </a-row>
    </a-form>
    <!---------- 查询表单form end ----------->

    <a-card size="small" :bordered="false" :hoverable="true">
        <!---------- 表格 begin ----------->
        <a-table
                size="small"
                :dataSource="tableData"
                :columns="columns"
                rowKey="id"
                bordered
                :loading="tableLoading"
                :pagination="false"
                
        >
        </a-table>
        <!---------- 表格 end ----------->

        <div class="smart-query-table-page">
            <a-pagination
                    showSizeChanger
                    showQuickJumper
                    show-less-items
                    :pageSizeOptions="PAGE_SIZE_OPTIONS"
                    :defaultPageSize="queryForm.pageSize"
                    v-model:current="queryForm.pageNum"
                    v-model:pageSize="queryForm.pageSize"
                    :total="total"
                    @change="queryData"
                    @showSizeChange="queryData"
                    :show-total="(total) => `共${total}条`"
            />
        </div>

        <!-- <StkInventoryForm  ref="formRef" @reloadList="queryData"/> -->

    </a-card>
</template>
<script setup>
    import { reactive, ref, onMounted } from 'vue';
    import { message, Modal } from 'ant-design-vue';
    import { SmartLoading } from '/@/components/framework/smart-loading';
    import { stkInventoryApi } from '../../../../../../api/business/mes/stock/inventory-api';
    import WarehouseSelect  from '/@/components/business/mes/stock/werehouse/warehouse-select/index.vue';
    import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
    import { OPERATION_TYPE } from '/@/constants/business/mes/stock/inventory-const';
    import { BillTypeEnum } from '/@/constants/business/mes/common/bill-type-const';
    import { smartSentry } from '/@/lib/smart-sentry';
    import TableOperator from '/@/components/support/table-operator/index.vue';
    import { number } from 'echarts';
    // ---------------------------- 表格列 ----------------------------

    const columns = ref([
    {
            title: '物料spu编号',
            dataIndex: 'materielSpuNumber',
            ellipsis: true,
        },
        {
            title: '物料sku编号',
            dataIndex: 'materielSkuNumber',
            ellipsis: true,
        },
        {
            title: '物料名称',
            dataIndex: 'materielName',
            ellipsis: true,
        },
        {
            title: '规格型号',
            dataIndex: 'materielModel',
            ellipsis: true,
        },
        // {
        //     title: '批号',
        //     dataIndex: 'lotNumber',
        //     ellipsis: true,
        // },
        {
            title: '单位',
            dataIndex: 'unitName',
            ellipsis: true,
        },
        {
            title: '数量',
            dataIndex: 'qty',
            ellipsis: true,
        },
        {
            title: '可用数量',
            dataIndex: 'avbQty',
            ellipsis: true,
        },
        {
            title: '已锁定数量',
            dataIndex: 'lockQty',
            ellipsis: true,
        },
       
        {
            title: '预测数量',
            dataIndex: 'predictQty',
            ellipsis: true,
        },
        // {
        //     title: '操作',
        //     dataIndex: 'action',
        //     fixed: 'right',
        //     width: 90,
        // },
    ]);
    // ---------------------------- 查询数据表单和方法 ----------------------------

    const queryFormState = {
        pageNum: 1,
        pageSize: 10,
        sortItemList: [{column: "create_time", isAsc: false}],
        warehouseId:undefined,
        lotNumber:undefined,
        materialSpuNumber:undefined,
        materialSkuNumber:undefined,
        materialName:undefined,
    };
    // 查询表单form
    const queryForm = reactive({ ...queryFormState });
    // 表格加载loading
    const tableLoading = ref(false);
    // 表格数据
    const tableData = ref([]);
    // 总数
    const total = ref(0);
    //仓库id下拉列表
    const warehouseIdList = ref([]);

    // 重置查询条件
    function resetQuery() {
        let pageSize = queryForm.pageSize;
        Object.assign(queryForm, queryFormState);
        queryForm.pageSize = pageSize;
        queryData();
    }

    // 查询数据
    async function queryData() {
        tableLoading.value = true;
        try {
            let queryResult = await stkInventoryApi.queryPageByStkInventory(queryForm);
            tableData.value = queryResult.data.list;
            total.value = queryResult.data.total;
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            tableLoading.value = false;
        }
    }
    onMounted(queryData);

    // ---------------------------- 添加/修改 ----------------------------
    const formRef = ref();

    function showForm(data) {
        formRef.value.show(data);
    }

    // ---------------------------- 单个删除 ----------------------------
    //确认删除
    function onDelete(data){
        Modal.confirm({
            title: '提示',
            content: '确定要删除选吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestDelete(data);
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求删除
    async function requestDelete(data){
        SmartLoading.show();
        try {
            let deleteForm = {
                goodsIdList: selectedRowKeyList.value,
            };
            await stkInventoryApi.delete(data.id);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }

    // ---------------------------- 批量删除 ----------------------------

    // 选择表格行
    const selectedRowKeyList = ref([]);

    function onSelectChange(selectedRowKeys) {
        selectedRowKeyList.value = selectedRowKeys;
    }

    // 批量删除
    function confirmBatchDelete() {
        Modal.confirm({
            title: '提示',
            content: '确定要批量删除这些数据吗?',
            okText: '删除',
            okType: 'danger',
            onOk() {
                requestBatchDelete();
            },
            cancelText: '取消',
            onCancel() {},
        });
    }

    //请求批量删除
    async function requestBatchDelete() {
        try {
            SmartLoading.show();
            await stkInventoryApi.batchDelete(selectedRowKeyList.value);
            message.success('删除成功');
            queryData();
        } catch (e) {
            smartSentry.captureError(e);
        } finally {
            SmartLoading.hide();
        }
    }
</script>

<!--
  * 工序库
  *
  * @Author:    xmt
  * @Date:      2024-07-13 15:19:57
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="300px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-row>
        <a-form-item label="备注" name="remark">
          <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注"/>
        </a-form-item>
        <a-form-item label="工艺库编号码" name="processLibraryNumber">
          <a-input style="width: 100%" v-model:value="form.processLibraryNumber" placeholder="工艺库编号码"/>
        </a-form-item>
        <a-form-item label="停用标识;0启用，1停用" name="enableFlag">
          <a-input-number style="width: 100%" v-model:value="form.enableFlag" placeholder="停用标识;0启用，1停用"/>
        </a-form-item>
        <a-form-item label="名称" name="name">
          <a-input style="width: 100%" v-model:value="form.name" placeholder="名称"/>
        </a-form-item>
      </a-row>

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
import {reactive, ref, nextTick} from 'vue';
import _ from 'lodash';
import {message} from 'ant-design-vue';
import {SmartLoading} from '/src/components/framework/smart-loading';
import {processLibraryApi} from '/@/api/business/mes/process/process-library-api';
import {smartSentry} from '/src/lib/smart-sentry';

// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);

function show(rowData) {
  Object.assign(form, formDefault);
  if (rowData && !_.isEmpty(rowData)) {
    Object.assign(form, rowData);
  }
  visibleFlag.value = true;
  nextTick(() => {
    formRef.value.clearValidate();
  });
}

function onClose() {
  Object.assign(form, formDefault);
  visibleFlag.value = false;
}

// ------------------------ 表单 ------------------------

// 组件ref
const formRef = ref();

const formDefault = {
  id: undefined,
  remark: undefined, //备注
  processLibraryNumber: undefined, //工艺库编号码
  enableFlag: undefined, //停用标识;0启用，1停用
  name: undefined, //名称
};

let form = reactive({...formDefault});

const rules = {
  processLibraryNumber: [{required: true, message: '工艺库编号码 必填'}],
  enableFlag: [{required: true, message: '停用标识 必填'}],
  name: [{required: true, message: '名称 必填'}],
};

// 点击确定，验证表单
async function onSubmit() {
  try {
    await formRef.value.validateFields();
    save();
  } catch (err) {
    message.error('参数验证错误，请仔细填写表单数据!');
  }
}

// 新建、编辑API
async function save() {
  SmartLoading.show();
  try {
    if (form.id) {
      await processLibraryApi.update(form);
    } else {
      await processLibraryApi.add(form);
    }
    message.success('操作成功');
    emits('reloadList');
    onClose();
  } catch (err) {
    smartSentry.captureError(err);
  } finally {
    SmartLoading.hide();
  }
}

defineExpose({
  show,
});
</script>

/**
 * 生产跟单 api 封装
 *
 * @Author:    lwj
 * @Date:      2024-07-26 21:36:36
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const produceInstructFollowApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/produceInstructFollow/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/produceInstructFollow/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/produceInstructFollow/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (param) => {
    return postRequest('/produceInstructFollow/delete', param);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/produceInstructFollow/batchDelete', idList);
  },
};

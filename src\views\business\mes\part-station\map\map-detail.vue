<template>
  <a-card style="margin-bottom: 5px;">
    <div style="display: flex;">
      <div style="margin-right: 100px">
        <div style="display: inline-block;margin-right: 30px">
          <div >仓库名: {{warehouse.name}}</div>
          <div>货架数: {{rackCount}}</div>
        </div>
        <div style="display: inline-block">
          <div>仓库编码: {{warehouse.warehouseCode}}</div>
          <div>货位数: {{binCount}}</div>
        </div>
      </div>
      <div style="margin-right: 100px">
        <div>未设置货架数: {{rackOptions.length- usedRackIds.length}}</div>
        <div>已设置货架数: {{usedRackIds.length}}</div>
      </div>
<!--      <div style="margin-right: 100px">-->
<!--        <div>出入口检查:</div>-->
<!--      </div>-->
      <div>
        <div style="margin-bottom: 4px">
          鼠标悬浮显示:
          <a-switch v-model:checked="popoverSwitch" style="margin-left: 10px"/>
        </div>
        <div>
          是否展示:
          <a-switch v-model:checked="visibleFlag" style="margin-left: 10px" checked-children="展示" un-checked-children="不展示"/>
        </div>
      </div>

    </div>


  </a-card>
  <a-row>
    <a-col :span="5">
      <a-card>
        <a-form>
          <a-form-item>
            <div style="width: 100%;display: flex;justify-content: space-between">
              <a-button type="primary" @click="saveMap">保存</a-button>
              <a-button @click="resetMap">重置</a-button>
              <a-button @click="checkMap">检查</a-button>
            </div>
          </a-form-item>
          <a-form-item label="地图宽">
            <a-input-number v-model:value="wCount" :min="1" :max="100" addon-after="格"/>
          </a-form-item>
          <a-form-item label="地图高">
            <a-input-number v-model:value="hCount" :min="1" :max="100" addon-after="格"/>
          </a-form-item>
          <a-form-item>
            <a-radio-group v-model:value="cellType">

              <a-radio class="radio-box" :value="1">
                <div class="cell-type-box">
                  <div class="block" :style="{backgroundColor: CELL_TYPE_ENUM.OBSTACLE.color}"></div>
                  障碍
                </div>
              </a-radio>
              <a-radio class="radio-box" :value="2">
                <div class="cell-type-box">
                  <div class="block" :style="{backgroundColor: CELL_TYPE_ENUM.RACK.color}"></div>
                  货架
                </div>
              </a-radio>
              <a-radio class="radio-box" :value="3">
                <div class="cell-type-box">
                  <div class="block" :style="{backgroundColor: CELL_TYPE_ENUM.EXIT.color}"></div>
                  <div>
                    出口
                  </div>
                </div>
              </a-radio>
              <a-radio class="radio-box" :value="4">
                <div class="cell-type-box">
                  <div class="block" :style="{backgroundColor: CELL_TYPE_ENUM.ENTRY.color}"></div>
                  <div>
                    入口
                  </div>
                </div>
              </a-radio>
              <a-radio class="radio-box" :value="5">
                <div class="cell-type-box">
                  <div class="block" :style="{backgroundColor: CELL_TYPE_ENUM.ENTRY_EXIT.color}"></div>
                  <div>
                    出入口
                  </div>
                </div>
              </a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>

        <div>
          <p>
            地图必要条件：<br/>
            1、地图中必须至少包含一个出/入口；<br/>
            2、地图中必须至少包含一个货架；<br/>
            3、出口，入口，出入口需设立在边界上；<br/>
          </p>
          <p>
            地图设置：<br/>
            1、点击单选按钮，选择地图类型；<br/>
            2、左键点击地图，设置地图类型；<br/>
            3、右键点击地图，取消地图类型；<br/>
            4、点击 “保存”，保存地图设置信息；<br/>
            5、点击 “重置”，重置地图信息与大小；
          </p>
        </div>
      </a-card>
    </a-col>
    <a-col :span="19">
      <a-card>
        <div id="map" style="width: 100%; ">
          <div v-for="(row, i) in map" :key="i" class="row">
            <a-popover :title="cell.desc" :trigger="popoverSwitch===true&&cell.desc?'hover':'focus'"
                       v-for="(cell, j) in row"
                       :key="j">
              <div class="cell"
                   :style="{ backgroundColor: cell.color, color: cell.fontColor }"
                   @click="leftClick(cell, i, j)"
                   @contextmenu.prevent.stop="rightClick(cell, i, j)">
                {{cell.desc}}
              </div>
            </a-popover>


          </div>
        </div>
      </a-card>
    </a-col>
  </a-row>


  <a-modal v-model:open="rackSelectOpen" title="选择货架" @ok="handleSelectRack">
    <a-select v-model:value="tempRack"
              style="width: 100%"
              placeholder="请选择货架"
              :options="canUseRackOptions"
              :filter-option="filterOption"
              show-search/>
  </a-modal>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import {
  cell,
  CELL_TYPE_ENUM,
  edgeCheck,
  entryAndExitCheck, getUsedRackIds,
  initMap
} from "/@/views/business/mes/part-station/map/map.js";
import {SmartLoading} from '/src/components/framework/smart-loading';
import {message} from "ant-design-vue";
import {partStationRackApi} from "/@/api/business/mes/part-station/rack/part-station-rack-api.js";
import {useRoute} from "vue-router";
import {partStationWarehouseApi} from "/@/api/business/mes/part-station/warehouse/part-station-warehouse-api.js";
import {partStationWarehouseMapApi} from "/@/api/business/mes/part-station/map/part-station-warehouse-map-api.js";

const route = useRoute();

const wCount = ref(20);// 地图宽
const hCount = ref(10);// 地图高
const map = ref([]);// 地图

const cellType = ref(1)// 选择地图类型
const popoverSwitch = ref(false);// 是否显示提示信息
const visibleFlag = ref(true);


const rackSelectOpen = ref(false);// 货架选择弹窗

const warehouseId = ref(2);// 仓库id
const warehouse = ref({});
const rackOptions = ref([]);// 货架选择
const usedRackIds = ref([]);// 货架已使用id
const tempRack = ref(null);// 货架临时变量
const clickX = ref(null);
const clickY = ref(null);

const rackCount=  ref(0)
const binCount = ref(0)

onMounted( () => {
  if (route.query.mapPartStationWarehouseId) {
    warehouseId.value = route.query.mapPartStationWarehouseId
    queryRackOptions()
    queryWarehouse()
    queryRackCount()
    queryBinCount()
    loadMap()
  }

})


async function loadMap() {
  try {
    SmartLoading.show("加载中...")
    const result = await partStationWarehouseMapApi.queryByWarehouseId(warehouseId.value)
    map.value = JSON.parse(result.data.mapDataStr)
    wCount.value = map.value[0].length
    hCount.value = map.value.length
    visibleFlag.value = result.data.visibleFlag
    usedRackIds.value = getUsedRackIds(map.value)
    // console.log('map',usedRackIds.value,"有地图")
  }catch (e){
    map.value = initMap(wCount.value, hCount.value);
    // console.log('map',map.value,"无地图")
  }finally {
    SmartLoading.hide()
  }
}

function resetMap() {
   // 初始化地图数据
   map.value = initMap(wCount.value, hCount.value);
  // 清空已使用的货架ID列表
  usedRackIds.value = [];
}

function checkMap() {
  let result = entryAndExitCheck(map.value)
  if (result) {
    message.success('地图设置通过')
  } else {
    message.error('地图设置不通过,请检查出入口')
  }

}




function leftClick(cell, i, j) {


  let type = CELL_TYPE_ENUM.getEnum(cellType.value)
  if (!type) {
    return
  }
  if (type === CELL_TYPE_ENUM.EXIT || type === CELL_TYPE_ENUM.ENTRY || type === CELL_TYPE_ENUM.ENTRY_EXIT) {
    // console.log(map.value.length,map.value[0].length, i, j)
    if (!edgeCheck( map.value.length,map.value[0].length, i, j)) {
      message.warn('出/入口须设置在地图边界上')
      return
    }
  } else if (type === CELL_TYPE_ENUM.RACK) {
    clickX.value = i
    clickY.value = j
    rackSelectOpen.value = true;
    return;
  }

  cell.type = type.value;
  cell.desc = type.desc;
  cell.color = type.color;
  cell.fontColor = type.fontColor;
  // 由于cell对象没有classes属性，所以这里不需要修改
}

function rightClick(cell, i, j) {
  if (cell.type === CELL_TYPE_ENUM.RACK.value) {
    //取消货架放置
    removeUseRackId(cell.rackId);
  }

  cell.type = CELL_TYPE_ENUM.PATH.value;
  cell.desc = null;
  cell.color = CELL_TYPE_ENUM.PATH.color;
  cell.fontColor = CELL_TYPE_ENUM.PATH.fontColor;

}


//----------------------------------------------------------
function handleSelectRack() {
  if (!tempRack.value) {
    rackSelectOpen.value = false;
    return
  }

  if (map.value[clickX.value][clickY.value].type === CELL_TYPE_ENUM.RACK.value) {
    // 取消货架放置，移除已放置货架id
    removeUseRackId(map.value[clickX.value][clickY.value].rackId)
  }

  let rack = rackOptions.value.find(e => e.value === tempRack.value)
  map.value[clickX.value][clickY.value] = {
    rackId: rack.value,
    type: CELL_TYPE_ENUM.RACK.value,
    desc: rack.label,
    color: CELL_TYPE_ENUM.RACK.color,
    fontColor: CELL_TYPE_ENUM.RACK.fontColor
  }

  // 记录已使用货架id
  addUseRackId(rack.value)
  tempRack.value = null
  clickX.value = null
  clickY.value = null
  rackSelectOpen.value = false;
}

const filterOption = (input, option) => {
  // console.log(option)
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const canUseRackOptions = computed(() => {
  return rackOptions.value.filter(e => !usedRackIds.value.includes(e.value));
});
function addUseRackId(id) {
  usedRackIds.value.push(id)
  // console.log(usedRackIds.value)
  // console.log(rackOptions.value.filter(e => !usedRackIds.value.includes(e.value)))
}

function removeUseRackId(id) {
  usedRackIds.value= usedRackIds.value.filter(e => e !== id)
  // console.log(usedRackIds.value)
  console.log(rackOptions.value.filter(e => !usedRackIds.value.includes(e.value)))
}

async function queryRackOptions() {
  let res = await partStationRackApi.getAll({warehouseId: warehouseId.value});
  //构造options
  rackOptions.value = res.data.map((e) => {
    return {
      value: e.id,
      label: e.rackCode
    }
  })
  // console.log(rackOptions.value.length)
}

async function queryWarehouse() {
  let res = await partStationWarehouseApi.queryById(warehouseId.value);
  warehouse.value = res.data;
}

async function queryBinCount(){
  let res = await partStationWarehouseApi.getBinNum({warehouseId: warehouseId.value});
  binCount.value = res.data;
}

async function queryRackCount(){
  let res = await partStationWarehouseApi.getRackNum({warehouseId: warehouseId.value});
  rackCount.value = res.data;
}

async function saveMap(){
  try {
    let checkFlag = entryAndExitCheck(map.value)
    if (!checkFlag) {
      message.error('地图设置不通过,请检查出入口')
      return
    }

    SmartLoading.show("保存中...")
    let res = await partStationWarehouseMapApi.updateMap({
      warehouseId: warehouseId.value,
      mapDataStr: JSON.stringify(map.value),
      visibleFlag:visibleFlag.value
    })
    message.success("保存成功")
  }catch (e){
    message.error(e.message)
  }finally {
    SmartLoading.hide()
  }
}


</script>

<style scoped>

.radio-box {
  display: flex;
  line-height: 50px;
  height: 50px;

  .cell-type-box {
    display: flex;
    /* flex-direction: column; */
    justify-content: center;
    align-items: center;
  }

  .block {
    width: 34px;
    height: 34px;
    background-color: #000;
    margin-right: 10px;
  }
}

#map {
  width: 30%;
  overflow: hidden; /* 防止内容溢出 */
  position: relative;

}

.row {
  display: flex;
  width: 100%;
}

.cell {
  flex: 1;
  aspect-ratio: 1 / 1; /* 这会使元素的高度和宽度相等 */;
  display: flex;
  background-color: #e6ecf2;
  text-align: center;
  border: 1px solid #eff0f3;
  cursor: pointer;
  //transition: background-color 0.1s; /* 平滑过渡效果 */
  align-items: center;
  justify-content: center; /* 使内容居中 */


  font-size: 12px;
  /*文字过长忽略*/
  overflow: hidden;
  text-overflow: ellipsis;
  //white-space: nowrap;

}
</style>

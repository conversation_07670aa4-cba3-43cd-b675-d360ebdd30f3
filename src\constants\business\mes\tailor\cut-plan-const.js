export const CUT_PLAN_STATUS_ENUM = {
  // 暂存
  DRAFT: {
    value: 'DRAFT',
    label: '暂存',
    color: 'blue',
  },
  // 待审核
  WAIT_AUDIT: {
    value: 'WAIT_AUDIT',
    label: '待审核',
    color: 'orange',
  },
  // 审核通过
  AUDIT_PASS: {
    value: 'AUDIT_PASS',
    label: '审核通过',
    color: 'green',
  },
  // 审核不通过
  AUDIT_REJECT: {
    value: 'AUDIT_REJECT',
    label: '审核不通过',
    color: 'red',
  },
  //获取方法
  getEnum(value) {
    if (value === 'DRAFT') {
        return CUT_PLAN_STATUS_ENUM.DRAFT
    } else if (value === 'WAIT_AUDIT') {
        return CUT_PLAN_STATUS_ENUM.WAIT_AUDIT;
    } else if (value === 'AUDIT_PASS') {
        return CUT_PLAN_STATUS_ENUM.AUDIT_PASS;
    } else if (value === 'AUDIT_REJECT') {
        return CUT_PLAN_STATUS_ENUM.AUDIT_REJECT;
    }
}
};

export const CUT_PLAN_AUDIT_ENUM ={
  //提交
  SUBMIT: {
    value: 'SUBMIT',
    label: '提交',
  },
  //审核
  AUDIT: {
    value: 'AUDIT',
    label: '审核',
  },
  //反审核
  UN_AUDIT: {
    value: 'UN_AUDIT',
    label: '反审核',
  },
}
export default {
  CUT_PLAN_STATUS_ENUM,
  CUT_PLAN_AUDIT_ENUM,
};

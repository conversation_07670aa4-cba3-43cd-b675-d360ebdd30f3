<template>
  <div class="chart-header">
    <div class="chart-title">松布静置时长倒计时</div>
  </div>
  <div class="echarts-box">
    <div class="chart-main" id="loosen-task-chart"></div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import * as echarts from 'echarts';

  // 图表实例
  let myChart = null;
  let timer = null;

  const taskData = ref([
    { name: 'SCJH-231221-01', remainTime: 55 },
    { name: 'SCJH-231221-02', remainTime: 48 },
    { name: 'SCJH-231221-03', remainTime: 39 },
    { name: 'SCJH-231221-04', remainTime: 30 },
    { name: 'SCJH-231221-05', remainTime: 25 },
    { name: 'SCJH-231221-06', remainTime: 15 },
    { name: 'SCJH-231221-07', remainTime: 7 },
  ]);

  // 格式化时间显示，只显示分钟
  const formatTime = (minutes) => {
    // 四舍五入到分钟
    const mins = Math.round(minutes);
    return `${mins}分钟`;
  };

  // 根据剩余时间获取颜色
  const getColorByTime = (remainTime) => {
    // 剩余时间越少，颜色越红
    if (remainTime <= 5) {
      return '#ff0000'; // 红色，紧急
    } else if (remainTime <= 15) {
      return '#ff4500'; // 橙红色，警告
    } else if (remainTime <= 30) {
      return '#ff8c00'; // 深橙色，注意
    } else {
      return '#4687f0'; // 蓝色，正常
    }
  };

  // 初始化图表
  const initChart = () => {
    // 筛选剩余时间大于0且不超过1小时的任务，并按剩余时间升序排序
    const filteredData = taskData.value.filter((task) => task.remainTime > 0 && task.remainTime <= 60).sort((a, b) => a.remainTime - b.remainTime);

    // 准备数据
    const taskNames = filteredData.map((item) => item.name);
    const remainTimeData = filteredData.map((item) => ({
      value: item.remainTime,
      itemStyle: {
        color: getColorByTime(item.remainTime),
      },
      label: {
        formatter: (params) => {
          return `${Math.round(params.value)}分钟`;
        },
      },
    }));

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params) => {
          const taskName = params[0].axisValue;
          const remainTime = params[0].value;
          return `${taskName}<br/>剩余时间: ${Math.round(remainTime)}分钟`;
        },
      },
      grid: {
        left: '10%',
        right: '15%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: '剩余时间(分钟)',
        max: 60,
        nameTextStyle: {
          fontSize: 10,
        },
        axisLabel: {
          fontSize: 9,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      yAxis: {
        type: 'category',
        data: taskNames,
        inverse: true,
        axisLabel: {
          margin: 4,
          fontSize: 9,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      series: [
        {
          name: '剩余时间',
          type: 'bar',
          data: remainTimeData,
          barWidth: '50%',
          label: {
            show: true,
            position: 'right',
            fontSize: 9,
            color: '#333',
            formatter: (params) => {
              return `${Math.round(params.value)}分钟`;
            },
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          animationDuration: 300,
        },
      ],
    };

    // 初始化或更新图表
    if (myChart) {
      myChart.setOption(option);
    } else {
      const chartDom = document.getElementById('loosen-task-chart');
      if (chartDom) {
        myChart = echarts.init(chartDom);
        myChart.setOption(option);

        // 添加响应式调整
        window.addEventListener('resize', () => {
          myChart.resize();
        });
      }
    }
  };

  // 开始倒计时，每分钟更新一次
  const startCountdown = () => {
    // 初始化立即显示一次
    initChart();

    // 每分钟更新一次
    timer = setInterval(() => {
      // 更新所有任务的剩余时间
      taskData.value.forEach((task) => {
        // 每次减去1分钟
        task.remainTime -= 1;
      });

      // 更新图表
      initChart();
    }, 60000);
  };

  onMounted(() => {
    startCountdown();
  });

  onUnmounted(() => {
    // 清除定时器
    if (timer) {
      clearInterval(timer);
    }

    // 销毁图表实例
    if (myChart) {
      window.removeEventListener('resize', () => {
        myChart.resize();
      });
      myChart.dispose();
      myChart = null;
    }
  });
</script>

<style scoped lang="less">
  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;

    .chart-title {
      font-size: 14px;
      font-weight: bold;
    }
  }

  .echarts-box {
    display: flex;
    align-items: center;
    justify-content: center;

    .chart-main {
      width: 90%;
      height: 220px;
      background: #fff;
      margin: 0 auto;
    }
  }
</style>

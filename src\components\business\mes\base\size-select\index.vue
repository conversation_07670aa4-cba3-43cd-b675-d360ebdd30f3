<!--
  * 款式尺码选择组件
  * 返回所选尺码列表
  * @Author:    fkf
  * @Date:      2024-1-17
  * @Copyright  zscbdic
-->
<template>
  <a-select
  :options="options"
  :showSearch="true"
  :allowClear="true"
  v-model:value="selectValue"
  @change="onChange"
  :style="`width: ${props.width}`"
  placeholder="请选择尺码"
  :listHeight="150"
  >
       <template #dropdownRender="{ menuNode: menu }">
      <v-nodes :vnodes="menu" />
      <a-divider style="margin: 4px 0" />
      <a-space style="padding: 4px 8px;width:100%;display: flex;justify-content: flex-end;">
        <a-button @click.stop="showForm" class="smart-margin-left10 ">
          <template #icon>
            <PlusOutlined />
          </template>
          添加尺寸
        </a-button>
      </a-space>
    </template>
  </a-select>

  <szieForm ref="formRef" @reloadList="queryData"/>

</template>
<script setup>
import {defineComponent,onMounted, ref,watch} from 'vue'
import {sizeApi} from '/@/api/business/mes/base/size-api.js';
import  szieForm from "/@/views/business/mes/base/size/size-form.vue";

// 定义 VNodes 组件用于渲染下拉菜单
const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes;
  },
});

const formRef = ref();
function showForm(data) {
  formRef.value.show(data);
}
// =========== 属性定义 和 事件方法暴露 =============

const props = defineProps({
  value: {
    type: String,
    default: undefined
  },
 width: {
      type: String,
      default: '100%',
    },
    sizeList: {
      type: Array,
      default: () => []
    },
});

const emit = defineEmits(['update:value', 'change']);

// =========== 请求尺寸数据 =============
const options = ref([])
async function queryData(){
  let res = await sizeApi.queryAll({})
  let filteredList = res.data;

  // 如果传入了 sizeList，则只显示 sizeList 中包含的尺码
  if (props.sizeList && props.sizeList.length > 0) {
    filteredList = res.data.filter(item =>
      props.sizeList.includes(item.sizeMessage)
    );
  }

  options.value = filteredList.map((item)=>{
    return{
      value:item.sizeMessage,
      label:item.sizeMessage,
    }
  })
}
onMounted(queryData)

// 监听 sizeList 变化，重新查询数据
watch(
  () => props.sizeList,
  (newValue) => {
    console.log('sizeList changed:', newValue);
    queryData();
  },
  { deep: true }
);

// =========== 选择 监听、事件 =============
  
  const selectValue = ref(props.value);
  watch(
    () => props.value,
    (newValue) => {
      selectValue.value = newValue;
    }
  );

  function onChange(value) {
    emit('update:value', value);
    emit('change', value);
  }
</script>

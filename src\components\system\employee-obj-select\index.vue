<template>
  <a-select
    v-model:value="selectValue"
    :style="`width: ${width}`"
    :placeholder="placeholder"
    :show-search="true"
    :allow-clear="true"
    @change="handleChange"
  >
    <a-select-option
      v-for="item in employeeList"
      :key="item.employeeId"
      :value="item.employeeId"
    >
      {{ item.actualName }}
      <template v-if="item.departmentName">（{{ item.departmentName }}）</template>
    </a-select-option>
  </a-select>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { employeeApi } from '/src/api/system/employee-api';
import { smartSentry } from '/@/lib/smart-sentry';

const props = defineProps({
  value: [String, Number], 
  placeholder: {
    type: String,
    default: '请选择负责人'
  },
  width: {
    type: String,
    default: '100%'
  }
});

const emit = defineEmits(['update:value', 'change']); 

const employeeList = ref([]);
const selectValue = ref(props.value); 

// 监听props变化
watch(() => props.value, (newVal) => { 
  selectValue.value = newVal;
});

// 获取员工列表
async function queryEmployeeList() {
  try {
    const res = await employeeApi.queryAll({});
    employeeList.value = res.data;
  } catch (err) {
    smartSentry.captureError(err);
  }
}

// 处理选择变化
function handleChange(value) {
  const selectedEmployee = employeeList.value.find(e => e.employeeId == value);
  emit('update:value', value); 
  emit('change', selectedEmployee);
}

onMounted(() => {
  queryEmployeeList();
});
</script>
<template>
  <a-modal
    :title="form.id ? '编辑' : '新增'"
    :width="1200"
    :open="visibleFlag"
    @cancel="onClose"
    @ok="onSubmit"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <!--表单begin    -->
    <a-form ref="formRef" :model="form" :rules="formRules" :label-col="{ span: 6 }">
      <a-row>
        <a-col :span="8">
          <a-form-item label="工艺库编号" name="processLibraryNumber">
            <a-input style="width: 90%" v-model:value="form.processLibraryNumber" placeholder="工艺库编号码" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="工艺库名称" name="name">
            <a-input style="width: 90%" v-model:value="form.name" placeholder="名称" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="停用标识" name="enableFlag">
            <a-radio-group v-model:value="form.enableFlag" button-style="solid" style="width: 100%">
              <a-radio-button :value="false">启用</a-radio-button>
              <a-radio-button :value="true">停用</a-radio-button>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="备注" name="remark">
            <a-input style="width: 90%" v-model:value="form.remark" placeholder="备注" />
          </a-form-item>
        </a-col>
      </a-row>
      <process-select-modal ref="processModal" @select-data="selectItemData" />
    </a-form>

    <!--表格 begin    -->
    <a-button type="primary" @click="showItemModal()">添加</a-button>
    <a-table size="small" :dataSource="tableData" :columns="columns" :pagination="false" bordered :scroll="{ x: 120 }">
      <template #bodyCell="{ record, column, index }">
        <template v-if="column.dataIndex === 'serialNumber'">
          {{ record.serialNumber }}
        </template>
        <template v-else-if="column.dataIndex === 'name'">
          <a-input disabled placeholder="请输入工序名称" v-model:value="record.name" />
        </template>
        <template v-else-if="column.dataIndex === 'position'">
          <a-input disabled placeholder="请输入工序部位" v-model:value="record.position" />
        </template>
        <template v-else-if="column.dataIndex === 'processType'">
          <dict-select key-code="PROCESS_TYPE" v-model:value="record.processType" width="100%" disabled />
        </template>
        <template v-else-if="column.dataIndex === 'standardTime'">
          <a-input-number placeholder="请输入标准工时" v-model:value="record.standardTime" min="1" :precision="0">
            <template #addonAfter> 秒 </template>
          </a-input-number>
        </template>
        <template v-else-if="['unitPrice1', 'unitPrice2', 'unitPrice3', 'unitPrice4', 'unitPrice5', 'unitPrice6'].includes(column.dataIndex)">
          <a-input-number placeholder="请输入工价一" v-model:value="record[column.dataIndex]" min="0" prefix="￥" :precision="3" />
        </template>
        <template v-else-if="column.dataIndex === 'workshopId'">
          <a-select v-model:value="record.workshopId" style="width: 100%" :options="workshopList" />
        </template>
        <template v-else-if="column.dataIndex === 'auditFlag'">
          <a-switch v-model:checked="record.auditFlag" checked-children="审核" un-checked-children="不审核" />
        </template>
        <template v-else-if="column.dataIndex === 'overflowWorkFlag'">
          <a-switch v-model:checked="record.overflowWorkFlag" checked-children="允许" un-checked-children="不允许" />
        </template>
        <template v-else-if="column.dataIndex === 'processControl'">
          <a-select v-model:value="record.processControl" style="width: 100%">
            <a-select-option value="0">自制</a-select-option>
            <a-select-option value="1">委外</a-select-option>
            <a-select-option value="2">不限</a-select-option>
          </a-select>
        </template>
        <template v-else-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate">
            <a-button @click="deleteItem(record.key)" danger type="link">删除</a-button>
            <a-button type="link" @click="moveUp(record, index)" :disabled="index === 0">上移</a-button>
            <a-button type="link" @click="moveDown(record, index)" :disabled="index === (tableData?.length || 0) - 1">下移</a-button>
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
  import { onMounted, ref, nextTick, watch } from 'vue';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { workshopApi } from '/@/api/business/mes/factory/workshop-api.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { processLibraryApi } from '/@/api/business/mes/process/process-library-api.js';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading/index.js';
  import ProcessSelectModal from '/@/components/business/mes/process/process-info-select-modal/index.vue';

  const workshopList = ref([]); //车间

  const columns = ref([
    {
      title: '序号',
      dataIndex: 'serialNumber',
      width: 50,
      fixed: 'left',
    },
    {
      title: '工序名称(必填)',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '部位',
      dataIndex: 'position',
      width: 100,
    },
    {
      title: '工序类型',
      dataIndex: 'processType',
      width: 100,
    },
    {
      title: '车间',
      dataIndex: 'workshopId',
      width: 100,
    },
    {
      title: '是否审核',
      dataIndex: 'auditFlag',
      width: 100,
    },
    {
      title: '超数生产',
      dataIndex: 'overflowWorkFlag',
      width: 100,
    },
    {
      title: '工序控制',
      dataIndex: 'processControl',
      width: 100,
    },
    {
      title: '标准工时',
      dataIndex: 'standardTime',
      width: 100,
    },
    {
      title: '工价一',
      dataIndex: 'unitPrice1',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      width: 130,
    },
  ]);

  let tableData = ref([]);
  //----------------------- 显示 -----------------------
  const visibleFlag = ref(false);
  const emit = defineEmits(['reloading']);
  //----------------------- 关闭 -----------------------
  function onClose() {
    visibleFlag.value = false;
    onReverse();
    emit('reloading');
  }
  //----------------------- 显示 -----------------------
  function show(rowData) {
    visibleFlag.value = true;
    if (rowData.id) {
      Object.assign(form.value, rowData);
      // 这里需要根据实际情况加载关联的工序数据
      loadProcessData(rowData.id);
    }
  }

  // 存储当前行record
  const currentRecord = ref(null);

  const processModal = ref();

  function showItemModal(record) {
    currentRecord.value = record || null;
    processModal.value.showModal();
  }

  function selectItemData(selectedItems) {
    if (!selectedItems.length) return;

    // 如果是点击顶部添加按钮 (currentRecord.value 为 null)
    if (!currentRecord.value) {
      selectedItems.forEach((selectedItem) => {
        const newData = {
          key: tableData.value.length,
          processId: selectedItem.id,
          name: selectedItem.name,
          position: selectedItem.position,
          processControl: selectedItem.processControl,
          processNumber: selectedItem.processNumber,
          processType: selectedItem.processType,
          standardTime: selectedItem.standardTime,
          unitPrice1: selectedItem.unitPrice1,
          unitPrice2: selectedItem.unitPrice2,
          unitPrice3: selectedItem.unitPrice3,
          unitPrice4: selectedItem.unitPrice4,
          unitPrice5: selectedItem.unitPrice5,
          unitPrice6: selectedItem.unitPrice6,
          auditFlag: false,
          overflowWorkFlag: true,
          processControl: '0',
          serialNumber: tableData.value.length + 1,
        };
        tableData.value.push(newData);
      });
    } else {
      // 获取当前选中行的索引
      const currentIndex = tableData.value.findIndex((item) => item.key === currentRecord.value.key);

      selectedItems.forEach((selectedItem, index) => {
        if (index === 0) {
          // 第一条数据更新到当前选中行
          Object.assign(currentRecord.value, {
            processId: selectedItem.id,
            name: selectedItem.name,
            position: selectedItem.position,
            processControl: selectedItem.processControl,
            processNumber: selectedItem.processNumber,
            processType: selectedItem.processType,
            standardTime: selectedItem.standardTime,
            unitPrice1: selectedItem.unitPrice1,
            unitPrice2: selectedItem.unitPrice2,
            unitPrice3: selectedItem.unitPrice3,
            unitPrice4: selectedItem.unitPrice4,
            unitPrice5: selectedItem.unitPrice5,
            unitPrice6: selectedItem.unitPrice6,
            auditFlag: false,
            overflowWorkFlag: true,
            processControl: '0',
          });
        } else {
          // 后续数据插入为新行
          const newData = {
            key: tableData.value.length,
            processId: selectedItem.id,
            name: selectedItem.name,
            position: selectedItem.position,
            processControl: selectedItem.processControl,
            processNumber: selectedItem.processNumber,
            processType: selectedItem.processType,
            standardTime: selectedItem.standardTime,
            unitPrice1: selectedItem.unitPrice1,
            unitPrice2: selectedItem.unitPrice2,
            unitPrice3: selectedItem.unitPrice3,
            unitPrice4: selectedItem.unitPrice4,
            unitPrice5: selectedItem.unitPrice5,
            unitPrice6: selectedItem.unitPrice6,
            auditFlag: false,
            overflowWorkFlag: true,
            processControl: '0',
          };
          // 在当前行后插入新行
          tableData.value.splice(currentIndex + index, 0, newData);
        }
      });
    }

    // 重新排序序号和key
    tableData.value.forEach((item, index) => {
      item.key = index;
      item.serialNumber = index + 1;
    });

    message.success(`成功添加 ${selectedItems.length} 条工序数据`);
  }

  //-----------------------------行上下移功能---------------------------
  //上移
  function moveUp(record, index) {
    // 交换当前行与上一行
    [tableData.value[index], tableData.value[index - 1]] = [tableData.value[index - 1], tableData.value[index]];

    updateSerialNumbers();
    emit('change', tableData.value);
  }

  //下移
  function moveDown(record, index) {
    // 交换当前行与下一行
    [tableData.value[index], tableData.value[index + 1]] = [tableData.value[index + 1], tableData.value[index]];

    updateSerialNumbers();
    emit('change', tableData.value);
  }

  // 重新排序序号和key
  function updateSerialNumbers() {
    tableData.value.forEach((item, index) => {
      item.serialNumber = index + 1;
      item.key = index;
    });
  }

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined,
    remark: undefined,
    processLibraryNumber: undefined, //工艺库编号码
    name: undefined, //工艺库名称
    enableFlag: false, //停用标识;0启用，1停用
  };
  let form = ref({ ...formDefault });
  const formRules = {
    name: [{ required: true, message: '工艺库名称必填' }],
    processLibraryNumber: [{ required: true, message: '工艺库编号码必填' }],
    enableFlag: [{ required: true, message: '停用标识必填' }],
  };

  onMounted(() => {
    queryWorkshopList();
  });

  function deleteItem(key) {
    tableData.value.splice(key, 1);

    tableData.value.forEach((item, index) => {
      item.key = index;
      item.serialNumber = index + 1;
    });
  }

  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
      tableData.value = tableData.value.filter((e) => {
        return e.name !== undefined && e.position !== undefined;
      });
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 重置
  async function onReverse() {
    let id = form.value.id;
    form.value = {};
    form.value.id = id;
    form.value.enableFlag = false;
    tableData.value = [];
  }

  // 查询车间列表
  async function queryWorkshopList() {
    try {
      let queryResult = await workshopApi.queryList({});
      workshopList.value = queryResult.data.map((item) => ({
        value: item['id'],
        label: item['name'],
      }));
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 新增加载工序数据方法
  async function loadProcessData(id) {
    try {
      const res = await processLibraryApi.queryById(id);
      tableData.value = res.data.processLibraryDetailsVOList || [];
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 保存
  async function save() {
    SmartLoading.show();
    try {
      if (form.value.id) {
        let reqData = Object.assign({}, form.value);
        reqData.processLibraryDetailsAddFormList = tableData.value;
        await processLibraryApi.update(reqData);
      } else {
        let reqData = Object.assign({}, form.value);
        reqData.processLibraryDetailsAddFormList = tableData.value;
        await processLibraryApi.add(reqData);
      }
      message.success('操作成功');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  // 暴露方法给父组件
  defineExpose({
    show,
  });
</script>

<style scoped>
  .editable-row-operations {
    margin-right: 8px;
    display: flex;
    justify-content: space-around;
  }

  .ant-switch {
    background-color: green; /* 开关关闭时的背景颜色 */
  }
  .ant-switch-checked {
    background-color: #f0a800; /* 开关打开时的背景颜色 */
  }
</style>

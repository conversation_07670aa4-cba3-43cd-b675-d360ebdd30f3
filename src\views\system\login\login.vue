<!---
  * 登录
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>

  <div class="login-container">
    <div class="entry">
      <div class="login-box">
        <div style="margin-bottom: 40px;margin-top:70px;display: flex;align-items: center;">
          <!--          <div style="margin-right: 20px">-->
          <!--            <img class="login-qr" :src="zscLogo" width="50" style="margin-right: 20px"/>-->
          <!--            <img class="login-qr" :src="yuanyiLogo" width="40"/>-->
          <!--          </div>-->
          <div>
            <span style="color: #217fe5;font-size: 36px;font-weight:600;">智裁</span>
            <span style="color: #409EFF;font-size: 16px;font-style: italic;font-weight:600;">weavewise</span>
          </div>
        </div>

        <account-login v-if="loginWay===1"/>
        <app-qr-login v-if="loginWay===2"/>
        <feishu-qr-login v-if="loginWay===3"/>
        <work-wechat-qr-login v-if="loginWay===4"/>
        <phone-login v-if="loginWay===5"/>

        <div class="more">

          <div v-if="loginWay!==1" style="text-align: center;margin-bottom: 70px;color: #00a0e9;cursor: pointer;"
               @click="changeLoginWay(1)"> 返回账号登录
          </div>


          <div class="title-box">
            <p class="line"></p>
            <p class="title">其他方式登录</p>
            <p class="line"></p>
          </div>
          <div class="login-type">
            <img :src="phoneIcon" style="height: 20px;width: 20px" @click="changeLoginWay(5)"/>
            <img :src="mesIcon" style="height: 20px;width: 20px" @click="changeLoginWay(2)">
            <img :src="feishuIcon" style="height: 20px;width: 20px" @click="changeLoginWay(3)"/>
<!--                        <img :src="wechatIcon" />-->
<!--                        <img :src="aliIcon" />-->
<!--                        <img :src="douyinIcon" />-->
<!--                        <img :src="qqIcon" />-->
<!--                        <img :src="weiboIcon" />-->
<!--                        <img :src="googleIcon" />-->
                        <img :src="workWechatIcon" style="height: 20px;width: 20px" @click="changeLoginWay(4)"/>

          </div>
        </div>
      </div>
    </div>
    <div style="margin-right: 480px;height: 100%">
      <div class="img-drap"></div>
    </div>
  </div>

  <thirdAuthBind ref="thirdAuthBind"/>

</template>
<script setup>
import wechatIcon from '/@/assets/images/login/wechat-icon.png';
import aliIcon from '/@/assets/images/login/ali-icon.png';
import douyinIcon from '/@/assets/images/login/douyin-icon.png';
import qqIcon from '/@/assets/images/login/qq-icon.png';
import weiboIcon from '/@/assets/images/login/weibo-icon.png';
import feishuIcon from '/@/assets/images/login/feishu-icon.png';
import googleIcon from '/@/assets/images/login/google-icon.png';
import phoneIcon from '/@/assets/images/login/phone-icon.png'
import workWechatIcon from '/@/assets/images/login/work-wechat-icon.png';
import mesIcon from '/@/assets/images/login/mes-icon.png';
import AccountLogin from "/@/views/system/login/components/account-login.vue";
import WorkWechatQrLogin from "/@/views/system/login/components/workwechat-qr-login.vue";
import {onMounted, ref} from "vue";
import AppQrLogin from "/src/views/system/login/components/app-qr-login.vue";
import FeishuQrLogin from "/src/views/system/login/components/feishu-qr-login.vue";
import ThirdAuthBind from "/@/views/system/login/components/third-bind.vue";

import {message} from "ant-design-vue";
import {oauth2Api} from "/@/api/system/oauth2-api.js";
import _ from 'lodash'
import PhoneLogin from "/@/views/system/login/components/phone-login.vue";

/**
 * 1、账号密码登录
 * 2、app扫码登录
 * 3、飞书扫码登陆
 * 4. 企业微信扫码登录
 * 5.手机号码登录
 */
const loginWay = ref(1)

function changeLoginWay(way) {
  loginWay.value = way
}

const thirdAuthBind = ref()

onMounted(
    judgeThirdLogin
)

async function judgeThirdLogin() {
  const params = new URLSearchParams(window.location.search)
  /*
  * 利用Array.from()方法，手动判断数组长度
  * 替换之前的params.size() 方法，因为params.size()方法在IE浏览器中、2345、搜狗浏览器不支持
  *
   */
  const size = Array.from(params.entries()).length;
  if (size!==0) {
    const code = params.get('code')
    const state = params.get('state')
    if (code !== null || state !== null) {
      try {
        let res = await oauth2Api.thirdLogin(code, state);
        thirdAuthBind.value.show(res.data);
      } catch (error) {
        message.error('第三方登录请求失败:', error);
      }
    } else handleCancel()

  }
}

// 处理扫码登陆失败时，重定向回首页并清空URI
function handleCancel() {
  message.warning("取消绑定")
  // 跳转回登录页；协议+//+ 域名
  window.location.href = window.location.protocol + '//' + window.location.host;
}

</script>

<style lang="less" scoped>
//@import './login.less';
.login-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  overflow-y: auto;
  background: #f8f9fd;
}

.img-drap {
  min-width: 260px;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url(/@/assets/images/login/login-bg8.jpg);
}

.entry {
  position: relative;
  width: 480px;
  height: 100%;
  float: right;
  background: #fff;
  overflow-y: auto;

  .login-box {
    padding: 10px 72px;
  }

  .btn {
    width: 350px;
    height: 50px;
    background: #1748FD;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    color: #ffffff;
    line-height: 50px;
    cursor: pointer;
  }
}


.more {
  //放在底部
  position: absolute;
  bottom: 5%;
  margin-top: 30px;

  .title-box {
    display: flex;
    align-items: center;
    justify-content: center;

    > p {
      margin-bottom: 0;
    }
  }

  .line {
    width: 100px;
    height: 1px;
    background: #e6e6e6;
  }

  .title {
    font-size: 14px;
    font-weight: 500;
    color: #a1aebe;
    margin: 0 19px;
  }

  .login-type {
    padding: 0 5px;
    margin-top: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    > img {
      width: 30px;
      height: 30px;
    }
  }
}
</style>

<template>
  <a-card>
    <div style="display: flex;justify-content: space-between;">
      <div style="font-size: 16px;font-weight: bold">库存分布</div>
    </div>
    <div class="echarts-box">
      <div class="chart-main" id="bar-main-1"></div>
    </div>
    <div>
    </div>
  </a-card>
</template>
<script setup>
import {onMounted, reactive} from "vue";
import * as echarts from "echarts";

let myChart = null;
onMounted(() => {
  init();
});
let option = reactive({});

function init() {
  option = {
    xAxis: {
      type: 'category',
      data: ["扣子", "面料A", "面料C", "面料D", "面料B", "面料E", "纸箱"],
    },
    yAxis: {
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    legend: {
      data: ['数量'],
    },
    grid: {
      x: 50,
      y: 40,
      x2: 30,
      y2: 30,
    },
    // title: {
    //   text: '出入库数量趋势',
    // },
    series: [
      {
        name: '数量',
        data: [32, 20, 28, 37, 42, 60, 21],
        type: 'bar',
        symbolSize: 8, //设定实心点的大小
        smooth: false,
        itemStyle: {
          color: '#2a56ff',
        },
        label: {
          show: true, // 是否显示标签
          position: 'top', // 标签的位置
        },
      },
    ],
  };
  let chartDom = document.getElementById('bar-main-1');
  if (chartDom) {
    myChart = echarts.init(chartDom);
    option && myChart.setOption(option);
  }
  window.addEventListener("resize", () => {
    myChart.resize();
  });
}


//-------------------------------------
</script>

<style scoped lang="less">
.echarts-box {
  display: flex;
  align-items: center;
  justify-content: center;

  .chart-main {
    width: 100%;
    height: 200px;
    background: #fff;
  }
}
</style>

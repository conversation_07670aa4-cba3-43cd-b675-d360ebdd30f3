<!--
  * 仓库
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:13:18
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="form.id ? '编辑' : '添加'"
      width="500px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 7 }" >
          <!-- <a-row> -->
                    <!-- <a-form-item label="主键"  name="id" v-if="form.id">
                      <a-input-number style="width: 100%" v-model:value="form.id" placeholder="主键" disabled/>
                    </a-form-item> -->
                   
                    <a-form-item label="仓库编号"  name="number">
                      <a-input style="width: 100%" v-model:value="form.number" placeholder="仓库编号" />
                    </a-form-item>
                    <a-form-item label="仓库名称"  name="name">
                      <a-input style="width: 100%" v-model:value="form.name" placeholder="仓库名称" />
                    </a-form-item>
                    <a-form-item label="仓库地址"  name="address">
                      <a-input style="width: 100%" v-model:value="form.address" placeholder="仓库地址" />
                    </a-form-item>
                    <!-- <a-form-item label="负责人id"  name="principalId">
                      <a-input-number style="width: 100%" :value="form.principalId" placeholder="负责人id" disabled/>
                    </a-form-item> -->
                    <a-form-item label="负责人名称"  name="principalName">
                        <employee-obj-select
                        v-model:value="form.principalId"
                         @change="handleEmployeeChange"
                        style="width: 100%"
                        placeholder="请选择负责人"
                        />
                    </a-form-item>
                    <a-form-item label="负责人电话"  name="tel">
                      <a-input style="width: 100%" :value="form.tel" placeholder="负责人电话" disabled/>
                    </a-form-item>
                    <a-form-item label="是否启用仓位管理" name="openLocationFlag">
                      <a-radio-group v-model:value="form.openLocationFlag">
                        <a-radio :value="true">是</a-radio>
                        <a-radio :value="false">否</a-radio>
                      </a-radio-group>
                    </a-form-item>
                    <a-form-item label="是否允许负库存" name="allowNegativeFlag">
                      <a-radio-group v-model:value="form.allowNegativeFlag">
                        <a-radio :value="true">是</a-radio>
                        <a-radio :value="false">否</a-radio>
                      </a-radio-group>
                    </a-form-item>
                     <a-form-item label="备注"  name="remark">
                      <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
                    </a-form-item>
          <!-- </a-row> -->

    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
 import { stkWarehouseApi } from '/@/api/business/mes/stock/stk-warehouse-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import EmployeeObjSelect from '/@/components/system/employee-obj-select/index.vue';
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // 处理员工选择变化
function handleEmployeeChange(employee) {
    form.tel = employee.phone;
    form.principalName = employee.actualName;
}
  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
              id: undefined, //主键
              remark: undefined, //备注
              number: undefined, //仓库编号
              name: undefined, //仓库名称
              address: undefined, //仓库地址
              principalId: undefined, //负责人id
              principalName: undefined, //负责人名称
              tel: undefined, //负责人电话
              openLocationFlag: undefined, //是否启用仓位管理;0否 1是
              allowNegativeFlag: undefined, //是否允许负库存;0否 1是
  };

  let form = reactive({ ...formDefault });

  const rules = {
                  // id: [{ required: true, message: '主键 必填' }],
                  number: [{ required: true, message: '仓库编号' }],
                  name: [{ required: true, message: '仓库名称' }],
                  openLocationFlag: [{ required: true, message: '是否启用仓位管理' }],
                  allowNegativeFlag: [{ required: true, message: '是否允许负库存' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await stkWarehouseApi.update(form);
      } else {
        await stkWarehouseApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

<!--
  * 主物料表
  *
  * @Author:    cjm
  * @Date:      2024-07-02 08:33:07
  * @Copyright  zscbdic
-->
<template>
  <a-modal
      :title="equipName"
      width="70%"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="" class="smart-query-form-item">
          <a-input style="width: 200px" v-model:value="queryForm.fieldName" placeholder="请输入属性名称" @change="handleSearch">
            <template #addonAfter>
              <SearchOutlined />
            </template>
          </a-input>
        </a-form-item>
      </a-row>
    </a-form>
  <div style="display: flex;flex-wrap: wrap">
    <a-card
            style="height: 80px; width:24%;margin: 0 0.8% 0.8% 0;"
            :body-style="{padding: ' 10px 0 0 5%'}"
            v-for="(property,index) in propertiesCopy" :key="index"
    >
      <span style="font-size: 12px;font-weight: normal;">{{property.fieldName}}</span>
      <p style="font-size: 14px; color: rgba(0, 0, 0, 0.85);font-weight: 500;margin-top: 5px">
        {{property.fieldValueStr}}
      </p>
    </a-card>
  </div>
    <div class="smart-query-table-page">
      <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change=" handlePageSizeChange"
          @showSizeChange="handlePageSizeChange"
          :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
import {reactive, ref} from 'vue';
import {equipmentScadaDataApi} from "/@/api/business/mes/equipment/equipment-scada-data-api.js";
import _ from 'lodash'
import {PAGE_SIZE_OPTIONS} from "/@/constants/common-const.js";
// ------------------------ 事件 ------------------------

const emits = defineEmits(['reloadList']);

// ------------------------ 显示与隐藏 ------------------------
// 是否显示
const visibleFlag = ref(false);
const equipName = ref('');

function show(equipmentId,equipmentName) {
  queryData(equipmentId)
  visibleFlag.value = true;
  equipName.value = equipmentName
}

// ------------------------ 设备属性列表及排序 ------------------------
// 总数
const total = ref(0);
const properties = ref([]);
// 克隆
const propertiesCopy = ref([])
async function queryData(equipmentId){
  let queryResult = await equipmentScadaDataApi.queryScadaProperties(equipmentId)
  properties.value = queryResult.data.properties
  total.value = queryResult.data.properties.length
  handleSort()
  propertiesCopy.value = _.cloneDeep(properties.value)
  handlePageSizeChange()
}

function handleSort(){
  // 对于属性值fieldValueStr进行排序，空的属性排在后面
  properties.value.sort((a) => {
    if (a.fieldValueStr === null|| a.fieldValueStr.length===0  ) {
      return 1;
    } else{
      return -1;
    }
  })
}
// ------------------ 处理搜索事件以及手动分页 ------------------
const defaultQueryForm = {
  fieldName: '',
  pageNum: 1,
  pageSize: 12,
};
const queryForm = reactive({...defaultQueryForm});


function handleSearch(){
  propertiesCopy.value = properties.value.filter((item)=>{
    return item.fieldName.includes(queryForm.fieldName)
  })
  // 搜索栏清空重新分页
  if(queryForm.fieldName.length===0){
    handlePageSizeChange()
  }
}

// 根据页码信息变化手动分页切割
function handlePageSizeChange(){
  propertiesCopy.value = properties.value
      .slice((queryForm.pageNum-1)*queryForm.pageSize,queryForm.pageNum*queryForm.pageSize)
}

function onClose() {
  Object.assign(queryForm, defaultQueryForm)
  visibleFlag.value = false;
}
defineExpose({
  show,
});
</script>

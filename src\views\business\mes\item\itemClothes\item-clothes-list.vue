<!--
  * 新增成衣表
  *
  * @Author:    cy
  * @Date:      2024-07-18 17:33:07
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <a-form-item label="停用标识" class="smart-query-form-item">
        <a-select v-model:value="queryForm.enableFlag" placeholder="请选择">
          <a-select-option value="false">启用</a-select-option>
          <a-select-option value="true">停用</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showAddForm" type="primary" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          新建
        </a-button>
        <a-button @click="showImportForm" size="small">
          <template #icon>
            <PlusOutlined />
          </template>
          导入
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 1300 }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="showEditForm(record)" type="link">编辑</a-button>
            <a-button @click="onDelete(record)" danger type="link">删除</a-button>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'number'">
          <a-button type="link" @click="showEditForm(record)">{{ text }}</a-button>
        </template>
        <template v-else-if="column.dataIndex === 'category'">
          <a-tag :color="ITEM_CATEGORY_ENUM.getEnum(text).color">{{ ITEM_CATEGORY_ENUM.getEnum(text).label }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-preview-container"><file-preview :file-list="record.imgUrl || []" type="picture" :width="35" /></div>
        </template>
        <!--  停用标识      -->
        <template v-else-if="column.dataIndex === 'enableFlag'">
          <a-tag :color="text === true ? 'red' : 'green'">{{ text === true ? '停用' : '启用' }}</a-tag>
        </template>
      </template>

      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">
                <a-typography-text>{{ value }}</a-typography-text>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <ItemColthesAddForm ref="addFormRef" @reload="queryData" />
    <ItemClothesEditForm ref="editFormRef" @reload="queryData" />
    <ImportForm
      ref="importFormRef"
      @reloadList="queryData"
      :downloadTemplateApi="itemClothesApi.downloadTemplate"
      :importfileApi="itemClothesApi.importfile"
    />
  </a-card>
</template>
<script setup>
  import { reactive, ref, onMounted, computed } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { itemClothesApi } from '/@/api/business/mes/item/item-clothes.js';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import FilePreview from '/@/components/support/file-preview/index.vue';
  import { ITEM_ATTRIBUTE_ENUM, ITEM_CATEGORY_ENUM } from '/@/constants/business/mes/item/item-const.js';
  import ItemColthesAddForm from './components/item-clothes-add-form.vue';
  import ItemClothesEditForm from './components/item-clothes-edit-from.vue';
  import ImportForm from '/@/components/business/mes/common/data-import-modal/index.vue';
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      align: 'center',
      width: 50,
    },
    {
      title: '物料编号',
      dataIndex: 'number',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '物料SKU编号',
      dataIndex: 'skuNumber',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '物料名称',
      dataIndex: 'name',
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '价格',
      dataIndex: 'price',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '停用标识',
      dataIndex: 'enableFlag',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '颜色',
      dataIndex: 'styleColor',
      ellipsis: true,
      width: 150,
      align: 'center',
    },
    {
      title: '尺码',
      dataIndex: 'size',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 90,
      align: 'center',
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    enableFlag: undefined, //停用标识;0启用，1停用
    attribute: ITEM_ATTRIBUTE_ENUM.CLOTHES.value,
    pageNum: 1,
    pageSize: 10,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await itemClothesApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  //-------------------------------合计----------------------------------------
  const totals = computed(() => {
    const sums = {
      totalPrice: 0, //总价
    };
    tableData.value.forEach((item) => {
      sums.totalPrice += parseFloat(item.price);
    });
    sums.totalPrice = parseFloat(sums.totalPrice.toFixed(3));
    return sums;
  });
  //计算剩余的空单元格
  const summaryColSpans = 6; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => columns.value.length - summaryColSpans - Object.keys(totals.value).length);

  // ---------------------------- 添加/修改 ----------------------------
  const addFormRef = ref();
  const editFormRef = ref();
  const importFormRef = ref();
  function showAddForm() {
    addFormRef.value.show();
  }
  function showEditForm(record) {
    editFormRef.value.show(record.id);
  }
  function showImportForm() {
    importFormRef.value.show();
  }
  // ---------------------------- 删除 ----------------------------
  function onDelete(record) {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该数据吗?',
      okText: '删除',
      okType: 'danger',
      onOk() {
        requestDelete(record.id);
      },
      cancelText: '取消',
      onCancel() {},
    });
  }
  async function requestDelete(id) {
    try {
      SmartLoading.show();
      await itemClothesApi.delete(id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }
</script>
<style scoped>
  .image-preview-container {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }

  .image-preview-container :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
</style>

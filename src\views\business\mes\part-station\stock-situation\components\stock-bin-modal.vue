<template>
  <a-modal v-model:open="open" width="60%" @cancel="closeModal" cancelText="关闭"
           :ok-button-props="{ style: { display: 'none' } }">


    <template v-slot:title>
      货位详情
      <a-popover placement="right" trigger="click">
        <template #content>
          <a-image
              :width="100"
              :src=qrCode
          />
        </template>
        <a-button @click="showQRById(id)" type="link">二维码</a-button>
      </a-popover>
    </template>

    <a-table :columns="columns" :dataSource="tickets" :pagination="false" bordered
             :scroll="{x:1300,y:400}"/>


  </a-modal>
</template>
<script setup>
import {onMounted, ref} from 'vue';
import {partStationBinApi} from "/@/api/business/mes/part-station/bin/part-station-bin-api.js";
import {message} from "ant-design-vue";
import {smartSentry} from "/@/lib/smart-sentry.js";

const open = ref(false);

const tickets = ref([]);//票据列表

const qrCode = ref(null);

const id = ref(null);

const columns = [
  {
    title: '指令单编号',
    dataIndex: 'instructOrderNumber',
    ellipsis: true,
    width: 135
  },
  {
    title: '物料编号',
    dataIndex: 'itemNumber',
    ellipsis: true,
    width: 135
  },
  {
    title: '扎号',
    dataIndex: 'tieNum',
    ellipsis: true,
    align: "center",
    width: 50

  },
  {
    title: '床号',
    dataIndex: 'cutNum',
    ellipsis: true,
    align: "center",
    width: 50
  },
  {
    title: '款式颜色',
    dataIndex: 'styleColor',
    ellipsis: true,
    width: 80
  },
  {
    title: '部位',
    dataIndex: 'positions',
    ellipsis: true,
    width: 80

  },
  {
    title: '尺码',
    dataIndex: 'size',
    ellipsis: true,
    width: 120

  },
  {
    title: '数量',
    dataIndex: 'num',
    ellipsis: true,
    align: "center",
    width: 50
  },
  {
    title: '入库时间',
    dataIndex: 'stockInTime',
    ellipsis: true,
    width: 145
  },
  {
    title: '最后盘库时间',
    dataIndex: 'lastCheckTime',
    ellipsis: true,
    width: 145
  },
]

defineExpose({
  showModal,
});


async function showQRById(id) {
  // console.log(id)
  try {
    let res = await partStationBinApi.getQRURL(id)
    qrCode.value = res.data
  } catch (e) {
    message.error("二维码不存在")
    smartSentry.captureError(e)
  }
}

async function showModal(binId) {
  open.value = true;
  id.value = binId;
  await onSearch(binId);
}

function closeModal() {
  open.value = false;
  id.value = null;
}

async function onSearch(binId) {
  let res = await partStationBinApi.queryBinFeTicket(binId)
  tickets.value = res.data
  console.log(tickets)
}
</script>
<style scoped lang="less">

</style>

/**
 * 报工功能 api 封装
 *
 * @Author:   fkf
 */
import { postRequest } from '/@/lib/axios';

export const workRepoetApi = {

    /**
     * 通过  <AUTHOR>
     */
    assistReport: (param) => {
        return postRequest('/workReport/assist',param);
    },
    /**
     * 驳回  <AUTHOR>
     */
    unReport: (param) => {
        return postRequest('/workReport/unReport/tickets',param);
    },

    /**
     * 根据菲票报工
     *   <AUTHOR>
     */
    feiTicketReport: (data) => {
        return postRequest('/workReport/feiTicket',data);
    },

};

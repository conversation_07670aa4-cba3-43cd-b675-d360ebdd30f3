<!--
  * 更改菲票数量对话框
  *
  * @Author:    Linwj
  * @Date:      2024-10-01 16:55:01
  * @Copyright  Linwj
-->
<template>
  <a-modal
      :title="form.id ? '生产指令单编号：'+form.instructOrderNumber : '添加'"
      width="400px"
      :open="visibleFlag"
      @cancel="onClose"
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="modifyNumFormRef" :model="form" >
      <a-col>
        <a-form-item label="数量"  name="num">
          <a-input-number style="width: 100%" v-model:value="form.num" placeholder="数量" />
        </a-form-item>
      </a-col>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="change(form)">确认</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { feTicketApi } from '/@/api/business/mes/tailor/fe-ticket-api';
  import { smartSentry } from '/@/lib/smart-sentry';


  // ------------------------ 事件 ------------------------
  async function change(data){
    let param = {
      id:data.id,
      num:data.num
    }
    try{
      let res = await feTicketApi.modifyNum(param)
      message.success(res.msg)
    }catch (e) {
      smartSentry.captureError(e)
    }finally {
      emits('reloadList');
      onClose()
    }
  }
  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      modifyNumFormRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const modifyNumFormRef = ref();

  const formDefault = {
      num: undefined, //数量
  };

  let form = reactive({ ...formDefault });

  const rules = {
        num: [{ required: true, message: '数量 必填' }],
  };


  defineExpose({
    show,
  });
</script>

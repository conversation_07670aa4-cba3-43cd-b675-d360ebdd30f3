/*
 * 单据序列号
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-03 21:57:52
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const serialNumberApi = {
  // 生成单号 <AUTHOR>
  generate: (generateForm) => {
    return postRequest('/support/serialNumber/generate', generateForm);
  },
  // 获取所有单号定义 <AUTHOR>
  getAll: () => {
    return getRequest('/support/serialNumber/all');
  },
  // 获取生成记录 <AUTHOR>
  queryRecord: (form) => {
    return postRequest('/support/serialNumber/queryRecord', form);
  },
};

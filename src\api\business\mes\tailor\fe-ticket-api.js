/**
 * 菲票 api 封装
 *
 * @Author:    cjm
 * @Date:      2024-07-14 16:55:01
 * @Copyright  cjm
 */
import { postRequest, getRequest, getDownload, postDownload } from '/@/lib/axios';
import axios from 'axios';

export const feTicketApi = {
  /**
   * 分页查询  <AUTHOR>
   */
  queryPage: (param) => {
    return postRequest('/feTicket/queryPage', param);
  },

  /**
   * 增加  <AUTHOR>
   */
  add: (param) => {
    return postRequest('/feTicket/add', param);
  },

  /**
   * 修改  <AUTHOR>
   */
  update: (param) => {
    return postRequest('/feTicket/update', param);
  },

  /**
   * 删除  <AUTHOR>
   */
  delete: (id) => {
    return getRequest(`/feTicket/delete/${id}`);
  },

  /**
   * 批量删除  <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/feTicket/batchDelete', idList);
  },
  /**
   * 下载菲票  <AUTHOR>
   */
  downLoad: (param) => {
    return postDownload('/feTicket/downLoad', param);
  },

  /**
   * 更改菲票数量  <AUTHOR>
   */
  modifyNum: (param) => {
    return postRequest('/feTicket/modifyNum', param);
  },

  /**
   * 查看菲票二维码 <AUTHOR>
   */
  getQRURL: (id) => {
    return getRequest(`/feTicket/getQrCode/${id}`);
  },

  /**
   * 根据菲票id查询详情
   */
  queryById: (id) => {
    return getRequest(`/feTicket/queryById/${id}`);
  },
  /**
   * 菲票追踪
   */
  trace: (param) => {
    return postRequest('/feTicketTraceLog/queryPage', param);
  },

  /**
   * 生成预览按比例菲票和裁床单
   */
  preview: (param) => {
    return postRequest('/feTicket/arrangeByRatio/preview', param);
  },
  /**
   * 提交按比例生成菲票和裁床单
   */
  addByRatio: (param) => {
    return postRequest('/feTicket/arrangeByRatio', param);
  },
  /**
   * 查询菲票列表
   */
  queryList: (param) => {
    return postRequest('/feTicket/queryList', param);
  },
  /**
   * 查询指令单历史菲票部位
   */
  queryPartList: (produceInstructOrderId) => {
    return getRequest(`/feTicket/queryPartList?produceInstructOrderId=${produceInstructOrderId}`);
  },
  /**
   * 拆分菲票
   */
  splitTicket: (param) => {
    return postRequest('/feTicket/splitTicket', param);
  },
};

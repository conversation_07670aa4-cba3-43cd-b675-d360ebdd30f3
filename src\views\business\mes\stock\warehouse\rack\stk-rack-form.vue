<!--
  * 货架
  *
  * @Author:    cjm
  * @Date:      2025-02-05 15:13:43
  * @Copyright  zscbdic
-->
<template>
  <a-modal :title="form.id ? '编辑' : '添加'" width="400px" :open="visibleFlag" @cancel="onClose" :maskClosable="false" :destroyOnClose="true">
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <a-form-item label="所属仓库" name="warehouseId" :label-col="{ span: 5 }">
        <WarehouseSelect v-model:value="form.warehouseId" style="width: 100%" @change="handleWarehouseSelect" />
      </a-form-item>

      <a-form-item label="货架编号" name="number">
        <a-input style="width: 100%" v-model:value="form.number" placeholder="货架编号" />
      </a-form-item>
      <a-form-item label="位置描述" name="location">
        <a-input style="width: 100%" v-model:value="form.location" placeholder="位置描述" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input style="width: 100%" v-model:value="form.remark" placeholder="备注" />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-modal>
</template>
<script setup>
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { stkRackApi } from '/@/api/business/mes/stock/stk-rack-api.js';
  import { smartSentry } from '/@/lib/smart-sentry';
  import WarehouseSelect from '/@/components/business/mes/stock/werehouse/warehouse-select/index.vue';

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // 仓库选择处理函数
  function handleWarehouseSelect(item) {
    form.warehouseId = item.id;
    form.warehouseName = item.name;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
    id: undefined, //主键
    remark: undefined, //备注
    warehouseId: undefined, //所属仓库id
    warehouseName: undefined, //所属仓库名称
    number: undefined, //货架编号
    location: undefined, //位置描述
  };

  let form = reactive({ ...formDefault });

  const rules = {
    warehouseId: [{ required: true, message: '所属仓库 必填' }],
    number: [{ required: true, message: '货架编号 必填' }],
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.id) {
        await stkRackApi.update(form);
      } else {
        await stkRackApi.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>

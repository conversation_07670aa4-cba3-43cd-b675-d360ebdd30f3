<!--
  * 执行汇总
  *
  * @Author:    xmt
  * @Date:      2024-07-04 11:12:45
  * @Copyright  zscbdic
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键字查询" class="smart-query-form-item">
        <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字查询" />
      </a-form-item>
      <InsItemSelect ref="insItemSelectRef" @change="insItemChange" />
      <a-form-item label="优先级" class="smart-query-form-item">
        <a-select ref="initProducePriority" v-model:value="queryForm.priority" style="width: 100px" :options="OPTIONS" />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block"></div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :scroll="{ x: 2200 }"
      @expand="onRowExpand"
      :expand-column-width="70"
      @change="handleTableChange"
    >
      <template #expandedRowRender="{ record }">
        <execute-detail :id="record.id" />
      </template>
      <template #expandColumnTitle>
        <span>展开</span>
      </template>

      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'produceStatus'">
          <span> {{ PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM.getEnum(text).label }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'produceType'">
          <span>{{ PRODUCE_TYPE_ENUM.getEnum(text).label }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag :color="PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).color">
            {{ PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM.getEnum(text).label }}
          </a-tag>
        </template>
        <template v-else-if="column.dataIndex === 'unCutNum'">
          <span v-if="record.produceNum - record.cutNum <= 0" style="color: green">
            +{{ -(record.produceNum - record.cutNum) }}
            <span v-if="record.produceNum - record.cutNum < 0" style="color: green">超裁</span>
          </span>
          <span v-else style="color: red"> {{ -(record.produceNum - record.cutNum) }} 欠裁 </span>
        </template>
        <template v-else-if="column.dataIndex === 'unFinishNum'">
          <span v-if="record.produceNum - record.finishNum <= 0" style="color: green"> +{{ -(record.produceNum - record.finishNum) }} </span>
          <span v-else style="color: red">
            {{ -(record.produceNum - record.finishNum) }}
          </span>
        </template>
        <template v-else-if="column.dataIndex === 'percent'">
          <!--toFix()返回的是字符串 ，percent接收的是数值，parseInt转换-->
          <a-progress :percent="parseInt(((record.finishNum / record.produceNum) * 100).toFixed(0))" :size="6" />
        </template>
        <template v-else-if="column.dataIndex === 'imgUrl'">
          <div class="image-preview-container">
            <file-preview :file-list="(record.imgUrl && record.imgUrl.length > 0) > 0 ? [record.imgUrl[0]] : []" type="picture" :width="35" />
          </div>
        </template>
      </template>

      <!-- 合计行 -->
      <template #summary>
        <a-table-summary v-if="tableData && tableData.length > 0">
          <a-table-summary-row>
            <a-table-summary-cell :colspan="summaryColSpans" style="text-align: center">当页合计</a-table-summary-cell>
            <template v-for="(value, key) in totals" :key="key">
              <a-table-summary-cell style="text-align: center">
                <template v-if="key === 'totalUnCutNum'">
                  <a-typography-text :style="{ color: value > 0 ? 'green' : value < 0 ? 'red' : '' }">
                    {{ value !== 0 ? (value > 0 ? '+' + value + ' 超裁' : value + ' 欠裁') : 0 }}
                  </a-typography-text>
                </template>
                <template v-else-if="key === 'totalUnFinishNum'">
                  <a-typography-text :style="{ color: value > 0 ? 'green' : value < 0 ? 'red' : '' }">
                    {{ value }}
                  </a-typography-text>
                </template>
                <template v-else>
                  {{ value }}
                </template>
              </a-table-summary-cell>
            </template>
            <!-- 剩余的空单元格 -->
            <template v-for="index in emptyColumnNum" :key="index">
              <a-table-summary-cell style="text-align: center" />
            </template>
          </a-table-summary-row>
        </a-table-summary>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>
<script setup>
  import { onMounted, reactive, ref, computed } from 'vue';
  import { PAGE_SIZE_OPTIONS } from '/src/constants/common-const';
  import { smartSentry } from '/src/lib/smart-sentry';
  import TableOperator from '/src/components/support/table-operator/index.vue';
  import { produceReportApi } from '/@/api/business/mes/produce/produce-report-api.js';
  import {
    PRODUCE_INSTRUCT_ORDER_PRIORITY_ENUM,
    PRODUCE_INSTRUCT_ORDER_PRODUCE_STATUS_ENUM,
    PRODUCE_TYPE_ENUM,
  } from '/@/constants/business/mes/produce/produce-instruct-order-const.js';
  import InsItemSelect from '/@/components/business/mes/produce/ins-item-select/index.vue';
  import ExecuteDetail from '/@/views/business/mes/report/produce/execute-summary/components/execute-detail.vue';
  import FilePreview from '/@/components/support/file-preview/index.vue';

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '图片',
      dataIndex: 'imgUrl',
      ellipsis: true,
      width: 50,
      align: 'center',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      ellipsis: true,
      width: 80,
      align: 'center',
    },
    {
      title: '单据编号',
      dataIndex: 'instructNumber',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '物料编号',
      dataIndex: 'itemNumber',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '物料名称',
      dataIndex: 'itemName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '进度',
      dataIndex: 'percent',
      ellipsis: true,
      width: 120,
      align: 'center',
    },
    {
      title: '下达数量',
      dataIndex: 'produceNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },

    {
      title: '已裁剪数量',
      dataIndex: 'cutNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '未裁剪数量',
      dataIndex: 'unCutNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '完成数量',
      dataIndex: 'finishNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '未完成数',
      dataIndex: 'unFinishNum',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '生产状态',
      dataIndex: 'produceStatus',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '生产类型',
      dataIndex: 'produceType',
      ellipsis: true,
      width: 100,
      align: 'center',
    },
    {
      title: '交货日期',
      dataIndex: 'deliverTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '下达日期',
      dataIndex: 'issuedTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '真实开工时间',
      dataIndex: 'realStartTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
    {
      title: '真实完工时间',
      dataIndex: 'realFinishTime',
      ellipsis: true,
      align: 'center',
      sorter: true, //启用排序
      sortDirections: ['ascend', 'descend'], // 允许升序和降序
    },
  ]);

  const insItemSelectRef = ref();
  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    queryKey: undefined, //关键字查询
    pageNum: 1,
    pageSize: 10,
    instructNumber: undefined,
    itemNumber: undefined,
    priority: undefined, //优先级;0一般,1紧急,2非常紧急
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  const expandedRowKeys = ref([]);

  async function onRowExpand(expanded, record) {
    if (expanded) {
      // let executeDetail = await produceReportApi.queryExecuteDetail(record.id);
      // record.executeDetail = executeDetail.data;
      // expandedRowKeys.value = [record.id];
    } else {
      // record.executeDetail = undefined;
      // expandedRowKeys.value = [];
    }
  }

  function insItemChange(data) {
    queryForm.instructNumber = data.instructOrderNumber;
    queryForm.itemNumber = data.itemNumber;
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    insItemSelectRef.value.clearAllNumber();
    queryForm.pageSize = pageSize;
    queryData();
  }
  //处理表格时间顺序变化事件
  async function handleTableChange(pagination, filters, sorter) {
    if (sorter && sorter.field === 'deliverTime') {
      queryForm.sortItemList = [
        {
          column: 'deliver_time',
          isAsc: sorter.order === 'ascend',
        },
      ];
    } else if (sorter && sorter.field === 'issuedTime') {
      queryForm.sortItemList = [
        {
          column: 'issued_time',
          isAsc: sorter.order === 'ascend',
        },
      ];
    } else if (sorter && sorter.field === 'realStartTime') {
      queryForm.sortItemList = [
        {
          column: 'real_start_time',
          isAsc: sorter.order === 'ascend',
        },
      ];
    } else if (sorter && sorter.field === 'realFinishTime') {
      queryForm.sortItemList = [
        {
          column: 'real_finish_time',
          isAsc: sorter.order === 'ascend',
        },
      ];
    }
    if (!sorter.order) {
      queryForm.sortItemList = [
        {
          column: 'create_time',
          isAsc: false,
        },
      ];
    }
    await queryData();
  }
  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await produceReportApi.queryExecuteSummary(queryForm);
      if (!queryResult.data) {
        tableData.value = queryResult.data;
        total.value = 0;
      } else {
        tableData.value = queryResult.data.list;
        total.value = queryResult.data.total;
      }
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  //-------------------------------合计----------------------------------------

  const totals = computed(() => {
    const sums = {
      totalProduceNum: 0, // 下达数量
      totalCutNum: 0, // 裁剪数量
      totalUnCutNum: 0, // 未裁剪数量 = 总裁剪数量 - 总下达数量
      totalFinishNum: 0, // 完成数量
      totalUnFinishNum: 0, // 未完成数量 = 总完成数量 - 总下达数量
    };
    tableData.value.forEach((item) => {
      sums.totalProduceNum += item.produceNum;
      sums.totalCutNum += item.cutNum;
      sums.totalFinishNum += item.finishNum;
    });
    sums.totalUnCutNum = sums.totalCutNum - sums.totalProduceNum;
    sums.totalUnFinishNum = sums.totalFinishNum - sums.totalProduceNum;

    return sums;
  });

  //计算剩余的空单元格
  const summaryColSpans = 8; //"合计"单元格占据列数
  const emptyColumnNum = computed(() => 1 + columns.value.length - summaryColSpans - Object.keys(totals.value).length); //加1是因为有展开列

  // 指令单优先级
  const OPTIONS = [
    {
      value: '0',
      label: '一般',
      color: '#46c26f',
    },
    {
      value: '1',
      label: '紧急',
      color: '#f0a800',
    },
    {
      value: '2',
      label: '非常紧急',
      color: '#eb5050',
    },
  ];
</script>
<style scoped>
  .image-preview-container {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin: 0 auto;
  }

  .image-preview-container :deep(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 4px;
  }
</style>

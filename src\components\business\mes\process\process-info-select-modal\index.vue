<template>
  <a-modal v-model:open="visible" :width="900" title="选择物料" @cancel="closeModal" @ok="onSelectItem">
    <a-form class="smart-query-form">
      <a-row class="smart-query-form-row">
        <a-form-item label="关键字" class="smart-query-form-item">
          <a-input style="width: 150px" v-model:value="queryForm.queryKey" placeholder="关键字" />
        </a-form-item>

        <a-form-item class="smart-query-form-item smart-margin-left10">
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="reset" class="smart-margin-left10">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-row>
    </a-form>
    <a-table
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange, type: 'checkbox' }"
      :loading="tableLoading"
      size="small"
      :columns="columns"
      :data-source="tableData"
      :pagination="false"
      bordered
      rowKey="id"
      :scroll="{ y: 300 }"
    />
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-modal>
</template>
<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/constants/common-const.js';
  import { smartSentry } from '/@/lib/smart-sentry.js';
  import { message } from 'ant-design-vue';
  import { dictApi } from '/@/api/support/dict-api.js';
  import { processApi } from '/@/api/business/mes/process/process-api.js';

  const emits = defineEmits(['selectData']);
  defineExpose({
    showModal,
  });
  const props = defineProps({});

  const visible = ref(false);
  const selectedRowKeys = ref([]);

  const tableLoading = ref(false);
  const total = ref();

  const queryFormState = {
    queryKey: undefined, //关键字查询
    pageNum: 1,
    pageSize: PAGE_SIZE,
    sortItemList: [{ column: 'create_time', isAsc: false }],
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });

  //将工序类型映射
  const codeNames = ref([]);
  async function getProcessType(code) {
    const list = await dictApi.valueList(code);
    codeNames.value = list.data;
  }

  const columns = ref([
    {
      title: '工序名称',
      dataIndex: 'name',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '部位',
      dataIndex: 'position',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '工序类型',
      dataIndex: 'processType',
      ellipsis: true,
      align: 'center',
      customRender: (text) => {
        for (let i = 0; i < codeNames.value.length; i++) {
          if (text.text === codeNames.value[i].valueCode) {
            //将工序类型重新映射
            return codeNames.value[i].valueName;
          }
        }
      },
    },
    {
      title: '标准工时',
      dataIndex: 'standardTime',
      ellipsis: true,
      align: 'center',
    },
    {
      title: '工价一',
      dataIndex: 'unitPrice1',
      ellipsis: true,
      align: 'center',
      customRender: (text) => {
        return '￥' + text.value.toFixed(3);
      },
    },
    {
      title: '工序控制',
      dataIndex: 'processControl',
      ellipsis: true,
      align: 'center',
      customRender: (text) => {
        if (text.text === '0') return '自制';
        else if (text.text === '1') return '委外';
        else if (text.text === '2') return '不限';
        else return '未知';
      },
    },
  ]);
  let tableData = ref([]);

  async function showModal() {
    await getProcessType('PROCESS_TYPE');
    selectedRowKeys.value = [];
    visible.value = true;
    onSearch();
  }

  function closeModal() {
    Object.assign(queryForm, queryFormState);
    selectedRowKeys.value = [];
    visible.value = false;
  }

  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  function reset() {
    Object.assign(queryForm, queryFormState);
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await processApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  function onSelectChange(keys) {
    selectedRowKeys.value = keys;
  }
  function onSelectItem() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择工序');
      return;
    }

    // 获取选中的数据
    const selectedItems = tableData.value.filter((item) => selectedRowKeys.value.includes(item.id));

    if (selectedItems.length > 0) {
      emits('selectData', selectedItems);
      closeModal();
    } else {
      message.warning('未找到选中的工序数据');
    }
  }
</script>

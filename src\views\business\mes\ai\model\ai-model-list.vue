<template>
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="关键词查询" class="smart-query-form-item">
        <a-input v-model:value="queryForm.queryKey" style="width: 180px" placeholder="请输入" allowClear />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="queryData">
          <template #icon><SearchOutlined /></template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>

  <a-card>
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="showForm()" type="primary" size="small" style="height: 30px">
          <template #icon><PlusOutlined /></template>
          新建
        </a-button>
      </div>
    </a-row>

    <!-- 卡片布局 -->
    <div class="card-list">
      <a-row :gutter="24">
        <a-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8" v-for="item in tableData" :key="item.id">
          <a-card :bordered="false" hoverable @click="showForm(item)" class="card-item">
            <div class="card-tag">
              <a-tag :color="item.enableFlag ? 'green' : 'red'" class="status-tag">{{ item.enableFlag ? '启用' : '停用' }}</a-tag>
            </div>

            <div class="card-content">
              <div class="card-header">
                <a-avatar shape="square" :size="50" :src="getAvatarSrc(item.avatar)" />
                <div class="model-info">
                  <div class="model-name">{{ item.modelName }}</div>
                  <div class="model-nickname">{{ item.modelNickname }}</div>

                </div>
              </div>

              <div class="card-footer">
                <div class="card-tags">
                  <a-tag class="tag-type" color="#e7f3ff">{{ getModelTypeDesc(item.modelType) }}</a-tag>
                  <a-tag class="tag" color="#ece7fa" v-if="item.useToolFlag">Tools</a-tag>
                  <a-tag class="tag" color="#ece7fa" v-if="item.knowledgeUseFlag">知识库</a-tag>
                  <a-tag class="tag" color="#ece7fa" v-if="item.visionUseFlag">视觉</a-tag>
                </div>
                <div>
                  <a-button type="link" @click="showForm(item)" class="edit-btn">编辑</a-button>
                  <a-button type="link" danger @click.stop="confirmDelete(item)" class="delete-btn">删除</a-button>
                </div>

              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <ai-model-form ref="formModalRef" @reloadList="queryData" />
</template>

<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { aiModelApi } from '/@/api/business/mes/ai/ai-model-api';
  import { message, Modal } from 'ant-design-vue';
  import { AI_MODEL_TYPE_ENUM } from '/@/constants/business/mes/ai/ai-model-const';
  import aiModelForm from '/@/views/business/mes/ai/model/ai-model-form.vue';
  import { smartSentry } from '/@/lib/smart-sentry';

  const queryFormState = {
    queryKey: undefined,
    pageNum: 1,
    pageSize: 10,
  };
  // 查询表单form
  const queryForm = reactive({ ...queryFormState });
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, queryFormState);
    queryForm.pageSize = pageSize;
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await aiModelApi.queryPage(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      message.error('数据加载失败');
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  //----------------------------------------单个删除---------------------------------
  //二次确认
  function confirmDelete(item) {
    Modal.confirm({
      title: '删除确认',
      content: `确定要删除模型吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk() {
        handleDelete(item);
      },
      onCancel() {},
    });
  }
  //删除
  async function handleDelete(item) {
    try {
      await aiModelApi.delete(item.id);
      message.success('删除成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 获取模型类型枚举值描述
  const getModelTypeDesc = (typeValue) => {
    const item = Object.values(AI_MODEL_TYPE_ENUM).find((i) => i.value === typeValue);
    return item ? item.desc : typeValue || '未知类型';
  };

  //获取头像base64
  function getAvatarSrc(avatar) {
    if (!avatar) return '';
    if (avatar.startsWith('data:image')) return avatar;
    return `data:image/png;base64,${avatar}`;
  }
  //打开编辑模态框
  const formModalRef = ref(null);
  function showForm(data) {
    formModalRef.value.show(data);
  }

  onMounted(() => {
    queryData();
  });
</script>

<style scoped>
  .card-list {
    margin-bottom: 16px;
  }

  .card-item {
    position: relative;
    margin-bottom: 24px;
  }

  .card-tag {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 1;
  }

  .card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 100px;
  }

  .card-header {
    display: flex;
    margin-bottom: 16px;
  }

  .model-info {
    padding-left: 12px;
  }

  .model-nickname {
    font-size: 14px;
    color: #909399;
  }

  .model-name {
    font-size: 16px;
    font-weight: 500;
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;
  }

  .card-tags {
    display: flex;
    flex-wrap: wrap;
  }

  .status-tag {
    font-size: 14px;
    height: auto;
    line-height: 25px;
  }

  .delete-btn {
    padding: 0;
    align-self: flex-end;
  }

  .tag-type {
    color: #2764c2;
    margin-right: 4px;
    margin-bottom: 4px;
  }
  .tag {
    color: #845acc;
    margin-right: 4px;
    margin-bottom: 4px;
  }
</style>

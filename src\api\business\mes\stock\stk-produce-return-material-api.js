/**
 * 生产退料单 api 封装
 *
 * @Author:    pxz
 * @Date:      2025-02-16
 * @Copyright  zscbdic
 */
import { postRequest, getRequest } from '/@/lib/axios';

export const stkProduceReturnMaterialApi = {
    /**
     * 分页查询  <AUTHOR>
     */
    queryPage: (param) => {
        return postRequest('/stkProdReturnMtrl/queryPage', param);
    },
    /**
     * 添加  <AUTHOR>
     */
    add: (param) => {
        return postRequest('/stkProdReturnMtrl/add', param);
    },
    /**
     * 修改  <AUTHOR>
     */
    update: (param) => {
        return postRequest('/stkProdReturnMtrl/update', param);
    },
    /**
     * 根据id查询  <AUTHOR>
     */
    getById: (id) => {
        return getRequest(`/stkProdReturnMtrl/byId?id=${id}`);
    },
    /**
     * 修改单据状态  <AUTHOR>
     */
    updateStatus: (id) => {
        return getRequest(`/stkProdReturnMtrl/status?id=${id}`);
    },
    /**
     * 删除单据  <AUTHOR>
     */
    deleteById: (id) => {
        return getRequest(`/stkProdReturnMtrl/delete?id=${id}`);
    }
}